<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			// Load Objects for page -------------------------------------------------------------------- ::
			this.objMemberFieldSet							= CreateObject("component","model.admin.MemberFieldSets.MemberFieldSets");
			// use resourceID of the site for security -------------------------------------------------- ::
			this.siteResourceID = arguments.event.getValue('mc_siteInfo.siteSiteResourceID');
			local.toolTypeSiteResourceID = arguments.event.getValue('mc_admintoolInfo.toolType.siteResourceID');
			
			// set rights into event
			local.tmpRights = buildRightAssignments(siteResourceID=local.toolTypeSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));			
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;			

			// build quick links ------------------------------------------------------------------------ ::
			this.link.list = buildCurrentLink(arguments.event,"list");
			this.link.editFieldSet = buildCurrentLink(arguments.event,"editFieldSet") & "&mode=direct";
			this.link.editField	= buildCurrentLink(arguments.event,"editField") & "&mode=stream";
			this.link.saveField	= buildCurrentLink(arguments.event,"saveField") & "&mode=stream";
			this.link.exportStructureZIP = buildCurrentLink(arguments.event,"exportStructureZIP") & "&mode=stream";
			this.link.doImportFieldSets	= buildCurrentLink(arguments.event,"doImportFieldSets") & "&mode=stream";
			this.link.previewFieldset = buildLinkToTool(toolType='MemberFieldSetAdmin',mca_ta='previewFieldset') & "&mode=stream";
			this.link.copyFieldSet = buildCurrentLink(arguments.event,"copyFieldSet") & "&mode=direct";
			this.link.message = buildCurrentLink(arguments.event,"message");
			// method to run ---------------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
	
	<cffunction name="list" access="public" output="false" returntype="struct" hint="displays the fieldsets">
		<cfargument name="event" type="any">
		<cfargument name="impExData" type="string" required="false">
	
		<cfset var local = structNew()>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.viewMemberFieldSetAdmin') is not 1>
			<cflocation url="#this.link.message#&message=1" addtoken="false">
		</cfif>

		<cfif arguments.event.getValue('tab','') eq 'ex'>
			<cfif arguments.event.getValue('importFileName','') neq ''>
				<cfset local.prepResult = prepareMemberFieldSetImport(event=arguments.event)>
			</cfif>
		</cfif>

		<cfset local.fieldsetLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberFieldSetsJSON&meth=getFieldsSetList&mode=stream">
		<cfset local.FSAuditLogLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberFieldSetsJSON&meth=getFieldSetsAuditLog&mode=stream">
		
		<cfset local.qryCategories = this.objMemberFieldSet.getMemberFieldSetCategories(siteID=arguments.event.getValue('mc_siteinfo.siteID'))>

		<cfset local.filterKeyName = "MFSAFieldSets">
		<cfset local.tmpStr = { "fFieldSetName": '', "fFSCategoryID":0, "fFieldSetUID":'' }>
		<cfset local.MFSFilter = application.objCommon.getToolFiltersData(keyname=local.filterKeyName, defaultFilterData=local.tmpStr)>

		<cfset local.filterKeyNameLog = "MFSAFieldSetsAuditLog">
		<cfset local.tmpStrLog = { "fDescription": '', "fDateFrom":'#dateFormat(dateAdd('d',-14,now()),"m/d/yyyy")#', "fDateTo":'#dateFormat(now(),"m/d/yyyy")#' }>
		<cfset local.MFSLogFilter = application.objCommon.getToolFiltersData(keyname=local.filterKeyNameLog, defaultFilterData=local.tmpStrLog)>
		<cfif len(local.MFSLogFilter.fDateFrom)>
			<cfset local.MFSLogFilter.fDateFrom = dateFormat(local.MFSLogFilter.fDateFrom,"m/d/yyyy")>
		</cfif>
		<cfif len(local.MFSLogFilter.fDateTo)>
			<cfset local.MFSLogFilter.fDateTo = dateFormat(local.MFSLogFilter.fDateTo,"m/d/yyyy")>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_list.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>		
	</cffunction>
	
	<cffunction name="editFieldSet" access="public" output="false" returntype="struct" hint="creates a new fieldset">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.viewMemberFieldSetAdmin') is not 1>
			<cflocation url="#this.link.message#&message=1&mode=direct" addtoken="false">
		</cfif>

		<cfset local.fieldSetID = int(val(arguments.event.getValue('fsID',0)))>
		<cfset local.usageMode = arguments.event.getValue('usageMode','FSAdmin')>
		<cfset local.isModalContent = arguments.event.getValue('isModalContent', 1)>
		<cfset local.qryFieldSet = this.objMemberFieldSet.getFieldSet(fieldSetID=local.fieldSetID)>
		<cfset local.qryCategories = this.objMemberFieldSet.getMemberFieldSetCategories(siteID=arguments.event.getValue('mc_siteinfo.siteID'))>
		<cfset local.fieldSetID = val(local.qryFieldSet.fieldsetID)>

		<cfif local.fieldSetID GT 0>
			<cfset local.memberFieldsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberFieldSetsJSON&meth=getMemberFields&fieldSetID=#local.fieldSetID#&mode=stream">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editFieldSet.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="previewFieldset" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.qryFieldSet = this.objMemberFieldSet.getFieldSet(fieldSetID=arguments.event.getValue('fsID',0))>

		<!--- if a superuser, pick a random membernumber from the org instead of their own, since they aren't a member in this org --->
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and arguments.event.getValue('mc_siteinfo.orgID') is not 1>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetMemberNumber">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT TOP 1 memberNumber
				FROM dbo.ams_members
				WHERE orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">
				AND status = 'A';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfset local.memberNumberForPreview = local.qryGetMemberNumber.memberNumber>
		<cfelse>
			<cfset local.memberNumberForPreview = session.cfcUser.memberData.memberNumber>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_previewFieldset.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewFieldSetUsagesByMode" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.qryFieldSetUsages = this.objMemberFieldSet.getFieldSetUsagesByMode(siteID=arguments.event.getValue('mc_siteinfo.siteID'),
			fieldSetID=arguments.event.getValue('fsID',0), usageMode=arguments.event.getValue('usageMode',''))>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_viewFieldsetUsages.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="copyFieldSet" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.fsid = int(val(arguments.event.getValue('fsid',0)))>
	
		<cfif local.fsid eq 0>
			<cflocation url="#this.link.list#" addtoken="no">
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFieldSet">
			SELECT mfs.fieldsetID, mfs.fieldsetName
			FROM dbo.ams_memberFieldSets as mfs
			WHERE mfs.fieldSetID = <cfqueryparam value="#local.fsid#" cfsqltype="cf_sql_integer">
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_fieldsetCopy.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="checkFieldSetName" access="public" output=false returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="fsID" type="numeric" required="true">
		<cfargument name="fieldSetName" type="string" required="true">
		
		<cfset var qryCheckFSName = "">

		<cfquery name="qryCheckFSName" datasource="#application.dsn.membercentral.dsn#">
			select mfs.fieldsetID
			from dbo.ams_memberFieldSets mfs
			where mfs.siteID = <cfqueryparam value="#arguments.mcproxy_siteID#" cfsqltype="CF_SQL_INTEGER">
			AND mfs.fieldsetName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fieldSetName#">
			<cfif arguments.fsID neq 0>
				AND mfs.fieldsetID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fsID#">
			</cfif>
		</cfquery>
		
		<cfreturn { "success":true, "fsnameinuse":qryCheckFSName.recordCount GT 0 }>
	</cffunction>
	
	<cffunction name="saveCopyFieldSet" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="defaultLanguageID" type="numeric" required="true">
		<cfargument name="fieldsetID" type="numeric" required="true">
		<cfargument name="newFieldSetName" type="string" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfif arguments.fieldSetID eq 0 or not len(arguments.newFieldSetName)>
				<cfthrow message="Invalid request">
			</cfif>

			<cfstoredproc procedure="ams_copyFieldSet" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.defaultLanguageID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldsetID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.newFieldSetName#">
			</cfstoredproc>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveMemberFieldSet" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="defaultLanguageID" type="numeric" required="true">
		<cfargument name="fieldsetID" type="numeric" required="true">
		<cfargument name="fieldSetName" type="string" required="true">
		<cfargument name="nameFormat" type="string" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="newCategoryName" type="string" required="true">
		<cfargument name="maskEmail" type="boolean" required="true">
		<cfargument name="showHelp" type="boolean" required="true">
		<cfargument name="fieldSetUID" type="string" required="true">
		<cfargument name="descriptionPosition" type="string" required="true">
		<cfargument name="descriptionContent" type="string" required="true">
		<cfargument name="usageMode" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>	
			<cfset local.SiteSRID = application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode).siteSiteResourceID>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySaveMemberFieldSet">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					<cfif arguments.fieldsetID GT 0>
						IF OBJECT_ID('tempdb..##tmpExistingMFS') IS NOT NULL
							DROP TABLE ##tmpExistingMFS;
						IF OBJECT_ID('tempdb..##tmpLogMessages') IS NOT NULL
							DROP TABLE ##tmpLogMessages;
						CREATE TABLE ##tmpExistingMFS (fieldsetName varchar(200), nameFormat varchar(10), showHelp bit, [uid] varchar(36), 
							descriptionContentID int, descriptionContent varchar(max), categoryID int, categoryName varchar(200),
							maskEmail bit, descriptionPosition varchar(25));
						CREATE TABLE ##tmpLogMessages (rowID int IDENTITY(1,1), msg varchar(MAX));
					</cfif>
					
					DECLARE @siteID int, @orgID int, @controllingSiteResourceID int, @categoryID int, @enteredByMemberID int, @fieldsetID int,
						@categoryTreeID int, @reportAdminSRID int, @useID int, @descriptionContentID int,
						@fieldSetName varchar(200), @fsUID uniqueidentifier, @newUID uniqueidentifier, @nameFormat varchar(10), 
						@descriptionPosition varchar(25), @defaultLanguageID int, @maskEmail bit, @showHelp bit, @descriptionContent varchar(max),
						@oldCategoryID int, @categoryName varchar(200), @hasChanges bit, @crlf varchar(10), @msgjson varchar(MAX);

					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
					SET @fieldsetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldsetID#">;
					SET @fieldSetName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fieldSetName#">;
					SET @nameFormat = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.nameFormat#">;
					SET @descriptionPosition = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.descriptionPosition#">;
					SET @descriptionContent = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.descriptionContent#">;
					SET @maskEmail = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.maskEmail#">;
					SET @showHelp = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.showHelp#">;
					SET @defaultLanguageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.defaultLanguageID#">;
					SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;
					SET @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
					
					<cfif arguments.categoryID IS 0>
						select @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.SiteSRID#">;

						select @categoryTreeID = ct.categoryTreeID 
						from dbo.cms_categoryTrees as ct
						inner join dbo.cms_siteResources as sr on sr.siteID = @siteID 
							and sr.siteResourceID = ct.siteResourceID
							and sr.siteResourceStatusID = 1
						where ct.controllingSiteResourceID = @controllingSiteResourceID 
						and ct.categoryTreeCode = 'MEMFIELDSETS';

						SET @categoryName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.newCategoryName#">;
					<cfelse>
						SET @categoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryID#">;

						SELECT @categoryName = categoryName
						FROM dbo.cms_categories
						WHERE categoryID = @categoryID;
					</cfif>

					<cfif arguments.fieldsetID GT 0>
						INSERT INTO ##tmpExistingMFS (fieldsetName, nameFormat, showHelp, [uid], descriptionContentID, descriptionContent, 
							categoryID, categoryName, maskEmail, descriptionPosition)
						SELECT mfs.fieldsetName, mfs.nameformat, mfs.showHelp, mfs.uid, mfs.descriptionContentID, desccontent.rawContent, 
							c.categoryID, c.categoryName, mfs.maskEmail, mfs.descriptionPosition
						FROM dbo.ams_memberFieldSets as mfs
						INNER JOIN dbo.cms_categories AS c ON c.categoryID = mfs.categoryID
						CROSS APPLY dbo.fn_getContent(mfs.descriptionContentID,1) as desccontent
						WHERE mfs.fieldSetID = @fieldsetID;
						
						SELECT @descriptionContentID = descriptionContentID, @fsUID = [uid], @oldCategoryID = categoryID,
							@hasChanges = CASE WHEN categoryID <> ISNULL(@categoryID,0) OR fieldSetName <> @fieldSetName THEN 1 ELSE 0 END
						FROM ##tmpExistingMFS;

						/* audit log */
						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'Field Set name changed from [' + fieldsetName + '] to [' + @fieldsetName + '].'
						FROM ##tmpExistingMFS
						WHERE fieldsetName <> @fieldsetName;

						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'Category changed from [' + categoryName + '] to [' + @categoryName + '].'
						FROM ##tmpExistingMFS
						WHERE categoryID <> ISNULL(@categoryID,0);

						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'Mask Email Addresses changed from ' + CASE WHEN maskEmail = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @maskEmail = 1 THEN 'Yes' ELSE 'No' END + '.'
						FROM ##tmpExistingMFS
						WHERE maskEmail <> @maskEmail;

						<cfif NOT listFindNoCase("fsWidget,fsWidgetMultiple,fsWidgetSavedRpt,fsWidgetMultipleSavedRpt",arguments.usageMode)>
							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Name Format changed from ' + CASE WHEN nameFormat = 'LSXPFM' THEN 'Last, First Middle' ELSE 'First Middle Last' END + ' to ' + CASE WHEN @nameFormat = 'LSXPFM' THEN 'Last, First Middle' ELSE 'First Middle Last' END + '.'
							FROM ##tmpExistingMFS
							WHERE nameFormat <> @nameFormat;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Show Help changed from ' + CASE WHEN showHelp = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @showHelp = 1 THEN 'Yes' ELSE 'No' END + '.'
							FROM ##tmpExistingMFS
							WHERE showHelp <> @showHelp;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Description Position changed from ' + descriptionPosition + ' to ' + @descriptionPosition + '.'
							FROM ##tmpExistingMFS
							WHERE descriptionPosition <> @descriptionPosition;

							INSERT INTO ##tmpLogMessages(msg)
							SELECT 'Description updated.'
							FROM ##tmpExistingMFS
							WHERE descriptionContent <> @descriptionContent;
						</cfif>

						<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) AND len(arguments.fieldSetUID)>
							SET @newUID = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#arguments.fieldSetUID#">;

							IF @fsUID <> @newUID
								INSERT INTO ##tmpLogMessages(msg)
								VALUES ('Field Set UID changed from ' + CAST(@fsUID AS varchar(36)) + ' to ' + CAST(@newUID AS varchar(36)) + '.');
						</cfif>

						IF EXISTS (SELECT 1 FROM ##tmpLogMessages) BEGIN
							SET @crlf = CHAR(13) + CHAR(10);
							SET @msgjson = 'The following changes have been made:';

							SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + dbo.fn_cleanInvalidXMLChars(msg)
							FROM ##tmpLogMessages
							WHERE msg IS NOT NULL;
						END
					<cfelse>
						SET @hasChanges = 1;
					</cfif>

					BEGIN TRAN;
						<cfif arguments.categoryID IS 0>
							EXEC dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName=@categoryName, @categoryDesc='', @categoryCode='', 
								@parentCategoryID=NULL, @contributorMemberID=@enteredByMemberID, @categoryID=@categoryID OUTPUT
						</cfif>

						<cfif arguments.fieldsetID GT 0>
							<cfif listFindNoCase("fsWidget,fsWidgetMultiple,fsWidgetSavedRpt,fsWidgetMultipleSavedRpt",arguments.usageMode)>
								UPDATE dbo.ams_memberFieldSets
								SET fieldSetName = @fieldSetName,
									categoryID = @categoryID,
									maskEmail = @maskEmail
								WHERE fieldSetID = @fieldsetID;
							<cfelse>
								UPDATE dbo.ams_memberFieldSets
								SET fieldSetName = @fieldSetName,
									categoryID = @categoryID,
									nameFormat = @nameFormat,
									maskEmail = @maskEmail,
									showHelp = @showHelp,
									descriptionPosition = @descriptionPosition
								WHERE fieldSetID = @fieldsetID;

								EXEC dbo.cms_updateContent @contentID=@descriptionContentID, @languageID=@defaultLanguageID, @isHTML=1, @contentTitle=@fieldSetName, 
									@contentDesc='', @rawcontent=@descriptionContent, @memberID=@enteredByMemberID;
							</cfif>

							<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) AND len(arguments.fieldSetUID)>
								IF @fsUID <> @newUID
									UPDATE dbo.ams_memberFieldSets
									SET [uid] = @newUID
									WHERE fieldSetID = @fieldsetID;
							</cfif>

							-- delete unassigned fs category
							IF @oldCategoryID <> @categoryID 
								AND (select count(mfs.fieldSetID)
									from dbo.cms_categories as c
									inner join dbo.ams_memberFieldSets as mfs on mfs.categoryID = c.categoryID 
									where c.categoryID = @oldCategoryID
									and c.isActive = 1) = 0 BEGIN
								DECLARE @catInUse bit;
								EXEC dbo.cms_deleteCategory @categoryID=@oldCategoryID, @recordedByMemberID=@enteredByMemberID, @catInUse=@catInUse OUTPUT;
							END

							IF @msgjson IS NOT NULL
								INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
								VALUES ('{ "c":"auditLog", "d": {
									"AUDITCODE":"MFS",
									"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
									"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
									"ACTORMEMBERID":' + CAST(@enteredByMemberID AS VARCHAR(20)) + ',
									"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
									"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');
						<cfelse>
							EXEC dbo.ams_createMemberFieldset @siteID=@siteID, @fieldsetName=@fieldsetName, @nameformat=@nameFormat, 
								@categoryID=@categoryID, @maskEmail=@maskEmail, @showHelp=@showHelp, @enteredByMemberID=@enteredByMemberID, 
								@defaultLanguageID=@defaultLanguageID, @fieldsetID=@fieldsetID OUTPUT;

							-- for saved reports, add the created fieldset to report settings first
							<cfif listFindNoCase("fsWidgetSavedRpt,fsWidgetMultipleSavedRpt",arguments.usageMode)>
								select top 1 @reportAdminSRID = sr.siteResourceID 
								from dbo.cms_siteResources as sr
								inner join dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = sr.resourceTypeID 
									and srt.resourceType = 'rpt_ReportSettings'
								where sr.siteID = @siteID
								and sr.siteResourceStatusID = 1;

								EXEC dbo.ams_createMemberFieldUsage @siteResourceID=@reportAdminSRID, @fieldsetID=@fieldsetID,
									@area='custom', @createSiteResourceID=1, @useID=@useID OUTPUT;
							</cfif>
						</cfif>
					COMMIT TRAN;

					SELECT fieldSetID, [uid], @hasChanges AS hasChanges
					FROM dbo.ams_memberFieldSets
					WHERE fieldsetID = @fieldsetID;

					<cfif arguments.fieldsetID GT 0>
						IF OBJECT_ID('tempdb..##tmpExistingMFS') IS NOT NULL
							DROP TABLE ##tmpExistingMFS;
						IF OBJECT_ID('tempdb..##tmpLogMessages') IS NOT NULL
							DROP TABLE ##tmpLogMessages;
					</cfif>

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfset local.returnStruct = { "success":true, "fieldsetid":local.qrySaveMemberFieldSet.fieldSetID,
			"fieldsetuid":local.qrySaveMemberFieldSet.uid, "haschanges":local.qrySaveMemberFieldSet.hasChanges IS 1 }>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doRemoveFieldSet" access="public" output="false" returntype="struct" hint="deletes a fieldset">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="fsid" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc procedure="ams_deleteMemberFieldSet" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.fsid#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<!--- field functions --->
	<cffunction name="editField" access="public" output="false" returntype="struct" hint="edit a field set field">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.fieldSetID = int(val(arguments.event.getValue('fsID')))>
		<cfset local.fieldID = int(val(arguments.event.getValue('fID')))>		

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.viewMemberFieldSetAdmin') is not 1>
			<cflocation url="#this.link.message#&message=1" addtoken="false">
		</cfif>

		<cfif local.fieldID GT 0>
			<cfset local.possibleMemberFields = this.objMemberFieldSet.getPossibleMemberFields(orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
			<cfset local.qryMemberField = this.objMemberFieldSet.getMemberField(fID=local.fieldID)>
				
			<cfquery dbtype="query" name="local.getFieldName">
				SELECT fieldLabel
				FROM [local].possibleMemberFields
				WHERE fieldCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.qryMemberField.fieldCode#">
			</cfquery>
		<cfelse>
			<cfset local.qryMemberField = { fieldLabel = "", isRequired = 0, isGrouped = 0, fieldDescription = "" }>
			<cfset local.strFields = this.objMemberFieldSet.getPossibleMemberFieldsForDropdowns(orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
			
			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qrySavedFields">
				SELECT fieldCode 
				FROM dbo.ams_memberFields AS mf
				WHERE mf.fieldSetID =  <cfqueryparam cfsqltype="cf_sql_integer" value="#local.fieldSetID#">
			</cfquery>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_field.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="saveField" access="public" output="false" returntype="struct" hint="saves a field to a fieldset">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.viewMemberFieldSetAdmin') is not 1>
			<cflocation url="#this.link.message#&message=1" addtoken="false">
		</cfif>
				
		<cfif arguments.event.getValue('fID',0) gt 0>
			<cfset local.fieldLabel = arguments.event.getTrimValue('fieldLabel','')>
			<cfset local.isRequired = arguments.event.getValue('isRequired',2)>
			<cfset local.isGrouped = arguments.event.getValue('isGrouped',2)>
			<cfset local.fieldDescription = arguments.event.getTrimValue('fieldDescription','')>

			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.updateMemberField">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##tmpExistingField') IS NOT NULL
						DROP TABLE ##tmpExistingField;
					IF OBJECT_ID('tempdb..##tmpLogMessages') IS NOT NULL
						DROP TABLE ##tmpLogMessages;
					CREATE TABLE ##tmpExistingField (fieldLabel varchar(420), fieldDescription varchar(300), isRequired bit, isGrouped bit);
					CREATE TABLE ##tmpLogMessages (rowID int IDENTITY(1,1), msg varchar(MAX));

					DECLARE @orgID int, @siteID int, @fieldID int, @fieldSetID int, @crlf varchar(10), @msgjson varchar(max),
						@fieldLabel varchar(420), @fieldDescription varchar(300), @isRequired bit, @isGrouped bit,
						@fieldSetName varchar(200), @enteredByMemberID int;

					SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
					SET @fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fID',0)#">;
					SET @fieldSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fsID',0)#">;
					<cfif LEN(local.fieldLabel)>
						SET @fieldLabel = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.fieldLabel#">;
					</cfif>
					<cfif local.isRequired NEQ 2>
						SET @isRequired = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.isRequired#">;
					</cfif>
					<cfif local.isGrouped NEQ 2>
						SET @isGrouped = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.isGrouped#">;
					</cfif>
					SET @fieldDescription = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.fieldDescription#">;
					SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

					INSERT INTO ##tmpExistingField (fieldLabel, fieldDescription, isRequired, isGrouped)
					SELECT fieldLabel, fieldDescription, isRequired, isGrouped
					FROM dbo.ams_memberFields
					WHERE fieldID = @fieldID
					AND fieldsetID = @fieldSetID;

					SELECT @fieldSetName = fieldSetName
					FROM dbo.ams_memberFieldSets
					WHERE fieldsetID = @fieldSetID;

					/* audit log */
					<cfif LEN(local.fieldLabel)>
						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'Field Label changed from [' + fieldLabel + '] to [' + @fieldLabel + '].'
						FROM ##tmpExistingField
						WHERE fieldLabel <> @fieldLabel;
					</cfif>
					<cfif local.isRequired NEQ 2>
						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'Is Required changed from ' + CASE WHEN isRequired = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @isRequired = 1 THEN 'Yes' ELSE 'No' END + '.'
						FROM ##tmpExistingField
						WHERE isRequired <> @isRequired;
					</cfif>
					<cfif local.isGrouped NEQ 2>
						INSERT INTO ##tmpLogMessages(msg)
						SELECT 'Grouped changed from ' + CASE WHEN isGrouped = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @isGrouped = 1 THEN 'Yes' ELSE 'No' END + '.'
						FROM ##tmpExistingField
						WHERE isGrouped <> @isGrouped;
					</cfif>
					INSERT INTO ##tmpLogMessages(msg)
					SELECT 'Field Description updated.'
					FROM ##tmpExistingField
					WHERE fieldDescription <> @fieldDescription;

					IF EXISTS (SELECT 1 FROM ##tmpLogMessages) BEGIN
						SET @crlf = CHAR(13) + CHAR(10);
						SELECT @msgjson = 'The following changes have been made to the Field Set [' + @fieldSetName + '] Field [' + fieldLabel + ']:'
						FROM ##tmpExistingField;

						SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + dbo.fn_cleanInvalidXMLChars(msg)
						FROM ##tmpLogMessages
						WHERE msg IS NOT NULL;
					END

					BEGIN TRAN;
					<cfif LEN(local.fieldLabel) OR local.isRequired NEQ 2 OR local.isGrouped NEQ 2>
						UPDATE dbo.ams_memberFields
						SET
						<cfif LEN(local.fieldLabel)>
							fieldLabel = @fieldLabel,
						</cfif>
						<cfif local.isGrouped NEQ 2>
							isGrouped = @isGrouped,
						</cfif>
						<cfif local.isRequired NEQ 2>
							isRequired = @isRequired,
						</cfif>
							fieldDescription = @fieldDescription
						WHERE fieldID = @fieldID
						AND fieldsetID = @fieldSetID;

						IF @msgjson IS NOT NULL
							INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
							VALUES ('{ "c":"auditLog", "d": {
								"AUDITCODE":"MFS",
								"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
								"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
								"ACTORMEMBERID":' + CAST(@enteredByMemberID AS VARCHAR(20)) + ',
								"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
								"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');
					
						COMMIT TRAN;
					</cfif>

					IF OBJECT_ID('tempdb..##tmpExistingField') IS NOT NULL
						DROP TABLE ##tmpExistingField;
					IF OBJECT_ID('tempdb..##tmpLogMessages') IS NOT NULL
						DROP TABLE ##tmpLogMessages;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		<cfelse>
			<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="ams_createMemberFieldsByFieldCodeList">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fsID',0)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('fieldCodeList','')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>
		</cfif>
	
		<cfreturn returnAppStruct("Successfully Saved.","echo")>		
	</cffunction>

	<cffunction name="doRemoveField" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="fid" type="numeric" required="true">
		<cfargument name="fsid" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRemove">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @orgID int, @siteID int, @fieldID int, @fieldSetID int, @fieldLabel varchar(420), 
						@fieldCode varchar(30), @fieldSetName varchar(200), @enteredByMemberID int;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
					SET @fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fid#">;
					SET @fieldSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fsid#">;
					SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

					SELECT @fieldLabel = mf.fieldLabel, @fieldCode = mf.fieldCode, @fieldSetName = mfs.fieldSetName, @orgID = s.orgID
					FROM dbo.ams_memberFields AS mf
					INNER JOIN dbo.ams_memberFieldSets AS mfs ON mfs.fieldSetID = mf.fieldSetID
						AND mfs.siteID = @siteID
					INNER JOIN dbo.sites AS s ON s.siteID = mfs.siteID
					WHERE mf.fieldID = @fieldID
					AND mf.fieldsetID = @fieldSetID;

					IF @fieldLabel IS NULL
						RAISERROR('invalid field set',16,1);
				
					BEGIN TRAN;
						DELETE FROM dbo.ams_memberFields
						WHERE fieldID = @fieldID
						AND fieldSetID = @fieldSetID;
						
						EXEC dbo.ams_reordermemberFields @fieldSetID=@fieldSetID;

						INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
						VALUES ('{ "c":"auditLog", "d": {
							"AUDITCODE":"MFS",
							"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
							"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
							"ACTORMEMBERID":' + CAST(@enteredByMemberID AS VARCHAR(20)) + ',
							"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
							"MESSAGE":"' + REPLACE('Field [' + @fieldLabel + '(' + @fieldCode + ')] deleted from the Field Set [' + @fieldSetName + '].','"','\"') + '" } }');
					COMMIT TRAN;
				
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
		
	<cffunction name="exportStructureZIP" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.zipFileName = "MemberFieldsets.zip">

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_exportMemberFieldsetStructure">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\">
		</cfstoredproc>

		<!--- zip the bcp files --->
		<cfzip action="zip" file="#local.strFolder.folderPath#/#local.zipFileName#" source="#local.strFolder.folderPath#" filter="*.bcp" storePath="no" />

		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.zipFileName#", displayName=local.zipFileName, forceDownload=1, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#this.link.list#&tab=ex" addtoken="no">
		</cfif>
	</cffunction>

	<cffunction name="prepareMemberFieldSetImport" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.rs = structNew()>
		<cfset local.rs.success = true>
		<cfset local.rs.errorCode = 999>
		<cfset local.rs.errorInfo = structNew()>

		<cfsetting requesttimeout="500">

		<!--- Attempt upload of zip --->
		<cftry>
			<cfset local.strImportFile = {}>
			<cfset local.strImportFile.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>

			<cffile action="upload" filefield="importfilename" destination="#local.strImportFile.strFolder.folderPath#" result="local.uploadResult" nameconflict="OVERWRITE">

			<cfset local.strImportFile.uploadFilenameWithExt = local.uploadResult.ServerFile>
			<cfset local.strImportFile.uploadFilenameWithoutExt = local.uploadResult.ServerFileName>
			<cfset local.strImportFile.uploadFilenameExt = local.uploadResult.ServerFileExt>

			<cfif local.strImportFile.uploadFilenameExt neq "zip">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#">
				<cfset local.errMsg = "Uploaded file was not in the proper format (#local.strImportFile.uploadFilenameExt#).">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 1>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,local.errMsg)>
			<cfelseif "#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" neq "#local.strImportFile.strFolder.folderPath#/MemberFieldsets.zip">
				<cffile action="rename" source="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" destination="#local.strImportFile.strFolder.folderPath#/MemberFieldsets.zip">
			</cfif> 
		<cfcatch type="Any">
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem uploading the selected file. A valid backup file is required for your import.")>
		</cfcatch>
		</cftry>

		<!--- check zip file and extract --->
		<cfif local.rs.success>
			<cftry>
				<cfzip action="list" file="#local.strImportFile.strFolder.folderPath#/MemberFieldsets.zip" name="local.qryFiles">
				<cfquery name="local.qryFilesCheck" dbtype="query">
					select count(*) as theCount
					from [local].qryFiles
					where name = 'sync_mfs_fieldsets.bcp' 
					or name = 'sync_mfs_fields.bcp' 
					or name = 'sync_mfs_allfields.bcp' 
					or name = 'sync_mfs_supporting.bcp'
				</cfquery>
				<cfif local.qryFiles.recordcount neq 4>
					<cfthrow message="The backup file contains #local.qryFiles.recordcount# files when it should contain 4.">
				<cfelseif local.qryFilesCheck.theCount neq 4>
					<cfthrow message="One or more required files in the backup file is missing.">
				</cfif>

				<cfzip file="#local.strImportFile.strFolder.folderPath#/MemberFieldsets.zip" action="unzip" filter="*.bcp" storepath="no" destination="#local.strImportFile.strFolder.folderPath#">
			<cfcatch type="Any">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/MemberFieldsets.zip">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 6>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"#cfcatch.message# Try the upload again or contact us for assistance.")>
			</cfcatch>
			</cftry>
		</cfif>

  		<!--- prepare import --->
  		<cfif local.rs.success>

			<!--- parse, validate, and compare xml in another thread --->
			<cfset local.threadID = createUUID()>
			<cfset local.threadVars = { threadID=local.threadID, threadName="Member FieldSets Import #local.threadID#", strFolder=local.strImportFile.strFolder }>
			<cfset local.paramStruct = { threadID=local.threadID, orgID=arguments.event.getValue('mc_siteinfo.orgid'), siteID=arguments.event.getValue('mc_siteinfo.siteID') }>

			<cfset local.FieldSetImportStruct = application.mcCacheManager.sessionGetValue(keyname='FieldSetImportStruct', defaultValue=structNew())>
			<cfset structInsert(local.FieldSetImportStruct, local.threadID, local.strImportFile.strFolder, true)>
			<cfset application.mcCacheManager.sessionSetValue(keyname='FieldSetImportStruct', value=local.FieldSetImportStruct)>

			<cfthread action="run" name="#local.threadVars.threadName#" threadid="#local.threadVars.threadID#" strFolder="#local.threadVars.strFolder#" paramStruct="#local.paramStruct#">
				<cftry>
					<cfset doPrepareImport(paramStruct=attributes.paramStruct, strFolder=attributes.strFolder)>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=attributes)>
				</cfcatch>
				</cftry>
			</cfthread>

			<!--- Echo message with local.threadID --->
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div id="loadingGif" class="row mt-2">
					<div class="col-auto">
						<i class="fa-light fa-circle-notch fa-spin fa-4x"></i> 
					</div>
					<div class="col">
						<div class="pb-3">We're analyzing your import file.</div>
						<div class="text-dark">Hang tight -- this could take up to a few minutes to compare the data.<br/>Stay on this page to see the results of the comparison.</div>
						<div id="loadingStatement" class="pt-3"></div>
					</div>
				</div>
				<div id="importCompareReport"></div>
				</cfoutput>
			</cfsavecontent>

			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					$(function() {
						isFieldSetImportCompareReady('#local.threadID#');
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<div class="alert alert-danger">#local.rs.errorInfo[local.rs.errorCode]#</div>
					<button type="button" class="btn btn-sm btn-secondary" onclick="self.location.href='#this.link.list#&tab=ex';">Try Again</button> 
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doPrepareImport" access="private" output="false" returntype="void">
		<cfargument name="paramStruct" type="struct" required="yes">
		<cfargument name="strFolder" type="struct" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.rs = { success=true, errorCode=999, errorInfo=StructNew() } >

		<cftry>
			<cfquery name="local.qryPrepareImport" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @siteID int, @pathToImport varchar(400), @importResult xml, @errCount int;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.paramStruct.siteID#">;
					SET @pathToImport = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strFolder.folderPathUNC#\">;

					EXEC dbo.ams_prepareMemberFieldSetsImport @siteID=@siteID, @pathToImport=@pathToImport, @importResult=@importResult OUTPUT;

					set @errCount = @importResult.value('count(/import/errors/error)','int');

					SELECT @importResult as importResult, @errCount as errCount;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			
			<cfset local.rs.importResultXML = xmlparse(local.qryPrepareImport.importResult)>
			<cfset local.rs.numFatalErrors = local.qryPrepareImport.errCount>

			<cfif local.rs.numFatalErrors gt 0>
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 105>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,'')>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem preparing the import.")>
		</cfcatch>
		</cftry>

		<cfset local.importCompareReport = showMemberFieldSetImportCompareResults(siteID=arguments.paramStruct.siteID, threadID=arguments.paramStruct.threadID, strResult=local.rs, doAgainURL="#this.link.list#&tab=ex")>

		<cffile action="write" file="#arguments.strFolder.folderPath#/FieldSetImportReport.html" output="#application.objcommon.minText(local.importCompareReport)#">
	</cffunction>

	<cffunction name="showMemberFieldSetImportCompareResults" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.hasChanges = false>
		
		<cfif arguments.strResult.success>
			<cfset local.strImportResult = structNew()>
			<cfset local.strImportResult.arrNewFieldSets = XMLSearch(arguments.strResult.importResultXML,"/import/newfieldsets/fieldset")>
			<cfset local.strImportResult.arrUpdateFieldSets = XMLSearch(arguments.strResult.importResultXML,"/import/updatefieldsets/fieldset")>
			<cfset local.strImportResult.arrRemoveFieldSets = XMLSearch(arguments.strResult.importResultXML,"/import/removefieldsets/fieldset")>
			<cfset local.strImportResult.arrNewFields = XMLSearch(arguments.strResult.importResultXML,"/import/newfields/field")>
			<cfset local.strImportResult.arrUpdateFields = XMLSearch(arguments.strResult.importResultXML,"/import/updatefields/field")>
			<cfset local.strImportResult.arrRemoveFields = XMLSearch(arguments.strResult.importResultXML,"/import/removefields/field")>
			<cfset local.strImportResult.arrRemoveCategories = XMLSearch(arguments.strResult.importResultXML,"/import/removecategories/category")>

			<cfloop collection="#local.strImportResult#" item="local.thisArr">
				<cfif arrayLen(local.strImportResult[local.thisArr])>
					<cfset local.hasChanges = true>
					<cfbreak>
				</cfif>
			</cfloop>

			<cfset local.importReport = generateMemberFieldSetImportResultsReport(siteID=arguments.siteID, threadID=arguments.threadID, strImportResult=local.strImportResult)>

		<!--- import errors --->
		<cfelseif arguments.strResult.errorCode eq 105>
			<cfset local.errorMsg = XMLSearch(arguments.strResult.importResultXML,'string(/import/errors/error/@msg)')>
			<cfset local.errorCode = XMLSearch(arguments.strResult.importResultXML,'string(/import/errors/error/@errorcode)')>
			<cfset local.errorReport = generateMemberFieldSetImportErrorReport(siteID=arguments.siteID, errorcode=local.errorCode, errorMsg=local.errorMsg)>
		</cfif>

		<!--- If fatal errors --->
		<cfif NOT arguments.strResult.success>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Member FieldSets Import Issue Report
								</div>
							</div>
							<div class="card-body pb-3">
								<div class="alert alert-danger">
									<cfif arguments.strResult.errorCode eq 105>
										<cfif len(local.errorReport)>
											<div>#local.errorReport#</div>
										<cfelse>
											<div class="font-weight-bold">An undetermined error occurred during the import.</div>
										</cfif>
									<cfelse>
										<div class="font-weight-bold">The import was stopped and requires your attention.</div>
										<div class="mt-2">#arguments.strResult.errorInfo[arguments.strResult.errorCode]#</div>
									</cfif>
									<button class="btn btn-sm btn-secondary mt-3" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Try upload again</button>
								</div>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success but no changes needed --->
		<cfelseif arguments.strResult.success and not local.hasChanges>
			<cfset cancelMemberFieldSetImport(siteID=arguments.siteID)>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Member FieldSets Import No Action Needed
								</div>
							</div>
							<div class="card-body pb-3">
								<div>There were no changes to process.</div>
								<button class="btn btn-sm btn-secondary mt-3" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Upload another file</button>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success with changes to confirm --->
		<cfelseif arguments.strResult.success and local.hasChanges>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<cfif len(local.importReport)>
					<div>#local.importReport#</div>
					<br/>
				</cfif>
				<br/>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">Member FieldSets Import Issue Report</div>
							</div>
							<div class="card-body pb-3">
								<div class="alert alert-danger font-weight-bold">
									An undetermined error occurred during the import.
								</div>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>
			
		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateMemberFieldSetImportResultsReport" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strImportResult" type="struct" required="yes">

		<cfset var local = structNew()>

		<cfif arrayLen(arguments.strImportResult.arrUpdateFieldSets)>
			<cfquery name="local.qryImportFileUpdateFieldSets" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select distinct fieldsetID as syncFieldsetID, fieldsetName, nameFormat, maskEmail, showHelp, uid, descriptionPosition, 
					categoryName, descriptionContent
				from dbo.sync_mfs_fieldsets
				where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				and uid in (
					'#arguments.strImportResult.arrUpdateFieldSets[1].xmlAttributes.uid#'
					<cfloop array="#arguments.strImportResult.arrUpdateFieldSets#" index="local.thisRule">
						,'#local.thisRule.xmlAttributes.uid#'
					</cfloop>);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryOrgUpdateFieldSets" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

				SELECT mfs.fieldsetID, mfs.fieldsetName, mfs.nameformat, mfs.maskEmail, mfs.showHelp, mfs.uid, mfs.descriptionPosition, 
					c.categoryName, desccontent.rawContent as descriptionContent
				FROM dbo.ams_memberFieldSets as mfs
				INNER JOIN dbo.cms_categories as c on c.categoryID = mfs.categoryID
				CROSS APPLY dbo.fn_getContent(mfs.descriptionContentID,1) as desccontent
				WHERE mfs.siteID = @siteID
				AND uid in (#listQualify(valueList(local.qryImportFileUpdateFieldSets.uid), "'")#)
				ORDER BY mfs.fieldsetName;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryImportFileUpdateMemberFields" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

				select mf.fieldsetID as syncFieldsetID, mf.fieldID as syncFieldID, mf.fieldLabel, mf.uid as syncFieldUID, mfs.uid as syncFieldSetUID, 
					mf.dbObject, mf.dbObjectAlias, mf.dbField, mf.fieldCode, mf.displayTypeUID, mf.dataTypeUID, 
					mf.isRequired, mf.fieldOrder, mf.fieldDescription, mf.isGrouped, af.useFieldcode, mf.finalAction
				from dbo.sync_mfs_fields as mf
				inner join dbo.sync_mfs_fieldsets as mfs on mfs.siteID = @siteID and mfs.fieldSetID = mf.fieldSetID
					and mfs.fieldsetID in (0#valueList(local.qryImportFileUpdateFieldSets.syncFieldsetID)#)
				inner join dbo.sync_mfs_allfields as af on af.siteID = @siteID and af.fieldCode = mf.fieldCode
				where mf.siteID = @siteID;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryOrgUpdateMemberFields" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

				SELECT mf.fieldID, mf.fieldsetID, mf.dbObject, mf.dbObjectAlias, mf.dbField, mf.fieldCode, mf.fieldLabel, 
					dt.uid as displayTypeUID, ddt.uid as dataTypeUID, mf.isRequired, mf.fieldOrder, mf.fieldDescription, 
					mf.uid, mf.isGrouped
				FROM dbo.ams_memberFieldSets as mfs
				INNER JOIN dbo.ams_memberFields as mf on mf.fieldsetID = mfs.fieldsetID
				INNER JOIN dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = mf.displayTypeID
				INNER JOIN dbo.ams_memberDataColumnDataTypes as ddt on ddt.dataTypeID = mf.dataTypeID
				WHERE mfs.siteID = @siteID
				AND mf.fieldsetID in (0#valueList(local.qryOrgUpdateFieldSets.fieldsetID)#)
				ORDER BY mf.fieldOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>		

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importCompare.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateMemberFieldSetImportErrorReport" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="errorCode" type="string" required="yes">
		<cfargument name="errorMsg" type="string" required="yes">

		<cfset var local = structNew()>

		<cfswitch expression="#arguments.errorCode#">
			<cfcase value="USEFIELDCODENULL">
				<cfquery name="local.qryInvalidFields" datasource="#application.dsn.datatransfer.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select fieldCode, fieldLabel
					from dbo.sync_mfs_allfields 
					where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
					and useFieldcode is null;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfquery name="local.qryFieldSetsUsingFields" datasource="#application.dsn.datatransfer.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					declare @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

					select distinct smfs.fieldsetName, smf.fieldCode
					from dbo.sync_mfs_fieldsets as smfs
					inner join dbo.sync_mfs_fields as smf on smf.siteID = @siteID and smf.fieldsetID = smfs.fieldsetID
					where smfs.siteID = @siteID
					and smf.fieldCode in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" list="true" value="#valueList(local.qryInvalidFields.fieldCode)#">);

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
			</cfcase>
		</cfswitch>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importErrors.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doImportFieldSets" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.success = false>
		<cfset local.resultMessage = "">
		<cfset local.threadID = arguments.event.getTrimValue('threadID','')>

		<cfsetting requesttimeout="500">

		<cfset local.FieldSetImportStruct = application.mcCacheManager.sessionGetValue(keyname='FieldSetImportStruct', defaultValue=structNew())>
		<cfif NOT structKeyExists(local.FieldSetImportStruct, local.threadID)>
			<cfset local.resultMessage = "There was a problem importing the Member Field Sets. The import data is no longer available.">
		<cfelse>
			<cftry>
				<cfstoredproc procedure="ams_importMemberFieldSets" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				</cfstoredproc>

				<cfset local.success = true>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.success = false>
				<cfset local.resultMessage = "There was a problem importing the Member Field Sets file.<br/>" & cfcatch.message>
			</cfcatch>
			</cftry>

			<!--- when done, remove from struct --->
			<cfset StructDelete(local.FieldSetImportStruct, local.threadID)>
			<cfif structCount(local.FieldSetImportStruct)>
				<cfset application.mcCacheManager.sessionSetValue(keyname='FieldSetImportStruct', value=local.FieldSetImportStruct)>
			<cfelse>
				<cfset application.mcCacheManager.sessionDeleteValue(keyname='FieldSetImportStruct')>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importReport.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="cancelMemberFieldSetImport" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var qryDeleteSyncData = "">

		<cfquery name="qryDeleteSyncData" datasource="#application.dsn.datatransfer.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @siteID INT = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				
				BEGIN TRAN;
					DELETE FROM dbo.sync_mfs_fieldsets WHERE siteID = @siteID;
					DELETE FROM dbo.sync_mfs_fields WHERE siteID = @siteID;
					DELETE FROM dbo.sync_mfs_allfields WHERE siteID = @siteID;
					DELETE FROM dbo.sync_mfs_supporting WHERE siteID = @siteID;
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>
	
	<cffunction name="fetchReportData" access="public" output="false" returntype="struct">
		<cfargument name="reportuid" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.success = false>
		<cfset local.FieldSetImportStruct = application.mcCacheManager.sessionGetValue(keyname='FieldSetImportStruct', defaultValue=structNew())>

		<cftry>
			<cfif structKeyExists(local.FieldSetImportStruct,arguments.reportuid)>
				<cfset local.reportFileName = local.FieldSetImportStruct[arguments.reportuid].folderPath & "/FieldSetImportReport.html">
				<cfset local.returnStruct.reportOutput = "">
				<cfif fileExists(local.reportFileName)>
					<cffile action="read" file="#local.reportFileName#" variable="local.returnStruct.reportOutput">
					<cfset local.returnStruct.success = true>
				</cfif>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>	

	<!--- ajax Functions --->
	<cffunction name="testFieldExists" access="public" output="false" returntype="struct">
		<cfargument name="fsID" type="numeric" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="fieldLabel" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryTestField" datasource="#application.dsn.membercentral.dsn#">		
			SET NOCOUNT ON;
			
			DECLARE @isValid bit = 1;

			IF EXISTS (
				SELECT 1
				FROM dbo.ams_memberFields
				WHERE fieldSetID = <cfqueryparam value="#arguments.fsID#" cfsqltype="CF_SQL_INTEGER">
				AND fieldLabel = <cfqueryparam value="#arguments.fieldLabel#" cfsqltype="CF_SQL_VARCHAR">
				AND fieldID <> <cfqueryparam value="#arguments.fieldID#" cfsqltype="CF_SQL_INTEGER">
			)
				SET @isValid = 0;

			SELECT @isValid as isValid;
		</cfquery>

		<cfreturn { "success":true, "isvalid":local.qryTestField.isValid }>
	</cffunction>

	<cffunction name="validateFieldsList" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="fsID" type="numeric" required="true">
		<cfargument name="fieldCodeList" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryTestFields" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @fsID int = <cfqueryparam value="#arguments.fsID#" cfsqltype="CF_SQL_INTEGER">,
				@orgID int = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">;
			DECLARE @duplicateFieldsList varchar(max), @duplicateFieldLabelsList varchar(max);

			SELECT @duplicateFieldsList = STRING_AGG(p.fieldLabel,', ')
			FROM dbo.fn_varcharListToTable(<cfqueryparam value="#arguments.fieldCodeList#" cfsqltype="CF_SQL_VARCHAR">,',') AS tmp
			INNER JOIN dbo.fn_ams_getPossibleMemberFields(@orgID) AS p ON p.fieldCode = tmp.listitem
			INNER JOIN dbo.ams_memberFields AS mf ON mf.fieldSetID = @fsID
				AND mf.fieldCode = tmp.listitem;

			SELECT @duplicateFieldLabelsList = STRING_AGG(p.fieldLabel,', ')
			FROM dbo.fn_varcharListToTable(<cfqueryparam value="#arguments.fieldCodeList#" cfsqltype="CF_SQL_VARCHAR">,',') AS tmp
			INNER JOIN dbo.fn_ams_getPossibleMemberFields(@orgID) AS p ON p.fieldCode = tmp.listitem
			INNER JOIN dbo.ams_memberFields AS mf ON mf.fieldSetID = @fsID
				AND mf.fieldCode <> tmp.listitem
				AND mf.fieldLabel = p.fieldLabel;

			SELECT @duplicateFieldsList AS duplicateFieldsList, @duplicateFieldLabelsList AS duplicateFieldLabelsList
		</cfquery>

		<cfset local.data.duplicatefieldslist = local.qryTestFields.duplicateFieldsList>
		<cfset local.data.duplicatefieldlabelslist = local.qryTestFields.duplicateFieldLabelsList>
		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getAvailableAndSelectedFieldSets" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="resourceType" type="string" required="true">
		<cfargument name="area" type="string" required="true">

		<cfset var returnStruct = createObject("component","memberFieldSets").getAvailableAndSelectedFieldSets(siteID=arguments.siteID, resourceType=arguments.resourceType, area=arguments.area)>

		<cfreturn returnStruct>
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry...</h4>
			
				<cfif arguments.event.valueExists('message')>
					<p>
						<cfswitch expression="#arguments.event.getValue('message')#">
							<cfcase value="1"><b>You do not have rights to this section.</b></cfcase>
							<cfcase value="2"><b>That field set was not found.</b></cfcase>
						</cfswitch>
					</p>				
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

</cfcomponent>