<cfsavecontent variable="local.pageJS">
	<cfoutput>
	
	<script type="text/javascript">
		var #toScript(arguments.event.getValue('fldName'),"memField")#
		var #toScript(int(val(arguments.event.getValue('autoClose'))),"autoClose")#

		function doSearchMembersMemNumContains() {
			let loadingMsg = '<div class="alert alert-info">We didn\'t find an exact MemberNumber match. We\'ll search for members containing that MemberNumber.</div><div class="mt-4"><div class="text-center"><div class="spinner-border" role="status"></div><h4 class="mt-2">Searching...</h4></div></div>';
			let postURL = '#arguments.event.getValue("formLink")#&runMemNumContains=1';
			$("##btn_st_s").prop('disabled',true);
			var fd = $('##frmLocator').serializeArray();
			$('##divSearchFormContainer').hide();
			$("##divSearchResultsContainer").html(loadingMsg).load(postURL,fd).show();
		}		
		function doSearchMembers() {
			if (validateALSearch()) {
				let loadingMsg = '<div class="mt-4"><div class="text-center"><div class="spinner-border" role="status"></div><h4 class="mt-2">Searching...</h4></div></div>';
				let postURL = '#arguments.event.getValue("formLink")#';
				$("##btn_st_s").prop('disabled',true);
				var fd = $('##frmLocator').serializeArray();
				$('##divSearchFormContainer').hide();
				$("##divSearchResultsContainer").html(loadingMsg).load(postURL,fd).show();
			}
		}		
		function selectMember(mID, mNum, mName) {
			if (memField != '') #arguments.event.getValue('retFunction')#(memField, mID, mNum, mName);
			if ($('##divSearchFormContainer').length > 0) cancelSearchResults(1);
			if (autoClose == 1) #arguments.event.getValue('closeFunction')#();
			<cfif arguments.event.getValue('closeModal','') neq 0>
				if (top.MCModalUtils.isShown()) top.MCModalUtils.hideModal();
			</cfif>
		}
		function cancelSearchResults(x=0) {
			$('##frmLocator')[0].reset();
			hideALAlert();
			$("##btn_st_s").prop('disabled',false);
			$('##divSearchResultsContainer').html('').hide();
			top.$('##MCModalFooter').removeClass('d-none');
			if(x!==1)
				$('##divSearchFormContainer').show(300);
		}
		$(document).ready(function () {
			if (top.MCModalUtils.isShown()) {			
				$("##divSearchFormContainer h4").addClass('d-none');				
				$('.frmLocatorPopup').remove();
			}else{
				$('.frmLocatorModal').remove();
				$('.frmLocatorPopup').removeClass('d-none');
			}
			<cfif len(trim(local.runMemNumContains))>
				if ($("##m_membernumber").length) {
					$("##m_membernumber").val('#local.runMemNumContains#');
					$("##fs_match").val('c');
					doSearchMembersMemNumContains();
				}
			</cfif>
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">

<cfoutput>
<div id="divSearchFormContainer">
	<cfif len(arguments.event.getTrimValue('dispTitle')) AND arguments.event.getTrimValue('dispTitle') NEQ 'Go to Member'>
		<h4>#arguments.event.getTrimValue('dispTitle')#</h4>
	</cfif>
	<cfif len(arguments.event.getTrimValue('inGrp'))>
		<div class="p-2 alert alert-info">Results will be limited to members of <i>#local.qryGroup.GroupPathExpanded#</i>.</div>
	</cfif>
	<cfif len(arguments.event.getTrimValue('notInGrp'))>
		<div class="p-2 alert alert-info">Results will exclude members of <i>#local.qryGroup.GroupPathExpanded#</i>.</div>
	</cfif>
	<cfif len(arguments.event.getTrimValue('inEvReg'))>
		<div class="p-2 alert alert-info">Results will be limited to registrants of <i>#EncodeForHTML(local.qryEvent.contentTitle)#</i>.</div>
	</cfif>
	<cfif len(arguments.event.getTrimValue('notInEvReg'))>
		<div class="p-2 alert alert-info">Results will exclude registrants of <i>#EncodeForHTML(local.qryEvent.contentTitle)#</i>.</div>
	</cfif>
	<cfif len(arguments.event.getValue('addbtnName'))>
		<div class="text-right mb-2">
			<button type="button" class="btn btn-sm btn-primary" onclick="javascript:window.open('#local.linkToMembers#','_blank');">#arguments.event.getValue('addbtnName')#</button>
		</div>
	</cfif>
	#createObject("component","model.system.user.accountLocater").displayAdminLocatorSearch(fieldsetID=local.qryGetFS.fieldsetID, searchPostURL=arguments.event.getValue('formLink'), buttonText="Filter Members", onSubmitHandler="doSearchMembers", isPopup=1, hasAddMemberRights=local.hasAddMemberRights, addMemNewTab=local.addMemNewTab)#
</div>
<div id="divSearchResultsContainer" style="display:none;"></div>
</cfoutput>