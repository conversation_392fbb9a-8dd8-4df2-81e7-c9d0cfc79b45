ALTER PROC dbo.report_DocumentContributions
@orgCode varchar(10),
@radioDoc varchar(10),
@startDate datetime,
@endDate datetime,
@isSummary bit,
@exportFileName varchar(200)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @selectsql varchar(max);
	declare @approvedStatusID int 
	select @approvedStatusID=statusID from  trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

	IF OBJECT_ID('tempdb..#tmpDocs') IS NOT NULL 
		DROP TABLE #tmpDocs;
	IF OBJECT_ID('tempdb..#tmpDocsSum') IS NOT NULL 
		DROP TABLE #tmpDocsSum;
	CREATE TABLE #tmpDocs (documentID int, contributingAssociation varchar(10), Jurisdiction varchar(50),
		ExpertName varchar(500), Style varchar(255), documentDate datetime, dateEntered datetime, CreditAmount decimal(18,2),
		Notes varchar(max), DepoAmazonBucks bit, depomemberdataid int, firstname varchar(100), lastname varchar(100), 
		email varchar(100), DepoAmazonBucksEmail varchar(100), documentDateYear varchar(4), documentType varchar(100), Status varchar(25));
	CREATE TABLE #tmpDocsSum (contributingAssociation varchar(10), NumberContrib int, NumberAmazonBucks int);

	INSERT INTO #tmpDocs (documentID, contributingAssociation, Jurisdiction, ExpertName, Style, documentDate, dateEntered,
		Notes, DepoAmazonBucks, depomemberdataid, firstname, lastname, email, DepoAmazonBucksEmail, documentDateYear, documentType,
		Status, CreditAmount)
	select doc.documentID, doc.state as contributingAssociation, doc.Jurisdiction, doc.ExpertName, doc.Style, 
		doc.documentDate, doc.dateEntered, doc.Notes, doc.DepoAmazonBucks, 
		d.depomemberdataid, d.firstname, d.lastname, d.email, doc.DepoAmazonBucksEmail, year(doc.documentDate), 
		dt.Description as documentType, ds.statusName,
		case when doc.DepoAmazonBucks = 1
			then isnull((select sum(pc.AmazonBucksCreditAmount) from dbo.PurchaseCredits as pc where pc.DocumentID = doc.DocumentID),0)
			else isnull((select sum(pc.PurchaseCreditAmount) from dbo.PurchaseCredits as pc where pc.DocumentID = doc.DocumentID),0)
			end
	from dbo.depoDocuments as doc
	inner join trialsmith.dbo.depoDocumentStatusHistory as dsh 
		on dsh.depoDocumentHistoryID = doc.currentStatusHistoryID
		and dsh.statusID = @approvedStatusID
	inner join dbo.depoDocumentTypes as dt on dt.TypeID = doc.DocumentTypeID
	inner join dbo.depoMemberData as d on d.depomemberdataid = doc.depomemberdataid
	inner join dbo.depoDocumentStatuses as ds on ds.statusID = dsh.statusID
	where doc.DateEntered between @startDate and @endDate
	and 1 = case when @orgCode <> 'All' AND doc.state <> @orgCode then 0 else 1 end
	and 1 = case 
		when @radioDoc = 'ALL' and doc.DocumentTypeID NOT IN (2,9) then 1
		when @radioDoc = 'ALLNODEPS' and doc.DocumentTypeID NOT IN (1,2,9) then 1
		when @radioDoc = 'DEPSAMAZON' and doc.DocumentTypeID = 1 and doc.DepoAmazonBucks = 1 then 1
		when @radioDoc = 'DEPS' and doc.DocumentTypeID = 1 then 1
		else 0
		end;

	IF @isSummary = 1 BEGIN
		INSERT INTO #tmpDocsSum
		SELECT contributingAssociation, COUNT(documentID) AS NumberContrib, SUM(case when DepoAmazonBucks = 1 then 1 else 0 end) as NumberAmazonBucks
		FROM #tmpDocs
		GROUP BY contributingAssociation;

		IF @exportFileName is null BEGIN

			select contributingAssociation as State, NumberContrib, NumberAmazonBucks
			from #tmpDocsSum
			order by contributingAssociation;
			
			select sum(NumberContrib) as DocCount
			from #tmpDocsSum;

			select documentDateYear, count(documentID) as DocCount
			from #tmpDocs
			group by documentDateYear
			order by documentDateYear;
			
		END ELSE BEGIN

			SET @selectsql = '
				SELECT contributingAssociation, NumberContrib, NumberAmazonBucks, ROW_NUMBER() OVER(order by contributingAssociation) as mcCSVorder 
				*FROM* #tmpDocsSum';
			EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@exportFileName, @returnColumns=0;

		END
	END ELSE BEGIN
		IF @exportFileName is null BEGIN
			select documentID, contributingAssociation, Jurisdiction, ExpertName, Style, documentDate, dateEntered,
				Notes, DepoAmazonBucks, CreditAmount, depomemberdataid, firstname, lastname, documentType, Status
			from #tmpDocs
			order by contributingAssociation, lastname, firstname, depomemberdataid;
		END ELSE BEGIN
			SET @selectsql = '
				SELECT *, ROW_NUMBER() OVER(order by contributingAssociation, lastname, firstname, depomemberdataid) as mcCSVorder 
				*FROM* #tmpDocs';
			EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@exportFileName, @returnColumns=0;
		END

	END

	IF OBJECT_ID('tempdb..#tmpDocs') IS NOT NULL 
		DROP TABLE #tmpDocs;
	IF OBJECT_ID('tempdb..#tmpDocsSum') IS NOT NULL 
		DROP TABLE #tmpDocsSum;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
