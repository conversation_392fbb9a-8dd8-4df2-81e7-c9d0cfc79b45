<cfset local.selectedTab = event.getTrimValue("tab","current")>
<cfif event.getTrimValue("lockTab","false")>
	<cfset local.lockTab = local.selectedTab>
<cfelse>
	<cfset local.lockTab = "">
</cfif>

<cfsavecontent variable="local.js1">
	<cfoutput>
	<script language="javascript">
		var #ToScript(this.link.grpSelectGotoLink,"link_grpSelectGotoLink")#
		var #ToScript(this.link.memSelectGotoLink,"link_memSelectGotoLink")#
		var #ToScript(arguments.event.getValue('mc_adminNav.adminHomeResource'),"link_adminHomeResource")#

		let subTabInitArr = new Array();
			subTabInitArr["subscribers"] = false;
			subTabInitArr["accounting"] = false;
			subTabInitArr["changelogs"] = false;
			subTabInitArr["schedules"] = false;
		
		function onSubTabChangeHandler(ActiveTab) {
			if (!subTabInitArr[ActiveTab.id]) {
				subTabInitArr[ActiveTab.id] = true;
				switch(ActiveTab.id) {
					case "subscribers":
						initSubsTab(); break;
					case "accounting":
						initSubAcctTab(); break;
					case "changelogs":
						initSubLogsTab(); break;
					case "schedules":
						initSubScheduleTab(); break;
				}
			}
		}
		function editMember(mID) {
			window.open('#local.editMemberLink#&memberID=' + mID + '&tab=subscriptions','_blank');
		}
		function showSubTree(mid,sid) {
			MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'Subscription Tree for',
				iframe: true,
				contenturl: '#this.link.showSubTree#&mid=' + mid + '&sid=' + sid
			});
		}
		function closeBox() { MCModalUtils.hideModal(); }

		$(function() {
			mca_initNavPills('subscriptionAdminTabs', '#local.selectedTab#', '#local.lockTab#', onSubTabChangeHandler);
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.js1)#">

<cfoutput>
<h4>Manage Subscribers</h4>
<ul class="nav nav-pills nav-pills-dotted" id="subscriptionAdminTabs">
	<cfset local.thisTabName = "subscribers">
	<cfset local.thisTabID = "subscribers">
	<li class="nav-item">
		<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" 
				aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Subscribers</a>
	</li>

	<cfset local.thisTabName = "accounting">
	<cfset local.thisTabID = "accounting">
	<li class="nav-item">
		<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" 
				aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Accounting</a>
	</li>

	<cfset local.thisTabName = "changelogs">
	<cfset local.thisTabID = "changelogs">
	<li class="nav-item">
		<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" 
				aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Subscriber Change Logs</a>
	</li>

	<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and arguments.event.getValue('mc_siteInfo.useAccrualAcct')>
		<cfset local.thisTabName = "schedules">
		<cfset local.thisTabID = "schedules">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" 
					aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Modify Recog Schedules</a>
		</li>
	</cfif>

	<cfif local.hasRightsImportSubscribers OR local.hasRightsImportSubscribersCOF OR local.hasRightsImportCustom>
		<cfset local.thisTabName = "import">
		<cfset local.thisTabID = "import">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" 
					aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Import</a>
		</li>
	</cfif>
</ul>
</cfoutput>

<div class="tab-content mc_tabcontent p-2 pb-0" id="pills-tabContent">
	<div class="tab-pane fade" id="pills-subscribers" role="tabpanel" aria-labelledby="subscribers">
		<cfinclude template="dsp_subscriptionReports_subscribers.cfm">
	</div>
	<div class="tab-pane fade" id="pills-accounting" role="tabpanel" aria-labelledby="accounting">
		<cfinclude template="dsp_subscriptionReports_accounting.cfm">
	</div>
	<div class="tab-pane fade" id="pills-changelogs" role="tabpanel" aria-labelledby="changelogs">
		<cfinclude template="dsp_subscriptionReports_changelogs.cfm">
	</div>
	<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and arguments.event.getValue('mc_siteInfo.useAccrualAcct')>
		<div class="tab-pane fade" id="pills-schedules" role="tabpanel" aria-labelledby="schedules">
			<cfinclude template="dsp_subscriptionReports_schedules.cfm">
		</div>
	</cfif>
	<cfif local.hasRightsImportSubscribers OR local.hasRightsImportSubscribersCOF OR local.hasRightsImportCustom>
		<div class="tab-pane fade" id="pills-import" role="tabpanel" aria-labelledby="import">
			<cfif local.showImpTemplate eq 1>
				<cfinclude template="dsp_subscriptionReports_import.cfm">
			<cfelse>
				<cfoutput>
					#local.impData#
				</cfoutput>
			</cfif>
		</div>
	</cfif>
</div>