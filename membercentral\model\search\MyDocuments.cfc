<cfcomponent extends="Bucket">

	<cfset variables.thisBucketTypeID = 14>
	<cfset variables.thisBucketType = "mydocuments">
	<cfset variables.thisBucketCartItemTypeID = 0> <!--- ignored --->
	<cfset variables.thisBucketMaxPerPage = 10>
	<cfset variables.thisBucketMaxShown = 10000>

	<cffunction name="showHeader" access="private" output="false" returntype="string">
		<cfargument name="bucketName" type="string" required="yes">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var header = "">

		<cfsavecontent variable="header">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/myDocuments/header.cfm">
		</cfsavecontent>

		<cfreturn header>
	</cffunction>

	<cffunction name="showSearchForm" access="public" output="false" returntype="string">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="searchID" required="no" type="numeric" default="0">
		<cfargument name="viewDirectory" type="string" required="no" default="default">
		
		<cfset var local = StructNew()>
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>
		
		<!--- load search if passed in --->
		<cfset local.strSearchForm = prepSearchForSearchForm(arguments.searchID,arguments.bucketID)>

		<!--- get states --->
		<cfquery name="local.qryStates" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SELECT distinct s.orderpref, s.code, s.name
			from dbo.depodocuments as d
			inner join dbo.states as s on s.code = d.jurisdiction
			order by s.orderpref, s.name
		</cfquery>
		
		<cfset showCommonJS(bucket=variables.thisBucketType, bucketID=arguments.bucketID, viewDirectory=arguments.viewDirectory)>	
				
		<cfsavecontent variable="local.data">
			<cfinclude template="/views/search/commonSearchFormJS.cfm">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/myDocuments/searchForm.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="prepSearchForSearchForm" access="private" returntype="struct" output="no" hint="parses the searchXML and populates search form">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
	
		<cfset var local = structNew()>
		
		<cfscript>
		local.returnStruct = StructNew();

		if (arguments.searchID gt 0) {
			// convert origin searchXML to bucket searchXML
			local.searchXML = application.objSearchTranslate.convertSearchXML(arguments.searchID,variables.thisBucketTypeID);

			// read/clean from xml
			local.returnStruct.s_fname = replace(local.searchXML.search["s_fname"].xmlText,chr(34),'','ALL');
			local.returnStruct.s_lname = replace(local.searchXML.search["s_lname"].xmlText,chr(34),'','ALL');
			local.returnStruct.s_casename = local.searchXML.search["s_casename"].xmlText;
			local.returnStruct.s_type = local.searchXML.search["s_type"].xmlText;
			local.returnStruct.s_jurisdiction = local.searchXML.search["s_jurisdiction"].xmlText;
			local.returnStruct.s_depodatefrom = prepareSearchDate(local.searchXML.search["s_depodatefrom"].xmlText);
			local.returnStruct.s_depodateto = prepareSearchDate(local.searchXML.search["s_depodateto"].xmlText);
			local.returnStruct.s_key_all = local.searchXML.search["s_key_all"].xmlText;
			local.returnStruct.s_key_one = local.searchXML.search["s_key_one"].xmlText;
			local.returnStruct.s_key_phrase = local.searchXML.search["s_key_phrase"].xmlText;
			local.returnStruct.s_key_x = local.searchXML.search["s_key_x"].xmlText;
		} else {
			local.returnStruct.s_fname = '';
			local.returnStruct.s_lname = '';
			local.returnStruct.s_casename = '';
			local.returnStruct.s_type = '';
			local.returnStruct.s_jurisdiction = '';
			local.returnStruct.s_depodatefrom = '';
			local.returnStruct.s_depodateto = '';
			local.returnStruct.s_key_all = '';
			local.returnStruct.s_key_one = '';
			local.returnStruct.s_key_phrase = '';
			local.returnStruct.s_key_x = '';
		}

		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="prepSearch" access="private" returntype="struct" output="no" hint="parses the searchXML and prepares search criteria">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
	
		<cfset var local = structNew()>
		
		<cfscript>
		// convert origin searchXML to bucket searchXML
		local.searchXML = application.objSearchTranslate.convertSearchXML(arguments.searchID,variables.thisBucketTypeID);

		// read/clean from xml
		local.s_fname = preparePhraseString(local.searchXML.search["s_fname"].xmlText);
		local.s_lname = preparePhraseString(local.searchXML.search["s_lname"].xmlText);
		local.s_casename = prepareSearchString(local.searchXML.search["s_casename"].xmlText);
		local.s_type = local.searchXML.search["s_type"].xmlText;
		local.s_jurisdiction = local.searchXML.search["s_jurisdiction"].xmlText;
		local.s_depodatefrom = prepareSearchDate(local.searchXML.search["s_depodatefrom"].xmlText);
		local.s_depodateto = prepareSearchDate(local.searchXML.search["s_depodateto"].xmlText);
		local.s_key_all = prepareSearchString(local.searchXML.search["s_key_all"].xmlText);
		local.s_key_one = prepareSearchString(local.searchXML.search["s_key_one"].xmlText,true);
		local.s_key_phrase = preparePhraseString(local.searchXML.search["s_key_phrase"].xmlText);
		local.s_key_x = prepareSearchString(local.searchXML.search["s_key_x"].xmlText);

		//prepare expertname keywords
		local.keywordsexpertname = "";
		if (Len(local.s_fname))
			local.keywordsexpertname = listAppend(local.keywordsexpertname,local.s_fname,chr(7));
		if (Len(local.s_lname))
			local.keywordsexpertname = listAppend(local.keywordsexpertname,local.s_lname,chr(7));
		local.keywordsexpertname = Replace(local.keywordsexpertname,chr(7)," and ","ALL");
		local.keywordscasename = local.s_casename;

		// prepare keywords
		local.keywordsInclude = "";
		if (Len(local.s_key_all))
			local.keywordsInclude = listAppend(local.keywordsInclude,local.s_key_all,chr(7));
		if (Len(local.s_key_one))
			local.keywordsInclude = listAppend(local.keywordsInclude,local.s_key_one,chr(7));
		if (Len(local.s_key_phrase))
			local.keywordsInclude = listAppend(local.keywordsInclude,local.s_key_phrase,chr(7));
		local.keywordsInclude = Replace(local.keywordsInclude,chr(7)," and ","ALL");

		local.keywordsExclude = "";
		if (Len(local.s_key_x))
			local.keywordsExclude = replaceNoCase(local.s_key_x," and "," or ","all");
		if (len(local.keywordsExclude) and len(local.keywordsInclude)) 
			local.finalKeywords = local.keywordsInclude & " and not " & local.keywordsExclude;
		else if (len(local.keywordsInclude))
			local.finalKeywords = local.keywordsInclude;
		else if (len(local.keywordsExclude))
			local.finalKeywords = "a and not " & local.keywordsExclude;
		else 
			local.finalKeywords = "";

		//check type
		if ((local.s_type eq "contributed") or (local.s_type eq "purchased"))
			local.searchtype = local.s_type;
		else
			local.searchtype = "";
		
		// return search struct
		local.returnStruct = structNew();
		structInsert(local.returnStruct,"keywords",local.finalKeywords);
		structInsert(local.returnStruct,"expertname",local.keywordsexpertname);
		structInsert(local.returnStruct,"casename",local.keywordscasename);
		structInsert(local.returnStruct,"type",local.searchtype);
		structInsert(local.returnStruct,"jurisdiction",local.s_jurisdiction);
		structInsert(local.returnStruct,"fromdate",local.s_depodatefrom);
		structInsert(local.returnStruct,"todate",local.s_depodateto);

		// do i have enough criteria to run a search?
		structInsert(local.returnStruct,"searchAccepted",application.objUser.isLoggedIn(cfcuser=session.cfcuser));
		</cfscript>

		<cfset local.fulltextsql = "">
		<cfset local.fulltextsqlTempTables = "">
		<cfset local.rankExpressionList = "">
		<cfset local.CRLF = chr(13) & chr(10)>

		<cfsavecontent variable="local.docstojoinsql">
			DECLARE @depoIDsAllowedToAccess TABLE (depomemberdataid int PRIMARY KEY)

			declare @approvedStatusID int 
			select @approvedStatusID=statusID from trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

			insert into @depoIDsAllowedToAccess
			select distinct @depomemberdataid as depomemberdataid
			union
			select distinct mypdocsfpl.depoMemberDataID
			from dbo.tlaFirmPlanLink as mypdocsfpl
			where mypdocsfpl.firmPlanID = (select firmPlanID from dbo.tlaFirmPlanLink where depoMemberDataID = @depomemberdataid)			
			
			DECLARE @docsToJoin TABLE (documentid int PRIMARY KEY)
			
			<cfif not len(local.s_type) or local.s_type eq "purchased">
				insert into @docsToJoin (documentid)
				select distinct mypdocs.documentid 
				from dbo.depodocuments as mypdocs
				inner join dbo.depoDocumentStatusHistory as dsh on dsh.depoDocumentHistoryID = mypdocs.currentStatusHistoryID
					and dsh.statusID = @approvedStatusID
				inner join dbo.depodocumenttypes as mypdocst on mypdocs.documenttypeid = mypdocst.typeid and (mypdocst.acctcode between 3000 and 3999 or mypdocst.acctcode in (5005,5006))
				inner join dbo.depoPermissions as mypdocsp on mypdocs.documentid = mypdocsp.documentid
				inner join @depoIDsAllowedToAccess as mypdocsaccountmembers on mypdocsaccountmembers.depomemberdataid = mypdocsp.depomemberdataid
			</cfif>
			<cfif not len(local.s_type)>
				union
			</cfif>
			<cfif not len(local.s_type) or local.s_type eq "contributed">
				select distinct mycdocs.documentid 
				from dbo.depodocuments as mycdocs
				inner join dbo.depoDocumentStatusHistory as dsh on dsh.depoDocumentHistoryID = mycdocs.currentStatusHistoryID
					and dsh.statusID = @approvedStatusID
				inner join dbo.depodocumenttypes as mycdocst on mycdocs.documenttypeid = mycdocst.typeid and mycdocst.acctcode between 3000 and 3999
				inner join @depoIDsAllowedToAccess as mycdocsaccountmembers on mycdocsaccountmembers.depomemberdataid = mycdocs.depomemberdataid
			</cfif>
		</cfsavecontent>

		<cfif len(local.returnStruct.expertname)>
			<cfset local.fulltextsql = local.fulltextsql & local.CRLF & "inner join containstable(trialsmith.dbo.depodocuments,expertname,@expertsearchterms) as expertsearch on expertsearch.[key] = docs.documentid">
			<cfset local.rankExpressionList = listappend(local.rankExpressionList,"expertsearch.[rank]")>
		</cfif>
		<cfif len(local.returnStruct.casename)>
			<cfset local.fulltextsql = local.fulltextsql & local.CRLF & "inner join containstable(trialsmith.dbo.depodocuments,style,@casenamesearchterms) as casesearch on casesearch.[key] = docs.documentid">
			<cfset local.rankExpressionList = listappend(local.rankExpressionList,"casesearch.[rank]")>
		</cfif>
		<cfif len(local.returnStruct.keywords)>
			<cfsavecontent variable="local.fulltextsqlTempTables">
				DECLARE @FTSmatches TABLE (documentid int PRIMARY KEY, rank int)
				
				insert into @FTSmatches (documentid, rank)
				select sd.documentid, rank
				from containstable(trialsmith.dbo.depodocuments,*,@keywordsearchterms) as dsearch
					inner join search.dbo.depodocuments sd on dsearch.[key] = sd.id 
					inner join @docsToJoin dj on dj.documentid = sd.documentid
				
				insert into @FTSmatches (documentid, rank)
				select sd.documentid, sdsearch.rank
				from containstable(search.dbo.depodocuments,searchtext,@keywordsearchterms) as sdsearch
					inner join search.dbo.depodocuments sd on sdsearch.[key] = sd.id
					inner join @docsToJoin dj on dj.documentid = sd.documentid
					left join @FTSmatches fts on dj.documentid = fts.documentid
				where fts.documentid is null
			</cfsavecontent>
			<cfsavecontent variable="local.fulltextsql">
				<cfoutput>#local.fulltextsql#</cfoutput>
				inner join (
					select documentid, max(rank) as rank 
					from @FTSmatches as fsearch 
					group by documentid
				) as keywordsearch on keywordsearch.documentid = docs.documentid
			</cfsavecontent>
			<cfset local.rankExpressionList = listappend(local.rankExpressionList,"keywordsearch.[rank]")>
		</cfif>
		<cfif len(local.fulltextsql)>
			<cfsavecontent variable="local.fulltextsql">
				inner join (
					select docs.documentid, (<cfoutput>#replacenocase(local.rankExpressionList,","," + ","all")#</cfoutput>) as rank
					from dbo.depoDocuments as docs
					<cfoutput>#local.fulltextsql#</cfoutput>
				) as searchhits on searchhits.documentid = d.documentid
			</cfsavecontent>
		</cfif>
			
		<cfset structInsert(local.returnStruct,"fulltextsql",local.fulltextsql)>
		<cfset structInsert(local.returnStruct,"fulltextsqlTempTables",local.fulltextsqlTempTables)>
		<cfset structInsert(local.returnStruct,"rankExpressionList",local.rankExpressionList)>
		<cfset structInsert(local.returnStruct,"docstojoinsql",local.docstojoinsql)>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getResultsCount" access="public" returntype="struct" output="no" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="mcproxy_siteID" required="no">
		<cfargument name="searchID" required="no">
		<cfargument name="bucketID" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["itemcount"] = 'N/A'>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>
		
		<cfif 
			(NOT StructKeyExists(arguments, "mcproxy_siteID") OR NOT IsNumeric(arguments.mcproxy_siteID)) OR
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID)) OR
			(NOT StructKeyExists(arguments, "bucketID") OR NOT IsNumeric(arguments.bucketID))>
				<cfset local.isValidArguments = false>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = doGetResultsCount(siteID=arguments.mcproxy_siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doGetResultsCount" access="private" returntype="struct" output="no" hint="searches and returns a count">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">

		<cfset var local = StructNew()>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfset local.strSearch = prepSearch(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfset local.returnStruct = StructNew()>

		<cfif NOT local.strSearch.searchAccepted>
			<cfset StructInsert(local.returnStruct,"itemcount",'N/A')>
			<cfset StructInsert(local.returnStruct,"success",true)>
			<cfset saveBucketCount(arguments.searchID,arguments.bucketID,-1)>
		<cfelse>
			<cfset local.cachedItemCount = getCachedBucketCount(arguments.searchID,arguments.bucketID)>
			<cfif local.cachedItemCount gte 0>
				<cfset StructInsert(local.returnStruct,"itemcount",local.cachedItemCount)>
				<cfset StructInsert(local.returnStruct,"success",true)>
			<cfelse>
				<cfquery name="local.qryResults" datasource="#application.dsn.tlasites_trialsmith.dsn#" result="local.qryStat">
					set nocount on;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					declare @itemCount int, @depomemberdataid int, @keywordsearchterms varchar(8000), @expertsearchterms varchar(8000), @casenamesearchterms varchar(8000);
					set @depomemberdataid = <cfqueryparam value="#session.cfcuser.memberdata.depomemberdataid#" cfsqltype="cf_sql_integer">;
					set @keywordsearchterms = <cfqueryparam value="#local.strSearch.keywords#" cfsqltype="CF_SQL_LONGVARCHAR">;
					set @expertsearchterms = <cfqueryparam value="#local.strSearch.expertname#" cfsqltype="CF_SQL_LONGVARCHAR">;
					set @casenamesearchterms = <cfqueryparam value="#local.strSearch.casename#" cfsqltype="CF_SQL_LONGVARCHAR">;

					#preservesinglequotes(local.strSearch.docstojoinsql)#
					
					#preservesinglequotes(local.strSearch.fulltextsqlTempTables)#
		
					select @itemCount = count(*)
					from @docsToJoin as docstojoinsql
					inner join dbo.depoDocuments d on docstojoinsql.documentid = d.documentid 
					<cfif len(local.strSearch.fulltextsql)>
						#preservesinglequotes(local.strSearch.fulltextsql)#
					</cfif>
					where 1=1
					<cfif len(local.strSearch.jurisdiction)>
						and d.jurisdiction = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.strSearch.jurisdiction#">
					</cfif>
					<cfif len(local.strSearch.fromdate)>
						and d.documentdate >= <cfqueryparam cfsqltype="cf_sql_date" value="#local.strSearch.fromdate#">
					</cfif>
					<cfif len(local.strSearch.todate)>
						and d.documentdate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.strSearch.todate#">
					</cfif>;

					select @itemCount as itemCount;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfset saveStats(arguments.searchID,'#variables.thisBucketType#.getResultsCount',local.qryStat.ExecutionTime,local.qryResults.itemCount)>
				<cfset saveBucketCount(arguments.searchID,arguments.bucketID,local.qryResults.itemCount)>
	
				<cfset StructInsert(local.returnStruct,"itemcount",local.qryResults.itemCount)>
				<cfset StructInsert(local.returnStruct,"success",true)>
			</cfif>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getNotLoggedInResults" access="private" returntype="struct" output="no" hint="searches and returns not logged in text">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="viewDirectory" type="string" required="no" default="default">
		
		<cfset var local = StructNew()>
		<cfset local.strResultsCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>

		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/myDocuments/notLoggedInResults.cfm">
		</cfsavecontent>
		<cfset local.stResults = ReReplace(local.stResults,'\s{2,}',' ','ALL')>

		<cfset local.returnStruct = StructNew()>
		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfset StructInsert(local.returnStruct,"itemcount",local.strResultsCount.itemCount)>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="showNotAllowed" access="private" output="false" returntype="struct">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="bucketName" required="yes" type="string">
		<cfargument name="accessDeniedMessage" required="yes" type="string">
		<cfargument name="includeBucketCount" required="no" type="boolean" default="true">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>

		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/myDocuments/notAllowed.cfm">
		</cfsavecontent>

		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfif arguments.includeBucketCount>
			<cfset local.strCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
			<cfset StructInsert(local.returnStruct,"itemcount",local.strCount.itemCount)>
		<cfelse>
			<cfset StructInsert(local.returnStruct,"itemcount",-1)>
		</cfif>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getResults" access="public" returntype="struct" output="no" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="mcproxy_siteID" required="no">
		<cfargument name="searchID" required="no">
		<cfargument name="bucketID" required="no">
		<cfargument name="startRow" required="no">
		<cfargument name="sortType" required="no">
		<cfargument name="viewDirectory" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["errmsg"] = 'Invalid Request'>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>
		
		<cfif 
			(NOT StructKeyExists(arguments, "mcproxy_siteID") OR NOT IsNumeric(arguments.mcproxy_siteID)) OR
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID)) OR
			(NOT StructKeyExists(arguments, "bucketID") OR NOT IsNumeric(arguments.bucketID)) OR
			(NOT StructKeyExists(arguments, "startRow") OR NOT IsNumeric(arguments.startRow)) OR
			NOT StructKeyExists(arguments, "sortType") OR
			(NOT StructKeyExists(arguments, "viewDirectory") OR NOT ListFind(variables.viewDirectories, arguments.viewDirectory))>
				<cfset local.isValidArguments = false>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = doGetResults(siteID=arguments.mcproxy_siteID, searchID=arguments.searchID, bucketID=arguments.bucketID,
				startrow=arguments.startrow, sortType=arguments.sortType, viewDirectory=arguments.viewDirectory)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doGetResults" access="private" returntype="struct" output="no" hint="searches and returns a query result">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="startRow" required="yes" type="numeric">
		<cfargument name="sortType" required="yes" type="string">
		<cfargument name="viewDirectory" required="yes" type="string">

		<cfset var local = StructNew()>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfset local.strSearch = prepSearch(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>
		<cfset local.returnStruct = StructNew()>

		<!--- checks.
		1. logged in?
		2. RestrictToGroup?
		3. search accepted?
		4. TrialSmithPending?
		--->
		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfreturn getNotLoggedInResults(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID,viewDirectory=arguments.viewDirectory)>
		<cfelseif NOT variables.cfcuser_isSiteAdmin AND val(local.qryBucketInfo.restrictToGroupID) GT 0 AND local.qryBucketInfo.isMemberInRestrictedGroup NEQ 1>
			<cfreturn showNotAllowed(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, bucketName=local.qryBucketInfo.bucketName, 
				accessDeniedMessage=local.qryBucketInfo.accessDeniedMessage, viewDirectory=arguments.viewDirectory)>
		<cfelseif NOT local.strSearch.searchAccepted>
			<cfreturn showSearchNotAccepted(searchID=arguments.searchID,bucketID=arguments.bucketID,bucketName=local.qryBucketInfo.bucketName,viewDirectory=arguments.viewDirectory)>
		<cfelseif application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='TrialSmithPending') is 1>
			<cfreturn showTrialsmithPending(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfelse>
			<cfset local.strCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>

			<cfquery name="local.qryResults" datasource="#application.dsn.tlasites_trialsmith.dsn#" result="local.qryStat">
				set nocount on;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
				declare @depomemberdataid int, @keywordsearchterms varchar(8000), @expertsearchterms varchar(8000), @casenamesearchterms varchar(8000)
				set @depomemberdataid = <cfqueryparam value="#session.cfcuser.memberdata.depomemberdataid#" cfsqltype="cf_sql_integer">
				set @keywordsearchterms = <cfqueryparam value="#local.strSearch.keywords#" cfsqltype="CF_SQL_LONGVARCHAR">
				set @expertsearchterms = <cfqueryparam value="#local.strSearch.expertname#" cfsqltype="CF_SQL_LONGVARCHAR">
				set @casenamesearchterms = <cfqueryparam value="#local.strSearch.casename#" cfsqltype="CF_SQL_LONGVARCHAR">
	
				#preservesinglequotes(local.strSearch.docstojoinsql)#
					
				#preservesinglequotes(local.strSearch.fulltextsqlTempTables)#
		
				<!--- create temp table --->
				DECLARE @tmpResults TABLE (autoid int IDENTITY(1,1), documentid int, rank int)

				<!--- insert all into temp --->
				INSERT INTO @tmpResults (documentid, rank)
				Select TOP (<cfqueryparam value="#min(arguments.startRow + variables.thisBucketMaxPerPage - 1,variables.thisBucketMaxShown)#" cfsqltype="CF_SQL_INTEGER">)
					d.documentid, <cfif len(local.strSearch.keywords & local.strSearch.expertname & local.strSearch.casename)>searchhits.rank<cfelse>1000</cfif> as rank
				from @docsToJoin as docstojoinsql
				<cfif len(local.strSearch.docstojoinsql)>
					inner join dbo.depoDocuments d on docstojoinsql.documentid = d.documentid 
				</cfif>
				<cfif len(local.strSearch.fulltextsql)>
					#local.strSearch.fulltextsql#
				</cfif>
				where 1=1
				<cfif len(local.strSearch.jurisdiction)>
					and d.jurisdiction = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.strSearch.jurisdiction#">
				</cfif>
				<cfif len(local.strSearch.fromdate)>
					and d.documentdate >= <cfqueryparam cfsqltype="cf_sql_date" value="#local.strSearch.fromdate#">
				</cfif>
				<cfif len(local.strSearch.todate)>
					and d.documentdate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.strSearch.todate#">
				</cfif>
				<cfswitch expression="#arguments.sortType#">
					<cfcase value="date">
						order by d.DocumentDate desc
					</cfcase>
					<cfcase value="witness">
						order by d.ExpertName
					</cfcase>
					<cfcase value="case">
						order by d.style
					</cfcase>
					<cfdefaultcase>
						<cfset arguments.sortType = "rank">
						order by rank desc, d.DocumentDate desc
					</cfdefaultcase>
				</cfswitch>

				<!--- return top x --->
				select TOP (<cfqueryparam value="#variables.thisBucketMaxPerPage#" cfsqltype="CF_SQL_INTEGER">) #local.strCount.itemcount# as itemCount,
					d.documentid, d.expertname, d.documentdate, d.style, d.state,
					ct.description as causedesc, d.notes, m.email, m.firstname, m.lastname, m.billingfirm, 
					m.phone
				from @tmpResults as tmp
				inner join dbo.depodocuments AS d on d.documentid = tmp.documentid
				inner join dbo.depoCaseTypes as ct on d.caseTypeId = ct.caseTypeID
				inner join dbo.depomemberdata as m on m.depomemberdataid = d.depomemberdataid
				where tmp.autoid >= <cfqueryparam value="#arguments.startRow#" cfsqltype="CF_SQL_INTEGER">
				ORDER BY tmp.autoid
				
				set nocount off;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfset saveStats(arguments.searchID,'#variables.thisBucketType#.getResults',local.qryStat.ExecutionTime)>

			<!--- adjust maxperpage based on actual data if necessary and get page variables --->
			<cfscript>
			local.MaxPerPage = iif(local.qryResults.recordcount gt variables.thisBucketMaxPerPage,variables.thisBucketMaxPerPage,local.qryResults.recordcount);
			if (local.MaxPerPage gt 0) {
				local.NumTotalPages = Ceiling(local.qryResults.itemCount / variables.thisBucketMaxPerPage);
				local.NumCurrentPage = int((int(arguments.startRow) + variables.thisBucketMaxPerPage - 1) / variables.thisBucketMaxPerPage);
			} else {
				local.NumTotalPages = 0;
				local.NumCurrentPage = 0;
			}
			</cfscript>

			<!--- return content --->
			<cfsavecontent variable="local.stResults">
				<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/myDocuments/results.cfm">
			</cfsavecontent>

			<cfset local.stResults = ReReplace(local.stResults,'\s{2,}',' ','ALL')>
			<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
			<cfset StructInsert(local.returnStruct,"thisBucketCartItemTypeID",variables.thisBucketCartItemTypeID)>
			<cfset StructInsert(local.returnStruct,"numTotalPages",val(local.NumTotalPages))>
			<cfset StructInsert(local.returnStruct,"numCurrentPage",val(local.NumCurrentPage))>
			<cfset StructInsert(local.returnStruct,"itemcount",val(local.qryResults.itemCount))>
			<cfset StructInsert(local.returnStruct,"success",true)>
		
			<cfreturn local.returnStruct>
		</cfif>
	</cffunction>

	<cffunction name="saveSearchForm" access="public" output="no" returntype="numeric" hint="saves the form vars to a search and returns the searchid">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="formvars" required="yes" type="struct">

		<cfset var local = structNew()>
		
		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfif StructKeyExists(arguments.formvars,"s_jurisdiction") and listlen(arguments.formvars.s_jurisdiction)>
			<cfquery name="local.statesLookup" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				select name 
				from dbo.states
				where code in (<cfqueryparam cfsqltype="cf_sql_varchar" list="yes" value="#trim(arguments.formvars.s_jurisdiction)#">)
			</cfquery>
			<cfset local.expandedStates = xmlFormat(valuelist(local.statesLookup.name,"^"))>
			<cfset local.xmlStates = xmlFormat(trim(arguments.formvars.s_jurisdiction))>
		<cfelse>
			<cfset local.expandedStates = "">
			<cfset local.xmlStates = "">
		</cfif>

		<cfsavecontent variable="local.xmlSearch">
			<cfoutput>
			<search xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2">
				<bid><cfif StructKeyExists(arguments.formvars,"bid")>#val(arguments.formvars.bid)#</cfif></bid>
				<s_casename><cfif StructKeyExists(arguments.formvars,"s_casename")>#xmlFormat(trim(arguments.formvars.s_casename))#</cfif></s_casename>
				<cfif StructKeyExists(arguments.formvars,"s_depodatefrom") and isValid("date",arguments.formvars.s_depodatefrom)>
					<s_depodatefrom>#DateFormat(arguments.formvars.s_depodatefrom,"yyyy-mm-dd")#Z</s_depodatefrom>
				<cfelse>
					<s_depodatefrom xsi:nil="true" />
				</cfif>
				<cfif StructKeyExists(arguments.formvars,"s_depodateto") and isValid("date",arguments.formvars.s_depodateto)>
					<s_depodateto>#DateFormat(arguments.formvars.s_depodateto,"yyyy-mm-dd")#Z</s_depodateto>
				<cfelse>
					<s_depodateto xsi:nil="true" />
				</cfif>
				<s_fname><cfif StructKeyExists(arguments.formvars,"s_fname")>#xmlFormat(trim(replace(arguments.formvars.s_fname,chr(34),'','ALL')))#</cfif></s_fname>
				<s_jurisdiction expanded="#local.expandedStates#">#local.xmlStates#</s_jurisdiction>
				<s_key_all><cfif StructKeyExists(arguments.formvars,"s_key_all")>#xmlFormat(trim(arguments.formvars.s_key_all))#</cfif></s_key_all>
				<s_key_one><cfif StructKeyExists(arguments.formvars,"s_key_one")>#xmlFormat(trim(arguments.formvars.s_key_one))#</cfif></s_key_one>
				<s_key_phrase><cfif StructKeyExists(arguments.formvars,"s_key_phrase")>#xmlFormat(trim(arguments.formvars.s_key_phrase))#</cfif></s_key_phrase>
				<s_key_x><cfif StructKeyExists(arguments.formvars,"s_key_x")>#xmlFormat(trim(arguments.formvars.s_key_x))#</cfif></s_key_x>
				<s_lname><cfif StructKeyExists(arguments.formvars,"s_lname")>#xmlFormat(trim(replace(arguments.formvars.s_lname,chr(34),'','ALL')))#</cfif></s_lname>
				<s_type><cfif StructKeyExists(arguments.formvars,"s_type")>#xmlFormat(trim(arguments.formvars.s_type))#</cfif></s_type>
			</search>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.searchID = saveSearchXML(val(arguments.formvars.bid),local.xmlSearch)>
		
		<cfreturn local.searchID>
	</cffunction>
</cfcomponent>