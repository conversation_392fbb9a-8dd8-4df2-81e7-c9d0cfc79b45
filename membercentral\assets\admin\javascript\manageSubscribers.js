function prepSubsFilterForm() {
	mca_setupDatePickerRangeFields('fTermStartFrom','fTermStartTo');
	mca_setupDatePickerRangeFields('fTermEndFrom','fTermEndTo');
	mca_setupDatePickerRangeFields('fOffrExpFrom','fOffrExpTo');
	mca_setupCalendarIcons('frmFilter');
	mca_setupSelect2();
	
    $('body').on('change', '#fSubType', function(e) {
		mca_callChainedSelect('fSubType','fSubscription', link_adminHomeResource, 'subs', 'getSubscriptionsForSubType', 'typeid', 0, false, false);
		$('#fRate').empty().trigger('change.select2');
	});

	$('#fSubStatus').on('change',onSubStatusChange);

	$('body').on('change', '#fSubscription', function(e) {
		mca_callChainedSelect('fSubscription','fRate', link_adminHomeResource, 'subs', 'getSubRatesForSub', 'subid', 0, true, false, [{name:'data-isrenewalrate', datafield:'isrenewalrate'}]);
	});

	if ($('#associatedMemberID').val() == 0 && $('#associatedGroupID').val() == 0) $("#divAssociatedVal").hide();

	$("#frmFilter .assocType").on("click",function(){	
		var assocType = $('#frmFilter input:radio[name=assocType]:checked').val();
		if (assocType != undefined) {
			if (assocType == "group") selectGroupInvFilter();
			else selectMemberInvFilter();
		}
	});

	$("#aClearAssocType").on("click",function() {
		$("#frmFilter .assocType").each(function(){
		     $(this).attr("checked",false);
		});	
		$('#associatedVal').html("");
		$('#associatedMemberID').val(0);
		$('#associatedGroupID').val(0);
		$('#associatedMemberName').val('');
		$('#associatedMemberNum').val('');
		$('#associatedGroupName').val('');
		$('#divAssociatedVal').hide();
		$("#expandSearch").hide();
	});

	renderSubActionButtons('');
}
function onSubStatusChange(r) {
	if($('#fSubStatus').val() == 'O') {
		$('.offrExpWrap').removeClass('d-none');
	} else {
		$('.offrExpWrap input').val('');
		$('.offrExpWrap').addClass('d-none');
	}
}
function renderSubActionButtons(subStatus) {
	let subBtnBarTemplate = Handlebars.compile($('#mc_subBtnBarTemplate').html());
	let subStatusActBtns = mc_subStatusBtns['default'];
	if (subStatus.length && Object.hasOwn(mc_subStatusBtns, subStatus)) subStatusActBtns = mc_subStatusBtns[subStatus];
	else if (subStatus == '0' && $.fn.DataTable.isDataTable('#subsListTable')) subStatusActBtns = mc_subStatusBtns['ALL'];
	$('#subsButtonBar').html(subBtnBarTemplate({ buttons: subStatusActBtns }));
}
function selectGroupInvFilter() {
	var selhref = link_grpSelectGotoLink+'&mode=direct&fldName=associatedGroupID&retFunction=top.updateGroupField&dispTitle=' + escape('Filter Subscribers by Group');
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Filter Subscribers by Group',
		iframe: true,
		contenturl: selhref,
		strmodalfooter: {
			classlist: 'd-none'
		}
	});
}
function selectMemberInvFilter() {		
	var selhref = link_memSelectGotoLink+'&mode=direct&fldName=associatedMemberID&retFunction=top.updateField&dispTitle=';
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Filter Subscribers by Member',
		iframe: true,
		contenturl: selhref,
		strmodalfooter : {
			classlist: 'd-none',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '',
			extrabuttonlabel: 'Submit',
		}
	});
}
function updateField(fldID, mID, mNum, mName) {
	var fld = $('#'+fldID);
	var fldName = $('#associatedVal');
	fld.val(mID);
	if ((mName.length > 0) && (mNum.length > 0)) {
		$('#associatedMemberName').val(mName);
		$('#associatedMemberNum').val(mNum);
		fldName.html(mName + ' (' + mNum + ')');
		$('#expandSearch').show();
		$('#associatedGroupID').val(0);
		$('#divAssociatedVal').show();
	} else {
		fldName.html('');
		$('#expandSearch').hide();
		$('#divAssociatedVal').hide();
	}
}
function updateGroupField(fldID,gID,gPath) {
	var fld = $('#'+fldID);
	var fldName = $('#associatedVal');		
	fld.val(gID);
	if (gPath.length > 0) {
		var newgPath = gPath.split("\\");
			newgPath.shift();
			newgPath = newgPath.join(" \\ ");	
		$('#associatedGroupName').val(newgPath);
		fldName.html(newgPath);
		$('#associatedMemberID').val(0);
		$('#divAssociatedVal').show();
		$('#expandSearch').hide();
	} else {
		fldName.html('');
		$('#divAssociatedVal').hide();
		$('#expandSearch').hide();
	}
}
function clearFilterSubGrid() {
	/* since reset() won't clear fields with default values */
	$('#frmFilter input[type="hidden"], #frmFilter input[type="text"], #fHasCardOnFile').val('');
	$('#fChkAll, #fSubPaymentStatus, #fFreq, #fSubType, #fSubscription, #fRevenueGL').val(0);
	$('#fSubStatus').val('0');
	$('#fSubType, #fSubscription').trigger('change');
	$('#fRate').empty().trigger('change.select2');
	$('#aClearAssocType').click();
	filterSubGrid();
}
function getSubFilters() {
	let objParams = {};
	objParams.sID = $('#fSubStatus').val();
	objParams.stID = $('#fSubType').val();
	if (objParams.stID.length == 0) objParams.stID = '0';
	objParams.subID = $('#fSubscription').val();
	if (objParams.subID.length == 0) objParams.subID = '0';
	objParams.freqID = $('#fFreq').val();
	objParams.dtTSf = $('#fTermStartFrom').val();
	objParams.dtTSt = $('#fTermStartTo').val();
	objParams.dtTEf = $('#fTermEndFrom').val();
	objParams.dtTEt = $('#fTermEndTo').val();
	objParams.dtOffrExpF = $('#fOffrExpFrom').val();
	objParams.dtOffrExpT = $('#fOffrExpTo').val();
	objParams.spID = $('#fSubPaymentStatus').val();
	objParams.hasCard = $('#fHasCardOnFile').val();
	objParams.associatedMemberID = $('#associatedMemberID').val();
	objParams.linkedRecords = $('#frmFilter input[name="linkedRecords"]:checked').val();
	objParams.associatedGroupID = $('#associatedGroupID').val();
	objParams.associatedMemberName = $('#associatedMemberName').val();
	objParams.associatedMemberNum = $('#associatedMemberNum').val();
	objParams.associatedGroupName = $('#associatedGroupName').val();
	objParams.rateID = $('#fRate').val() || '';
	if (objParams.rateID.length) { 
		objParams.rateID = objParams.rateID.toString();
	} else { 
		objParams.rateID = '0';
	}
	
	objParams.chkAll = checkAllSubs;
	objParams.subsList = checkAllSubs ? '' : arrChkedSubs.join(',');
	objParams.notSubsList = checkAllSubs ? arrUnchkedSubs.join(',') : '';

	return objParams;
}
function resetAllRecordSelections(f) {
	$('#masterCheckBox').prop('checked',f);
	doCheckAllSubs(f);
}
function generateCustomSubRadioFilterVerbose(filterField) {
	let label = "";
	let value = "";
	const fieldId = filterField.attr('id');

	if (filterField.is(':checked')) {
		switch (fieldId) {
			case 'assocTypeMember':
				label = 'Associated With Member';
				value = $('#associatedMemberName').val() + ' (' + $('#associatedMemberNum').val() + ')';
				let incLinkedRecordFldID = $('#frmFilter input[name="linkedRecords"]:checked').attr('id');
				if (incLinkedRecordFldID) value += ' [' + $(`label[for='${incLinkedRecordFldID}']`).text().trim() + ']';
			break;

			case 'assocTypeGroup':
				label = 'Associated With Group';
				value = $('#associatedGroupName').val();
			break;

			default:
				label = $(`label[for='${fieldId}']`).text().trim();
				value = filterField.val();
		}
	}

	return { label, value };
}
async function filterSubGrid() {
	$('#subsButtonBar').children().remove();
	await mca_generateVerboseFilterMessage('frmFilter');
	reloadSubsTable();
}
function quickSelectSubRates(elmID, isRenewalRate){
	let arrRateIDs = $('select#' + elmID).find('option[data-isrenewalrate="'+isRenewalRate+'"]').map(function(index,element){ 
			return $(element).attr("value");
		}).toArray();
	$('select#' + elmID).val(arrRateIDs).trigger('change');
}
function loadSubAuditTrail(at=10) {
	$('#subAuditTrailRpt').html(mca_getLoadingHTML()).load(link_listAuditRpt + '&at=' + at).show();
}
function toggleATRow(rowID) {
	var st = $('#atChanges_' + rowID).css("display");
	if (st == 'none') {
		$('#atChanges_' + rowID).show();
		$('#atTreeImg_' + rowID).attr("src","/assets/common/images/tree-open.jpg");
	} else {
		$('#atChanges_' + rowID).hide();
		$('#atTreeImg_' + rowID).attr("src","/assets/common/images/tree-closed.jpg");
	}
}
function toggleATGrid() {
	var st = $('#subAuditTrail').css("display");
	if (st == 'none') {
		$('#subAuditTrail').show();
		$('#auditResultsShow').hide();
		$('#auditResultsHide').show();
	} else {
		$('#subAuditTrail').hide();
		$('#auditResultsShow').show();
		$('#auditResultsHide').hide();
	}
}
function startExportSubs() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Export Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		checkSubEntry();
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: title,
			iframe: true,
			contenturl: link_exportSubs + '&' + $('#frmFilter').serialize(),
			strmodalfooter : {
				classlist: 'd-flex',
				showclose: true,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto d-none',
				extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnContinue").click',
				extrabuttonlabel: 'Continue',
			}
		});
	}
}
function exportStandard() {	top.closeBox(); }
function exportMailhouse() { top.closeBox(); }
function checkMarkExpired() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Expire Selected Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		checkSubEntry();
		showSubscriptionModal(title, link_startMarkExpired);
	}	
}

function startGenSub() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Generate Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		checkSubEntry();
		showSubscriptionModal(title, link_startGenerateSub);
	}
}

function massUpdateGraceEndDate() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Change Subscription Grace End Date';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		checkSubEntry();
		showSubscriptionModal(title, link_startUpdateGraceEndDate);
	}
}

function checkMarkInactive() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Inactivate Selected Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		checkSubEntry();
		showSubscriptionModal(title, link_startMarkInactive);
	}
}

function checkMarkActive() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Activating Selected Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		checkSubEntry();
		showSubscriptionModal(title, link_startMarkActive);
	}
}

function checkMarkBilled() {	
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Billing Selected Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		checkSubEntry();
		showSubscriptionModal(title, link_startMarkBilled);
	}
}

function startAddSub(subupd) {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Add Subscription';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		checkSubEntry();
		showSubscriptionModal(title, link_startForceAddSub+'&subupd=' + subupd);
	}
}

function deleteRenewals() {	
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Deleting Selected Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		checkSubEntry();
		showSubscriptionModal(title, link_startDeleteRenewals);
	}
}

function removePaymentMethods() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Remove Pay Method from Selected Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		checkSubEntry();
		showSubscriptionModal(title, link_removePaymethod);
	}
}

function removeAddOn() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Remove AddOn Subscriptions';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		checkSubEntry();
		showSubscriptionModal(title, link_startRemoveAddons);
	}
}

function checkSendOffers() {
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Select Email Template';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		checkSubEntry();
		var sendOffersLink = link_startGenerateOffers + '&' + $('#frmFilter').serialize();
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: title,
			iframe: true,
			contenturl: sendOffersLink,
			strmodalfooter: {
				classlist: 'd-flex justify-content-between',
				showclose: false,
				buttons: [
					{
						class:"btn btn-sm btn-secondary d-none",
						clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnChangeTemplate").click',
						label: 'Change Template', 
						name: 'btnChangeTemplate',
						id: 'btnChangeTemplate'
					},
					{
						class:"btn btn-sm btn-secondary d-none mr-auto",
						clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnTestTemplate").click',
						label: 'Send Test E-mail', 
						name: 'btnTestTemplate',
						id: 'btnTestTemplate'
					},
					{
						class:"btn btn-sm btn-primary ml-auto",
						clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSelectTemplate").click',
						label: 'Select Template', 
						name: 'btnSelectTemplate',
						id: 'btnSelectTemplate'
					},
					{
						class: "btn btn-primary btn-sm ml-auto d-none",
						clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnProcessTemplate").click',
						label: 'Send Emails', 
						name: 'btnProcessTemplate',
						id: 'btnProcessTemplate'
					}
				]
			}
		});
	}
}

function checkMarkAccepted() {	
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Mark as Accepted';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	} else {
		checkSubEntry();
	
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: title,
			iframe: true,
			contenturl: link_startMarkAccepted + '&' + $('#frmFilter').serialize()
		});
	}
}

function changeOfferExpirationDate() {	
	strSubExist =  existsSubsOnFilteredGrid();
	var title = 'Change Subscription Offer Expiration Date';
	
	if (strSubExist.length) {
		showSubscriptionWarningModal(title, strSubExist);
	}else{
		checkSubEntry();
		showSubscriptionModal(title, link_startUpdateOfferExpirationDate);
	}
}
function existsSubsOnFilteredGrid() {
	if(totalSubsCount == 0 || (!checkAllSubs && arrChkedSubs.length == 0) || (checkAllSubs && arrUnchkedSubs.length == totalSubsCount)) {
		return 'No subscriptions were selected.';
	} else {
		return '';
	}
}
function showSubRenewalLink(sid) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Subscription Renewal Link and Code',
		iframe: true,
		contenturl: link_subRenewal+'&sid='+sid
	});
}
function showSubscriptionWarningModal(title, message) {
	MCModalUtils.showModal({
		verticallycentered: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false,
		},
		size: 'md',
		title: title,
		strmodalbody: {
			content: '<div class="alert alert-warning">' + message + '</div>'
		},
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
		}
	});
}
function showSubscriptionModal(title, link) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: title,
		iframe: true,
		contenturl: link + '&' + $('#frmFilter').serialize()
	});
}
function initSubsTable(){
	let domString = "<'row'<'d-flex col-sm-10 col-md-10'<'mt-2'l><'#selSubCountDisp.d-flex flex-wrap p-1 m-2'>><'col-sm-2 col-md-2'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";

	subsListTable = $('#subsListTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 25,
		"lengthMenu": [ 25, 50, 100 ],
		"dom": domString,
		"language": {
			"lengthMenu": "_MENU_"
		},
		"ajax": { 
			"url": subsListLink,
			"type": "post",
			"data": function(d) {
				let strFilters = getSubFilters();
				d['sID'] = strFilters.sID;
				d['spID'] = strFilters.spID;
				d['stID'] = strFilters.stID;
				d['subID'] = strFilters.subID;
				d['rateID'] = strFilters.rateID;
				d['freqID'] = strFilters.freqID;
				d['fCard'] = strFilters.hasCard;
				d['dtTSf'] = strFilters.dtTSf;
				d['dtTSt'] = strFilters.dtTSt;
				d['dtTEf'] = strFilters.dtTEf;
				d['dtTEt'] = strFilters.dtTEt;
				d['dtOffrExpF'] = strFilters.dtOffrExpF;
				d['dtOffrExpT'] = strFilters.dtOffrExpT;
				d['associatedMemberID'] = strFilters.associatedMemberID;
				d['associatedGroupID'] = strFilters.associatedGroupID;
				d['linkedRecords'] = strFilters.linkedRecords;
				d['chkAll'] = strFilters.chkAll;
			},
			"deferLoading":400
		},
		"autoWidth": false,
		"columns": [
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						var isChecked = "";
						if($("#masterCheckBox:checkbox:checked").length){
							isChecked = "checked";
						}
						renderData += '<input type="checkbox" name="subCheckbox" class="subCheckbox" '+ isChecked +' value="' + data.subscriberID + '" onclick="onCheckSubEntry(this);">';
					}
					return type === 'display' ? renderData : data;
				},
				'searchable': false,
				'className': 'text-center align-top',
				"width": "5%",
				"orderable": false
			},
			{ 
				"data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<div><a href="javascript:editMember('+data.memberID+')" _target="blank">'+data.memberName+'</a></div>';
						if(data.membercompany.length) renderData += '<div class="text-dim small">'+data.membercompany+'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"className": "align-top",
				"width": "25%"
			},
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<div class="float-right">';
						if (data.paymentStatusCode == 'N' && ['A','P'].includes(data.statusCode)) renderData += '<span class="badge badge-warning">Activation Not Met</span>';
						renderData += '<span class="badge badge-neutral-second text-second">' + data.statusName + '</span></div>';
						renderData += '<div>'+data.subscriptionName+'</div>';
						renderData += '<div class="text-dim small">'+ data.ratename +'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"className": "align-top"
			},
			{ "data": "substartdate", "width": "9%", "className": "align-top text-right" },
			{
				"data": null,
				"render": function (data, type) {
					let renderData = '';
					if (type === 'display') {
						renderData += '<div>'+data.subenddate+'</div>';
						if (data.graceEndDate.length) renderData += '<div class="text-dim small">'+ data.graceEndDate +'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"className": "align-top text-right",
				"width": "9%"
			},
			{ "data": "offerRescindDate", "width": "9%", "className": "align-top text-right" },
			{ 
				"data": null,
				"render": renderSubscriptionActions,
				"className": "text-center align-top",
				"orderable": false
			}
		],
		"order": [[1, 'asc']],
		"searching": false,
		"pagingType": "simple",
		"drawCallback": function(settings) {
			totalSubsCount = subsListTable.page.info().recordsTotal;
			$(".subCheckbox:checkbox").prop('checked', checkAllSubs);

			if(checkAllSubs == 1) {
				$.each(arrUnchkedSubs, function( index, value ) {
					$(".subCheckbox:checkbox[value="+value+"]").prop('checked', false);
				});
			} else {
				$.each(arrChkedSubs, function( index, value ) {
					$(".subCheckbox:checkbox[value="+value+"]").prop('checked', true);
				});
			}
			checkSubEntry();

			let subStatus = $('#fSubStatus').val();
			if (subStatus == 'O') {
				subsListTable.column(5).visible(true);
			} else {
				subsListTable.column(5).visible(false);
			}
			if (subStatus == 'R') {
				if ($('#subAuditTrailRpt').attr('data-init') == 0) {
					loadSubAuditTrail();
					$('#subAuditTrailRpt').attr('data-init',1);
				}
			} else {
				$('#subAuditTrailRpt').html('').hide();
			}
			renderSubActionButtons(subStatus);

			let actionToolWidth = $('#subsListTable tbody .actionTool:first a').length ? $('#subsListTable tbody .actionTool:first a').length * 36 : 100;
			$('#subsListTable thead th:last').css('width',actionToolWidth + 'px'); 
		}
	});

	mca_generateVerboseFilterMessage('frmFilter');
}
function renderSubscriptionActions( data, type, row, meta){
	let renderData = '';
	if (type === 'display')	{	
		renderData += '<div class="actionTool">';
		
		if (data.anyshowCCIcon) {
			if([1, 2].includes(data.payMethod)) {
				renderData += '<a href="#" class="btn btn-xs p-1 mx-1 mb-1 '+(data.payMethod == 2 ? 'text-warning' : 'text-dark')+'" onclick="assocSubCC('+data.subscriberID+','+data.memberID+');return false;" data-toggle="tooltip" title="Pay Method for Subscription"><i class="fa-solid fa-credit-card"></i></a>';
			} else {
				renderData += '<a href="#" class="btn btn-xs p-1 mx-1 text-muted disabled"><i class="fa-solid fa-credit-card"></i></a>';
			}
		}
		if (data.anyCanPay) {
			if(data.addPayment == 1){
				renderData += '<a href="#" class="btn btn-xs text-primary p-1 mx-1 mb-1" onclick="mca_addPayment(\''+data.addPaymentEncString+'\');return false;" data-toggle="tooltip" title="Pay for Subscription"><i class="fa-solid fa-dollar-sign"></i></a>';
			}else{
				renderData += '<a href="#" class="btn btn-xs p-1 mx-1 mb-1 text-muted disabled"><i class="fa-solid fa-dollar-sign"></i></a>';
			}
		}
		if (data.billedSubsCount) {
			if(data.statusName == 'Billed'){
				strdownload = "'download'";
				renderData += '<a href="#" onclick="generatePaperStatement('+data.subscriberID+','+data.memberID+','+strdownload+');return false;" class="btn btn-xs text-primary p-1 mx-1 mb-1"  data-toggle="tooltip" title="Generate Paper Statement"><i class="fa-solid fa-file-pdf"></i></a>';
			}else{
				renderData += '<a href="#" class="btn btn-xs p-1 mx-1 mb-1 text-muted disabled" ><i class="fa-solid fa-file-pdf"></i></a>';
			}
		}
		if (data.anycanEdit) {
			if(data.canEditSub == 1){
				renderData += '<a href="#" class="btn btn-xs text-primary p-1 mx-1 mb-1" onclick="editMemberSub('+data.subscriberID+','+data.memberID+');return false;" data-toggle="tooltip" title="Edit Subscription"><i class="fa-solid fa-pencil"></i></a>';
			}else{
				renderData += '<a href="#" class="btn btn-xs p-1 mx-1 text-muted disabled"><i class="fa-solid fa-pencil"></i></a>';
			}
		}
		if (data.anyAI) {
			if(data.markActive == 1){
				renderData += '<a href="#" class="btn btn-xs text-primary p-1 mx-1 mb-1" onclick="markInactiveSub('+data.subscriberID+','+data.memberID+');return false;" data-toggle="tooltip" title="Mark Inactive"><i class="fa-solid fa-shuffle"></i></a>';
			}else if(data.markActive == 2){
				renderData += '<a href="#" class="btn btn-xs text-primary p-1 mx-1 mb-1" onclick="markActiveSub('+data.subscriberID+','+data.memberID+');return false;" data-toggle="tooltip" title="Mark Active"><i class="fa-solid fa-shuffle"></i></a>';
			}else{
				renderData += '<a href="#" class="btn btn-xs p-1 mx-1 text-muted disabled"><i class="fa-solid fa-shuffle"></i></a>';
			}
		}
		if (data.anycanRenew) {
			if(data.subCanRenew == 1){
				renderData += '<a href="#" class="btn btn-xs text-primary p-1 mx-1 mb-1" onclick="renewMemberSub('+data.subscriberID+','+data.memberID+');return false;" data-toggle="tooltip" title="Renew Subscription"><i class="fa-solid fa-rotate-right"></i></a>';
			}else{
				renderData += '<a href="#" class="btn btn-xs  p-1 mx-1 mb-1 text-muted disabled"><i class="fa-solid fa-rotate-right"></i></a>';
			}
		}
		if (data.billedSubsCount) {
			if(data.acceptSub == 1){
				renderData += '<a href="#" class="btn btn-xs text-primary p-1 mx-1 mb-1" onclick="acceptSubscription('+data.subscriberID+','+data.memberID+');return false;" data-toggle="tooltip" title="Accept Subscription"><i class="fa-solid fa-check-double"></i></a>';
			}else{
				renderData += '<a href="#" class="btn btn-xs p-1 mx-1 text-muted disabled"><i class="fa-solid fa-check-double"></i></a>';
			}
		}
		if (data.anycanEmail) {
			if(data.emailOfferSub == 1){
				renderData += '<a href="#" class="btn btn-xs text-primary p-1 mx-1 mb-1" onclick="emailOfferSub('+data.subscriberID+','+data.memberID+');return false;" data-toggle="tooltip" title="Email Offer"><i class="fa-solid fa-envelope"></i></a>';
			}else{
				renderData += '<a href="#" class="btn btn-xs text-muted disabled p-1 mx-1 mb-1"><i class="fa-solid fa-envelope"></i></a>';
			}
		}
		if (data.anycanCleanupInvoices) {
			if(data.cleanUpInvoice == 1){
				renderData += '<a href="#" class="btn btn-xs text-danger p-1 mx-1 mb-1" onclick="cleanupInvoices('+data.subscriberID+','+data.memberID+');return false;" data-toggle="tooltip" title="Cleanup Invoices"><i class="fa-solid fa-file-invoice-dollar"></i></a>';
			}else{
				renderData += '<a href="#" class="btn btn-xs text-muted disabled p-1 mx-1 mb-1"><i class="fa-solid fa-file-invoice-dollar"></i></a>';
			}
		}
		if (data.anyCanBillExpire || data.billedSubsCount) {
			if(data.markBilledExpired == 1){
				renderData += '<a href="#" class="btn btn-xs text-primary p-1 mx-1 mb-1" onclick="markBilledSub('+data.subscriberID+','+data.memberID+');return false;" data-toggle="tooltip" title="Mark as Billed"><i class="fa-solid fa-check"></i></a>';
			}else if(data.markBilledExpired == 2 ){
				renderData += '<a href="#" class="btn btn-xs text-primary p-1 mx-1 mb-1" onclick="markExpiredSub('+data.subscriberID+','+data.memberID+');return false;" data-toggle="tooltip" title="Mark as Expired"><i class="fa-solid fa-calendar-xmark"></i></a>';
			}else if(data.statusCode == 'O'){
				renderData += '<a href="#" class="btn btn-xs text-primary p-1 mx-1 mb-1" onclick="showSubRenewalLink('+data.subscriberID+');return false;" data-toggle="tooltip" title="Subscription Renewal Link"><i class="fa-regular fa-copy"></i></a>';
			}else{
				renderData += '<a href="#" class="btn btn-xs text-muted disabled p-1 mx-1 mb-1"><i class="fa-solid fa-calendar-xmark"></i></a>';
			}
		}
		if (data.anycanActivate) {
			if(data.markOverrideSub == 1){
				renderData += '<a href="#" class="btn btn-xs text-primary p-1 mx-1 mb-1" onclick="markOverrideActivationSub('+data.subscriberID+');return false;" data-toggle="tooltip" title="Mark Activated"><i class="fa-solid fa-play"></i></a>';
			}else{
				renderData += '<a href="#" class="btn btn-xs p-1 mx-1 text-muted disabled"><i class="fa-solid fa-play"></i></a>';
			}
		}
		if (data.anycanRemove) {
			if(data.removeSub == 1){
				renderData += '<a href="#" class="btn btn-xs text-danger p-1 mx-1 mb-1" onclick="removeMemberSub('+data.subscriberID+','+data.memberID+');return false;" data-toggle="tooltip" title="Remove Subscription"><i class="fa-solid fa-minus-circle"></i></a>';
			}else{
				renderData += '<a href="#" class="btn btn-xs p-1 mx-1 text-muted disabled"><i class="fa-solid fa-minus-circle"></i></a>';
			}
		}
		renderData += '</div>';
	}
	return type === 'display' ? renderData : data;
}
function reloadSubsTable(hideModal){
	subsListTable.draw();
	if (hideModal) MCModalUtils.hideModal();
}
function resetAndReloadSubsTable() {
	resetAllRecordSelections(false);
	reloadSubsTable();
}
function doCheckAllSubs(chk) {
	arrUnchkedSubs = [];
	arrChkedSubs = [];
	checkAllSubs = chk ? 1 : 0;
	if (chk) {
		$("#masterCheckBox").val(1);
		$(".subCheckbox:checkbox").prop('checked', true);
	} else {
		$("#masterCheckBox").val(0);
		$(".subCheckbox:checkbox").prop('checked', false);
	}
	checkSubEntry();
}
function onCheckSubEntry(thisObj) {
	var subscriberID = $(thisObj).val();
	if ($(thisObj).is(':checked')) {
		if(arrUnchkedSubs.includes(subscriberID)){
			arrUnchkedSubs = $.grep(arrUnchkedSubs, function(value) {
				return value != subscriberID;
			});
		}		
		if(!arrChkedSubs.includes(subscriberID)){
			arrChkedSubs.push(subscriberID);
		}
	} else{
		if(arrChkedSubs.includes(subscriberID)){
			arrChkedSubs = $.grep(arrChkedSubs, function(value) {
				return value != subscriberID;
			});
		}
		if(!arrUnchkedSubs.includes(subscriberID)){
			arrUnchkedSubs.push(subscriberID);
		}
	}
	checkSubEntry();
}
function setSelectedSubCountDisplay(c){
	$('#selSubCountDisp').data('selcount', c);
	if(totalSubsCount != 0){
		$('#selSubCountDisp').html('<span><b>' + totalSubsCount + '</b> ' + (totalSubsCount == 1 ? "subscription tree" : "subscription trees") + ' found&nbsp;/&nbsp;</span><span><b>' + c + '</b> ' + (c == 1 ? "subscription tree" : "subscription trees") + ' selected</span>').show();
	}else{
		$('#selSubCountDisp').html('');
	}
}
function checkSubEntry(){
	$.each(arrUnchkedSubs, function( index, value ) {
		$(".subCheckbox:checkbox[value="+value+"]").prop('checked', false);
	});
	var displayCount = 0;
	if ($('#masterCheckBox').is(':checked')) {
		displayCount = totalSubsCount - arrUnchkedSubs.length;
	} else {
		var selectedIds = arrChkedSubs.join(",");
		displayCount = selectedIds.length ? selectedIds.split(',').length : 0;
	}
	setSelectedSubCountDisplay(displayCount);

	document.forms['frmFilter'].fChkAll.value = checkAllSubs;
	document.forms['frmFilter'].fChkedSubs.value = arrChkedSubs.join(',');
	document.forms['frmFilter'].fUnchkedSubs.value = arrUnchkedSubs.join(',');
}

// sub datatable actions
function assocSubCC(sid,mid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		title: 'Associate Pay Method to Subscription',
		contenturl: mcma_link_subassoc+'&sid='+sid+'&mid='+mid,
		iframe: true
	});
}
function closeAssocCC() {
	reloadSubsTable(1);
}
function generatePaperStatement(sid,mid,act) {
	let title = '<span id="topModalTitle">Statement Options</span>';
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: title,
		iframe: true,
		contenturl: mcma_link_generatePaperStatement+'&mid='+mid+'&fChkedSubs='+sid+'&act='+act,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto d-none',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnContinue").click',
			extrabuttonlabel: 'Continue',
		}
	});
}
function editMemberSub(sid,mid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'xl',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Edit Subscription',
		iframe: true,
		contenturl: mcma_link_sub+'&mid='+mid+'&sid='+sid
	});
}
function markInactiveSub(sid,mid) {
	var inactivateResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') reloadSubsTable(); 
		else alert('Unable to inactivate subscription.');
	};

	if (confirm('Marking this subscription inactive will also inactivate any addon subscriptions that belong to it.\r\nAre you sure you wish to do this?')) {
		var objParams = { memberID:mid, subscriberID:sid };
		TS_AJX('ADMSUBS','inactivateMemberSubscription',objParams,inactivateResult,inactivateResult,20000,inactivateResult);
	}
}
function markActiveSub(sid,mid)	{
	var activateResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') reloadSubsTable();
		else alert('Unable to activate subscription.');
	};

	if (confirm('Marking this subscription active will also activate any addon subscriptions that belong to it.\r\nAre you sure you wish to do this?')) {
		var objParams = { memberID:mid, subscriberID:sid };
		TS_AJX('ADMSUBS','activateMemberSubscription',objParams,activateResult,activateResult,20000,activateResult);
	}
}
function renewMemberSub(sid,mid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'md',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Confirm Renewal Generation',
		iframe: true,
		contenturl: mcma_link_confirmrenew+'&sid='+sid+'&mid='+mid+'&chkAll=0&fSubscribers='+ sid +'&fNotSubscribers=""' ,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSubmit").click',
			extrabuttonlabel: 'Generate Renewals',
		}
	});
}
function acceptSubscription(sid,mid) {
	MCModalUtils.showModal({
		verticallycentered: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false,
			},
			size: 'lg',
			title: 'Should Subscribers Receive Email Notifications?',
			iframe: true,
			contenturl: linkAcceptSubscription+'&mid='+mid+'&sid='+sid,
			strmodalbody:{
				classlist:'mcModalBodyCustom'
			},
			strmodalfooter: {
				classlist: '',
				showclose: false,
				buttons: [
					{
						class:"btn btn-sm btn-primary btnSubAction",
						clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSend").click',
						label: 'Send Emails', 
						name: 'btnSend',
						id: 'btnSend'
					},
					{
						class: "btn btn-sm btn-primary btnSubAction",
						clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSkip").click',
						label: 'Skip Emails', 
						name: 'btnSkip',
						id: 'btnSkip'
					}
				]
			}
	});
}
function closeAddPayment(po) { 
	reloadSubsTable();
	if (Number(mcma_hasrights_allocpmt) == 1) mca_allocIndivPayment(po);
	else MCModalUtils.hideModal();
}
function allocIndivPayment(po) {
	mca_allocIndivPayment(po);
}
function closeAllocPayment() { 
	reloadSubsTable();
	MCModalUtils.hideModal();
}
function emailOfferSub(sid,mid) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Select Email Template',
		iframe: true,
		contenturl: mcma_link_startgenoffers+'&mid='+mid+'&subscriberOnly='+sid,
		strmodalfooter: {
			classlist: 'd-flex justify-content-between',
			showclose: false,
			buttons: [
				{
					class:"btn btn-sm btn-secondary d-none",
					clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnChangeTemplate").click',
					label: 'Change Template', 
					name: 'btnChangeTemplate',
					id: 'btnChangeTemplate'
				},
				{
					class:"btn btn-sm btn-secondary d-none mr-auto",
					clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnTestTemplate").click',
					label: 'Send Test E-mail', 
					name: 'btnTestTemplate',
					id: 'btnTestTemplate'
				},
				{
					class:"btn btn-sm btn-primary ml-auto",
					clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnSelectTemplate").click',
					label: 'Select Template', 
					name: 'btnSelectTemplate',
					id: 'btnSelectTemplate'
				},
				{
					class: "btn btn-primary btn-sm ml-auto d-none",
					clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnProcessTemplate").click',
					label: 'Send Emails', 
					name: 'btnProcessTemplate',
					id: 'btnProcessTemplate'
				}
			]
		}
	});
}
function cleanupInvoices(sid,mid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Cleanup Invoices For Subscription',
		iframe: true,
		contenturl: mcma_link_cleanupinv+'&mid='+mid+'&sid='+sid,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnCleanupSub").click',
			extrabuttonlabel: 'Continue',
		}
	});
}
function markBilledSub(sid,mid)	{
	var markBilledResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') reloadSubsTable();
		else alert('Unable to Mark As Billed.');
	};
	if (confirm('Are you sure you want to update this subscription to Billed?')) {
		var objParams = { memberID:mid, subscriberID:sid };
		TS_AJX('ADMSUBS','markBilledMemberSubscription',objParams,markBilledResult,markBilledResult,20000,markBilledResult);
	}
}
function markExpiredSub(sid,mid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Expire Subscription',
		iframe: true,
		contenturl: mcma_link_expiresub+'&mid='+mid+'&sid='+sid,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnExpireSub").click',
			extrabuttonlabel: 'Expire Subscription',
		}
	});
}
function markOverrideActivationSub(sid) {
	var activateResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') reloadMemberSubs();
		else alert('Unable to activate subscription.');
	};

	if (confirm('Activating this subscription cannot be undone.\r\nAre you sure you wish to do this?')) {
		var objParams = { subscriberID:sid };
		TS_AJX('ADMSUBS','overrideActivationMemberSubscription',objParams,activateResult,activateResult,20000,activateResult);
	}
}
function removeMemberSub(sid,mid) {
	MCModalUtils.showModal({
		isslideout: true,
		size: 'lg',
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		title: 'Remove Subscription',
		iframe: true,
		contenturl: mcma_link_removesub+'&mid='+mid+'&sid='+sid,
		strmodalfooter : {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#btnRemoveSub").click',
			extrabuttonlabel: 'Remove Subscription',
			extrabuttoniconclass: 'fa-solid fa-trash-can'
		}
	});
}
function subdoc_download(u) {
	top.location.href='/tsdd/' + u;
}
function reloadMemberSubs() { 
	subsListTable.draw();
}
function reloadSubsTableAndHideModal() {
	reloadSubsTable(1);
}
function showRefundPaymentOnSubRemoveSuccess(mid){
	$('#MCModal').on('hidden.bs.modal', function() { refundPayment(mid); });
	MCModalUtils.hideModal();
}
function refundPayment(mid,ptid) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Issue Refund',
		iframe: true,
		contenturl: mcma_link_refpayment+'&mid=' + mid + (typeof ptid == 'undefined' ? '' : '&ptid=' + ptid)
	});
}
function closeRefundPayment() { reloadSubsTable(1); }