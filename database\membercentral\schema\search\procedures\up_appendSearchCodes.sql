ALTER PROC dbo.up_appendSearchCodes
@documentID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @appendString varchar(max);

	--- Verify append has not been performed.
	select documentID from search.dbo.depodocuments where documentID = @documentID and searchtext like '%*tsdocbeginheader*%';
	IF @@ROWCOUNT = 0 BEGIN
		SELECT @appendString =
		'  *%*%*%*%*%*%*tsdocbeginheader*%*%*%*%*%*%* ------------------------------------------------------------------------ For TrialSmith Office use Only ------------------------------------------------------------------------ ' +
		convert(varchar(20), d.DocumentID) + ' ' + isnull(d.ExpertName,'') + ' ' + isnull(d.Style, '') + ' ' +  isnull(d.DefenseLawyer, '') + ' ' + 
		convert(varchar(20), d.DocumentDate) + ' ' + isnull(d.Notes,'') + ' ' + isnull(d.State, '') + ' ' + isnull(d.Category,'') + ' ' + 
		isnull(d.Summary, '') + ' ' + isnull(d.PlaintiffLawyer, '') + ' ' +	isnull(d.jurisdiction, '') + ' - ' +  isnull(s.name, '') + ' ' + 
		isnull(d.Disposition, '') + 'tsdoc' + convert(varchar(20), d.DocumentID) + 'xxx tsgroupid' + convert(varchar(20), d.GroupID) + 
		'xxx - ' + isnull(g.Description, '') + ' tsdoctypeid' + convert(varchar(20), d.DocumentTypeID) + 'xxx - ' + isnull(t.Description, '') + 
		' tscasetypeid' + convert(varchar(20), d.CaseTypeID) + 'xxx - ' + isnull(ct.Description, '') +
		case when t.dspecial = 1 THEN ' tsspecialcollectionxxx ' else '' end +
		case when t.dcourtdoc = 1 THEN ' tscourtdocxxx ' else '' end +
		case when d.hot = 'YES' OR d.hot = 'Y' THEN ' tshotdocxxx' else '' end+
		case when ds.statusName = 'Disabled' THEN ' tsdisableddocxxx ' else '' end
		FROM trialsmith.dbo.depoDocuments d 
		INNER JOIN trialsmith.dbo.depoDocumentStatusHistory as dsh on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
		inner join trialsmith.dbo.depoDocumentStatuses as ds on ds.statusID = dsh.statusID
		INNER JOIN trialsmith.dbo.depoGroups g ON d.GroupID = g.GroupID 
		INNER JOIN trialsmith.dbo.depoDocumentTypes t ON d.DocumentTypeID = t.TypeID 
		LEFT OUTER JOIN trialsmith.dbo.depoCaseTypes ct ON d.CaseTypeID = ct.CaseTypeID 
		LEFT OUTER join trialsmith.dbo.states s on d.jurisdiction = s.code 
		where d.documentid = @documentID;

		-- Append string 
		update search.dbo.depodocuments set searchtext = CONCAT(searchtext,  @appendString) where documentID = @documentID;
	END
		
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
