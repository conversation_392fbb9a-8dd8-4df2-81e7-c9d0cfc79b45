<cfcomponent extends="model.admin.admin" output="no">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="xml" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// Load Objects for page -------------------------------------------------------------------- ::
			this.objSubscriptions = CreateObject("component","subscriptions");
			this.objSubscriptionsReg = CreateObject("component","subscriptionReg");

			// RUN ASSIGNED METHOD 	--------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return application.objCommon.minText(local.methodToRun(arguments.event));
		</cfscript>
	</cffunction>
	
	<cffunction name="subscriptionTypesXML" access="public" output="false" returntype="xml">
		<cfargument name="Event" type="any">
	
		<cfset var local = structNew()>
		
		<cfset local.strAvailableSubs = this.objSubscriptionsReg.showSubscriptions(siteid=arguments.event.getValue('mc_siteinfo.siteid'), memberID=arguments.event.getValue('mid', '0'), filterTypeID=arguments.event.getValue('filterTypeID',0))>
		
		<cfsavecontent variable="local.returnXML">
			<cfoutput>
				<rows total_count="#local.strAvailableSubs.qrySubscriptionTypes.recordcount#">  
					<head>
						<column align="left" sort="str" type="ro" width="*">Subscription Type</column>
						<beforeInit>
							<call command="attachEvent"><param>onXLE</param><param>doTypeGridLoaded</param></call>
							<call command="attachEvent"><param>onRowSelect</param><param>doSelectType</param></call>
						</beforeInit>
						<afterInit>
							<call command="setSortImgState"><param>true</param><param>0</param><param>asc</param></call>
						</afterInit>
						<settings>
							<colwidth>px</colwidth>
						</settings>
					</head>
					<cfloop query="local.strAvailableSubs.qrySubscriptionTypes">
						<row id='#local.strAvailableSubs.qrySubscriptionTypes.typeID#'>
							<cell>#xmlformat(local.strAvailableSubs.qrySubscriptionTypes.typeName)#</cell>
						</row>
					</cfloop>
				</rows>
			</cfoutput>
		</cfsavecontent>
		<cfcontent reset="yes" type="text/xml; charset=UTF-8">
		<cfreturn application.objCommon.minText(local.returnXML)>
	</cffunction>
	
	<cffunction name="subscriptionsXML" access="public" output="false" returntype="xml">
		<cfargument name="Event" type="any">
	
		<cfset var local = structNew()>

		<cfset local.strAvailableSubs = this.objSubscriptionsReg.showSubscriptions(siteid=arguments.event.getValue('mc_siteinfo.siteid'), memberID=arguments.event.getValue('mid', '0'), filterTypeID=arguments.event.getValue('filterTypeID',0))>

		<cfsavecontent variable="local.returnXML">
			<cfoutput>
				<rows total_count="#local.strAvailableSubs.qrySubscriptions.recordcount#">  
					<head>
						<column align="left" sort="str" type="ro" width="*">Subscription</column>
						<beforeInit>
							<call command="attachEvent"><param>onRowSelect</param><param>doSelectSub</param></call>
						</beforeInit>
						<afterInit>
							<call command="setSortImgState"><param>true</param><param>0</param><param>asc</param></call>
						</afterInit>
						<settings>
							<colwidth>px</colwidth>
						</settings>
					</head>
					<cfloop query="local.strAvailableSubs.qrySubscriptions">
						<row id='#local.strAvailableSubs.qrySubscriptions.subscriptionID#'>
							<cell>#xmlformat(local.strAvailableSubs.qrySubscriptions.subscriptionName)#</cell>
						</row>
					</cfloop>
				</rows>
			</cfoutput>
		</cfsavecontent>
		<cfcontent reset="yes" type="text/xml; charset=UTF-8">
		<cfreturn application.objCommon.minText(local.returnXML)>
	</cffunction>

	<cffunction name="subscriptionTypesAllXML" access="public" output="false" returntype="xml">
		<cfargument name="Event" type="any">
	
		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriptionTypes">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select st.typeID, st.typeName, count(subs.subscriptionID) as numSubs, st.uid
			from dbo.sub_Types st 
			left outer join dbo.sub_subscriptions as subs on subs.typeID = st.typeID and subs.status = 'A'
			where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			and st.status = 'A'
			group by st.typeID, st.typeName, st.uid
			order by st.typeName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfsavecontent variable="local.returnXML">
			<cfoutput>
				<rows total_count="#local.qrySubscriptionTypes.recordcount#">  
					<head>
						<column align="left" sort="str" type="ro" width="*">Subscription Type</column>
						<column align="center" sort="na" type="img" width="25">Tools</column>
						<column align="center" sort="na" type="img" width="25">##cspan</column>
						<beforeInit>
							<call command="attachEvent"><param>onRowSelect</param><param>doTypeOnRowSelect</param></call>
						</beforeInit>
						<afterInit>
							<call command="setSortImgState"><param>true</param><param>0</param><param>asc</param></call>
						</afterInit>
						<settings>
							<colwidth>px</colwidth>
						</settings>
					</head>
					<cfloop query="local.qrySubscriptionTypes">
						<row id='#local.qrySubscriptionTypes.typeID#'>
							<cell>#xmlformat(local.qrySubscriptionTypes.typeName)#</cell>
							<cell>/assets/common/images/grid/pencil.png^Edit Type^javascript:editType(#local.qrySubscriptionTypes.typeID#)^_self</cell>
							<cfif local.qrySubscriptionTypes.numSubs eq 0>
								<cell>/assets/common/images/grid/delete.png^Delete Type^javascript:deleteType("#local.qrySubscriptionTypes.UID#")^_self</cell>
							<cfelse>
								<cell>/assets/common/images/spacer.gif^^</cell>
							</cfif>
						</row>
					</cfloop>
				</rows>
			</cfoutput>
		</cfsavecontent>

		<cfcontent reset="yes" type="text/xml; charset=UTF-8">
		<cfreturn application.objCommon.minText(local.returnXML)>
	</cffunction>
	
	<cffunction name="subscriptionsAllXML" access="public" output="false" returntype="xml">
		<cfargument name="Event" type="any">
	
		<cfset var local = structNew()>

		<cfset local.typeID = arguments.event.getValue('typeID', 0)>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriptions">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select subs.subscriptionID, subs.subscriptionName, subs.soldSeparately, subs.status,
				case when NOT EXISTS (
					SELECT top 1 s.subscriptionID 
					FROM dbo.sub_subscribers s
					inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode <> 'D'
					WHERE s.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">
					AND s.subscriptionID = subs.subscriptionID
				) then 1 else 0 end as canDelete
			from dbo.sub_subscriptions as subs
			inner join dbo.sub_Types as st on st.typeID = subs.typeID 
				and st.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
				and st.typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.typeID#">
			where subs.status in ('A','I')
			order by subs.subscriptionName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfsavecontent variable="local.returnXML">
			<cfoutput>
				<rows total_count="#local.qrySubscriptions.recordcount#">  
					<head>
						<column align="left" sort="str" type="ro" width="*">Subscription</column>
						<column align="center" sort="na" type="img" width="25">Tools</column>
						<column align="center" sort="na" type="img" width="25">##cspan</column>
						<column align="center" sort="na" type="img" width="25">##cspan</column>
						<column align="center" sort="na" type="img" width="25">##cspan</column>
						<afterInit>
							<call command="setSortImgState"><param>true</param><param>0</param><param>asc</param></call>
						</afterInit>
						<settings>
							<colwidth>px</colwidth>
						</settings>
					</head>
					<cfloop query="local.qrySubscriptions">
						<row id='#local.qrySubscriptions.subscriptionID#'<cfif local.qrySubscriptions.status eq 'I'> style="background-color:##FFFF99; border-top:1px solid ##e77979; border-bottom:1px solid ##e77979;"</cfif>>
							<cell>#xmlformat(local.qrySubscriptions.subscriptionName)#</cell>
							<cfif local.qrySubscriptions.soldSeparately>
								<cell>/assets/common/images/grid/accept.png^^^</cell>
							<cfelse>
								<cell>/assets/common/images/grid/accept_grey.png^^^</cell>
							</cfif>
							<cell>/assets/common/images/grid/pencil.png^Edit Subscription^javascript:editSub(#local.qrySubscriptions.subscriptionID#)^_self</cell>
							<cell>/assets/common/images/grid/event_copy.png^Copy Subscription^javascript:copySub(#local.qrySubscriptions.subscriptionID#)^_self</cell>
							<cfif local.qrySubscriptions.canDelete>
								<cell>/assets/common/images/grid/delete.png^Delete Subscription^javascript:chkDeleteSub(#local.qrySubscriptions.subscriptionID#)^_self</cell>
							<cfelse>
								<cell>/assets/common/images/grid/delete_dim.png^Cannot delete a subscription with non-deleted subscribers^^</cell>
							</cfif>
						</row>
					</cfloop>
				</rows>
			</cfoutput>
		</cfsavecontent>

		<cfcontent reset="yes" type="text/xml; charset=UTF-8">
		<cfreturn application.objCommon.minText(local.returnXML)>
	</cffunction>

	<cffunction name="membersForSubXML" access="public" output="false" returntype="xml">
		<cfargument name="Event" type="any">
	
		<cfscript>
			var local = structNew();
			// GRID SETTINGS ---------------------------------------------------------------------------- ::
			arguments.event.setValue('direct',arguments.event.getValue('direct3','asc'));
			arguments.event.setValue('lh',int(val(arguments.event.getValue('lh3',0))));
			arguments.event.setValue('orderby',int(val(arguments.event.getValue('orderby3',0))));
			
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('posStart',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('count',50))));

			local.subID = arguments.event.getValue('subID', 0);
		</cfscript>
		
		<cfif arguments.event.getValue('posStart') is 0>
			<cfquery name="local.qryMemCount" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select count(m.memberid) as memCount 
				from dbo.sub_subscribers s 
				inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
				inner join dbo.sub_types t on t.typeID = subs.typeID 
					and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
				inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'A'
				inner join dbo.ams_members as m on m.memberid = s.memberid and m.memberid = m.activememberID and m.status <> 'D'
				where s.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subID#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfset local.totalCount = local.qryMemCount.memCount>
		<cfelse>
			<cfset local.totalCount = "">
		</cfif>
		
		<!--- handle ordering --->
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"m.lastname + ', ' + m.firstname")>
		<cfset arrayAppend(local.arrCols,"m.lastname + ', ' + m.firstname")>
		<cfif arguments.event.getValue('direct') eq "DES"><cfset arguments.event.setValue('direct','DESC')></cfif>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>
		
		<!--- get members (50 at a time) --->
		<cfquery name="local.qryMembers" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select memberID, status, memberNumber, Member
			from (
				SELECT mActive.memberid, m.status, 
					m.lastname + ', ' + m.firstname as Member, m.memberNumber, 
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.event.getValue('direct')#) as row 
				from dbo.sub_subscribers s 
				inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
				inner join dbo.sub_types t on t.typeID = subs.typeID 
					and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
				inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'A'
				inner join dbo.ams_members as m on m.memberid = s.memberid 
				inner join dbo.ams_members as mActive on mActive.memberid = m.activememberID and mActive.status <> 'D'
				where s.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subID#">
			) as tmp
			where row > #arguments.event.getValue('posStart')# and row <= #arguments.event.getValue('posStart') + arguments.event.getValue('count')#
			order by row;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfsavecontent variable="local.returnXML">
			<cfoutput>
			<rows total_count="#local.totalCount#" pos="#arguments.event.getValue('posStart')#">
			<cfif arguments.event.getValue('lh') is 1 and arguments.event.getValue('posStart') is 0>
				<head>
					<column align="center" sort="na" type="img" width="35"> </column>
					<column align="left" sort="server" type="link" width="*">Member</column>
					<afterInit>
						<call command="enableSmartRendering"><param>true</param></call>
						<call command="setSortImgState"><param>true</param><param>1</param><param>asc</param></call>
					</afterInit>
					<settings>
						<colwidth>px</colwidth>
					</settings>
				</head>
			</cfif>
			<cfloop query="local.qryMembers"> 
				<row id="#local.qryMembers.memberID#" <cfif local.qryMembers.status eq "I">style="background-color:##e8aeae; border-top:1px solid ##e77979;border-bottom:1px solid ##e77979;"</cfif>>
					<cell type="ro" />
					<cell title="#xmlFormat(local.qryMembers.member)#">#xmlFormat(local.qryMembers.member)# (#xmlFormat(local.qryMembers.membernumber)#) <cfif local.qryMembers.status eq "I">(Inactive)</cfif>^javascript:viewMember(#local.qryMembers.memberID#)^_self</cell>
				</row>
			</cfloop>
			</rows>
			</cfoutput>
		</cfsavecontent>
		<cfcontent reset="yes" type="text/xml; charset=UTF-8">
		<cfreturn application.objCommon.minText(local.returnXML)>
	</cffunction>
	
	<cffunction name="showSubTreeXML" access="public" output="false" returntype="xml">
		<cfargument name="Event" type="any">
	
		<cfset var local = structNew()>
		<cfset arguments.event.setValue('lh',int(val(arguments.event.getValue('lh6',0))))>
		<cfset arguments.event.paramValue('grid',0)>
		<cfset local.statusFilter = arguments.event.getValue('sFil','')>
		<cfset local.paymentStatusFilter = arguments.event.getValue('spFil','')>
		
		<cfset local.memberID = arguments.event.getValue('mid', 0)>
		<cfset local.subscriberID = arguments.event.getValue('sid', 0)>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubs">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select subscriberID, subscriptionID, typeName, subscriptionName, [status], statusName,
				paymentStatus, paymentStatusName, subStartDate, subEndDate,
				parentSubscriberID, rootSubscriberID, thePath,ratename, rootSubscriberEndDate,
				frequencyName
			from (
				select s.subscriberID, s.subscriptionID, t.typeName, sub.subscriptionName, st.statusCode as status, st.statusName,
						pst.statusCode as paymentStatus, pst.statusName as paymentStatusName, s.subStartDate, s.subEndDate,
						s.parentSubscriberID, s.rootSubscriberID, convert(varchar, s.rootSubscriberID) + '.' + s.subscriberPath as thePath, r.ratename,
			 			sRoot.subEndDate as rootSubscriberEndDate,
			 			case 
			 			when rst.statusCode = 'A' then 1 
			 			when rst.statusCode = 'E' then 2
			 			when rst.statusCode = 'I' then 3 
			 			when rst.statusCode = 'D' then 4 
			 			else 5 
			 			end as statusSort, f.frequencyName               	
				from dbo.sub_subscribers s
				inner join dbo.sub_rateFrequencies rf on rf.rfid = s.rfid
				inner join dbo.sub_frequencies f on f.frequencyID = rf.frequencyID					
				inner join dbo.sub_rates r on r.rateID = rf.rateID
				inner join dbo.sub_subscriptions sub on sub.subscriptionID = s.subscriptionID
				inner join dbo.sub_types t on t.typeID = sub.typeID and t.siteID = #arguments.event.getValue('mc_siteinfo.siteid')#
				inner join dbo.sub_statuses st on st.statusID = s.statusID
				<cfif local.statusFilter eq 'AIE'>
					and st.statusCode in ('A','I','E')
				<cfelseif local.statusFilter eq 'AP'>
					and st.statusCode in ('A','P')
				<cfelseif (local.statusFilter neq '')>
					and st.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.statusFilter#">
				</cfif>
				inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
				<cfif (local.paymentStatusFilter neq '')>
					and pst.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.paymentStatusFilter#">
				</cfif>
				inner join sub_subscribers sRoot on sRoot.subscriberID = s.rootSubscriberID
				inner join dbo.sub_statuses rst on rst.statusID = sRoot.statusID
				where s.memberID in (select mAll.memberID 
										from dbo.ams_members m
										inner join dbo.ams_members mAll on mAll.activeMemberID = m.activeMemberID
										where m.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">)
				and s.rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subscriberID#">
			) x
			order by statusSort, rootSubscriberEndDate DESC, thePath;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.subscriptionTree = structNew()>
		<cfset local.arrCount = 0>
		<cfset local.currRootID = 0>

		<cfloop query="local.qrySubs">
			<cfset local.currentBranch = local.subscriptionTree>
			<cfset local.subscriptionDepth = listlen(local.qrySubs.thePath,".")>
			<cfset local.currentDepth = 0>
			
			<cfif local.currRootID neq local.qrySubs.rootSubscriberID>
				<cfset local.currRootID = local.qrySubs.rootSubscriberID>
				<cfset local.arrCount = local.arrCount + 1>
				<cfset local.currentBranch[NumberFormat(local.arrCount, "0009")] = StructNew()>
			</cfif>
			<cfset local.currentBranch = local.currentBranch[NumberFormat(local.arrCount, "0009")]>
			<cfset local.subscriberPathArray = listToArray(local.qrySubs.thePath,".")>

				<cfloop from="1" to="#arrayLen(local.subscriberPathArray)#" index="local.currentSubscription">
					<cfset local.currentDepth = local.currentDepth + 1>
					<cfif local.currentDepth is not 1>
						<cfif not structKeyExists(local.currentBranch,local.subscriberPathArray[local.currentSubscription])>
							<cfset local.currentBranch[local.subscriberPathArray[local.currentSubscription]] = StructNew()>
						<cfelseif local.currentSubscription eq arrayLen(local.subscriberPathArray)>
							<!--- 
								if this is the last segment of the subscriberpath, then it's trying to create a new subscription.
								Expired/Deleted statuses make it possible for the same subscription to exist more than once in a tree
								if this occurs we rename the branch of the previously existing entry so that we can build the current entry
							 --->
							<cfset local.newNameForPreexistingBranch = "-" & local.subscriberPathArray[local.currentSubscription] & "-" & local.currentBranch[local.subscriberPathArray[local.currentSubscription]].subscriberID>
							<cfset local.currentBranch[local.newNameForPreexistingBranch] = duplicate(local.currentBranch[local.subscriberPathArray[local.currentSubscription]])>
							<cfset local.currentBranch[local.subscriberPathArray[local.currentSubscription]] = StructNew()>
						</cfif>
						<cfset local.currentBranch = local.currentBranch[local.subscriberPathArray[local.currentSubscription]]>
					</cfif> 
					<cfif local.currentDepth is local.subscriptionDepth>
						<cfif local.qrySubs.subscriberID eq local.qrySubs.rootSubscriberID>
							<cfquery dbtype="query" name="local.qrySubscribers">
								select subscriberID
								from [local].qrySubs
								where rootSubscriberID = #local.qrySubs.rootSubscriberID#
							</cfquery>
						</cfif>

						<cfset local.currentBranch.subscriberID = local.qrySubs.subscriberID />
						<cfset local.currentBranch.parentSubscriberID = local.qrySubs.parentSubscriberID />
						<cfset local.currentBranch.rootSubscriberID = local.qrySubs.rootSubscriberID />
						<cfset local.currentBranch.subscriptionID = local.qrySubs.subscriptionID />
						<cfset local.currentBranch.subscriptionName = local.qrySubs.subscriptionName />
						<cfset local.currentBranch.ratename = local.qrySubs.ratename />
						<cfset local.currentBranch.frequencyName = local.qrySubs.frequencyName />
						<cfset local.currentBranch.typeName = local.qrySubs.typeName />
						<cfset local.currentBranch.status = local.qrySubs.status />
						<cfset local.currentBranch.statusName = local.qrySubs.statusName />
						<cfset local.currentBranch.paymentStatus = local.qrySubs.paymentStatus />
						<cfset local.currentBranch.subStartDate = len(local.qrySubs.subStartDate) ? local.qrySubs.subStartDate : "" />
						<cfset local.currentBranch.subEndDate = len(local.qrySubs.subEndDate) ? local.qrySubs.subEndDate : "" />
						<cfset local.currentBranch.thePath = local.qrySubs.thePath />
					</cfif>
				</cfloop>
		</cfloop>

		<cfsavecontent variable="local.returnXML">
			<cfoutput>
				<rows parent="0">
				<cfif arguments.event.getValue('lh') is 1>
					<head>
						<column align="left" sort="na" type="tree" width="*">Subscription Name</column>
						<column align="left" sort="na" type="ro" width="70">Start Date</column>
						<column align="left" sort="na" type="ro" width="70">End Date</column>
						<settings>
							<colwidth>px</colwidth>
						</settings>
					</head>
					</cfif>
					<cfset local.keyList = listsort(StructKeyList(local.subscriptionTree),"textnocase")>
					<cfloop index="local.currentKey" list="#local.keyList#">
						<cfif isStruct(local.subscriptionTree[local.currentKey])>
							#createMemberSubTreeXML(local.subscriptionTree[local.currentKey], 0, local.memberID, arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'))# 
						</cfif>
					</cfloop>
					
				</rows>
			</cfoutput>
		</cfsavecontent>
		<cfif arguments.event.getValue('grid') is 1><cfcontent reset="yes" type="text/xml; charset=UTF-8"></cfif>
		<cfreturn application.objCommon.minText(local.returnXML)>
	</cffunction>

	<cffunction name="createMemberSubTreeXML" access="private" returntype="string">
		<cfargument name="subscriptionStruct" type="struct" required="true">
		<cfargument name="numLevel" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="timeZoneID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin')>

 		<cfsavecontent variable="local.resultXML">
			<cfoutput>
			<cfif StructKeyExists(arguments.subscriptionStruct,"subscriberID")>
				<cfset local.subNameHover = "#arguments.subscriptionStruct.subscriptionName# (#xmlFormat(arguments.subscriptionStruct.ratename)#)  [#arguments.subscriptionStruct.typeName#] - #arguments.subscriptionStruct.frequencyName#">
				<cfset local.rateStartDateMod = len(arguments.subscriptionStruct.subStartDate) ? arguments.subscriptionStruct.subStartDate : "">
				<cfset local.rateEndDateMod = len(arguments.subscriptionStruct.subEndDate) ? arguments.subscriptionStruct.subEndDate : "">

				<row id="#arguments.subscriptionStruct.subscriberID#" 
							<cfif arguments.subscriptionStruct.status eq 'D'>style="background-color:##e8aeae; border-top:1px solid ##e77979;border-bottom:1px solid ##e77979;"
							<cfelseif (arguments.subscriptionStruct.paymentStatus eq 'N') AND ((arguments.subscriptionStruct.status eq 'A') OR (arguments.subscriptionStruct.status eq 'P'))>style="background-color:##ffff00; border-top:1px solid ##ffcc00;border-bottom:1px solid ##ffcc00;" open="1"
							<cfelse>open="1"</cfif>>
					<cell image="event.png" title="#xmlFormat(local.subNameHover)#"> #xmlFormat(arguments.subscriptionStruct.subscriptionName)# (#xmlFormat(arguments.subscriptionStruct.ratename)#)</cell>
					<cell> #dateformat(local.rateStartDateMod,'m/d/yy')#</cell>
					<cell> #dateformat(local.rateEndDateMod,'m/d/yy')# </cell>

				<cfset local.keyList = listsort(StructKeyList(arguments.subscriptionStruct),"textnocase")>
				<cfloop index="local.currentKey" list="#local.keyList#">
					<cfif isStruct(arguments.subscriptionStruct[local.currentKey])>
						#createMemberSubTreeXML(arguments.subscriptionStruct[local.currentKey], (arguments.numLevel + 1), arguments.memberID, arguments.timeZoneID)# 
					</cfif>
				</cfloop>
				</row>
			<cfelse>
				<cfset local.keyList = listsort(StructKeyList(arguments.subscriptionStruct),"textnocase")>
				<cfloop index="local.currentKey" list="#local.keyList#">
					<cfif isStruct(arguments.subscriptionStruct[local.currentKey])>
						#createMemberSubTreeXML(arguments.subscriptionStruct[local.currentKey], (arguments.numLevel + 1), arguments.memberID, arguments.timeZoneID)# 
					</cfif>
				</cfloop>
			</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.resultXML>
 	</cffunction>

	<cffunction name="allSubscribersXML" access="public" output="false" returntype="xml">
		<cfargument name="Event" type="any">
	
		<cfset var local = structNew()>
		<cfset arguments.event.setValue('lh',int(val(arguments.event.getValue('lh',0))))>
		<cfsetting requesttimeout="300">
		<cfset local.subStatus = arguments.event.getValue('sID','0')>
		<cfset local.subStartDate = arguments.event.getValue('dtTf','')>
		<cfset local.subEndDate = arguments.event.getValue('dtTt','')>
		<cfset local.subType = arguments.event.getValue('stID','0')>
		<cfset local.subID = arguments.event.getValue('subID','0')>
		<cfset local.rateID = arguments.event.getValue('rateID','0')>
		
		<cfscript>
			if (len(local.subEndDate)) local.subEndDate = dateadd("l",-3,dateadd("d",1,DATEADD("d",DATEDIFF("d",0,local.subEndDate), 0)));
		</cfscript>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRFIDs">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select rf.rfid
			from dbo.sub_rateFrequencies rf
			inner join dbo.sub_rates r on r.rateID = rf.rateID and r.rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfset local.rfidList = valueList(local.qryRFIDs.rfid)>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryParentSubs" result="local.qryParentSubsResult">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select topParentSubscriberID
			from dbo.fn_getRecursiveSubscriptions(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">)
			where 1=1
			<cfif local.subStatus neq "0">
				AND status = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.subStatus#">
			</cfif>
			<cfif len(local.subStartDate) gt 0>
				AND subStartDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.subStartDate#">
			</cfif>
			<cfif len(local.subEndDate) gt 0>
				AND subEndDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.subEndDate#">
			</cfif>
			<cfif local.subType neq "0">
				AND typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subType#">
			</cfif>
			<cfif local.subID neq "0">
				AND subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subID#">
			</cfif>
			<cfif len(local.rfidList) gt 0>
				AND rfid in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rfidList#" list="yes">)
			</cfif>
			group by topParentSubscriberID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.parentSubList = valueList(local.qryParentSubs.topParentSubscriberID)>
		<cfif len(local.parentSubList) eq 0>
			<cfset local.parentSubList = "0">
		</cfif>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubs">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select subscriberID, memberID, memberName, subscriptionID, typeID, typeName, subscriptionName, status, subStartDate, subEndDate, graceEndDate,
				parentSubscriberID, thePath, thePathExpanded
			from dbo.fn_getRecursiveSubscriptionsByID(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">, <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.parentSubList#">)
			order by memberName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.subscriptionTree = structNew()>

		<cfset local.currMemberID = 0>
		<cfset local.currCount = 0>
		<cfset local.currSubCount = 0>
		<cfloop query="local.qrySubs">
			<cfset local.currentBranch = local.subscriptionTree>
			<cfset local.subscriptionDepth = listlen(local.qrySubs.thePath,".")>
			<cfset local.currentDepth = 0>
			
			<!---<cfif local.qrySubs.status neq 'D'>--->

				<cfif local.qrySubs.memberID neq local.currMemberID>
					<cfset local.currCount = local.currCount + 1>
					<cfset local.currMemberID = local.qrySubs.memberID>
					<cfset local.currSubCount = 0>

					<cfset local.currentBranch[local.currCount] = StructNew()>
					<cfset local.currentBranch = local.currentBranch[local.currCount]>

					<cfset local.currentBranch.subscriberID 	= 0 />
					<cfset local.currentBranch.parentSubscriberID 	= 0 />
					<cfset local.currentBranch.memberID 			= local.qrySubs.memberID />
					<cfset local.currentBranch.memberName 		= local.qrySubs.memberName />
					<cfset local.currentBranch.subscriptionID 			= 0 />
					<cfset local.currentBranch.subscriptionName 		= '' />
					<cfset local.currentBranch.typeName 		= '' />
					<cfset local.currentBranch.status 		= '' />
					<cfset local.currentBranch.subStartDate 		= '' />
					<cfset local.currentBranch.subEndDate 		= '' />
					<cfset local.currentBranch.graceEndDate 	= '' />
					<cfset local.currentBranch.thePath 					= '' />
					<cfset local.currentBranch.thePathExpanded 	= '' />
				<cfelse>
					<cfset local.currentBranch = local.currentBranch[local.currCount]>
				</cfif>
				<cfset local.subscriberPathArray = listToArray(local.qrySubs.thePath,".")>
				<cfloop from="1" to="#arrayLen(local.subscriberPathArray)#" index="local.currentSubscription">
					
					<cfset local.currentDepth = local.currentDepth + 1>
					<cfif local.currentDepth is not 1>
						<cfif not structKeyExists(local.currentBranch,local.subscriberPathArray[local.currentSubscription])>
							<cfset local.currentBranch[local.subscriberPathArray[local.currentSubscription]] = StructNew()>
						<cfelseif local.currentSubscription eq arrayLen(local.subscriberPathArray)>
							<!--- 
								if this is the last segment of the subscriberpath, then it's trying to create a new subscription.
								Expired/Deleted statuses make it possible for the same subscription to exist more than once in a tree
								if this occurs we rename the branch of the previously existing entry so that we can build the current entry
							 --->
							<cfset local.newNameForPreexistingBranch = "-" & local.subscriberPathArray[local.currentSubscription] & "-" & local.currentBranch[local.subscriberPathArray[local.currentSubscription]].subscriberID>
							<cfset local.currentBranch[local.newNameForPreexistingBranch] = duplicate(local.currentBranch[local.subscriberPathArray[local.currentSubscription]])>
							<cfset local.currentBranch[local.subscriberPathArray[local.currentSubscription]] = StructNew()>
						</cfif>
						<cfset local.currentBranch = local.currentBranch[local.subscriberPathArray[local.currentSubscription]]>
					</cfif> 
		
	 				<cfif local.currentDepth is local.subscriptionDepth>

						<cfscript>
							local.subStartDate = "";
							local.subEndDate = "";
							local.subGraceEndDate = "";
							if (len(local.qrySubs.subStartDate))
							{
								local.subStartDate = local.qrySubs.subStartDate;
							}
							if (len(local.qrySubs.subEndDate))
							{
								local.subEndDate = local.qrySubs.subEndDate;
							}
							if (len(local.qrySubs.graceEndDate))
							{
								local.subGraceEndDate = local.qrySubs.graceEndDate;
							}
						</cfscript>
	
						<cfset local.currentBranch.subscriberID 	= local.qrySubs.subscriberID />
						<cfset local.currentBranch.parentSubscriberID 	= local.qrySubs.parentSubscriberID />
						<cfset local.currentBranch.memberID 			= local.qrySubs.memberID />
						<cfset local.currentBranch.memberName 		= local.qrySubs.memberName />
						<cfset local.currentBranch.subscriptionID 			= local.qrySubs.subscriptionID />
						<cfset local.currentBranch.subscriptionName 		= local.qrySubs.subscriptionName />
						<cfset local.currentBranch.typeName 		= local.qrySubs.typeName />
						<cfset local.currentBranch.status 		= local.qrySubs.status />
						<cfset local.currentBranch.subStartDate 		= local.subStartDate />
						<cfset local.currentBranch.subEndDate 		= local.subEndDate />
						<cfset local.currentBranch.graceEndDate 	= local.subGraceEndDate />
						<cfset local.currentBranch.thePath 					= local.qrySubs.thePath />
						<cfset local.currentBranch.thePathExpanded 	= local.qrySubs.thePathExpanded />
					</cfif>
				</cfloop>

			<!---</cfif>--->
		</cfloop>
		
		<cfsavecontent variable="local.returnXML">
			<cfoutput>
				<rows parent="0">
				<cfif arguments.event.getValue('lh') is 1>
					<head>
						<column align="left" sort="na" type="tree" width="200">Subscription Name</column>
						<cfif (local.subStatus eq "R") OR (local.subStatus eq "O")>
						<column align="left" sort="na" type="ch" width="*">Select</column>
						<cfelseif local.subStatus eq "A">
						<column align="left" sort="na" type="ch" width="*">Mark Expired</column>
						</cfif>
						<column align="left" sort="na" type="ro" width="*">Type</column>
						<column align="left" sort="na" type="ro" width="*">Start Date</column>
						<column align="left" sort="na" type="ro" width="*">End Date</column>
						<column align="left" sort="na" type="ro" width="*">Grace End</column>
						<column align="left" sort="na" type="ro" width="*">Status</column>
						<settings>
							<colwidth>px</colwidth>
						</settings>
					</head>
					</cfif>

					<cfset local.keyList = listsort(StructKeyList(local.subscriptionTree),"numeric")>
					<cfloop index="local.currentKey" list="#local.keyList#">
						<cfif isStruct(local.subscriptionTree[local.currentKey])>
							#createSubscriberTreeXML(local.subscriptionTree[local.currentKey], 0, arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'), local.subStatus)# 
						</cfif>
					</cfloop>
					
				</rows>
			</cfoutput>
		</cfsavecontent>
		<cfcontent reset="yes" type="text/xml; charset=UTF-8">
		<cfreturn application.objCommon.minText(local.returnXML)>
	</cffunction>

	<cffunction name="createSubscriberTreeXML" access="private" returntype="string">
		<cfargument name="subscriptionStruct" type="struct" required="true">
		<cfargument name="numLevel" type="numeric" required="true">
		<cfargument name="timeZoneID" type="numeric" required="true">
		<cfargument name="filterStatus" type="string" required="true">

		<cfset var local = structNew()>
		
 		<cfsavecontent variable="local.resultXML">
			<cfoutput>
			<cfif StructKeyExists(arguments.subscriptionStruct,"subscriberID")>
				<cfif arguments.subscriptionStruct.subscriberID eq 0>
					<cfset local.rowID = "m" & arguments.subscriptionStruct.memberID>
					<cfset local.nameField = "<b><a href='javascript:editMember(#arguments.subscriptionStruct.memberID#)'>" & arguments.subscriptionStruct.memberName & "</a></b>">
					<cfset local.imgFile = "group.png">
				<cfelse>
					<cfset local.rowID = arguments.subscriptionStruct.subscriberID>
					<cfset local.nameField = arguments.subscriptionStruct.subscriptionName>
					<cfset local.imgFile = "event.png">
				</cfif>
				
				<row id="#local.rowID#" <cfif arguments.subscriptionStruct.status eq "D">style="background-color:##e8aeae; border-top:1px solid ##e77979;border-bottom:1px solid ##e77979;"<cfelse>open="1"</cfif>>
					<cell image="#local.imgFile#"> #xmlFormat(local.nameField)#</cell>
					<cfif (arguments.filterStatus eq "R") OR (arguments.filterStatus eq "O") OR (arguments.filterStatus eq "A")>
						<cfif (arguments.numLevel neq 1)>
							<cell type="ro"></cell>
						<cfelse>
							<cell>1</cell>
						</cfif>
					</cfif>
					<cell> #xmlFormat(arguments.subscriptionStruct.typeName)#</cell>
					<cell> #dateformat(arguments.subscriptionStruct.subStartDate,'m/d/yy')#</cell>
					<cell> #dateformat(arguments.subscriptionStruct.subEndDate,'m/d/yy')# </cell>
					<cell> #dateformat(arguments.subscriptionStruct.graceEndDate,'m/d/yy')# </cell>
				<cfswitch expression="#arguments.subscriptionStruct.status#">
					<cfcase value="">
						<cell></cell>
					</cfcase>
					<cfcase value="A">
						<cell> Active </cell>
					</cfcase>
					<cfcase value="I">
						<cell> Inactive </cell>
					</cfcase>
					<cfcase value="E">
						<cell> Expired </cell>
					</cfcase>
					<cfcase value="R">
						<cell> Renewal Not Sent </cell>
					</cfcase>
					<cfcase value="O">
						<cell> Billed </cell>
					</cfcase>
					<cfcase value="P">
						<cell> Accepted </cell>
					</cfcase>
					<cfcase value="X">
						<cell> Renewal Expired </cell>
					</cfcase>
					<cfcase value="D">
						<cell> Deleted </cell>
					</cfcase>
					<cfdefaultcase>
						<cell> Unknown (#xmlFormat(arguments.subscriptionStruct.status)#) </cell>
					</cfdefaultcase>
				</cfswitch>
				<cfset local.keyList = listsort(StructKeyList(arguments.subscriptionStruct),"textnocase")>
				<cfloop index="local.currentKey" list="#local.keyList#">
					<cfif isStruct(arguments.subscriptionStruct[local.currentKey])>
						#createSubscriberTreeXML(arguments.subscriptionStruct[local.currentKey], (arguments.numLevel + 1), arguments.timeZoneID, arguments.filterStatus)# 
					</cfif>
				</cfloop>
				</row>
			<cfelse>
				<cfset local.keyList = listsort(StructKeyList(arguments.subscriptionStruct),"textnocase")>
				<cfloop index="local.currentKey" list="#local.keyList#">
					<cfif isStruct(arguments.subscriptionStruct[local.currentKey])>
						#createSubscriberTreeXML(arguments.subscriptionStruct[local.currentKey], (arguments.numLevel + 1), arguments.timeZoneID, arguments.filterStatus)# 
					</cfif>
				</cfloop>
			</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.resultXML>
 	</cffunction>

	<cffunction name="subRenewalsXML" access="public" output="false" returntype="xml">
		<cfargument name="Event" type="any">
	
		<cfset var local = structNew()>
		<cfset arguments.event.setValue('lh',int(val(arguments.event.getValue('lh',0))))>
		
		<cfset local.subStatus = arguments.event.getValue('sID','0')>
		<cfset local.subStartDate = arguments.event.getValue('dtTf','')>
		<cfset local.subEndDate = arguments.event.getValue('dtTt','')>
		<cfset local.subType = arguments.event.getValue('stID','0')>
		<cfset local.subID = arguments.event.getValue('subID','0')>
		<cfset local.rateID = arguments.event.getValue('rateID','0')>
		<cfset local.associatedMemberID = arguments.event.getValue('associatedMemberID',0)>
		<cfset local.associatedGroupID = arguments.event.getValue('associatedGroupID',0)>
		<cfset local.linkedRecords = arguments.event.getValue('linkedRecords','all')>
		
		<cfscript>
			if (len(local.subEndDate)) local.subEndDate = dateadd("l",-3,dateadd("d",1,DATEADD("d",DATEDIFF("d",0,local.subEndDate), 0)));
		</cfscript>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRFIDs">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select rf.rfid
			from dbo.sub_rateFrequencies rf
			inner join dbo.sub_rates r on r.rateID = rf.rateID and r.rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfset local.rfidList = valueList(local.qryRFIDs.rfid)>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubParents">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @dtNow datetime = getdate();

			select s.subscriberID
			from dbo.sub_subscribers s
			inner join dbo.ams_members as m on m.orgID = @orgID and s.memberID = m.memberID
			inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID and m2.status <> 'D'					
			<cfif local.associatedMemberID gt 0>
				AND m2.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.associatedMemberID#">
			</cfif>
			<cfif local.associatedGroupID gt 0>
				inner join dbo.cache_members_groups mg ON mg.orgID = @orgID and mg.memberID = m2.memberID AND mg.groupid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.associatedGroupID#">
			</cfif>
			inner join dbo.sub_subscriptions sc on sc.subscriptionID = s.subscriptionID
			inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			inner join dbo.sub_statuses st on st.statusID = s.statusID and s.statusCode <> 'D'
			<cfif local.subStatus neq "0">
				AND st.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.subStatus#">
			<cfelse>
				AND st.statusCode in ('A','I','E')
			</cfif>
			left outer join dbo.sub_rates r on r.rateID = (select max(rateID) from dbo.sub_rates r1 where r1.scheduleID = sc.scheduleID and r1.isRenewalRate = 1)
			left outer join dbo.sub_subscribers sP 
													inner join dbo.sub_statuses st2 on st2.statusID = sP.statusID and st2.statusCode in ('R','O','P')
												on sP.subscriptionID = s.subscriptionID 
												and sP.memberID = s.memberID 
			where s.parentSubscriberID is null
			and sP.subscriberID is null
			and (select case when ISNULL(r.termAFStartDate, @dtNow) <= @dtNow then @dtNow else r.termAFStartDate end) > s.subEndDate
			and (sP.subscriberID is null OR (st2.statusCode = 'P' AND ((select case when ISNULL(r.termAFStartDate, @dtNow) <= @dtNow then @dtNow else r.termAFStartDate end) > sP.subEndDate)))
			<cfif len(local.subStartDate) gt 0>
				AND s.subStartDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.subStartDate#">
			</cfif>
			<cfif len(local.subEndDate) gt 0>
				AND s.subEndDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.subEndDate#">
			</cfif>
			<cfif local.subType neq "0">
				AND t.typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subType#">
			</cfif>
			<cfif local.subID neq "0">
				AND s.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subID#">
			</cfif>
			<cfif len(local.rfidList) gt 0>
				AND s.rfid in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rfidList#" list="yes">)
			</cfif>;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.subParents = valueList(local.qrySubParents.subscriberID)>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubs">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select subscriberID, memberID, memberName, subscriptionID, typeID, typeName, subscriptionName, status, subStartDate, subEndDate, graceEndDate,
				parentSubscriberID, thePath, thePathExpanded
			from dbo.fn_getRecursiveSubscriptionsByID(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">, <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.subParents#">)
			order by memberName, thePath;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.subscriptionTree = structNew()>

		<cfset local.currMemberID = 0>
		<cfset local.currCount = 0>
		<cfset local.currSubCount = 0>
		<cfloop query="local.qrySubs">
			<cfset local.currentBranch = local.subscriptionTree>
			<cfset local.subscriptionDepth = listlen(local.qrySubs.thePath,".")>
			<cfset local.currentDepth = 0>
			
			<!---<cfif local.qrySubs.status neq 'D'>--->

				<cfif local.qrySubs.memberID neq local.currMemberID>
					<cfset local.currCount = local.currCount + 1>
					<cfset local.currMemberID = local.qrySubs.memberID>
					<cfset local.currSubCount = 0>

					<cfset local.currentBranch[local.currCount] = StructNew()>
					<cfset local.currentBranch = local.currentBranch[local.currCount]>

					<cfset local.currentBranch.subscriberID 	= 0 />
					<cfset local.currentBranch.parentSubscriberID 	= 0 />
					<cfset local.currentBranch.memberID 			= local.qrySubs.memberID />
					<cfset local.currentBranch.memberName 		= local.qrySubs.memberName />
					<cfset local.currentBranch.subscriptionID 			= 0 />
					<cfset local.currentBranch.subscriptionName 		= '' />
					<cfset local.currentBranch.typeName 		= '' />
					<cfset local.currentBranch.status 		= '' />
					<cfset local.currentBranch.subStartDate 		= '' />
					<cfset local.currentBranch.subEndDate 		= '' />
					<cfset local.currentBranch.graceEndDate 	= '' />
					<cfset local.currentBranch.thePath 					= '' />
					<cfset local.currentBranch.thePathExpanded 	= '' />
					
				<cfelse>
					<cfset local.currentBranch = local.currentBranch[local.currCount]>
				</cfif>
			
				<cfset local.subscriberPathArray = listToArray(local.qrySubs.thePath,".")>
				<cfloop from="1" to="#arrayLen(local.subscriberPathArray)#" index="local.currentSubscription">
					
					<cfset local.currentDepth = local.currentDepth + 1>
					<cfif local.currentDepth is not 1>
						<cfif not structKeyExists(local.currentBranch,local.subscriberPathArray[local.currentSubscription])>
							<cfset local.currentBranch[local.subscriberPathArray[local.currentSubscription]] = StructNew()>
						<cfelseif local.currentSubscription eq arrayLen(local.subscriberPathArray)>
							<!--- 
								if this is the last segement of the subscriberpath, then it's trying to create a new subscription.
								Expired/Deleted statuses make it possible for the same subscription to exist more than once in a tree
								if this occurs we rename the branch of the previously existing entry so that we can build the current entry
							 --->
							<cfset local.newNameForPreexistingBranch = "-" & local.subscriberPathArray[local.currentSubscription] & "-" & local.currentBranch[local.subscriberPathArray[local.currentSubscription]].subscriberID>
							<cfset local.currentBranch[local.newNameForPreexistingBranch] = duplicate(local.currentBranch[local.subscriberPathArray[local.currentSubscription]])>
							<cfset local.currentBranch[local.subscriberPathArray[local.currentSubscription]] = StructNew()>
						</cfif>
						<cfset local.currentBranch = local.currentBranch[local.subscriberPathArray[local.currentSubscription]]>
					</cfif> 				
	 				<cfif local.currentDepth is local.subscriptionDepth>
		 				
						<cfscript>
							local.subStartDate = "";
							local.subEndDate = "";
							local.subGraceEndDate = "";
							if (len(local.qrySubs.subStartDate))
							{
								local.subStartDate = local.qrySubs.subStartDate;
							}
							if (len(local.qrySubs.subEndDate))
							{
								local.subEndDate = local.qrySubs.subEndDate;
							}
							if (len(local.qrySubs.graceEndDate))
							{
								local.subGraceEndDate = local.qrySubs.graceEndDate;
							}
						</cfscript>
	
						<cfset local.currentBranch.subscriberID 	= local.qrySubs.subscriberID />
						<cfset local.currentBranch.parentSubscriberID 	= local.qrySubs.parentSubscriberID />
						<cfset local.currentBranch.memberID 			= local.qrySubs.memberID />
						<cfset local.currentBranch.memberName 		= local.qrySubs.memberName />
						<cfset local.currentBranch.subscriptionID 			= local.qrySubs.subscriptionID />
						<cfset local.currentBranch.subscriptionName 		= local.qrySubs.subscriptionName />
						<cfset local.currentBranch.typeName 		= local.qrySubs.typeName />
						<cfset local.currentBranch.status 		= local.qrySubs.status />
						<cfset local.currentBranch.subStartDate 		= local.subStartDate />
						<cfset local.currentBranch.subEndDate 		= local.subEndDate />
						<cfset local.currentBranch.graceEndDate 	= local.subGraceEndDate />
						<cfset local.currentBranch.thePath 					= local.qrySubs.thePath />
						<cfset local.currentBranch.thePathExpanded 	= local.qrySubs.thePathExpanded />
					</cfif>
				</cfloop>

			<!---</cfif>--->
		</cfloop>
		
		<cfsavecontent variable="local.returnXML">
			<cfoutput>
				<rows parent="0">
				<cfif arguments.event.getValue('lh') is 1>
					<head>
						<column align="left" sort="na" type="tree" width="200">Subscription Name</column>
						<column align="left" sort="na" type="ch" width="*">Renew</column>
						<column align="left" sort="na" type="ro" width="*">Type</column>
						<column align="left" sort="na" type="ro" width="*">Start Date</column>
						<column align="left" sort="na" type="ro" width="*">End Date</column>
						<column align="left" sort="na" type="ro" width="*">Grace End</column>
						<column align="left" sort="na" type="ro" width="*">Status</column>
						<settings>
							<colwidth>px</colwidth>
						</settings>
					</head>
					</cfif>

					<cfset local.keyList = listsort(StructKeyList(local.subscriptionTree),"textnocase")>
					<cfloop index="local.currentKey" list="#local.keyList#">
						<cfif isStruct(local.subscriptionTree[local.currentKey])>
							#createSubscriberRenewalTreeXML(local.subscriptionTree[local.currentKey], 0, arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'))# 
						</cfif>
					</cfloop>
					
				</rows>
			</cfoutput>
		</cfsavecontent>
		<cfcontent reset="yes" type="text/xml; charset=UTF-8">
		<cfreturn application.objCommon.minText(local.returnXML)>
	</cffunction>
	
	<cffunction name="createSubscriberRenewalTreeXML" access="private" returntype="string">
		<cfargument name="subscriptionStruct" type="struct" required="true">
		<cfargument name="numLevel" type="numeric" required="true">
		<cfargument name="timeZoneID" type="numeric" required="true">
		<cfset var local = structNew()>
		
 		<cfsavecontent variable="local.resultXML">
			<cfoutput>
			<cfif StructKeyExists(arguments.subscriptionStruct,"subscriberID")>
				<cfif arguments.subscriptionStruct.subscriberID eq 0>
					<cfset local.rowID = "m" & arguments.subscriptionStruct.memberID>
					<cfset local.nameField = "<b><a href='javascript:editMember(#arguments.subscriptionStruct.memberID#)'>" & arguments.subscriptionStruct.memberName & "</a></b>">
					<cfset local.imgFile = "group.png">
				<cfelse>
					<cfset local.rowID = arguments.subscriptionStruct.subscriberID>
					<cfset local.nameField = arguments.subscriptionStruct.subscriptionName>
					<cfset local.imgFile = "event.png">
				</cfif>
				
				
				<cfif arguments.numLevel eq 0>
				<row id="#local.rowID#" open="1">
				<cfelse>
				<row id="#local.rowID#" close="1">
				</cfif>
					<cell image="#local.imgFile#"> #xmlFormat(local.nameField)#</cell>
					<cfif arguments.numLevel neq 1>
						<cell type="ro"></cell>
					<cfelse>
						<cell>1</cell>
					</cfif>
					<cell> #xmlFormat(arguments.subscriptionStruct.typeName)#</cell>
					<cell> #dateformat(arguments.subscriptionStruct.subStartDate,'m/d/yy')#</cell>
					<cell> #dateformat(arguments.subscriptionStruct.subEndDate,'m/d/yy')# </cell>
					<cell> #dateformat(arguments.subscriptionStruct.graceEndDate,'m/d/yy')# </cell>
				<cfswitch expression="#arguments.subscriptionStruct.status#">
					<cfcase value="">
						<cell></cell>
					</cfcase>
					<cfcase value="A">
						<cell> Active </cell>
					</cfcase>
					<cfcase value="I">
						<cell> Inactive </cell>
					</cfcase>
					<cfcase value="E">
						<cell> Expired </cell>
					</cfcase>
					<cfcase value="R">
						<cell> Renewal Not Sent </cell>
					</cfcase>
					<cfcase value="O">
						<cell> Billed </cell>
					</cfcase>
					<cfcase value="P">
						<cell> Accepted </cell>
					</cfcase>
					<cfcase value="X">
						<cell> Renewal Expired </cell>
					</cfcase>
					<cfcase value="D">
						<cell> Deleted </cell>
					</cfcase>
					<cfdefaultcase>
						<cell> Unknown (#xmlFormat(arguments.subscriptionStruct.status)#) </cell>
					</cfdefaultcase>
				</cfswitch>
				<cfset local.keyList = listsort(StructKeyList(arguments.subscriptionStruct),"textnocase")>
				<cfloop index="local.currentKey" list="#local.keyList#">
					<cfif isStruct(arguments.subscriptionStruct[local.currentKey])>
						#createSubscriberRenewalTreeXML(arguments.subscriptionStruct[local.currentKey], (arguments.numLevel + 1), arguments.timeZoneID)# 
					</cfif>
				</cfloop>
				</row>
			<cfelse>
				<cfset local.keyList = listsort(StructKeyList(arguments.subscriptionStruct),"textnocase")>
				<cfloop index="local.currentKey" list="#local.keyList#">
					<cfif isStruct(arguments.subscriptionStruct[local.currentKey])>
						#createSubscriberRenewalTreeXML(arguments.subscriptionStruct[local.currentKey], (arguments.numLevel + 1), arguments.timeZoneID)# 
					</cfif>
				</cfloop>
			</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.resultXML>
 	</cffunction>

	<cffunction name="subGridXML" access="public" output="false" returntype="xml">
		<cfargument name="Event" type="any">
	
		<cfscript>
			var local = structNew();
			// GRID SETTINGS ---------------------------------------------------------------------------- ::
			arguments.event.setValue('direct',arguments.event.getValue('direct','asc'));
			arguments.event.setValue('lh',int(val(arguments.event.getValue('lh',1))));
			arguments.event.setValue('orderby',int(val(arguments.event.getValue('orderby',1))));
			
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('posStart',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('count',50))));
			// ------------------------------------------------------------------------------------------ ::
			
			local.rootName = arguments.event.getValue('mc_siteInfo.ORGSHORTNAME') & ' Subscriptions';
		</cfscript>
	
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.allSubs">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int, @orgID int;
			select @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
			select @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">;

			select categoryID, subCategory, typeID, typeName, subscriptionID, subscriptionName,	memberCount, thePath
			from (
				select sub.subscriptionID, sub.subscriptionName, t.typeID, t.typeName,
					count(distinct s.memberid) AS memberCount, 
					'1000' as categoryID, 'Active' as subCategory,
					'0001.1000.' + CAST(RIGHT('100000'+t.typeID,4) as varchar(max)) + '.' +
					CAST(RIGHT('100000'+(row_number() over (partition by typeName ORDER BY t.typeID, sub.subscriptionName)),4) as varchar(max)) as thePath
				from dbo.sub_subscriptions sub
				inner join dbo.sub_types t on t.typeID = sub.typeID and t.siteID = @siteID and t.status <> 'D'
				left outer join dbo.sub_subscribers s 
						inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'A'
						inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID and pst.statusCode = 'P'
					on s.subscriptionID = sub.subscriptionID
				where sub.status <> 'D'
				group by t.typeID, t.typeName, sub.subscriptionID, sub.subscriptionName
				UNION
				select sub.subscriptionID, sub.subscriptionName, t.typeID, t.typeName,
					count(distinct s.memberid) AS memberCount, 
					'2000' as categoryID, 'Not Activated' as subCategory,
					'0001.2000.' + CAST(RIGHT('100000'+t.typeID,4) as varchar(max)) + '.' +
					CAST(RIGHT('100000'+(row_number() over (partition by typeName ORDER BY t.typeID, sub.subscriptionName)),4) as varchar(max)) as thePath
				from dbo.sub_subscriptions sub
				inner join dbo.sub_types t on t.typeID = sub.typeID and t.siteID = @siteID and t.status <> 'D'
				left outer join dbo.sub_subscribers s 
						inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'A'
						inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID and pst.statusCode = 'N'
					on s.subscriptionID = sub.subscriptionID
				where sub.status <> 'D'
				group by t.typeID, t.typeName, sub.subscriptionID, sub.subscriptionName
				UNION
				select sub.subscriptionID, sub.subscriptionName, t.typeID, t.typeName,
					count(distinct s.memberid) AS memberCount, 
					'3000' as categoryID, 'Accepted' as subCategory,
					'0001.3000.' + CAST(RIGHT('100000'+t.typeID,4) as varchar(max)) + '.' +
					CAST(RIGHT('100000'+(row_number() over (partition by typeName ORDER BY t.typeID, sub.subscriptionName)),4) as varchar(max)) as thePath
				from dbo.sub_subscriptions sub
				inner join dbo.sub_types t on t.typeID = sub.typeID and t.siteID = @siteID and t.status <> 'D'
				left outer join dbo.sub_subscribers s 
						inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'P'
					on s.subscriptionID = sub.subscriptionID
				where sub.status <> 'D'
				group by t.typeID, t.typeName, sub.subscriptionID, sub.subscriptionName
				UNION
				select sub.subscriptionID, sub.subscriptionName, t.typeID, t.typeName,
					count(distinct s.memberid) AS memberCount, 
					'4000' as categoryID, 'Billed' as subCategory,
					'0001.4000.' + CAST(RIGHT('100000'+t.typeID,4) as varchar(max)) + '.' +
					CAST(RIGHT('100000'+(row_number() over (partition by typeName ORDER BY t.typeID, sub.subscriptionName)),4) as varchar(max)) as thePath
				from dbo.sub_subscriptions sub
				inner join dbo.sub_types t on t.typeID = sub.typeID and t.siteID = @siteID and t.status <> 'D'
				left outer join dbo.sub_subscribers s 
						inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'O'
					on s.subscriptionID = sub.subscriptionID
				where sub.status <> 'D'
				group by t.typeID, t.typeName, sub.subscriptionID, sub.subscriptionName
				UNION
				select sub.subscriptionID, sub.subscriptionName, t.typeID, t.typeName,
					count(distinct s.memberid) AS memberCount, 
					'5000' as categoryID, 'Renewal Not Sent' as subCategory,
					'0001.5000.' + CAST(RIGHT('100000'+t.typeID,4) as varchar(max)) + '.' +
					CAST(RIGHT('100000'+(row_number() over (partition by typeName ORDER BY t.typeID, sub.subscriptionName)),4) as varchar(max)) as thePath
				from dbo.sub_subscriptions sub
				inner join dbo.sub_types t on t.typeID = sub.typeID and t.siteID = @siteID and t.status <> 'D'
				left outer join dbo.sub_subscribers s 
						inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'R'
					on s.subscriptionID = sub.subscriptionID
				where sub.status <> 'D'
				group by t.typeID, t.typeName, sub.subscriptionID, sub.subscriptionName
				UNION
				select sub.subscriptionID, sub.subscriptionName, t.typeID, t.typeName,
					count(distinct s.memberid) AS memberCount, 
					'6000' as categoryID, 'Inactive' as subCategory,
					'0001.6000.' + CAST(RIGHT('100000'+t.typeID,4) as varchar(max)) + '.' +
					CAST(RIGHT('100000'+(row_number() over (partition by typeName ORDER BY t.typeID, sub.subscriptionName)),4) as varchar(max)) as thePath
				from dbo.sub_subscriptions sub
				inner join dbo.sub_types t on t.typeID = sub.typeID and t.siteID = @siteID and t.status <> 'D'
				left outer join dbo.sub_subscribers s 
						inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'I'
					on s.subscriptionID = sub.subscriptionID
				where sub.status <> 'D'
				group by t.typeID, t.typeName, sub.subscriptionID, sub.subscriptionName
				UNION
				select sub.subscriptionID, sub.subscriptionName, t.typeID, t.typeName,
					count(distinct s.memberid) AS memberCount, 
					'7000' as categoryID, 'Expired' as subCategory,
					'0001.7000.' + CAST(RIGHT('100000'+t.typeID,4) as varchar(max)) + '.' +
					CAST(RIGHT('100000'+(row_number() over (partition by typeName ORDER BY t.typeID, sub.subscriptionName)),4) as varchar(max)) as thePath
				from dbo.sub_subscriptions sub
				inner join dbo.sub_types t on t.typeID = sub.typeID and t.siteID = @siteID and t.status <> 'D'
				left outer join dbo.sub_subscribers s 
						inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'E'
					on s.subscriptionID = sub.subscriptionID
				where sub.status <> 'D'
				group by t.typeID, t.typeName, sub.subscriptionID, sub.subscriptionName
			) innerSubs
			order by thePath;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset queryAddRow(local.allSubs)>
		<cfset querySetCell(local.allSubs,'subCategory', local.rootName) />
		<cfset querySetCell(local.allSubs,'typeID', 0) />
		<cfset querySetCell(local.allSubs,'typeName', '') />
		<cfset querySetCell(local.allSubs,'subscriptionID', 0) />
		<cfset querySetCell(local.allSubs,'subscriptionName', '') />
		<cfset querySetCell(local.allSubs,'memberCount', '') />
		<cfset querySetCell(local.allSubs,'thePath', '0001') />
		
		<cfquery dbtype="query" name="local.categorySum">
			select subCategory, categoryID, sum(memberCount) as memberCount
			from [local].allSubs
			group by subCategory, categoryID
		</cfquery>
		
		<cfloop query="local.categorySum">
			<cfset queryAddRow(local.allSubs)>
			<cfset querySetCell(local.allSubs,'subCategory', local.categorySum.subCategory) />
			<cfset querySetCell(local.allSubs,'typeID', 0) />
			<cfset querySetCell(local.allSubs,'typeName', '') />
			<cfset querySetCell(local.allSubs,'subscriptionID', 0) />
			<cfset querySetCell(local.allSubs,'subscriptionName', '') />
			<cfset querySetCell(local.allSubs,'memberCount', local.categorySum.memberCount) />
			<cfset querySetCell(local.allSubs,'thePath', '0001.#local.categorySum.categoryID#') />
		</cfloop>
		
		<cfquery dbtype="query" name="local.typeSum">
			select subCategory, categoryID, typeID, sum(memberCount) as memberCount
			from [local].allSubs
			group by subCategory, categoryID, typeID
		</cfquery>

		<cfquery dbtype="query" name="local.data">
			SELECT subCategory,typeID,typeName,subscriptionID,subscriptionName,memberCount,thePath
			FROM [local].allSubs
			ORDER BY thePath
		</cfquery>
		
		<cfset local.subTree = structNew()>
		<cfset local.myPath = '9999'>
		<cfloop query="local.data">
			<cfset local.currentBranch = local.subTree>
			<cfset local.subDepth = listlen(local.data.thePath,".")>
			<cfset local.currentDepth = 0>
			<cfloop list="#local.data.thePath#" delimiters="." index="local.currentSub">
				<cfset local.currentDepth = local.currentDepth + 1>
				<cfif local.currentDepth is not 1>
					<cfif not structKeyExists(local.currentBranch, local.currentSub)>
						<cfset local.currentBranch[local.currentSub] = StructNew()>
						<cfif local.currentDepth eq 3>
							<cfquery dbtype="query" name="local.currTypeSum">
								select memberCount
								from [local].typeSum
								where subCategory = '#local.data.subCategory#'
								and typeID = #local.data.typeID#
							</cfquery>
							<cfset local.currentBranch[local.currentSub].subCategory 		= local.data.subCategory />
							<cfset local.currentBranch[local.currentSub].typeID 			= local.data.typeID />
							<cfset local.currentBranch[local.currentSub].typeName 			= local.data.typeName />
							<cfset local.currentBranch[local.currentSub].memberCount		= '#local.currTypeSum.memberCount#' />
							<cfset local.currentBranch[local.currentSub].subscriptionID 	= '' />
							<cfset local.currentBranch[local.currentSub].subscriptionName 	= '' />
							<cfset local.currentBranch[local.currentSub].thePath 			= local.myPath & '.' & LEFT(local.data.thePath, 14) />
						</cfif>
					</cfif>
					<cfset local.currentBranch = local.currentBranch[local.currentSub]>
				</cfif> 		
	 			<cfif local.currentDepth is local.subDepth>
					<cfset local.currentBranch.subCategory 							= local.data.subCategory />
					<cfset local.currentBranch.typeID 								= local.data.typeID />
					<cfset local.currentBranch.typeName 							= local.data.typeName />
					<cfset local.currentBranch.memberCount							= local.data.memberCount />
					<cfset local.currentBranch.subscriptionID 						= local.data.subscriptionID />
					<cfset local.currentBranch.subscriptionName 					= local.data.subscriptionName />
					<cfset local.currentBranch.thePath 								= local.myPath & '.' & local.data.thePath />
				</cfif>
			</cfloop>
		</cfloop>
		
		<cfsavecontent variable="local.returnXML">
			<cfoutput>
				<rows parent="0">
				<cfif arguments.event.getValue('lh') is 1>
	 				<head>
						<column align="left" sort="server" type="tree" width="*">Subscription Name</column>
						<column align="center" sort="server" type="ro" width="75">Members</column>
						<column align="center" sort="na" type="img" width="25">Tools</column>
						<column align="center" sort="na" type="img" width="25">##cspan</column>
						<settings>
							<colwidth>px</colwidth>
						</settings>
					</head>
					</cfif>
					#createSubGridTreeXML(local.subTree)#
				</rows>
			</cfoutput>
		</cfsavecontent>

		<cfcontent reset="yes" type="text/xml; charset=UTF-8">
		<cfreturn application.objCommon.minText(local.returnXML)>
	</cffunction>
	
	<cffunction name="createSubGridTreeXML" access="private" returntype="string">
		<cfargument name="subStruct" type="struct" required="true">
		<cfset var local = structNew()>

		<cfif (val(arguments.subStruct.subscriptionID) eq 0) AND (val(arguments.subStruct.typeID) eq 0)>
			<cfset local.displayName = arguments.subStruct.subCategory>
		<cfelseif (val(arguments.subStruct.subscriptionID) eq 0) AND (val(arguments.subStruct.typeID) neq 0)>
			<cfset local.displayName = arguments.subStruct.typeName>
		<cfelse>
			<cfset local.displayName = arguments.subStruct.subscriptionName>
		</cfif>

 		<cfsavecontent variable="local.resultXML">
			<cfoutput>
				<row id="#arguments.subStruct.thePath#" <cfif arguments.subStruct.subscriptionID is 0>open="1"</cfif>>
					
					<cell image="spacer.gif"> #xmlFormat(local.displayName)#</cell>
					<cell>#arguments.subStruct.memberCount#</cell>
					
					<cfif (val(arguments.subStruct.subscriptionID) neq 0) AND (val(arguments.subStruct.typeID) neq 0)>
						<cell>/assets/common/images/grid/event_edit.png^Edit Subscription^javascript:editSub(#arguments.subStruct.subscriptionID#)^_self</cell>
					<cfelse>
						<cell>/assets/common/images/spacer.gif^^</cell>
					</cfif>
						<cell>/assets/common/images/spacer.gif^^</cell>
					
					<cfset local.keyList = listsort(StructKeyList(arguments.subStruct),"textnocase")>
					<cfloop index="local.currentKey" list="#local.keyList#">
						<cfif isStruct(arguments.subStruct[local.currentKey])>
							#createSubGridTreeXML(arguments.subStruct[local.currentKey])# 
						</cfif>
					</cfloop>
				</row>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.resultXML>
	</cffunction>

	<cffunction name="emailTemplateGridXML" access="public" output="false" returntype="xml">
		<cfargument name="Event" type="any">
	
		<cfscript>
			var local = structNew();
			// GRID SETTINGS ---------------------------------------------------------------------------- ::
			arguments.event.setValue('direct',arguments.event.getValue('direct3','asc'));
			arguments.event.setValue('lh',int(val(arguments.event.getValue('lh3',1))));
			arguments.event.setValue('orderby',int(val(arguments.event.getValue('orderby3',1))));
			
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('posStart',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('count',50))));
		</cfscript>

		<cfset local.strFilters = this.objSubscriptions.getSubReportListFilters(event=arguments.event)>

		<!--- single subscriber or selected subscribers --->
		<cfif local.strFilters.fChkAll is 0 OR local.strFilters.fChkedSubs neq ''>
			<cfquery name="local.qrySubs" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">;

				select s.subscriberID, mActive.memberID, mActive.lastname + ', ' + mActive.firstname + ' (' + mActive.membernumber + ')' AS memberName, 
					mActive.company, sub.subscriptionName, me.email
				from dbo.sub_subscribers s
				inner join dbo.sub_subscriptions sub on sub.subscriptionID = s.subscriptionID
				inner join dbo.sub_statuses st on st.statusID = s.statusID
				inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
				inner join dbo.sub_types t on t.typeID = sub.typeID 
					and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
				inner join dbo.ams_members as m on m.orgID = @orgID 
					and s.memberID = m.memberID
				inner join dbo.ams_members as mActive on mActive.orgID = @orgID 
					and mActive.memberID = m.activeMemberID
				inner join dbo.ams_memberEmails as me on me.orgID = @orgID
					and me.memberID = mActive.memberID
				inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID 
					and metag.memberID = me.memberID 
					and metag.emailTypeID = me.emailTypeID
				inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
					and metagt.emailTagTypeID = metag.emailTagTypeID 
					and metagt.emailTagType = 'Primary'
				where s.subscriberID in (0#local.strFilters.fChkedSubs#);
			</cfquery>

			<cfif arguments.event.getValue('posStart') is 0>
				<cfset local.totalCount = local.qrySubs.recordCount>
			<cfelse>
				<cfset local.totalCount = "">
			</cfif>
		<cfelse>
			<cfif arguments.event.getValue('posStart') is 0>
				<cfset local.qrySubCount = this.objSubscriptions.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
																					subPaymentStatus=local.strFilters.fSubPaymentStatus,
																					subStartFromDate=local.strFilters.fTermStartFrom,
																					subStartToDate=local.strFilters.fTermStartTo,
																					subEndFromDate=local.strFilters.fTermEndFrom,
																					subEndToDate=local.strFilters.fTermEndTo,
																					subType=local.strFilters.fSubType,
																					subID=local.strFilters.fSubscription,
																					freqID=local.strFilters.fFreq,
																					rateID=local.strFilters.fRate,
																					hasCard=local.strFilters.fHasCardOnFile,
																					associatedMemberID=local.strFilters.associatedMemberID,
																					associatedGroupID=local.strFilters.associatedGroupID,
																					linkedRecords=local.strFilters.linkedRecords,
																					siteID=arguments.event.getValue('mc_siteinfo.siteid'),
																					offerEndFromDate=local.strFilters.fOffrExpFrom,
																					offerEndToDate=local.strFilters.fOffrExpTo,
																					notSubscribers=local.strFilters.fUnchkedSubs,
																					countOnly=true)>
				<cfset local.totalCount = local.qrySubCount.totalCount>
			<cfelse>
				<cfset local.totalCount = "">
			</cfif>

			<cfset local.subList = this.objSubscriptions.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
																			subPaymentStatus=local.strFilters.fSubPaymentStatus,
																			subStartFromDate=local.strFilters.fTermStartFrom,
																			subStartToDate=local.strFilters.fTermStartTo,
																			subEndFromDate=local.strFilters.fTermEndFrom,
																			subEndToDate=local.strFilters.fTermEndTo,
																			subType=local.strFilters.fSubType,
																			subID=local.strFilters.fSubscription,
																			freqID=local.strFilters.fFreq,
																			rateID=local.strFilters.fRate,
																			hasCard=local.strFilters.fHasCardOnFile,
																			associatedMemberID=local.strFilters.associatedMemberID,
																			associatedGroupID=local.strFilters.associatedGroupID,
																			linkedRecords=local.strFilters.linkedRecords,
																			siteID=arguments.event.getValue('mc_siteinfo.siteid'),
																			offerEndFromDate=local.strFilters.fOffrExpFrom,
																			offerEndToDate=local.strFilters.fOffrExpTo,
																			notSubscribers=local.strFilters.fUnchkedSubs,
																			posStart=arguments.event.getValue('posStart'),
																			rowLimit=arguments.event.getValue('count'),
																			direct=arguments.event.getValue('direct'),
																			mode='emailsub')>
			<cfset local.qrySubs = local.subList.qry>
		</cfif>
		
		<cfsavecontent variable="local.returnXML">
			<cfoutput>
			<rows total_count="#local.totalCount#" pos="#arguments.event.getValue('posStart')#">
					<cfif arguments.event.getValue('lh') is 1 and arguments.event.getValue('posStart') is 0>
	 				<head>
						<column align="left" sort="server" type="ro" width="*">Member Name</column>
						<column align="left" sort="server" type="ro" width="*">Subscription Name</column>
						<column align="center" sort="server" type="ro" width="75">Has Email?</column>
						<beforeInit>
							<call command="attachEvent"><param>onRowSelect</param><param>doEmailOnRowSelect</param></call>
						</beforeInit>
						<afterInit>
							<call command="enableSmartRendering"><param>true</param></call>
							<call command="setSortImgState"><param>true</param><param>0</param><param>asc</param></call>
						</afterInit>
						<settings>
							<colwidth>px</colwidth>
						</settings>
					</head>
				</cfif>
				<cfloop query="local.qrySubs">
					<row id="#local.qrySubs.subscriberID#">
						<cell>#xmlFormat(local.qrySubs.memberName)#</cell>
						<cell>#xmlFormat(local.qrySubs.subscriptionName)#</cell>
						<cell><cfif len(local.qrySubs.email)>Yes<cfelse>No</cfif></cell>
					</row>
				</cfloop>
			</rows>
			</cfoutput>
		</cfsavecontent>

		<cfcontent reset="yes" type="text/xml; charset=UTF-8">
		<cfreturn application.objCommon.minText(local.returnXML)>
	</cffunction>
</cfcomponent>