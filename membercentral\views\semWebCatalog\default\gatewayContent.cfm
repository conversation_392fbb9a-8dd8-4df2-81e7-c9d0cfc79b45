<cfset local.showBundleOption = (attributes.data.qrySWP.isSWL OR attributes.data.qrySWP.isSWOD) and attributes.data.hasActiveBundles>
<cfinclude template="/views/semwebCatalog/default/swCatalogcommonCSS.cfm">

<cfoutput>
<div class="swCatalog">
	<cfif structKeyExists(attributes.data,"strCarousel") AND NOT structIsEmpty(attributes.data.strCarousel)>
		<div class="SWCatalogBanner">#attributes.data.strCarousel.html#</div>
	</cfif>

	<div style="margin-bottom:20px;">
		<span style="float:right;" class="swLandingTopLinks">
			<cfif attributes.data.savedProgramsCount>
				<a href="#attributes.event.getValue('mainurl')#&panel=browse&_swis=1" title="View Saved" style="padding-left:12px;">
					<i class="bi bi-heart-fill sw_savedprogramscounticon swRed swIconBadge" data-swsavedprogramscount="#attributes.data.savedProgramsCount#" aria-hidden="true"></i>
				</a>
			<cfelse>
				<a href="javascript:void(0);" style="padding-left:12px;"><i class="bi bi-heart-fill swMuted" aria-hidden="true"></i></a>
			</cfif>
			<cfif attributes.data.swRegCart.recordCount>
				<a href="#attributes.event.getValue('mainurl')#&panel=showCart" class="swPrimary" title="View Cart" style="padding-left:12px;">
					<i class="bi bi-cart-fill sw_regcartcounticon swIconBadge" aria-hidden="true" data-swregcartcount="#attributes.data.swRegCart.recordCount#" aria-hidden="true"></i>
				</a>
			<cfelse>
				<a href="javascript:void(0);" style="padding-left:12px;"><i class="bi bi-cart-fill swMuted" aria-hidden="true"></i></a>
			</cfif>
			<a href="#attributes.event.getValue('mainurl')#&panel=My" class="swPrimary" title="#attributes.data.qrySWP.brandMyCLETab#" style="padding-left:12px;"><i class="bi bi-person-lines-fill" aria-hidden="true"></i></a>
			<a href="#attributes.event.getValue('mainurl')#&panel=showFAQ" class="swPrimary" title="FAQ" style="padding-left:12px;"><i class="bi bi-question-circle-fill" aria-hidden="true"></i></a>
		</span>
		<h5 class="swPrimary tsAppBodyText">#attributes.data.qrySWP.brandHomeTab#</h5>
		<hr size="1">
		<cfif len(attributes.data.qrySWP.brandHomeText)>
			<div style="margin-top:10px;" class="tsAppBodyText">#attributes.data.qrySWP.brandHomeText#</div>
		</cfif>
	</div>

	<table cellpadding="2" cellspacing="0" width="100%">
	<tr valign="top">
		<td width="50%">
			<h5 class="swPrimary tsAppBodyText">Search</h5>
			<div class="swPrimaryBkgd" style="padding:10px;margin-right:10px;margin-top:8px;">
				<p class="sw-mb-3 swWhite"><b>What do you want to learn?</b></p>
				<form method="GET" action="/">
				<input type="hidden" name="pg" value="semWebCatalog">
				<input type="hidden" name="panel" value="browse">
				<div class="sw-d-flex">
					<input type="text" name="_swkwl" id="_swkwl" class="sw-m-0" style="width:90%;" autocomplete="off">
					<button type="submit" class="tsAppBodyButton" style="padding:3px;"><i class="bi bi-search"></i></button>
				</div>
				<cfif attributes.data.qrySWP.isSWL OR attributes.data.qrySWP.isSWOD OR attributes.data.qrySWP.isConf>
					<cfif attributes.data.qrySWP.isSWL>
						<div style="margin-top:6px;">
							<input type="checkbox" name="_swft" id="sw_format_swl" value="swl" checked>
							<label for="sw_format_swl" class="sw-d-inline-block swWhite">
								#attributes.data.qrySWP.brandSWLTab#
							</label>
						</div>
					</cfif>
					<cfif attributes.data.qrySWP.isSWOD>
						<div style="margin-top:6px;">
							<input type="checkbox" name="_swft" id="sw_format_swod" value="swod" checked>
							<label for="sw_format_swod" class="sw-d-inline-block swWhite">
								#attributes.data.qrySWP.brandSWODTab#
							</label>
						</div>
					</cfif>
					<cfif attributes.data.qrySWP.isConf>
						<div style="margin-top:6px;">
							<input type="checkbox" name="_swft" id="sw_format_conf" value="conf" checked>
							<label for="sw_format_conf" class="sw-d-inline-block swWhite">
								#attributes.data.qrySWP.brandConfTab#
							</label>
						</div>
					</cfif>
					<cfif local.showBundleOption>
						<div style="margin-top:6px;">
							<input type="checkbox" name="_swft" id="sw_format_swb" value="swb" checked>
							<label for="sw_format_swb">
								#attributes.data.qrySWP.brandBundleTab#
							</label>
						</div>
					</cfif>
				</cfif>
				</form>
			</div>
		</td>
		<td width="30%">
			<h5 class="swPrimary tsAppBodyText" style="text-align:center">Browse</h5>
			<div class="browse" style="margin-top:8px;">
				<cfif attributes.data.qrySWP.isConf>
					<a href="#attributes.event.getValue('mainurl')#&panel=browse&_swft=Conf" class="mcEvent tsAppBodyText"><i class="bi bi-geo-alt-fill" aria-hidden="true"></i>#attributes.data.qrySWP.brandConfTab#</a>
				</cfif>
				<cfif attributes.data.qrySWP.isSWL>
					<a href="#attributes.event.getValue('mainurl')#&panel=browse&_swft=SWL" class="swWebinar tsAppBodyText"><i class="bi bi-laptop" aria-hidden="true"></i>#attributes.data.qrySWP.brandSWLTab#</a>
				</cfif>
				<cfif attributes.data.qrySWP.isSWOD>
					<a href="#attributes.event.getValue('mainurl')#&panel=browse&_swft=SWOD" class="swOnDemand tsAppBodyText"><i class="bi bi-play-circle" aria-hidden="true"></i>#attributes.data.qrySWP.brandSWODTab#</a>
				</cfif>
				<cfif local.showBundleOption>
					<a href="#attributes.event.getValue('mainurl')#&panel=browse&_swft=SWB" class="swOnDemand tsAppBodyText"><i class="bi bi-basket" aria-hidden="true"></i>#attributes.data.qrySWP.brandBundleTab#</a>
				</cfif>
				<cfif attributes.data.qrySWP.isSWL OR attributes.data.qrySWP.isSWOD OR attributes.data.qrySWP.isConf>
					<a href="#attributes.event.getValue('mainurl')#&panel=browse" class="swPrimary tsAppBodyText"><i class="bi bi-list" aria-hidden="true"></i>All Programs</a>
				</cfif>
			</div>
		</td>
		<td valign="middle" style="text-align:center">
			<a href="#attributes.event.getValue('mainurl')#&panel=My"><i class="bi bi-person-lines-fill fa-lg swPrimary" aria-hidden="true"></i></a>
			<div style="padding-top:10px;text-align:center !important;">
				<a href="#attributes.event.getValue('mainurl')#&panel=My" class="swPrimary tsAppBodyText" style="text-decoration:none;font-size:0.9em;">#attributes.data.qrySWP.brandMyCLETab#</a>
			</div>
		</td>
	</tr>
	</table>
</div>
</cfoutput>