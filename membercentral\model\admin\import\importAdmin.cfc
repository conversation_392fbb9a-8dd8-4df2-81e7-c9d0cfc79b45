<cfcomponent extends="model.admin.admin" output="no">
	<cfset variables.defaultEvent = "controller">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();

		// set rights into event
		local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
		arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

		// set links to different functions
		this.link.list = buildCurrentLink(arguments.event,"list");
		this.link.sampleImportTemplate = buildCurrentLink(arguments.event,"sampleImportTemplate");
		this.link.processImport = buildCurrentLink(arguments.event,"processImport");
		this.link.continueImport = buildCurrentLink(arguments.event,"continueImport") & "&mode=stream";
		this.link.cancelImport = buildCurrentLink(arguments.event,"cancelImport");
		this.link.importStats = buildCurrentLink(arguments.event,"importStats");
		this.link.processMassUpdateMemberNumbers = buildCurrentLink(arguments.event,"processMassUpdateMemberNumbers");
		this.link.processMassDeleteMembers = buildCurrentLink(arguments.event,"processMassDeleteMembers");
		this.link.sampleMassUpdateMemberNumberTemplate = buildCurrentLink(arguments.event,"sampleMassUpdateMemberNumberTemplate");
		this.link.sampleMassDeleteMembersTemplate = buildCurrentLink(arguments.event,"sampleMassDeleteMembersTemplate");
		this.link.loadQueryBuilderLink = buildCurrentLink(arguments.event,"loadQueryBuilder") & "&mode=stream";
		this.link.processMassUpdateMembersByQuery = buildCurrentLink(arguments.event,"processMassUpdateMembersByQuery") & "&mode=stream";
		this.link.loadLinkedRecordsQueryBuilderLink = buildCurrentLink(arguments.event,"loadLinkedRecordsQueryBuilder") & "&mode=stream";
		this.link.processMassUpdateMembersByLinkedRecordsQuery = buildCurrentLink(arguments.event,"processMassUpdateMembersByLinkedRecordsQuery") & "&mode=stream";
		
		local.methodToRun = this[arguments.event.getValue('mca_ta')];
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
	
	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();

		// Security --------------------------------------------------------------------------------- ::
		local.security.manage = checkRights(arguments.event,'manageImport');

		local.memberUpdateHistoryLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=importJSON&meth=getImportHistory&mode=stream";
		local.savedQueriesLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=importJSON&meth=getSavedQueries&mode=stream";
		local.savedLinkedRecordsQueriesLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=importJSON&meth=getSavedLinkedRecordsQueries&mode=stream";

		if (structKeyExists(arguments,"importResults")){
			local.importResults = arguments.importResults;
			appendBreadCrumbs(arguments.event,{ link='', text="Confirmation" });
		}
		</cfscript>

		<cfset local.customData = ''>
		<cfif FileExists(ExpandPath("model/admin/import/custom/#arguments.event.getValue('mc_siteinfo.orgcode')#.cfc"))>
			<cfinvoke component="custom.#arguments.event.getValue('mc_siteinfo.orgcode')#" method="list" returnvariable="local.customData" importAdmin="#this#">
		</cfif>
		<cfset local.qryReadyToProcessJobs = getReadyToProcessJobs(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfset local.qryIssuedBy = getIssuedBy(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfset local.strTitles = getTitles(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfset local.qryQueryCreators = CreateObject("component","import").getQueryCreators(siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
		<cfset local.qryLinkedRecordsQueryCreators = CreateObject("component","import").getLinkedRecordsQueryCreators(siteID=arguments.event.getValue('mc_siteInfo.siteID'))>

		<cfset local.filterKeyName = "PMIHistory">
		<cfset local.tmpStr = { "fTitle": '', "fIssuedBy": '', "fDateFrom": '#dateFormat(dateAdd('d',-14,now()),"m/d/yyyy")#', "fDateTo": '#dateFormat(now(),"m/d/yyyy")#' }>
		<cfset local.PMIHistoryFilter = application.objCommon.getToolFiltersData(keyname=local.filterKeyName, defaultFilterData=local.tmpStr)>
		<cfif len(local.PMIHistoryFilter.fDateFrom)>
			<cfset local.PMIHistoryFilter.fDateFrom = dateFormat(local.PMIHistoryFilter.fDateFrom,"m/d/yyyy")>
		</cfif>
		<cfif len(local.PMIHistoryFilter.fDateTo)>
			<cfset local.PMIHistoryFilter.fDateTo = dateFormat(local.PMIHistoryFilter.fDateTo,"m/d/yyyy")>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_massUpdateMembers.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="sampleImportTemplate" access="public" output="false" returntype="struct" hint="Sample Import Template">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = "The sample import file could not be generated. Contact MemberCentral for assistance.">
	
		<cfset local.ranCustom = false>
		<cfif arguments.event.getValue('mode','') eq 'custom' and FileExists(ExpandPath("model/admin/import/custom/#arguments.event.getValue('mc_siteinfo.orgcode')#.cfc"))>
			<cfset local.objCustomImport = CreateObject("component","model.admin.import.custom.#arguments.event.getValue('mc_siteinfo.orgcode')#")>
			<cfif isDefined("local.objCustomImport.sampleCustomImportTemplate")>
				<cfset local.ranCustom = true>
				<cfset local.data = local.objCustomImport.sampleCustomImportTemplate(event=arguments.event, listLink=this.link.list)>
			</cfif>
		</cfif>

		<cfif not local.ranCustom>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfset local.reportFileName = "sampleImportFile.csv">

			<cftry>
				<cfstoredproc procedure="ams_exportMemberDataTemplate" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\#local.reportFileName#">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('isPartial',0)#">
				</cfstoredproc>

				<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
				<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
				<cfif not local.docResult>
					<cflocation url="#this.link.list#" addtoken="no">
				</cfif>	
			<cfcatch type="Any">
				<cfif findNoCase("Query too large to export.",cfcatch.detail)>
					<cflocation url="#this.link.list#&err=TemplateQRYLARGE" addtoken="no">
				<cfelse>
					<cflocation url="#this.link.list#&err=TemplateUNK" addtoken="no">
				</cfif>
			</cfcatch>
			</cftry>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="processImport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objImport = CreateObject("component","model.admin.import.import");
		</cfscript>
		
		<!--- extend CF timeout --->
		<cfsetting requesttimeout="600">
		
		<!--- upload and process file --->
		<cfset local.data = "">
		<cfset local.ranCustom = false>
		<cfif arguments.event.getValue('mode','') eq 'custom' and FileExists(ExpandPath("model/admin/import/custom/#arguments.event.getValue('mc_siteinfo.orgcode')#.cfc"))>
			<cfset local.objCustomImport = CreateObject("component","model.admin.import.custom.#arguments.event.getValue('mc_siteinfo.orgcode')#")>
			<cfif isDefined("local.objCustomImport.processImport")>
				<cfset local.ranCustom = true>
				<cfset local.prepResult = local.objCustomImport.processImport(event=arguments.event)>
			</cfif>
		</cfif>
		<cfif not local.ranCustom>
			<cfset local.importTitle = "">
			<cfif arguments.event.valueExists('btnPartialUpload')>
				<cfset local.importTitle = "Manual Partial Update">
			<cfelseif arguments.event.valueExists('btnCompleteUpload')>
				<cfset local.importTitle = "Manual Full Membership Sync">
			</cfif>

			<cfset local.prepResult = local.objImport.processPartialMemberImport(orgid=arguments.event.getValue('mc_siteinfo.orgID'),
				importTitle=local.importTitle, orgcode=arguments.event.getValue('mc_siteinfo.orgCode'), autogenMemNum=arguments.event.getValue('autogenMemNum',0),
				activateIncMembers=arguments.event.getValue('incMemActive',0), inactivateNonIncMembers=arguments.event.getValue('nonIncMemInactive',0),
				ignoreInvalidColumns=arguments.event.getValue('ignoreInvalidCols',0), thresholdLimit=val(arguments.event.getValue('thresholdLimit',0)))>
		</cfif>
		
		<cfif structKeyExists(local,"prepResult")>
			<cfset local.importResults = local.objImport.showImportResults(strResult=local.prepResult, doAgainURL=this.link.list, 
				continueURL="#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mode=stream&mca_jsonlib=memberimport&mca_jsonfunc=continueImport", 
				cancelURL=this.link.cancelImport)>
			
			<cfreturn list(event=arguments.event, importResults=local.importResults)>
		<cfelse>
			<cfreturn list(event=arguments.event)>
		</cfif>
	</cffunction>

	<cffunction name="continueImport" access="public" output="false" returntype="string">
		<cfargument name="jobID" type="numeric" required="yes">
		<cfargument name="siteCode" type="string" required="yes">
		<cfargument name="logID" type="numeric" required="no" default="0">
		<cfargument name="referenceType" type="string" required="no" default="">
		<cfargument name="referenceID" type="number" required="no" default="0">

		<cfset var local = structNew()>
		<cfset local.objImport = CreateObject("component","model.admin.import.import")>
		<cfset local.strReturn = structNew()>
		
		<cfswitch expression="#arguments.referenceType#"> 
			<cfcase value="queryBuilder">
				<cfset local.cancelledMessage = "This update has been cancelled. Try again.">
				<cfset local.buttonLabel = "Return to Queries">
				<cfset local.buttonHandlerFn = "top.returnToQueriesFromImport">
			</cfcase>
			<cfcase value="linkedRecordsQueryBuilder">
				<cfset local.cancelledMessage = "This update has been cancelled. Try again.">
				<cfset local.buttonLabel = "Return to Linked Records Queries">
				<cfset local.buttonHandlerFn = "top.returnToLinkedRecordsQueriesFromImport">
			</cfcase>			
			<cfdefaultcase>
				<cfset local.cancelledMessage = "This update has been cancelled. Try the upload again.">
				<cfset local.buttonLabel = "Return to Uploads">
				<cfset local.buttonHandlerFn = "backToUploadChooser">
			</cfdefaultcase> 
		</cfswitch>

		<cfset local.jobExists = local.objImport.importJobExists(jobID=arguments.jobID)>
		<cfif NOT local.jobExists>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<h4>Member Update Cancelled</h4>
				<div class="alert alert-danger">#local.cancelledMessage#</div>
				<button type="button" name="btnDoOver" class="btn btn-sm btn-secondary" onclick="#local.buttonHandlerFn#();">#local.buttonLabel#</button>
				</cfoutput>
			</cfsavecontent>
			<cfset local.strReturn['success'] = true>
			<cfset local.strReturn['data'] = application.objCommon.minText(local.data)>
			<cfreturn SerializeJSON(local.strReturn)>
		</cfif>

		<cftry>
			<cfset local.success = local.objImport.continueImport(jobID=arguments.jobID, siteCode=arguments.siteCode, logID=arguments.logID)>
			<cfif arguments.referenceType eq "queryBuilder">
				<cfset local.objImport.insertQueryRunLog(queryID=arguments.referenceID)>
			<cfelseif arguments.referenceType eq "linkedRecordsQueryBuilder">
				<cfset local.objImport.insertLinkedRecordsQueryRunLog(queryID=arguments.referenceID)>				
			</cfif>
		<cfcatch type="Any">
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfif local.success>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<h4>Member Update Results</h4>
				<div class="alert alert-success">The update has been scheduled and will begin soon.</div>
				<button type="button" name="btnDoOver" class="btn btn-sm btn-secondary" onclick="#local.buttonHandlerFn#();">#local.buttonLabel#</button>
				</cfoutput>
			</cfsavecontent>
			<cfset local.strReturn['success'] = true>
			<cfset local.strReturn['data'] = application.objCommon.minText(local.data)>
		<cfelse>
			<cfset local.strReturn['success'] = false>
			<cfset local.strReturn['data'] = ''>
		</cfif>

		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="cancelImport" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset CreateObject("component","model.admin.import.import").cancelImport(jobID=arguments.event.getValue('jID',0), logID=arguments.event.getValue('lid',0))>
		
		<cflocation url="#this.link.list#" addtoken="no">
	</cffunction>

	<cffunction name="getReadyToProcessJobs" access="private" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">

		<cfset var qryReadyToProcessJobs = "">

		<cfquery name="qryReadyToProcessJobs" datasource="#application.dsn.platformQueue.dsn#">
			set nocount on;

			declare @orgID int, @statusReady int, @statusReadyDelete int, @queueStatus varchar(60);
			set @orgID = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">;
			EXEC dbo.queue_getStatusIDbyType @queueType='MemberImport', @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
			EXEC dbo.queue_getStatusIDbyType @queueType='memberDelete', @queueStatus='ReadyToProcess', @queueStatusID=@statusReadyDelete OUTPUT;

			select groupID, queueType, dateEntered, importTitle, queueStatus, numOfNewMembers, numOfExistingMembers, numOfDeletingMembers
			from (
				select cast(j.jobID as varchar(10)) as groupID, 'MemberImport' as queueType, j.dateEntered, mh.importTitle,
					case qs.queueStatus
						when 'ReadyToVerify' then 'Awaiting Verification'
						when 'ReadyToProcess' then 'Awaiting Processing'
						when 'done' then 'Completed'
						else qs.queueStatus
					end as queueStatus,
					(select count(m1.memberID) from dbo.memimport_members m1 where m1.jobID = j.jobID and m1.queueStatusID = qid.queueStatusID and m1.actualMemberID is null) as numOfNewMembers,
					(select count(m2.memberID) from dbo.memimport_members m2 where m2.jobID = j.jobID and m2.queueStatusID = qid.queueStatusID and m2.actualMemberID is not null) numOfExistingMembers,
					0 as numOfDeletingMembers
				from dbo.memimport_jobs as j
				inner join platformStatsMC.dbo.ams_memberImportHistory as mh on mh.jobID = j.jobID
				inner join dbo.memimport_members as qid on qid.jobID = j.jobID
				inner join dbo.tblQueueStatuses as qs on qs.queuestatusID = qid.queuestatusID
				where j.orgID = @orgID
				and j.statusID = @statusReady
				group by j.jobID, j.dateEntered, mh.importTitle, qid.queuestatusID, qs.queueStatus
					union
				select cast(itemGroupUID as varchar(60)) as groupID, 'memberDelete' as queueType, MIN(dateAdded) as dateEntered, 'Mass Member Delete' as importTitle, 'ReadyToProcess' as queueStatus,
					0 as numOfNewMembers, 0 as numOfExistingMembers, count(*) as numOfDeletingMembers
				from platformQueue.dbo.queue_memberDelete
				where orgID = @orgID
				and statusID = @statusReadyDelete
				group by itemGroupUID
			) tmp
			order by dateEntered, groupID;
		</cfquery>

		<cfreturn qryReadyToProcessJobs>
	</cffunction>

	<cffunction name="getIssuedBy" access="private" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">

		<cfset var qryMembers = "">

		<!--- ignore single member updates --->
		<cfquery name="qryMembers" datasource="#application.dsn.platformStatsMC.dsn#">
			select distinct mih.runByMemberID, m.firstName + ' ' + m.lastName as memberName
			from dbo.ams_memberImportHistory as mih 
			inner join membercentral.dbo.ams_members as m on m.memberID = mih.runByMemberID
			where mih.orgID = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">
			and mih.numSubmitted > 1
			order by memberName;
		</cfquery>

		<cfreturn qryMembers>
	</cffunction>

	<cffunction name="getTitles" access="private" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery name="local.qryTitles" datasource="#application.dsn.platformStatsMC.dsn#">
			select distinct importTitle
			from dbo.ams_memberImportHistory
			where orgID = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">
			and importTitle <> 'Save Member via API'
			order by importTitle;
		</cfquery>

		<cfset local.strReturn = { qryTitles=local.qryTitles }>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="sampleMassUpdateMemberNumberTemplate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.data = "The sample import file could not be generated. Contact MemberCentral for assistance.">
		
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteinfo.siteCode'))>
		<cfset local.reportFileName = "MassUpdateMemberNumberTemplate.csv">

		<cfset local.fieldNames = "MemberNumber,NewMemberNumber">
		<cfset local.arrFields = listToArray(local.fieldNames)>

		<cfquery name="local.qryExport" datasource="#application.dsn.membercentral.dsn#" result="local.qryExportResult">
			set nocount on;

			IF OBJECT_ID('tempdb..##tmpImportTemplate') IS NOT NULL 
				DROP TABLE ##tmpImportTemplate;
			CREATE TABLE ##tmpImportTemplate (
				autoID int
				<cfloop array="#local.arrFields#" index="local.thisCol">
					, #local.thisCol# varchar(30)
				</cfloop>
			);
			
			insert into ##tmpImportTemplate (#local.fieldNames#)
			values ('Req', 'Req');
			
			DECLARE @selectsql varchar(max) = '
				SELECT #local.fieldNames#, 1 as mcCSVorder 
				*FROM* ##tmpImportTemplate';
			EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

			IF OBJECT_ID('tempdb..##tmpImportTemplate') IS NOT NULL 
				DROP TABLE ##tmpImportTemplate;
		</cfquery>
				
		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="sampleMassDeleteMembersTemplate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.data = "The sample import file could not be generated. Contact MemberCentral for assistance.">
		
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteinfo.siteCode'))>
		<cfset local.reportFileName = "MassDeleteMembersTemplate.csv">

		<cfquery name="local.qryExport" datasource="#application.dsn.membercentral.dsn#" result="local.qryExportResult">
			SET NOCOUNT ON;

			IF OBJECT_ID('tempdb..##tmpImportTemplate') IS NOT NULL 
				DROP TABLE ##tmpImportTemplate;
			CREATE TABLE ##tmpImportTemplate (autoID INT, MemberNumber VARCHAR(30));
			
			INSERT INTO ##tmpImportTemplate (MemberNumber) VALUES ('Req');
			
			DECLARE @selectsql varchar(max) = 'SELECT MemberNumber, 1 AS mcCSVorder *FROM* ##tmpImportTemplate';
			EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

			IF OBJECT_ID('tempdb..##tmpImportTemplate') IS NOT NULL
				DROP TABLE ##tmpImportTemplate;
		</cfquery>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processMassUpdateMemberNumbers" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objImport = CreateObject("component","model.admin.import.import");
		</cfscript>
		
		<!--- extend CF timeout --->
		<cfsetting requesttimeout="600">
		<cfset local.prepResult = local.objImport.processMassUpdateMemberNumbers(orgid=arguments.event.getValue('mc_siteinfo.orgid'),
				siteid=arguments.event.getValue('mc_siteinfo.siteid'), orgcode=arguments.event.getValue('mc_siteinfo.orgCode'))>

		<cfset local.importResults = local.objImport.showImportResults(strResult=local.prepResult, doAgainURL=this.link.list, 
				continueURL="#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mode=stream&mca_jsonlib=memberimport&mca_jsonfunc=continueImport",
				cancelURL=this.link.cancelImport)>

		<cfreturn list(event=arguments.event, importResults=local.importResults)>
	</cffunction>

	<cffunction name="processMassDeleteMembers" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objImport = CreateObject("component","model.admin.import.import");
		</cfscript>
		
		<!--- extend CF timeout --->
		<cfsetting requesttimeout="600">
		<cfset local.prepResult = local.objImport.processMassDeleteMembers(orgID=arguments.event.getValue('mc_siteinfo.orgID'),
				siteID=arguments.event.getValue('mc_siteinfo.siteID'), orgCode=arguments.event.getValue('mc_siteinfo.orgCode'))>

		<cfif NOT local.prepResult.success>
			<cfsavecontent variable="local.importResults">
				<cfoutput>
				<h4>Mass Member Delete Issue Report</h4>
				<div class="alert alert-danger">
					<div class="mb-3"><b>The update was stopped and requires your attention.</b></div>
					<cfif local.prepResult.errorCode eq 105 and structKeyExists(local.prepResult, "importResultXML")>
						<cfset local.arrErrors = XMLSearch(local.prepResult.importResultXML,"/import/errors/error")>
						<div>
						<cfif arrayLen(local.arrErrors) gt 300>
							<b>Only the first 300 errors are shown.</b><br/><br/>
						</cfif>
						<cfset local.thisErrNum = 0>
						<cfloop array="#local.arrErrors#" index="local.thisErr">
							<cfset local.thisErrNum = local.thisErrNum + 1>
							#local.thisErr.xmlAttributes.msg#<br/>
							<cfif local.thisErrNum is 300>
								<cfbreak>
							</cfif>
						</cfloop>
						</div>
					<cfelse>
						<div>#local.prepResult.errorInfo[local.prepResult.errorCode]#</div>
					</cfif>
					<button type="button" name="btnDoOver" class="btn btn-secondary btn-sm mt-3" onclick="backToUploadChooser();">Try upload again</button>
				</div>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.importResults">
				<cfoutput>
				<h4>Mass Member Delete Results</h4>
				<div class="alert alert-success">The member delete has been scheduled and will begin soon.</div>
				<button type="button" name="btnDoOver" class="btn btn-sm btn-secondary" onclick="backToUploadChooser();">Return to Uploads</button>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn list(event=arguments.event, importResults=local.importResults)>
	</cffunction>

	<cffunction name="loadQueryBuilder" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.objImport = CreateObject("component","import")>
		<cfset local.strImportFields = local.objImport.getImportFields(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfset local.qrySavedQuery = local.objImport.getSavedQuery(queryID=val(arguments.event.getValue('queryID',0)))>
		<cfset local.arrSavedFields = local.objImport.getSavedQueriesFields(queryID=val(arguments.event.getValue('queryID',0))).arrfields>
		<cfset local.qryScheduledTaskIntervalTypes = CreateObject("component","model.scheduledTasks.scheduledTasks").getScheduledTaskIntervalTypes()>
		<cfset local.qryOrgProLicenses = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>

		<cfset local.editVGCLink = buildLinkToTool(toolType='VirtualGroup',mca_ta='editCondition') & "&mode=direct">

		<cfset local.interval = val(local.qrySavedQuery.interval) GT 0 ? local.qrySavedQuery.interval : 1>
		<cfset local.intervalTypeID = local.qrySavedQuery.intervalTypeID>
		<cfif NOT val(local.qrySavedQuery.intervalTypeID)>
			<cfset local.intervalTypeID = local.qryScheduledTaskIntervalTypes.filter(function(thisRow){ return arguments.thisRow.name EQ 'days' }).intervalTypeID>
		</cfif>
		<cfset local.nextRunDate = len(local.qrySavedQuery.nextRunDate) 
							? DateTimeFormat(local.qrySavedQuery.nextRunDate,'m/d/yyyy - h:nn tt') 
							: DateFormat(DateAdd("d",1,now()),"m/d/yyyy") & " - 6:00 AM">
		
		<cfset local.endRunDate = len(local.qrySavedQuery.endRunDate) 
							? DateTimeFormat(local.qrySavedQuery.endRunDate,'m/d/yyyy - h:nn tt') 
							: "">

		<cfquery name="local.arrImportFields" dbtype="query" returntype="array">
			SELECT columnName, columnDataType, max_length, allowMultiple, area, areaID, viewSuffix, viewAlias, 
				defaultValueID, MDdataTypeCode, MDdisplayTypeCode, allowNewValuesOnImport, isRequired, allowNull
			FROM [local].strImportFields.qryFields
		</cfquery>

		<cfquery name="local.arrImportFieldValues" dbtype="query" returntype="array">
			SELECT columnName, columnValue
			FROM [local].strImportFields.qryFieldValues
			ORDER BY columnName, columnValue
		</cfquery>

		<cfquery name="local.arrProLicenses" dbtype="query" returntype="array">
			SELECT PLTypeID, PLName
			FROM [local].qryOrgProLicenses
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_queryBuilder.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processMassUpdateMembersByQuery" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objImport = CreateObject("component","model.admin.import.import");

		// Security --------------------------------------------------------------------------------- ::
		local.security.manage = checkRights(arguments.event,'manageImport');

		if (NOT local.security.manage)
			return returnAppStruct("No Rights.","echo");

		local.ruleID = int(val(arguments.event.getValue('mcqb_ruleID',0)));
		// if rule has no conditions, dont run rule.
		local.objVGRAdmin = CreateObject("component","model.admin.virtualGroups.virtualGroupsAdmin");
		local.numConditions = local.objVGRAdmin.countRuleConditions(ruleID=local.ruleID);
		if (NOT local.numConditions) {
			local.ruleErr = true;
		} else {
			// ensure rule is active and cache refreshed. show error if necessary
			local.msg = local.objVGRAdmin.activateRule(ruleID=local.ruleID, forceCache=true);
			if (local.msg is not 0) local.ruleErr = true;
			else local.ruleErr = false;
		}

		if (local.ruleErr)
			return returnAppStruct("We were unable to identify members based on the current member filter.","echo");
		</cfscript>

		<cfset local.strImportFields = local.objImport.getImportFields(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>

		<cfset local.arrQueryFields = arrayNew(1)>
		<cfloop from="1" to="#arguments.event.getValue('mcqb_finalFieldIndex')#" index="local.i">
			<cfif arguments.event.valueExists('queryBuilderField_#local.i#') AND len(arguments.event.getTrimValue('queryBuilderField_#local.i#'))>
				<cfset var thisColumnName = arguments.event.getTrimValue('queryBuilderField_#local.i#')>
				<cfset local.qryThisField = QueryFilter(local.strImportFields.qryFields, 
											function(row) { 
												return arguments.row.columnName EQ thisColumnName;
											})>

				<cfif local.qryThisField.recordCount>
					<cfset local.tmpStr = { "columnName":thisColumnName, "columnValue":arguments.event.getTrimValue('queryBuilderFieldVal_#local.i#','') }>

					<cfif local.qryThisField.allowMultiple>
						<cfset local.tmpStr.columnValue = local.tmpStr.columnValue.replaceAll(',','|')>
						<cfif arguments.event.valueExists('queryBuilderFieldOptionalVal_#local.i#') AND len(arguments.event.getTrimValue('queryBuilderFieldOptionalVal_#local.i#'))>
							<cfset local.tmpStr.columnValue = listAppend(local.tmpStr.columnValue,arguments.event.getTrimValue('queryBuilderFieldOptionalVal_#local.i#'),'|')>
						</cfif>
					</cfif>
				</cfif>

				<cfset arrayAppend(local.arrQueryFields,local.tmpStr)>
			</cfif>
		</cfloop>

		<cfif NOT arrayLen(local.arrQueryFields)>
			<cfthrow message="There are no fields selected for the update.">
		</cfif>
		
		<!--- extend CF timeout --->
		<cfsetting requesttimeout="600">

		<cfset local.queryID = arguments.event.getValue('mcqb_queryID',0)>

		<cfset local.prepResult = local.objImport.processMassUpdateMembersByQuery(orgID=arguments.event.getValue('mc_siteinfo.orgid'),
				queryID=local.queryID, queryName=arguments.event.getValue('mcqb_queryName',''), isRecurringUpdate=arguments.event.getValue('recurringUpdate',0),
				schedInterval=arguments.event.getValue('schedQueryInterval',0), schedIntervalTypeID=arguments.event.getValue('schedQueryIntervalTypeID',0),
				schedNextRunDate=arguments.event.getValue('schedQueryNextRunDate',''), schedEndRunDate=arguments.event.getValue('schedQueryEndRunDate',''), arrQueryFields=local.arrQueryFields)>

		<cfset local.importResults = local.objImport.showImportResults(strResult=local.prepResult, doAgainURL="", 
				continueURL="#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mode=stream&mca_jsonlib=memberimport&mca_jsonfunc=continueImport&referenceType=queryBuilder&referenceID=#local.queryID#",
				cancelURL="", importMode="queryBuilder")>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<div id="uploadResultScreen" class="p-3">#local.importResults#</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="loadLinkedRecordsQueryBuilder" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif NOT application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfreturn returnAppStruct("No Rights.","echo")>
		</cfif>

		<cfset local.objImport = CreateObject("component","import")>
		<cfset local.objFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector")/>
		<cfset local.qrySavedLinkedRecordsQuery = local.objImport.getSavedLinkedRecordsQuery(queryID=val(arguments.event.getValue('queryID',0)))>
		<cfset local.qryScheduledTaskIntervalTypes = CreateObject("component","model.scheduledTasks.scheduledTasks").getScheduledTaskIntervalTypes()>
		<cfset local.qryOrgProLicenses = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>

		<cfset local.editVGCLink = buildLinkToTool(toolType='VirtualGroup',mca_ta='editCondition') & "&mode=direct">
		<cfset local.strLinkedRecordsQueryFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=arguments.event.getValue('mc_siteinfo.siteID'), selectorID="linkedRecordsQueryFieldSet", selectedValue=val(local.qrySavedLinkedRecordsQuery.fieldSetID), allowBlankOption=false)>
		
		<cfset local.interval = val(local.qrySavedLinkedRecordsQuery.interval) GT 0 ? local.qrySavedLinkedRecordsQuery.interval : 1>
		<cfset local.intervalTypeID = local.qrySavedLinkedRecordsQuery.intervalTypeID>
		<cfif NOT val(local.qrySavedLinkedRecordsQuery.intervalTypeID)>
			<cfset local.intervalTypeID = local.qryScheduledTaskIntervalTypes.filter(function(thisRow){ return arguments.thisRow.name EQ 'days' }).intervalTypeID>
		</cfif>
		<cfset local.nextRunDate = len(local.qrySavedLinkedRecordsQuery.nextRunDate) 
							? DateTimeFormat(local.qrySavedLinkedRecordsQuery.nextRunDate,'m/d/yyyy - h:nn tt') 
							: DateFormat(DateAdd("d",1,now()),"m/d/yyyy") & " - 6:00 AM">
							
		<cfset local.endRunDate = len(local.qrySavedLinkedRecordsQuery.endRunDate) 
							? DateTimeFormat(local.qrySavedLinkedRecordsQuery.endRunDate,'m/d/yyyy - h:nn tt') 
							: "">

		<cfquery name="local.arrProLicenses" dbtype="query" returntype="array">
			SELECT PLTypeID, PLName
			FROM [local].qryOrgProLicenses
		</cfquery>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_linkedRecordsQueryBuilder.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processMassUpdateMembersByLinkedRecordsQuery" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objImport = CreateObject("component","model.admin.import.import");

		// Security --------------------------------------------------------------------------------- ::
		local.security.manage = checkRights(arguments.event,'manageImport');

		if (NOT (local.security.manage AND application.objUser.isSuperUser(cfcuser=session.cfcuser)))
			return returnAppStruct("No Rights.","echo");

		local.objVGRAdmin = CreateObject("component","model.admin.virtualGroups.virtualGroupsAdmin");

		local.orgRuleID = int(val(arguments.event.getValue('mcqb_orgRuleID',0)));
		local.ruleErr = true;
		// if rule has no conditions, dont run rule.		
		local.numOrgConditions = local.objVGRAdmin.countRuleConditions(ruleID=local.orgRuleID);
		if (NOT local.numOrgConditions) {
			local.ruleErr = true;
		} else {
			// ensure rule is active and cache refreshed. show error if necessary
			local.msg = local.objVGRAdmin.activateRule(ruleID=local.orgRuleID, forceCache=true);
			if (local.msg is not 0) local.ruleErr = true;
			else local.ruleErr = false;
		}

		if (local.ruleErr)
			return returnAppStruct("We were unable to identify the members based on the current member filter.","echo");
		
		local.indRuleID = int(val(arguments.event.getValue('mcqb_indRuleID',0)));
		local.ruleErr = true;
		// if rule has no conditions, dont run rule.		
		local.numIndConditions = local.objVGRAdmin.countRuleConditions(ruleID=local.indRuleID);
		if (NOT local.numIndConditions) {
			local.ruleErr = true;
		} else {
			// ensure rule is active and cache refreshed. show error if necessary
			local.msg = local.objVGRAdmin.activateRule(ruleID=local.indRuleID, forceCache=true);
			if (local.msg is not 0) local.ruleErr = true;
			else local.ruleErr = false;
		}

		if (local.ruleErr)
			return returnAppStruct("We were unable to identify the members based on the current member filter.","echo");
		</cfscript>

		<cfif NOT val(arguments.event.getValue('linkedRecordsQueryFieldSet'))>
			<cfthrow message="There is no field set selected for the update.">
		</cfif>

		<cfset local.strImportFields = local.objImport.getImportFields(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>

		<cfset local.arrQueryFields = arrayNew(1)>
		<cfloop from="1" to="#arguments.event.getValue('mcqb_finalFieldIndex')#" index="local.i">
			<cfif arguments.event.valueExists('linkedRecordsQueryBuilderField_#local.i#') AND len(arguments.event.getTrimValue('linkedRecordsQueryBuilderField_#local.i#'))>
				<cfset var thisColumnName = arguments.event.getTrimValue('linkedRecordsQueryBuilderField_#local.i#')>
				<cfset local.qryThisField = QueryFilter(local.strImportFields.qryFields, 
											function(row) { 
												return arguments.row.columnName EQ thisColumnName;
											})>

				<cfif local.qryThisField.recordCount>
					<cfset local.tmpStr = { "columnName":thisColumnName, "columnValue":arguments.event.getTrimValue('linkedRecordsQueryBuilderFieldVal_#local.i#','') }>

					<cfif local.qryThisField.allowMultiple>
						<cfset local.tmpStr.columnValue = local.tmpStr.columnValue.replaceAll(',','|')>
						<cfif arguments.event.valueExists('linkedRecordsQueryBuilderFieldOptionalVal_#local.i#') AND len(arguments.event.getTrimValue('linkedRecordsQueryBuilderFieldOptionalVal_#local.i#'))>
							<cfset local.tmpStr.columnValue = listAppend(local.tmpStr.columnValue,arguments.event.getTrimValue('linkedRecordsQueryBuilderFieldOptionalVal_#local.i#'),'|')>
						</cfif>
					</cfif>
				</cfif>

				<cfset arrayAppend(local.arrQueryFields,local.tmpStr)>
			</cfif>
		</cfloop>

		<!--- extend CF timeout --->
		<cfsetting requesttimeout="600">

		<cfset local.queryID = arguments.event.getValue('mcqb_queryID',0)>

		<cfset local.prepResult = local.objImport.processMassUpdateMembersByLinkedRecordsQuery(orgID=arguments.event.getValue('mc_siteinfo.orgid'), 
			queryID=local.queryID, queryName=arguments.event.getValue('mcqb_queryName',''), fieldSetID=val(arguments.event.getValue('linkedRecordsQueryFieldSet')),
			arrQueryFields=local.arrQueryFields, isRecurringUpdate=arguments.event.getValue('recurringUpdate',0), schedInterval=arguments.event.getValue('schedQueryInterval',0), 
			schedIntervalTypeID=arguments.event.getValue('schedQueryIntervalTypeID',0), schedNextRunDate=arguments.event.getValue('schedQueryNextRunDate',''), schedEndRunDate=arguments.event.getValue('schedQueryEndRunDate',''))>

		<cfset local.importResults = local.objImport.showImportResults(strResult=local.prepResult, doAgainURL="", 
			continueURL="#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mode=stream&mca_jsonlib=memberimport&mca_jsonfunc=continueImport&referenceType=linkedRecordsQueryBuilder&referenceID=#local.queryID#",
			cancelURL="", importMode="linkedRecordsQueryBuilder")>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<div id="uploadResultScreen" class="p-3">#local.importResults#</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

</cfcomponent>