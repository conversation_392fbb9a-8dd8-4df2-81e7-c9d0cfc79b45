<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>

<cfsavecontent variable="local.js">
	<cfoutput>
	<script type="text/javascript" src="/assets/admin/javascript/manageSubscribers.js#local.assetCachingKey#"></script>
	<script language="javascript">
		let subsListTable, checkAllSubs = 1, arrUnchkedSubs = [], arrChkedSubs = [], totalSubsCount=0;
		const mc_subStatusBtns = {
			P: [
				{ label: "Export Subscribers", onclick: "startExportSubs();", title: "Click to export subscriptions.", icon: "fa-regular fa-file-export" },
				{ label: "Expire Subscriptions", onclick: "checkMarkExpired();", title: "Click to expire subscriptions.", icon: "fa-regular fa-hourglass-end" },
				{ label: "Generate Subscriptions", onclick: "startGenSub();", title: "Click to generate subscriptions.", icon: "fa-regular fa-list-check" },
				{ label: "Change Grace End Dates", onclick: "massUpdateGraceEndDate();", title: "Click to mass change grace end dates.", icon: "fa-regular fa-calendar-day" }
			],
			A: [
				{ label: "Export Subscribers", onclick: "startExportSubs();", title: "Click to export subscriptions.", icon: "fa-regular fa-file-export" },
				{ label: "Expire Subscriptions", onclick: "checkMarkExpired();", title: "Click to expire subscriptions.", icon: "fa-regular fa-hourglass-end" },
				{ label: "Mark as Inactive", onclick: "checkMarkInactive();", title: "Click to mark subscriptions as inactive.", icon: "fa-regular fa-user-slash" },
				{ label: "Generate Subscriptions", onclick: "startGenSub();", title: "Click to generate subscriptions.", icon: "fa-regular fa-list-check" },
				{ label: "Change Grace End Dates", onclick: "massUpdateGraceEndDate();", title: "Click to mass change grace end dates.", icon: "fa-regular fa-clock-rotate-left" }
			],
			I: [
				{ label: "Export Subscribers", onclick: "startExportSubs();", title: "Click to export subscriptions.", icon: "fa-regular fa-file-export" },
				{ label: "Expire Subscriptions", onclick: "checkMarkExpired();", title: "Click to expire subscriptions.", icon: "fa-regular fa-hourglass-end" },
				{ label: "Mark as Active", onclick: "checkMarkActive();", title: "Click to mark subscriptions as active.", icon: "fa-regular fa-user-check" },
				{ label: "Generate Subscriptions", onclick: "startGenSub();", title: "Click to generate subscriptions.", icon: "fa-regular fa-list-check" }
			],
			E: [
				{ label: "Export Subscribers", onclick: "startExportSubs();", title: "Click to export subscriptions.", icon: "fa-regular fa-file-export" },
				{ label: "Generate Subscriptions", onclick: "startGenSub();", title: "Click to generate subscriptions.", icon: "fa-regular fa-list-check" },
				{ label: "Remove Payment Method", onclick: "removePaymentMethods();", title: "Click to remove payment methods.", icon: "fa-regular fa-credit-card" }
			],
			R: [
				{ label: "Export Subscribers", onclick: "startExportSubs();", title: "Click to export subscriptions.", icon: "fa-regular fa-file-export" },
				{ label: "Mark as Billed", onclick: "checkMarkBilled();", title: "Click to mark subscriptions as billed.", icon: "fa-regular fa-file-invoice-dollar" },
				{ label: "Add Subscription", onclick: "startAddSub(1);", title: "Click to add subscription.", icon: "fa-regular fa-plus-circle" },
				{ label: "Change Grace End Dates", onclick: "massUpdateGraceEndDate();", title: "Click to mass change grace end dates.", icon: "fa-regular fa-clock-rotate-left" },
				{ label: "Delete Renewals", onclick: "deleteRenewals();", title: "Click to delete selected renewals.", icon: "fa-regular fa-trash-alt" },
				{ label: "Remove Payment Method", onclick: "removePaymentMethods();", title: "Click to remove payment methods.", icon: "fa-regular fa-credit-card" },
				{ label: "Remove Addons", onclick: "removeAddOn();", title: "Click to remove addons.", icon: "fa-regular fa-trash-alt" }
			],
			O: [
				{ label: "Export Subscribers", onclick: "startExportSubs();", title: "Click to export subscriptions.", icon: "fa-regular fa-file-export" },
				{ label: "Send Email", onclick: "checkSendOffers();", title: "Click to remove addons.", icon: "fa-regular fa-envelope" },
				{ label: "Add Subscription", onclick: "startAddSub(0);", title: "Click to add subscription.", icon: "fa-regular fa-plus-circle" },
				{ label: "Mark as Accepted", onclick: "checkMarkAccepted();", title: "Click to mark subscriptions as accepted.", icon: "fa-regular fa-check-circle" },
				{ label: "Delete Selected Renewals", onclick: "deleteRenewals();", title: "Click to delete selected renewals.", icon: "fa-regular fa-trash-alt" },
				{ label: "Remove Payment Method", onclick: "removePaymentMethods();", title: "Click to remove payment methods.", icon: "fa-regular fa-credit-card" },
				{ label: "Remove Addons", onclick: "removeAddOn();", title: "Click to remove addons.", icon: "fa-regular fa-trash-alt" },
				{ label: "Change Grace End Dates", onclick: "massUpdateGraceEndDate();", title: "Click to mass change grace end dates.", icon: "fa-regular fa-clock-rotate-left" },
				{ label: "Change Offer Expiration Date", onclick: "changeOfferExpirationDate();", title: "Click to mass change offer expiration dates.", icon: "fa-regular fa-calendar-times" }
			],
			X: [
				{ label: "Export Subscribers", onclick: "startExportSubs();", title: "Click to export subscriptions.", icon: "fa-regular fa-file-export" },
				{ label: "Remove Payment Method", onclick: "removePaymentMethods();", title: "Click to remove payment methods.", icon: "fa-regular fa-credit-card" }
			],
			D: [
				{ label: "Export Subscribers", onclick: "startExportSubs();", title: "Click to export subscriptions.", icon: "fa-regular fa-file-export" },
				{ label: "Remove Payment Method", onclick: "removePaymentMethods();", title: "Click to remove payment methods.", icon: "fa-regular fa-credit-card" }
			],
			ALL: [
				{ label: "Export Subscribers", onclick: "startExportSubs();", title: "Click to export subscriptions.", icon: "fa-regular fa-file-export" }
			],
			default: []
		};

		Handlebars.registerHelper('renderSubActBtns', function(buttons, options) {
			let result = '';
			const maxVisibleButtons = 15;

			if (buttons.length <= maxVisibleButtons+1) {
				buttons.forEach(btn => {
					result += options.fn(btn);
				});
			} else {					
				for (let i = 0; i < maxVisibleButtons; i++) {
					result += options.fn(buttons[i]);
				}
				
				result += `<div class="dropdown ml-2">
						<button class="btn btn-link dropdown-toggle p-0" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
							<i class="fa-regular fa-cog"></i> More Actions
						</button>
						<div class="dropdown-menu">`;
				for (let i = maxVisibleButtons; i < buttons.length; i++) {
					const btn = buttons[i];
					result += `<a class="dropdown-item" href="##" onclick="${btn.onclick}" title="${btn.title}">
							<i class="${btn.icon} mr-1"></i> ${btn.label}
						</a>`;
				}
				result += `</div></div>`;
			}

			return new Handlebars.SafeString(result);
		});
		
		var #ToScript(local.subsListLink,'subsListLink')#
		var #ToScript(this.link.listReports,"link_listReports")#
		var #ToScript(this.link.startExportSubscriptions,"link_exportSubs")#
		var #ToScript(this.link.startMarkExpired,"link_startMarkExpired")#
		var #ToScript(this.link.startGenerateSub,"link_startGenerateSub")#
		var #ToScript(this.link.startMassUpdateGraceEndDate,"link_startUpdateGraceEndDate")#
		var #ToScript(this.link.startMarkInactive,"link_startMarkInactive")#
		var #ToScript(this.link.startMarkActive,"link_startMarkActive")#
		var #ToScript(this.link.startMarkBilled,"link_startMarkBilled")#
		var #ToScript(this.link.startForceAddSub,"link_startForceAddSub")#
		var #ToScript(this.link.startDeleteRenewals,"link_startDeleteRenewals")#
		var #ToScript(this.link.removePaymentMethod,"link_removePaymentMethod")#
		var #ToScript(this.link.startRemoveAddons,"link_startRemoveAddons")#
		var #ToScript(this.link.startGenerateOffers,"link_startGenerateOffers")#
		var #ToScript(this.link.startMarkAccepted,"link_startMarkAccepted")#
		var #ToScript(this.link.startUpdateOfferExpirationDate,"link_startUpdateOfferExpirationDate")#
		var #ToScript(local.subRenewalLink,"link_subRenewal")#
		var #ToScript(this.link.dspRemovePaymentMethod,"link_removePaymethod")#
		var #ToScript(local.listAuditRptLink,"link_listAuditRpt")#
		var #ToScript(local.subAssocCC,"mcma_link_subassoc")#
		var #ToScript(local.showPaperStatementsForm,"mcma_link_generatePaperStatement")#
		var #ToScript(local.subLink,"mcma_link_sub")#
		var #ToScript(local.subConfirmRenewLink,"mcma_link_confirmrenew")#
		var #ToScript(local.linkAcceptSubscription,"linkAcceptSubscription")#
		var #ToScript(local.myRightsTransactionsAdmin.transAllocatePayment,"mcma_hasrights_allocpmt")#
		var #ToScript(local.startGenerateOffers,"mcma_link_startgenoffers")#
		var #ToScript(local.cleanupInvoicesLink,"mcma_link_cleanupinv")#
		var #ToScript(local.subExpireLink,"mcma_link_expiresub")#
		var #ToScript(local.subRemoveLink,"mcma_link_removesub")#
		var #ToScript(local.addPaymentURL,"mca_link_addpmt")#
		var #ToScript(local.allocatePaymentURL,"mca_link_allocpmt")#
		var #ToScript(local.refundPaymentURL,"mcma_link_refpayment")#

		async function initSubsTab() {
			<cfif local.SubReportFilter.listFilter.fSubType gt 0>
				await mca_callChainedSelect('fSubType', 'fSubscription', link_adminHomeResource, 'subs', 'getSubscriptionsForSubType', 'typeid', #local.SubReportFilter.listFilter.fSubscription#, false, false);
				<cfif local.SubReportFilter.listFilter.fSubscription gt 0>
					await mca_callChainedSelect('fSubscription', 'fRate', link_adminHomeResource, 'subs', 'getSubRatesForSub', 'subid', '#local.SubReportFilter.listFilter.fRate#', true, false, [{name:'data-isrenewalrate', datafield:'isrenewalrate'}]);
				</cfif>
			</cfif>

			prepSubsFilterForm();
			initSubsTable();
		}
	</script>
	<style type="text/css">
		.mcModalBodyCustom { min-height:100px; }
		##subsListTable tbody .actionTool a i { font-size: 13px !important; width: 15px !important; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.js)#">

<cfoutput>
<div id="subsButtonBar" class="toolButtonBar"></div>

<div id="divFilterSubsForm" class="mb-3" style="display:none;">
	<form name="frmFilter" id="frmFilter" onsubmit="filterSubGrid();return false;" data-filterwrapper="divFilterSubsForm" data-verbosemsgwrapper="divSubFilterVerbose" data-customverbose-radio="generateCustomSubRadioFilterVerbose" data-filterkey="listfilter">
		<input type="hidden" name="fChkAll" id="fChkAll" value="#local.checkAll#">
		<input type="hidden" name="fChkedSubs" id="fChkedSubs" value="">
		<input type="hidden" name="fUnchkedSubs" id="fUnchkedSubs" value="">
		<div class="card card-box mb-1">
			<div class="card-header py-1 bg-light">
				<div class="card-header--title font-weight-bold font-size-md">
					Filter Subscribers
				</div>
			</div>
			<div class="card-body">
				<div class="row">
					<div class="col-xl-6 col-lg-12">
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fSubType" id="fSubType" class="form-control">
									<option value="0">All Subscription Types</option>
									<cfloop query="local.qrySubTypes">
										<option value="#local.qrySubTypes.typeID#"<cfif local.SubReportFilter.listFilter.fSubType EQ local.qrySubTypes.typeID> selected</cfif>>#local.qrySubTypes.typeName#</option>
									</cfloop>
								</select>
								<label for="fSubType">Subscription Type</label>
							</div>
						</div>
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fSubscription" id="fSubscription" class="form-control">
									<option value="0">All Subscriptions</option>
								</select>
								<label for="fSubscription">Subscription</label>
							</div>
						</div>
						<div class="form-group">
							<div class="d-flex align-items-center mb-1">
								<span class="text-grey small mx-1">Quickly Select: </span>
								<a href="javascript:quickSelectSubRates('fRate',0);" class="badge badge-neutral-second text-second mr-1">Join Rates</a>
								<a href="javascript:quickSelectSubRates('fRate',1);" class="badge badge-neutral-second text-second">Renewal Rates</a>
							</div>
							<div class="form-label-group mb-2">
								<select name="fRate" id="fRate" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2"></select>
								<label for="fRate">Rate</label>
							</div>
						</div>
						<div class="form-row">
							<div class="col">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="fTermStartFrom" id="fTermStartFrom" value="#local.SubReportFilter.listFilter.fTermStartFrom#" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fTermStartFrom"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermStartFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="fTermStartFrom">Start Date From</label>
										</div>
									</div>
								</div>
							</div>
							<div class="col">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="fTermStartTo" id="fTermStartTo" value="#local.SubReportFilter.listFilter.fTermStartTo#" size="16" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fTermStartTo"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermStartTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="fTermStartTo">Start Date To</label>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="form-row">
							<div class="col">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="fTermEndFrom" id="fTermEndFrom" value="#local.SubReportFilter.listFilter.fTermEndFrom#" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fTermEndFrom"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermEndFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="fTermEndFrom">End Date From</label>
										</div>
									</div>
								</div>
							</div>
							<div class="col">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="fTermEndTo" id="fTermEndTo" value="#local.SubReportFilter.listFilter.fTermEndTo#" size="16" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fTermEndTo"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermEndTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="fTermEndTo">End Date To</label>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="form-row offrExpWrap<cfif local.SubReportFilter.listFilter.fSubStatus neq 'O'> d-none</cfif>">
							<div class="col">
								<div class="form-label-group mb-2">
									<div class="input-group dateFieldHolder">
										<input type="text" name="fOffrExpFrom" id="fOffrExpFrom" value="#local.SubReportFilter.listFilter.fOffrExpFrom#" class="form-control dateControl">
										<div class="input-group-append">
											<span class="input-group-text cursor-pointer calendar-button" data-target="fOffrExpFrom"><i class="fa-solid fa-calendar"></i></span>
											<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fOffrExpFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
										</div>
										<label for="fOffrExpFrom">Offer Expiration From</label>
									</div>
								</div>
							</div>
							<div class="col">
								<div class="form-label-group mb-2">
									<div class="input-group dateFieldHolder">
										<input type="text" name="fOffrExpTo" id="fOffrExpTo" value="#local.SubReportFilter.listFilter.fOffrExpTo#" size="16" class="form-control dateControl">
										<div class="input-group-append">
											<span class="input-group-text cursor-pointer calendar-button" data-target="fOffrExpTo"><i class="fa-solid fa-calendar"></i></span>
											<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fOffrExpTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
										</div>
										<label for="fOffrExpTo">Offer Expiration To</label>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="col-xl-6 col-lg-12">
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fSubStatus" id="fSubStatus" class="form-control">
									<option value="0">All Statuses</option>
									<cfloop query="local.qryStatuses">
										<option value="#local.qryStatuses.statusCode#"<cfif local.SubReportFilter.listFilter.fSubStatus eq local.qryStatuses.statusCode> selected</cfif>>#local.qryStatuses.statusName#</option>
									</cfloop>
								</select>
								<label for="fSubStatus">Status</label>
							</div>
						</div>
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fSubPaymentStatus" id="fSubPaymentStatus" class="form-control">
									<option value="0">All Activation Options</option>
									<cfloop query="local.qryPaymentStatuses">
										<option value="#local.qryPaymentStatuses.statusCode#"<cfif local.SubReportFilter.listFilter.fSubPaymentStatus eq local.qryPaymentStatuses.statusCode> selected</cfif>>#local.qryPaymentStatuses.statusName#</option>
									</cfloop>
								</select>
								<label for="fSubPaymentStatus">Activation Option</label>
							</div>
						</div>
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fFreq" id="fFreq" class="form-control">
									<option value="0">All Frequencies</option>
									<cfloop query="local.qryFrequencies">
										<option value="#local.qryFrequencies.frequencyID#" <cfif local.SubReportFilter.listFilter.fFreq eq local.qryFrequencies.frequencyID>selected</cfif>>#local.qryFrequencies.frequencyName#</option>
									</cfloop>
								</select>
								<label for="fFreq">Frequency</label>
							</div>
						</div>
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fHasCardOnFile" id="fHasCardOnFile" class="form-control">
									<option value="">With or Without Pay Method Associated</option>
									<option value="Y"<cfif local.SubReportFilter.listFilter.fHasCardOnFile eq "Y"> selected</cfif>>With Pay Method Associated</option>
									<option value="N"<cfif local.SubReportFilter.listFilter.fHasCardOnFile eq "N"> selected</cfif>>With no Pay Method Associated</option>
								</select>
								<label for="fHasCardOnFile">Pay Method</label>
							</div>
						</div>
					</div>
				</div>
				<div class="form-group row">
					<div class="col-md-12">
						<div class="row">
							<div class="col-auto">
								Associated With:
							</div>
							<div class="col">
								<div class="form-check form-check-inline">
									<input type="radio" name="assocType" id="assocTypeMember" class="assocType form-check-input"<cfif local.SubReportFilter.listFilter.associatedMemberID gt 0> checked</cfif> value="member">
									<label class="form-check-label" for="assocTypeMember">A Specific Member</label>
								</div>
								<div class="form-check form-check-inline">
									<input type="radio" name="assocType" id="assocTypeGroup" class="assocType form-check-input"<cfif local.SubReportFilter.listFilter.associatedGroupID gt 0 >checked</cfif> value="group">
									<label class="form-check-label" for="assocTypeGroup">A Specific Group</label>
								</div>
								<div id="divAssociatedVal">
									<span id="associatedVal" class="font-weight-bold">
										<cfset local.showExpandSearch = false>
										<cfif local.SubReportFilter.listFilter.associatedMemberID gt 0>
											<cfset local.showExpandSearch = true>
											#local.SubReportFilter.listFilter.associatedMemberName# (#local.SubReportFilter.listFilter.associatedMemberNum#)
										<cfelseif local.SubReportFilter.listFilter.associatedGroupID gt 0>
											#local.SubReportFilter.listFilter.associatedGroupName#
										</cfif>
									</span>
									<a href="##" id="aClearAssocType" class="ml-2">clear</a>
								</div>
								<input type="hidden" name="associatedMemberID" id="associatedMemberID" value="#local.SubReportFilter.listFilter.associatedMemberID#">
								<input type="hidden" name="associatedMemberName" id="associatedMemberName" value="#local.SubReportFilter.listFilter.associatedMemberName#">
								<input type="hidden" name="associatedMemberNum" id="associatedMemberNum" value="#local.SubReportFilter.listFilter.associatedMemberNum#">
								<input type="hidden" name="associatedGroupID" id="associatedGroupID" value="#local.SubReportFilter.listFilter.associatedGroupID#">
								<input type="hidden" name="associatedGroupName" id="associatedGroupName" value="#local.SubReportFilter.listFilter.associatedGroupName#">
							</div>
							<div class="row col-md-12" id="expandSearch"<cfif NOT local.showExpandSearch> style="display:none"</cfif>>
								<div class="col-md-12">
									<div class="row">
										<div class="col-sm-12 pr-md-0 font-weight-bold my-2">
											Expand search to consider linked records
										</div>
										<div class="col-md-10 col-sm-12">
											<div class="form-check form-check-inline">
												<input type="radio" name="linkedRecords" id="linkedRecordsAll" value="all" class="form-check-input filter-exc-verbose" <cfif local.SubReportFilter.listFilter.linkedRecords EQ "all">checked="true"</cfif>>
												<label class="form-check-label" for="linkedRecordsAll">Include children of the selected records in search</label>
											</div>
										</div>
										<div class="col-md-10 col-sm-12">
											<div class="form-check form-check-inline">
												<input type="radio" name="linkedRecords" id="linkedRecordSelected" value="selected" class="form-check-input filter-exc-verbose" <cfif local.SubReportFilter.listFilter.linkedRecords EQ "selected">checked="true"</cfif>>
												<label class="form-check-label" for="linkedRecordSelected">Only include the specific selected records</label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="card-footer p-2 text-right">
				<button type="button" name="btnResetFilterSubs" class="btn btn-sm btn-secondary" onclick="clearFilterSubGrid();">Clear Filters</button>
				<button type="submit" name="btnFilterSubs" class="btn btn-sm btn-primary">
					<i class="fa-light fa-filter"></i> Filter Subscribers
				</button>
				<button type="button" class="btnReApplyFilter d-none" onclick="reloadSubsTable();"></button><!--- hidden button used by verbose fn to refresh datatable --->
			</div>
		</div>
	</form>
</div>

<div id="divSubFilterVerbose" style="display:none;"></div>
<table id="subsListTable" class="table table-sm table-striped table-bordered" style="width:100%">
	<thead>
		<tr>
			<th data-orderable="false"><input type="checkbox" name="masterCheckBox" id="masterCheckBox" onclick="doCheckAllSubs(this.checked);" value="1" checked="checked"></th>
			<th>Member</th>
			<th>Subscription</th>
			<th align="right">Start</th>
			<th align="right">End</th>
			<th align="right">Offer Exp</th>
			<th data-orderable="false">Actions</th>
		</tr>
	</thead>
</table>

<div id="subAuditTrailRpt" class="mt-3" data-init="0"></div>

<script id="mc_subBtnBarTemplate" type="text/x-handlebars-template">
	{{##renderSubActBtns buttons}}
		<button class="btn btn-link p-0 mr-2" onclick="{{this.onclick}}" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="{{this.title}}">
			<i class="{{this.icon}}"></i> {{this.label}}
		</button>
	{{/renderSubActBtns}}
</script>
</cfoutput>