ALTER PROC dbo.queue_TSApprovalAutomation_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @queueTypeID INT, @processingStatusID INT, @readyStatusID INT, 
		@grabProcessingStatusID int, @failedStatusID int, @tier varchar(12), @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	SELECT @tier = tier from membercentral.dbo.fn_getServerSettings();
	EXEC dbo.queue_getQueueTypeID @queueType='TSApprovalAutomation', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForAttachCheck', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyForAttachCheck', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processAttachCheck', @queueStatusID=@processingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='failed', @queueStatusID=@failedStatusID OUTPUT;

	-- We dont upload when not in production, so delete the queue when not in prod
	IF @tier = 'Production' BEGIN
		-- TSApprovalAutomation / processAttachCheck autoreset to readyToProcess
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(minute, -10, GETDATE());
		SELECT @issueCount = count(itemID) FROM dbo.queue_TSApprovalAutomation WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
		IF @issueCount > 0 BEGIN
			UPDATE dbo.queue_TSApprovalAutomation
			SET statusID = @readyStatusID, 
				dateUpdated = GETDATE()
			WHERE statusID = @processingStatusID 
			AND dateUpdated < @timeToUse;

			SET @errorTitle = 'TSApprovalAutomation Queue Issue';
			SET @errorSubject = 'TSApprovalAutomation queue moved items from processAttachCheck to readyForAttachCheck';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END

		-- TSApprovalAutomation / grabbedForAttachCheck autoreset to readyForAttachCheck
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(minute, -10, GETDATE());
		SELECT @issueCount = count(itemID) FROM dbo.queue_TSApprovalAutomation WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
		IF @issueCount > 0 BEGIN
			UPDATE dbo.queue_TSApprovalAutomation
			SET statusID = @readyStatusID, 
				dateUpdated = GETDATE()
			WHERE statusID = @grabProcessingStatusID 
			AND dateUpdated < @timeToUse;

			SET @errorTitle = 'TSApprovalAutomation Queue Issue';
			SET @errorSubject = 'TSApprovalAutomation queue moved items from grabbedForAttachCheck to readyForAttachCheck';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END

		-- TSApprovalAutomation readyForAttachCheck notify
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(itemID) FROM dbo.queue_TSApprovalAutomation WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
		IF @issueCount > 0 BEGIN
			SET @errorTitle = 'TSApprovalAutomation Queue Issue';
			SET @errorSubject = 'TSApprovalAutomation queue has items in readyForAttachCheck with dateUpdated older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END

		-- TSApprovalAutomation failed notify. Use DateAdded since we change dateupdated upon fail but still keep trying to process it.
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(itemID) FROM dbo.queue_TSApprovalAutomation WHERE statusID = @failedStatusID AND dateAdded < @timeToUse AND isNotified = 0;
		IF @issueCount > 0 BEGIN
			UPDATE dbo.queue_TSApprovalAutomation
			SET isNotified = 1 
			WHERE statusID = @failedStatusID 
			AND dateAdded < @timeToUse 
			AND isNotified = 0;

			SET @errorTitle = 'TSApprovalAutomation Queue Issue';
			SET @errorSubject = 'TSApprovalAutomation queue has items in failed status with dateAdded older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END
	END ELSE BEGIN
		SELECT @issueCount = count(itemID) FROM dbo.queue_TSApprovalAutomation;
		IF @issueCount > 0
			DELETE FROM dbo.queue_TSApprovalAutomation;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
