-- generate renewals save filter cleanup
USE membercentral
GO

-- delete method saveSubRenewalsFilter
DECLARE @componentID int, @methodID int;

SELECT @componentID = componentID
FROM dbo.ajax_components
WHERE componentName = 'ADMSUBS';

SELECT @methodID = methodID
FROM dbo.ajax_componentMethods
WHERE componentID = @componentID
AND methodName = 'saveSubRenewalsFilter';

IF @methodID IS NOT NULL BEGIN
	DELETE FROM dbo.ajax_componentMethodAccess WHERE methodID = @methodID;
	DELETE FROM dbo.ajax_componentMethods WHERE methodID = @methodID;
END
GO

-- delete method saveSubReportFilter
DECLARE @componentID int, @methodID int;

SELECT @componentID = componentID
FROM dbo.ajax_components
WHERE componentName = 'ADMSUBS';

SELECT @methodID = methodID
FROM dbo.ajax_componentMethods
WHERE componentID = @componentID
AND methodName = 'saveSubReportFilter';

IF @methodID IS NOT NULL BEGIN
	DELETE FROM dbo.ajax_componentMethodAccess WHERE methodID = @methodID;
	DELETE FROM dbo.ajax_componentMethods WHERE methodID = @methodID;
END
GO