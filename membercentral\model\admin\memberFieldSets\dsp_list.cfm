<cfset local.selectedTab = event.getTrimValue("tab","fieldSet")>
<cfset local.lockTab = arguments.event.getTrimValue("lockTab","false") ? local.selectedTab : "">

<cfsavecontent variable="local.gridJS">
	<cfoutput>
	<script language="javascript">
		let fieldSetTable;
		let gridInitArray = new Array();
		gridInitArray["fieldSetTab"] = false;
		gridInitArray["auditlog"] = false;

		function onTabChangeHandler(ActiveTab) {
			if (!gridInitArray[ActiveTab.id]) {
				gridInitArray[ActiveTab.id] = true;
				switch(ActiveTab.id) {
					case "fieldSetTab":
						initializeFieldSetTable();
						break;
					case "auditlog":
						initFSAuditLogTable();
						break;
				}
			}
		}
		function initializeFieldSetTable() {
			let domString = "<'row'<'col-sm-6 col-md-6'<'float-left mt-2'l>><'col-sm-6 col-md-6'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";
			fieldSetTable = $('##fieldSetTable').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 25,
				"lengthMenu": [ 25, 50, 100 ],
				"dom": domString,
				"language": {
					"lengthMenu": "_MENU_"
				},
				"ajax": { 
					"url": "#local.fieldsetLink#",
					"type": "post",
					"data": function(d) {
						$.each($('##frmFilter').serializeArray(),function() {
							d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
						});
					}
				},
				"autoWidth": false,
				"columns": [
					{ "data": "categoryName", "width": "20%", },
					{ "data": "fieldsetName", "width": "40%", },
					{ "data": "fieldIDCount", "width": "20%", "className": "text-center"},
					{ "data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<a href="javascript:editFieldSet('+data.fieldsetID+');" class="btn btn-xs text-primary p-1 m-1" title="Edit Member Field Set"><i class="fa-solid fa-pencil"></i></a>';
								renderData += '<a href="javascript:previewFieldset('+data.fieldsetID+');" class="btn btn-xs text-primary p-1 m-1" title="Preview this Member Field Set"><i class="fa-solid fa-eye"></i></a>';
								renderData += '<a href="javascript:copyFieldSet('+data.fieldsetID+');" class="btn btn-xs text-primary p-1 m-1" title="Copy Member Field Set"><i class="fa-solid fa-copy"></i></a>';
								if (!data.fieldSetBeingUsed) {
									renderData += '<a href="javascript:deleteFieldSet('+data.fieldsetID+');" id="btnDel'+data.fieldsetID+'" class="btn btn-xs text-danger p-1 m-1" title="Delete Member Field Set"><i class="fa-solid fa-trash-can"></i></a>';
								} else {
									renderData += '<a class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-trash-can"></i></a>';
								}
							}
							return type === 'display' ? renderData : data;
						},
						"width": "20%",
						"className": "align-top text-center",
						"orderable": false
					}
				],
				"order": [[0, 'asc']],
				"searching": false,
				"pagingType": "simple"
			});
			mca_generateVerboseFilterMessage('frmFilter');
		}
		function editFieldSet(fsID) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: fsID > 0 ? 'Edit Member Field Set' : 'Create Member Field Set',
				iframe: true,
				contenturl: '#this.link.editFieldSet#&fsID='+fsID,
				strmodalfooter: {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: 'saveNewFieldSet',
					extrabuttonlabel: 'Save',
				}
			});
		}
		function saveNewFieldSet() {
			$('##MCModalBodyIframe')[0].contentWindow.validateAndSaveFieldSet();
		}
		function previewFieldset(fsID) {
			MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				title: 'Preview Member Field Set',
				contenturl: '#this.link.previewFieldset#&fsID='+fsID,
				strmodalfooter: {
					showclose: true
				}
			});
		}
		function copyFieldSet(fsid) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'md',
				title: 'Copy Member Field Set',
				iframe: true,
				contenturl: '#this.link.copyFieldSet#&fsid=' + fsid,
				strmodalfooter: {
					classlist: 'd-flex',
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmCopyFieldSet :submit").click',
					extrabuttonlabel: 'Save',
				}
			});
		}
		function deleteFieldSet(fsid) {
			var removeItem = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					fieldSetTable.draw();
				} else {
					delElement.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
					if(r.msg) alert(r.msg);
					else alert('We were unable to delete this Member Field Set. Try again.');
				}
			};
			let delElement = $('##btnDel'+fsid);
			mca_initConfirmButton(delElement, function(){
				var objParams = { fsid:fsid };
				TS_AJX('MEMBERFIELDSSETS','doRemoveFieldSet',objParams,removeItem,removeItem,10000,removeItem);	
			});
		}
		function reloadFieldSetsTable() {
			fieldSetTable.draw();
		}
		function doFilterFieldSets() {
			mca_generateVerboseFilterMessage('frmFilter');
			reloadFieldSetsTable();
		}
		function clearFilterFieldSets() {
			/* since reset() won't clear fields with default values */
			$('##frmFilter input[type="text"]').val('');
			$('##fFSCategoryID').val(0);
			doFilterFieldSets();
		}

		$(function() {
			mca_initNavPills('FieldSetPills', '#local.selectedTab#', '#local.lockTab#', onTabChangeHandler);
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.gridJS#">

<cfoutput>
<h4>Member Field Sets</h4>
<ul class="nav nav-pills nav-pills-dotted" id="FieldSetPills">
	<cfset local.thisTabID = "fieldSetTab">
	<cfset local.thisTabName = "fieldSet">
	<li class="nav-item">
		<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Member Field Sets</a>
	</li>
	<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.thisTabID = "auditlog">
		<cfset local.thisTabName = "auditlog">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Audit Log</a>
		</li>

		<cfset local.thisTabID = "importExportTab">
		<cfset local.thisTabName = "ex">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Import / Export</a>
		</li>
	</cfif>
</ul>
<div class="tab-content mc_tabcontent p-2 pb-0" id="pills-tabContent">
	<div class="tab-pane fade" id="pills-fieldSetTab" role="tabpanel" aria-labelledby="fieldSetTab">
		<!--- button bar --->
		<div class="toolButtonBar">
			<div><a href="javascript:editFieldSet(0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to add new member field set."><i class="fa-regular fa-circle-plus"></i> Create a Member Field Set</a></div>
		</div>
		
		<div id="divFilterForm" class="my-3 grpToolBarItem" style="display:none;">
			<form name="frmFilter" id="frmFilter" onsubmit="doFilterFieldSets();return false;" data-filterwrapper="divFilterForm" data-verbosemsgwrapper="divMemberFieldSetFilterVerbose" data-filterkey="#local.filterKeyName#">
				<div class="card card-box mb-1">
					<div class="card-header py-1 bg-light">
						<div class="card-header--title font-weight-bold font-size-lg">
							Filter Member Field Sets
						</div>
					</div>
					<div class="card-body pb-3">
						<div class="form-row">
							<div class="col-3">
								<div class="form-label-group mb-0">
									<input type="text" name="fFieldSetName" id="fFieldSetName" class="form-control" value="#local.MFSFilter.fFieldSetName#">
									<label for="fFieldSetName">Member Field Set Name Contains...</label>
								</div>
							</div>
							<div class="col-5">
								<div class="form-label-group mb-0">
									<select name="fFSCategoryID" id="fFSCategoryID" class="custom-select">
										<option value="0"></option>
										<cfloop query="local.qryCategories">
											<option value="#local.qryCategories.categoryID#" <cfif local.MFSFilter.fFSCategoryID eq local.qryCategories.categoryID>selected</cfif>>#local.qryCategories.categoryName#</option>
										</cfloop>										
									</select>
									<label for="fFSCategoryID">Member Field Set Category</label>
								</div>
							</div>
							<div class="col-4">
								<div class="form-label-group mb-0">
									<input type="text" name="fFieldSetUID" id="fFieldSetUID" class="form-control" maxlength="60" value="#local.MFSFilter.fFieldSetUID#">
									<label for="fFieldSetUID">API ID</label>
								</div>
							</div>
						</div>			
					</div>
					<div class="card-footer p-2 text-right">
						<button type="button" name="btnResetFilterFieldSets" class="btn btn-sm btn-secondary" onclick="clearFilterFieldSets();">Clear Filters</button>
						<button type="submit" name="btnFilterFieldSets" class="btn btn-sm btn-primary">
							<i class="fa-light fa-filter"></i> Filter Member Field Sets
						</button>
						<button type="button" class="btnReApplyFilter d-none" onclick="reloadFieldSetsTable();"></button>
					</div>
				</div>
			</form>
		</div>

		<div id="divMemberFieldSetFilterVerbose" style="display:none;"></div>
		<div id="FieldSets">
			<table id="fieldSetTable" class="table table-sm table-striped table-bordered" style="width:100%">
				<thead>
					<tr>
						<th>Category</th>			
						<th>Member Field Set</th>
						<th>## Fields</th>
						<th>Actions</th>
					</tr>
				</thead>
			</table>
		</div>
	</div>
	<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<div id="pills-auditlog" class="tab-pane fade" role="tabpanel" aria-labelledby="auditlog">
			<cfinclude template="dsp_fieldSets_auditLog.cfm">
		</div>
		<div class="tab-pane fade" id="pills-importExportTab" role="tabpanel" aria-labelledby="importExportTab">
			<cfinclude template="dsp_importexport.cfm">
		</div>
	</cfif>
</div>
</cfoutput>