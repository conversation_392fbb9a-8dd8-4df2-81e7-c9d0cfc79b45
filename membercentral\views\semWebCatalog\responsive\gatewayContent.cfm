<cfset local.showBundleOption = (attributes.data.qrySWP.isSWL OR attributes.data.qrySWP.isSWOD) and attributes.data.hasActiveBundles>
<cfinclude template="/views/semwebCatalog/responsive/swCatalogcommonCSS.cfm">

<cfoutput>
<div class="swCatalogWrapper swCatalog swCatalogLanding">
	<cfif structKeyExists(attributes.data,"strCarousel") AND NOT structIsEmpty(attributes.data.strCarousel)>
		<div class="SWCatalogBanner">#attributes.data.strCarousel.html#</div>
	</cfif>
	<div id="swHeaderDiv">
		<div class="container-fluid">
			<div class="flex">
				<h2 class="swPrimary">#attributes.data.qrySWP.brandHomeTab#</h2>
				<div class="ml-auto visible-tablet visible-desktop">
					<ul class="nav nav-tabs pull-right">
						<li>
							<cfif attributes.data.savedProgramsCount>
								<a href="#attributes.event.getValue('mainurl')#&panel=browse&_swis=1" class="swPrimary tooltip-icon" rel="tooltip" data-placement="bottom" title="View Saved">
									<i class="bi bi-heart-fill sw_savedprogramscounticon swRed swIconBadge" data-swsavedprogramscount="#attributes.data.savedProgramsCount#" aria-hidden="true"></i>
								</a>
							<cfelse>
								<a href="javascript:void(0);" class="muted"><i class="bi bi-heart-fill" aria-hidden="true"></i></a>
							</cfif>
						</li>
						<li>
							<cfif attributes.data.swRegCart.recordCount>
								<a href="#attributes.event.getValue('mainurl')#&panel=showCart" class="swPrimary tooltip-icon" rel="tooltip" data-placement="bottom" title="View Cart"><i class="bi bi-cart-fill sw_regcartcounticon swIconBadge" data-swregcartcount="#attributes.data.swRegCart.recordcount#" aria-hidden="true"></i></a>
							<cfelse>
								<a href="javascript:void(0);" class="muted"><i class="bi bi-cart-fill" aria-hidden="true"></i></a>
							</cfif>
						</li>
						<li><a href="#attributes.event.getValue('mainurl')#&panel=My" class="swPrimary tooltip-icon" rel="tooltip" data-placement="bottom" title="#attributes.data.qrySWP.brandMyCLETab#"><i class="bi bi-person-lines-fill" aria-hidden="true"></i></a></li>
						<li><a href="#attributes.event.getValue('mainurl')#&panel=showFAQ" class="swPrimary tooltip-icon" rel="tooltip" data-placement="bottom" title="FAQ"><i class="bi bi-question-circle-fill" aria-hidden="true"></i></a></li>
					</ul>
				</div>
				<div class="ml-auto alpha-sort dropdown visible-phone"> 
					<a class="dropdown-toggle swPrimary" href="##" data-toggle="dropdown"><i class="bi bi-three-dots-vertical drop-icon" aria-hidden="true"></i></a>
					<ul class="dropdown-menu">
						<li>
							<cfif attributes.data.savedProgramsCount>
								<a href="#attributes.event.getValue('mainurl')#&panel=browse&_swis=1"><i class="bi bi-heart-fill" aria-hidden="true"></i>View Saved <span class="savedCount">(#attributes.data.savedProgramsCount#)</span></a>
							<cfelse>
								<a href="javascript:void(0);" class="muted"><i class="bi bi-heart-fill" aria-hidden="true"></i>View Saved</a>
							</cfif>
						</li>
						<li>
							<cfif attributes.data.swRegCart.recordCount>
								<a href="#attributes.event.getValue('mainurl')#&panel=showCart"><i class="bi bi-cart-fill" aria-hidden="true"></i>View Cart <span class="cartCount">(#attributes.data.swRegCart.recordCount#)</span></a>
							<cfelse>
								<a href="javascript:void(0);" class="muted"><i class="bi bi-cart-fill" aria-hidden="true"></i>View Cart</a>
							</cfif>
						</li>
						<li><a href="#attributes.event.getValue('mainurl')#&panel=My"><i class="bi bi-person-lines-fill" aria-hidden="true"></i>#attributes.data.qrySWP.brandMyCLETab#</a></li>
						<li><a href="#attributes.event.getValue('mainurl')#&panel=showFAQ"><i class="bi bi-question-circle-fill" aria-hidden="true"></i>FAQ</a></li>
					</ul>
				</div>
			</div>
			<hr>
			<cfif len(attributes.data.qrySWP.brandHomeText)>
				<div class="row-fluid">
					<div class="span12">
						<div class="swGatewayText">#attributes.data.qrySWP.brandHomeText#</div>
					</div>
				</div>
			</cfif>
		</div>
	</div>

	<section class="swCatalogMiddleSec">
		<div class="container-fluid">
			<div class="row-fluid">
				<div class="flex">
					<div class="span6">
						<h3 class="swPrimary">Search</h3>
						<div class="swPrimaryBkgd swSearchBox">
							<p><span class="lead"><strong>What do you want to learn?</strong></span></p>
							<form class="form-search" method="GET" action="/">
							<input type="hidden" name="pg" value="semWebCatalog">
							<input type="hidden" name="panel" value="browse">
							<div class="swSearchBoxInputFieldFlex">
								<input type="search" name="_swkwl" id="_swkwl" autocomplete="off">
								<button class="btn" type="submit"><i class="bi bi-search"></i></button>
							</div>
							<cfif attributes.data.qrySWP.isSWL OR attributes.data.qrySWP.isSWOD OR attributes.data.qrySWP.isConf>
								<div>
								<cfif attributes.data.qrySWP.isSWL>
									<div class="swcustom-control swcustom-checkbox sw-d-inline-flex">
										<input type="checkbox" name="_swft" id="sw_format_swl" class="swcustom-control-input" value="swl" checked="checked">
										<label class="swcustom-control-label sw-text-light" for="sw_format_swl">
											#attributes.data.qrySWP.brandSWLTab#
										</label>
									</div>
								</cfif>
								<cfif attributes.data.qrySWP.isSWOD>
									<div class="swcustom-control swcustom-checkbox sw-d-inline-flex">
										<input type="checkbox" name="_swft" id="sw_format_swod" class="swcustom-control-input" value="swod" checked="checked">
										<label class="swcustom-control-label sw-text-light" for="sw_format_swod">
											#attributes.data.qrySWP.brandSWODTab#
										</label>
									</div>
								</cfif>
								<cfif attributes.data.qrySWP.isConf>
									<div class="swcustom-control swcustom-checkbox sw-d-inline-flex">
										<input type="checkbox" name="_swft" id="sw_format_conf" class="swcustom-control-input" value="conf" checked="checked">
										<label class="swcustom-control-label sw-text-light" for="sw_format_conf">
											#attributes.data.qrySWP.brandConfTab#
										</label>
									</div>
								</cfif>
								<cfif local.showBundleOption>
									<div class="swcustom-control swcustom-checkbox sw-d-inline-flex">
										<input type="checkbox" name="_swft" id="sw_format_swb" class="swcustom-control-input" value="swb" checked="checked">
										<label class="swcustom-control-label sw-text-light" for="sw_format_swb">
											#attributes.data.qrySWP.brandBundleTab#
										</label>
									</div>
								</cfif>
							</div>
							</cfif>
							</form>
						</div>
					</div>
					<div class="span4 browse">
						<h3 class="swPrimary">Browse</h3>
						<cfif attributes.data.qrySWP.isConf>
							<a href="#attributes.event.getValue('mainurl')#&panel=browse&_swft=Conf" class="mcEvent mcEventBorder"><i class="bi bi-geo-alt-fill" aria-hidden="true"></i>#attributes.data.qrySWP.brandConfTab#<i class="bi bi-chevron-double-right"></i></a>
						</cfif>
						<cfif attributes.data.qrySWP.isSWL>
							<a href="#attributes.event.getValue('mainurl')#&panel=browse&_swft=SWL" class="swWebinar swWebinarBorder"><i class="bi bi-laptop" aria-hidden="true"></i>#attributes.data.qrySWP.brandSWLTab#<i class="bi bi-chevron-double-right"></i></a>
						</cfif>
						<cfif attributes.data.qrySWP.isSWOD>
							<a href="#attributes.event.getValue('mainurl')#&panel=browse&_swft=SWOD" class="swOnDemand swOnDemandBorder"><i class="bi bi-play-circle" aria-hidden="true"></i>#attributes.data.qrySWP.brandSWODTab#<i class="bi bi-chevron-double-right"></i></a>
						</cfif>
						<cfif local.showBundleOption>
							<a href="#attributes.event.getValue('mainurl')#&panel=browse&_swft=SWB" class="swBundle swBundleBorder"><i class="bi bi-basket" aria-hidden="true"></i>#attributes.data.qrySWP.brandBundleTab#<i class="bi bi-chevron-double-right"></i></a>
						</cfif>
						<cfif attributes.data.qrySWP.isSWL OR attributes.data.qrySWP.isSWOD OR attributes.data.qrySWP.isConf>
							<a href="#attributes.event.getValue('mainurl')#&panel=browse" class="swPrimary swPrimaryBorder"><i class="bi bi-list" aria-hidden="true"></i>All Programs<i class="bi bi-chevron-double-right"></i></a>
						</cfif>
					</div>
					<div class="span2 text-center history">
						<a href="#attributes.event.getValue('mainurl')#&panel=My" class="swPrimary historyIcon"><i class="bi bi-person-lines-fill" aria-hidden="true"></i></a>
						<a href="#attributes.event.getValue('mainurl')#&panel=My" class="swPrimary historyText">#attributes.data.qrySWP.brandMyCLETab#</a>
					</div>
				</div>
			</div>
		</div>
	</section>

	<cfset local.featuredProgramsCount = arrayLen(attributes.data.strFeaturedPrograms.arrPrograms)>
	<cfif local.featuredProgramsCount>
		<cfif local.featuredProgramsCount gte 4>
			<cfset local.spanClass = "span3">
			<cfset local.thumbnailsClassList = "thumbnails text-center sw-d-md-flex sw-flex-md-wrap">
		<cfelseif local.featuredProgramsCount eq 3>
			<cfset local.spanClass = "span4">
			<cfset local.thumbnailsClassList = "thumbnails text-center sw-d-md-flex sw-flex-md-wrap threeColumn">
		<cfelse>
			<cfset local.spanClass = "span6">
			<cfset local.thumbnailsClassList = "thumbnails text-center sw-d-md-flex sw-flex-md-wrap twoColumn">
		</cfif>

		<section class="swCatalogThumbBox">
			<div class="container-fluid">
				<div class="row-fluid">
					<div class="span12">
						<h4 class="swPrimary">Featured Offerings</h4>
					</div>
				</div>
				<div class="row-fluid featuredPrograms">
					<div class="span12">
						<div class="#local.thumbnailsClassList#">
							<cfloop array="#attributes.data.strFeaturedPrograms.arrPrograms#" index="local.thisProgram">
								<cfset local.programTitleDisplay = attributes.data.objSWBrowse.getTrimmedProgramTitleDisplay(programTitle=local.thisProgram.programTitle, programSubTitle=local.thisProgram.programSubTitle)>

								<div class="#local.spanClass#">
									<cfswitch expression="#local.thisProgram.ft#">
										<cfcase value="SWL">
											<a href="#attributes.event.getValue('mainurl')#&panel=showLive&seminarid=#local.thisProgram.programID#" class="thumbnail swPrimaryBorderHover sw-w-100 sw-h-100<cfif local.spanClass eq 'span6' AND NOT len(local.thisProgram.featuredImagePath)> sw-pl-3</cfif>">
												<cfif len(local.thisProgram.featuredImagePath)>
													<img class="img-circle" src="#local.thisProgram.featuredImagePath#" />
												</cfif>
												<span class="swWebinar"><i class="bi bi-laptop" aria-hidden="true"></i>#local.thisProgram.programBrand#</span>
												<h5 class="swPrimary<cfif local.spanClass neq 'span6'> text-center</cfif>">
													#local.programTitleDisplay#
												</h5>
												<p><span class="muted">#DateFormat(local.thisProgram.dspStartDate,"mmmm d, yyyy")#</span></p>
											</a>
										</cfcase>
										<cfcase value="SWOD">
											<a href="#attributes.event.getValue('mainurl')#&panel=showSWOD&seminarid=#local.thisProgram.programID#" class="thumbnail swPrimaryBorderHover sw-w-100 sw-h-100<cfif local.spanClass eq 'span6' AND NOT len(local.thisProgram.featuredImagePath)> sw-pl-3</cfif>">
												<cfif len(local.thisProgram.featuredImagePath)>
													<img class="img-circle" src="#local.thisProgram.featuredImagePath#" />
												</cfif>
												<span class="swOnDemand"><i class="bi bi-play-circle" aria-hidden="true"></i>#local.thisProgram.programBrand#</span>
												<h5 class="swPrimary<cfif local.spanClass neq 'span6'> text-center</cfif>">
													#local.programTitleDisplay#
												</h5>
												<p><span class="muted">Anytime</span></p>
											</a>
										</cfcase>
										<cfcase value="SWB">
											<a href="#attributes.event.getValue('mainurl')#&panel=showBundle&bundleid=#local.thisProgram.programID#" class="thumbnail swPrimaryBorderHover sw-w-100 sw-h-100<cfif local.spanClass eq 'span6' AND NOT len(local.thisProgram.featuredImagePath)> sw-pl-3</cfif>">
												<cfif len(local.thisProgram.featuredImagePath)>
													<img class="img-circle" src="#local.thisProgram.featuredImagePath#" />
												</cfif>
												<span class="swBundle"><i class="bi bi-basket-fill" aria-hidden="true"></i>#local.thisProgram.programBrand#</span>
												<h5 class="swPrimary<cfif local.spanClass neq 'span6'> text-center</cfif>">
													#local.programTitleDisplay#
												</h5>
												<cfif local.thisProgram.isSWOD>
													<p><span class="muted">Anytime</span></p>
												</cfif>
											</a>
										</cfcase>
										<cfcase value="EV">
											<a href="#local.thisProgram.eventDetailLink#" class="thumbnail swPrimaryBorderHover sw-w-100 sw-h-100<cfif local.spanClass eq 'span6' AND NOT len(local.thisProgram.featuredImagePath)> sw-pl-3</cfif>">
												<cfif len(local.thisProgram.featuredImagePath)>
													<img class="img-circle" src="#local.thisProgram.featuredImagePath#" />
												</cfif>
												<span class="mcEvent"><i class="bi bi-geo-alt-fill" aria-hidden="true"></i>#local.thisProgram.programBrand#</span>
												<h5 class="swPrimary<cfif local.spanClass neq 'span6'> text-center</cfif>">
													#local.programTitleDisplay#
												</h5>
												<p><span class="muted">#DateFormat(local.thisProgram.displayStartTime,"mmmm d, yyyy")#</span></p>
											</a>
										</cfcase>
									</cfswitch>
								</div>
							</cfloop>
						</div>
					</div>
				</div>
			</div>
		</section>
	</cfif>
</div>
</cfoutput>