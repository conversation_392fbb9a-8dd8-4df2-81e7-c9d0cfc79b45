<cfoutput>
<div class="card card-box mt-4">
	<div class="card-header py-1 bg-light">
		<div class="card-header--title font-weight-bold font-size-md">
			Manage History <cfif ArrayLen(local.subOfferAuditTrail.arrValue) gt 0>(#local.subOfferAuditTrail.totalCount#)</cfif>
		</div>
	</div>
	<div class="card-body p-3">
		<cfif ArrayLen(local.subOfferAuditTrail.arrValue) gt 0>
			<div id="auditResultsShow" class="mt-1 mb-3"<cfif local.auditTrailFlag eq 'all'> style="display:none;"</cfif>>
				<input type="button" id="btnShowHistory" class="btn btn-sm btn-primary" onClick="toggleATGrid()" value="Show History" />
			</div>
			<div id="auditResultsHide" class="my-1 text-right"<cfif local.auditTrailFlag neq 'all'> style="display:none;"</cfif>>
				<input type="button" id="btnHideHistory" class="btn btn-sm btn-secondary" onClick="toggleATGrid()" value="Hide History" /> <cfif local.subOfferAuditTrail.totalCount neq local.subOfferAuditTrail.foundCount><input type="button" id="btnShowAllHistory" class="btn btn-sm btn-primary" onClick="loadSubAuditTrail('all')" value="Show All History" /></cfif>
			</div>
			<div id="subAuditTrail"<cfif local.auditTrailFlag neq 'all'> style="display: none;"</cfif>>
				<table class="table table-sm table-striped">
					<thead class="thead-light">
						<tr>
							<th></th>
							<th>Date</th>
							<th>Description</th>
							<th>Updated By</th>
						</tr>
					</thead>
					<cfloop from="1" to="#ArrayLen(local.subOfferAuditTrail.arrValue)#" index="local.atNdx">
						<cfset local.currATItem = local.subOfferAuditTrail.arrValue[local.atNdx]>
						<cfset local.atTS = parsedatetime(local.currATItem["_id__timestamp"])>
						<cfset local.actMemberInfo = application.objMember.getMemberInfo(memberID=local.currATItem.ACTORMEMBERID)>
						
						<tr onclick="toggleATRow('#local.currATItem["_id"].ToString()#')">
							<td class="align-top"><cfif IsDefined("local.currATItem.NOTADDED") and arraylen(local.currATItem.NOTADDED)><img id="atTreeImg_#local.currATItem["_id"].ToString()#" src="/assets/common/images/tree-closed.jpg" /></cfif></td>
							<td class="align-top">#DateFormat(local.atTS, "m/d/yyyy")# #TimeFormat(local.atTS, "HH:mm:ss")#</td>
							<td class="align-top">#local.currATItem.MAINMESSAGE#</td>
							<td class="align-top">#RTrim(local.actMemberInfo.firstname & ' ' &local.actMemberInfo.middleName)# #RTrim(local.actMemberInfo.lastName & ' ' & local.actMemberInfo.suffix)#</td>
						</tr>
						<cfif IsDefined("local.currATItem.NOTADDED") and arrayLen(local.currATItem.NOTADDED)>
							<tbody id="atChanges_#local.currATItem["_id"].ToString()#" style="display:none;">
								<tr onclick="toggleATRow('#local.currATItem["_id"].ToString()#')">
									<td colspan="2"></td>
									<td colspan="2">
										<cfset local.loopMemberID = 0>
										<cfloop array="#local.currATItem.NOTADDED#" index="local.thisMessage">
											<cfif local.loopMemberID neq local.thisMessage.memberID>
												<cfif local.loopMemberID neq 0><br><br></cfif>
												<a href="#local.editMemberLink#&memberID=#local.thisMessage.memberID#&tab=subscriptions" target="_blank">#local.thisMessage.memberName#</a><br>
												<cfset local.loopMemberID = local.thisMessage.memberID>
											</cfif>
											<cfif IsDefined("local.thisMessage.errMessage")>
												<cfset local.msgToUse = local.thisMessage.errMessage>
											<cfelse>
												<cfset local.msgToUse = local.thisMessage.errType>
											</cfif>
											<cfset local.atMsgLen = Len(local.msgToUse) + 5>
											<cfif local.atMsgLen gt 100>
												<cfset local.firstTimeInLoop = true>
												<cfset local.msgCutoff = 100>
												<cfset local.workingString = local.msgToUse>
												<cfloop condition="(Len(local.workingString) gt 0)">
													<cfif local.firstTimeInLoop>
														<cfset local.firstTimeInLoop = false>
														&nbsp;&nbsp; 
													<cfelse>
														&nbsp;&nbsp;&nbsp;
													</cfif>
													<cfif (Find(" ", local.workingString) neq 0) AND (Len(local.workingString) gt local.msgCutoff)>
														<cfset local.currSpaceIndex = Left(local.workingString, local.msgCutoff).lastIndexOf(" ")>
													<cfelse>
														<cfset local.currSpaceIndex = Len(local.workingString)>
													</cfif>
													#Left(local.workingString, local.currSpaceIndex)#
													<cfif (Len(local.workingString)-local.currSpaceIndex) gt 0>
														<cfset local.workingString = Right(local.workingString, Len(local.workingString)-local.currSpaceIndex)>
													<cfelse>
														<cfset local.workingString = "">
													</cfif>
												</cfloop>
											
											<cfelse>
											&nbsp;&nbsp;#local.msgToUse#
											</cfif>
										</cfloop>
									</td>
								</tr>
							</tbody>
						</cfif>
					</cfloop>
				</table>
			</div>
		<cfelse>
			<div id="auditResultsDesc">
				No billed history recorded.
			</div>
		</cfif>
	</div>
</div>
</cfoutput>