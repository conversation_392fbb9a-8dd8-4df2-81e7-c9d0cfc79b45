<cfcomponent extends="model.admin.admin" output="no">
	<cfscript>
		defaultEvent = 'controller';
	</cfscript>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			
			// load objects ----------------------------------------------------------------------------- ::
			this.objMemberHistory = CreateObject('component','memberHistory');
			this.objCategories = CreateObject('component','model.system.platform.category');
			
			// set rights into event -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			// build quick links ------------------------------------------------------------------------ ::
			this.link.listHistory = buildCurrentLink(arguments.event,"listHistory");

			this.link.listRelationships	= buildCurrentLink(arguments.event,"listRelationships");
			this.link.listNotes = buildCurrentLink(arguments.event,"listNotes");

			this.link.editMemberHistory = buildCurrentLink(arguments.event,"editMemberHistory");
			this.link.addMemberHistory = buildCurrentLink(arguments.event,"addMemberHistory");
			this.link.reCategorizeMemberHistory = buildCurrentLink(arguments.event,"reCategorizeMemberHistory");
			this.link.saveMemberHistory	= buildCurrentLink(arguments.event,"saveMemberHistory");
			this.link.insertMemberHistory = buildCurrentLink(arguments.event,"insertMemberHistory");
			this.link.updateReCategorizeMemberHistory = buildCurrentLink(arguments.event,"updateReCategorizeMemberHistory");
			this.link.listCategories = buildCurrentLink(arguments.event,"listCategories");
			this.link.saveCategory = buildCurrentLink(arguments.event,"saveCategory") & "&mode=stream";
			this.link.insertMultiCategories = buildCurrentLink(arguments.event,"insertMultiCategories") & "&mode=stream";

			this.link.exportMemberHistory = buildCurrentLink(arguments.event,"exportMemberHistory");
			this.link.deleteMemberHistoryItem = buildCurrentLink(arguments.event,"deleteMemberHistoryItem");
			this.link.deleteMemberHistoryItems = buildCurrentLink(arguments.event,"deleteMemberHistoryItems");

			this.link.preProcessImport = buildCurrentLink(arguments.event,"preProcessImport");
			this.link.massEmailMemberHistory = buildCurrentLink(arguments.event,"massEmailMemberHistory");
			this.link.message = buildCurrentLink(arguments.event,"message");
			
			// Run Assigned Method ---------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];

			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="listHistory" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="impData" type="string" required="false">
		
		<cfscript>
			var local = structNew();
			
			local.qryCategories = this.objMemberHistory.getParentChildCategories(siteID=arguments.event.getValue('mc_siteinfo.siteid'),siteResourceID=this.siteResourceID);
			local.historyTitle = "Member History";
			local.historyTitleSingular = "Member History";
			local.typeID = 1;
			local.editMemberTab = 'memhistory';
			local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
			local.memSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list');
			local.grpSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups');
			local.mhAnalyzeMemberHistory= buildCurrentLink(arguments.event,"dspAnalyzeMemberHistory");
			local.mhEditLink = this.link.editMemberHistory & '&ret=all&mode=direct';
			local.mhAddLink = this.link.addMemberHistory & '&ret=all&mode=direct';
			local.mhReCategorizeLink = this.link.reCategorizeMemberHistory & '&ret=all&mode=direct';
			local.mhExportLink = this.link.exportMemberHistory & '&typeID=#local.typeID#&ret=all&mode=stream';
			local.mhDeleteItemLink = this.link.deleteMemberHistoryItem & '&typeID=#local.typeID#&ret=all&mode=stream';
			local.mhDeleteItemsLink = this.link.deleteMemberHistoryItems & '&typeID=#local.typeID#&ret=all&mode=stream';
			local.mhMassEmailLink = this.link.massEmailMemberHistory & "&typeID=#local.typeID#&mode=direct";
			local.historyListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberHistoryJSON&meth=getMemberHistoryEntries&typeID=#local.typeID#&chkAll=1&mode=stream";
			local.historyBackupLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberHistoryJSON&meth=getMemberHistoryBackup&typeID=#local.typeID#&mode=stream";
			local.permsGotoLink = buildLinkToTool(toolType='PermissionsAdmin',mca_ta='showPerms') & '&mode=direct';
			local.editCategoryLink = buildCurrentLink(arguments.event,"editCategory") & "&strFrom=#local.editMemberTab#&mode=direct";
			local.addCategoryMulti = buildCurrentLink(arguments.event,"addCategoryMulti") & "&strFrom=#local.editMemberTab#&mode=direct";
			local.categoryHeader = 'Member History Category';
			local.categoryHeaderPlural = 'Member History Categories';
			local.categoryItemText = 'Member History items';
			local.canViewCategory = (arguments.event.getValue('mc_adminToolInfo.myRights.EditCategories') OR arguments.event.getValue('mc_adminToolInfo.myRights.ViewMemberHistoryTypes'));
			
			local.editCategoryLinkTitle = 'Edit Member History Category';
			local.editCategoryLinkFunctionName = 'editCategory';
			
			local.deleteCategoryLinkTitle = 'Remove Member History Category';
			local.deleteCategoryLinkFunctionName = 'removeCategory';

			local.checkModifyCategory = 1;

			local.categoriesLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=categoriesJSON&meth=getCategoryAndSubCategoryList&siteResourceID=#this.siteResourceID#&checkModifyCategory=#local.checkModifyCategory#&permResType=MemberHistoryCategory&mode=stream";

			local.filterKeyName = "MHAHistory";
			local.tmpStr = { "fDateFrom": '', "fDateTo": '', "fEndDateFrom": '', "fEndDateTo": '', "fEntDateFrom":'', "fEntDateTo":'', "parentChildCategoryID":0, 
				"fKeyword":'', "fQuantityFrom":'', "fQuantityTo":'', "fAmtFrom":'', "fAmtTo":'', "fSelectMemberID":'', "fSelectMemberName":'', "fSelectMemberNum":'', 
				"fAssignedMemberID":'', "fAssignedMemberName":'', "fAssignedMemberNum":'', "fAssignedGroupID":'', "fAssignedGroupName":'', "fLinkedMemberID":'', 
				"fLinkedMemberName":'', "fLinkedMemberNum":'', "fLinkedGroupID":'', "fLinkedGroupName":'' };
			local.strFilterData = application.objCommon.getToolFiltersData(keyname=local.filterKeyName, defaultFilterData=local.tmpStr);

			local.strETData = { 
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				treeCode='ETHISTORY',
				title="Member History / Notes / Relationships Email Templates ", 
				intro="Here you manage email templates used by member history, notes, and relationships.",
				gridext="#this.siteResourceID#_1",
				gridwidth=690,
				initGridOnLoad=false
			};
			local.strEmailTemplatesGrid = createObject("component","model.admin.emailTemplates.emailTemplateAdmin").manageEmailTemplates(strETData=local.strETData);
		</cfscript>
		
		<cfset local.sampleImportTemplate = buildCurrentLink(arguments.event,"sampleImportTemplate") & "&typeID=#local.typeID#&mode=stream">
		<cfif structKeyExists(arguments,"impData")>
			<cfset local.impData = arguments.impData> 
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfif arguments.event.getValue('mc_adminToolInfo.myRights.ViewMemberHistoryAdmin',false)>
				<cfinclude template="dsp_memberHistory.cfm">
			<cfelse>
				<cfoutput>
					<h2>Not Allowed</h2>
					<p>You do not have appropriate permissions to perform this action.</p>
				</cfoutput>
			</cfif>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="dspAnalyzeMemberHistory" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.qryHistory = this.objMemberHistory.getMemberHistoryFromFilters(event=arguments.event, mode='analyzeMH')>
		<cfset local.qryHistoryGrandTotal = this.objMemberHistory.getAnalyzeMemberHistoryTotals(qryHistory=local.qryHistory)>
		<cfset local.qryGetMemberInfo = application.objMember.getMemberInfo(memberid=arguments.event.getValue('LimitToMemberID','0'))>
		<cfset local.mhAnalyzeMemberHistory= buildCurrentLink(arguments.event,"dspAnalyzeMemberHistory")>
		<cfset local.historyTitle = "Analyze Member History">
		<cfset local.historyTitleSingular = "Member History">
		<cfset local.doNotAddList = "MCA_A,MCA_S,MCA_TA,MCA_TT,MODE,PG">
		<cfset local.urlVars = "">
		<cfloop collection="#url#" item="local.thisKey">
			<cfif not listFindNocase(local.doNotAddList,local.thisKey)>
				<cfset local.urlVars = listAppend(local.urlVars,local.thisKey & "=" & url[local.thisKey],"&")>
			</cfif>
		</cfloop>
		<cfset local.isPrinterFiendly = 0>
		<cfif arguments.event.valueExists("print")>
			<cfset local.isPrinterFiendly = 1>
		</cfif>
		
		<cfif not local.isPrinterFiendly>
			<cfsavecontent variable="local.data">
				<cfif arguments.event.getValue('mc_adminToolInfo.myRights.ViewMemberHistoryAdmin',false)>
					<cfinclude template="dsp_memberHistory_analyze.cfm">
				<cfelse>
					<cfoutput>
						<h2>Not Allowed</h2>
						<p>You do not have appropriate permissions to perform this action.</p>
					</cfoutput>
				</cfif>
			</cfsavecontent>
		<cfelse>
			<cfset local.returnPDFStruct = doGenerateAnalyzePrinterFriendly(event=arguments.event, analyzeStruct=local) />
			<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath=local.returnPDFStruct.referralFilePath, displayName=ListLast(local.returnPDFStruct.referralFilePath,"/"), deleteSourceFile=1)>
			<cfif not local.docResult>
				<cfsavecontent variable="local.data">
					<cfoutput>
						<script></script>
					</cfoutput>
				</cfsavecontent>
			</cfif>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="doGenerateAnalyzePrinterFriendly" access="public" returntype="struct" output="no">
		<cfargument name="Event" type="any" required="true" />
		<cfargument name="analyzeStruct" type="struct" required="true" />

		<cfset var local = structNew() />
		<cfset local.returnStruct = { packingSlipPath='' } />
		<cfset structAppend(local,arguments.analyzeStruct) />
		
		<cfset local.margintop = "0.5" />
		<cfset local.memberName = encodeForHTML("#local.qryGetMemberInfo.firstName#-#local.qryGetMemberInfo.lastName#")>
		<cfset local.memberNumber = local.qryGetMemberInfo.memberNumber>
		<cfif not len(trim(replace(local.memberName,"-","","all" )))>
			<cfset local.memberName = dateFormat(now(),"yyyymmdd") & "-" & timeFormat(now(), "hhmmss")>
		</cfif>
		<cfset local.qryHistoryGrandTotal = this.objMemberHistory.getAnalyzeMemberHistoryTotals(qryHistory=local.qryHistory)>
			
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_memberHistory_analyze_pdf.cfm">
		</cfsavecontent>
		
		<cfsavecontent variable="local.mhHeader">
			<cfoutput>
				<html>
				<head>
					<style type="text/css">
						##header h1 { font-size:20px;font-family:Verdana, Arial, Helvetica, sans-serif;font-weight:bold;line-height:20px;margin-bottom:20px; color:##0E568D;}
					</style>
				</head>
				<body>
				<div id="header">
					<h1>Member History Totals</h1>
				</div>
				</body>
				</html>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.mhFooter">
			<cfoutput>
			<html>
			<head></head>
			<body>
				<div style="font-family:Verdana, Arial, Helvetica, sans-serif; font-size:9px; color:##bbb; border-top:1px solid ##ccc; padding-top:7px; text-align:center;">
					<!-- cpp --> 
				</div>
			</body>
			</html>
			</cfoutput>
		</cfsavecontent>

		<!--- create a PDF --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.headercol = { type="header", evalAtPrint=true, txt=local.mhHeader } >
		<cfset local.footercol = { type="footer", evalAtPrint=true, txt=local.mhFooter } >
		<cftry>
			<cfdocument filename="#local.strFolder.folderPath#/un_#local.memberNumber#.pdf" pagetype="letter" margintop="#local.margintop#" marginbottom="0.5" marginright="0.5" marginleft="0.5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
				<cfoutput>
					<cfdocumentsection margintop="0.25" marginbottom="0.25">
						<cfdocumentitem attributeCollection="#local.headercol#">
							#local.mhHeader#
						</cfdocumentitem>
						#local.data#
						<cfdocumentitem attributeCollection="#local.footercol#">
							#replace(local.mhFooter,'<!-- cpp -->','Page #cfdocument.currentsectionpagenumber# of #cfdocument.totalsectionpagecount#')#
						</cfdocumentitem>
					</cfdocumentsection>
				</cfoutput>
			</cfdocument>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local) />
		</cfcatch>
		</cftry>

		<!--- File Name of Referral --->
		<cfset local.fullname = rereplaceNoCase('#local.memberName#','[^A-Z0-9]','','ALL') />
		<cfset local.repFileNameNoExt = "Report-#local.memberName#" />
		<cfset local.repFileNameNoExt = replace(local.repFileNameNoExt,' ','_','ALL') />
		<cfset local.repFileNameNoExt = replace(local.repFileNameNoExt,',','_','ALL') />

		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.memberNumber#.pdf","#local.strFolder.folderPath#/#local.repFileNameNoExt#.pdf","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l") />
		<cfset local.returnStruct.referralFilePath = "#local.strFolder.folderPath#/#local.repFileNameNoExt#.pdf" />
			
		<cfreturn local.returnStruct />
	</cffunction>

	<cffunction name="listRelationships" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="impData" type="string" required="false">
		
		<cfset var local = structNew()>

		<cfscript>
			local.RelationshipAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='RelationshipAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'));
			local.relationshipRights = buildRightAssignments(siteResourceID=local.RelationshipAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			local.qryCategories = this.objMemberHistory.getParentChildCategories(siteID=arguments.event.getValue('mc_siteinfo.siteid'),siteResourceID=local.RelationshipAdminSRID);
			local.historyTitle = "Relationships";
			local.historyTitleSingular = "Relationship";
			local.typeID = 2;
			local.editMemberTab = 'relationships';
			local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
			local.memSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list');
			local.grpSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups');
			local.mhAnalyzeMemberHistory= buildCurrentLink(arguments.event,"dspAnalyzeMemberHistory");
			local.mhEditLink = this.link.editMemberHistory & '&ret=all&mode=direct';
			local.mhAddLink = this.link.addMemberHistory & '&ret=all&mode=direct';
			local.mhReCategorizeLink = this.link.reCategorizeMemberHistory & '&ret=all&mode=direct';
			local.mhExportLink = this.link.exportMemberHistory & '&typeID=#local.typeID#&ret=all&mode=stream';
			local.mhDeleteItemLink = this.link.deleteMemberHistoryItem & '&typeID=#local.typeID#&ret=all&mode=stream';
			local.mhDeleteItemsLink = this.link.deleteMemberHistoryItems & '&typeID=#local.typeID#&ret=all&mode=stream';
			local.mhMassEmailLink = this.link.massEmailMemberHistory & "&typeID=#local.typeID#&mode=direct";
			local.historyListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberHistoryJSON&meth=getMemberHistoryEntries&typeID=#local.typeID#&chkAll=1&mode=stream";
			local.historyBackupLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberHistoryJSON&meth=getMemberHistoryBackup&typeID=#local.typeID#&mode=stream";	
			local.startDateFrom = arguments.event.getValue('fDateFrom','');
			local.startDateTo = arguments.event.getValue('fDateTo','');
			local.permsGotoLink = buildLinkToTool(toolType='PermissionsAdmin',mca_ta='showPerms') & '&mode=direct';
			local.editCategoryLink = buildCurrentLink(arguments.event,"editCategory") & "&strFrom=#local.editMemberTab#&mode=direct";
			local.addCategoryMulti = buildCurrentLink(arguments.event,"addCategoryMulti") & "&strFrom=#local.editMemberTab#&mode=direct";

			local.categoryHeader = 'Relationship Category';
			local.categoryHeaderPlural = 'Relationship Categories';
			local.categoryItemText = 'Relationships';
			local.canViewCategory = (local.relationshipRights.EditCategories OR local.relationshipRights.ViewCategories);
			
			local.editCategoryLinkTitle = 'Edit Relationship Category';
			local.editCategoryLinkFunctionName = 'editCategory';
			
			local.deleteCategoryLinkTitle = 'Remove Relationship Category';
			local.deleteCategoryLinkFunctionName = 'removeCategory';

			local.checkModifyCategory = 1;

			local.categoriesLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=categoriesJSON&meth=getCategoryAndSubCategoryList&siteResourceID=#local.RelationshipAdminSRID#&checkModifyCategory=#local.checkModifyCategory#&permResType=MemberHistoryCategory&mode=stream";

			local.filterKeyName = "RARelationships";
			local.tmpStr = { "fDateFrom": '', "fDateTo": '', "fEndDateFrom": '', "fEndDateTo": '', "fEntDateFrom":'', "fEntDateTo":'', "parentChildCategoryID":0, 
				"fKeyword":'', "fSelectMemberID":'', "fSelectMemberName":'', "fSelectMemberNum":'', "fAssignedMemberID":'', "fAssignedMemberName":'', "fAssignedMemberNum":'', 
				"fAssignedGroupID":'', "fAssignedGroupName":'', "fLinkedMemberID":'', "fLinkedMemberName":'', "fLinkedMemberNum":'', "fLinkedGroupID":'', "fLinkedGroupName":'' };
			local.strFilterData = application.objCommon.getToolFiltersData(keyname=local.filterKeyName, defaultFilterData=local.tmpStr);

			local.strETData = { 
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				treeCode='ETHISTORY',
				title="Member History / Notes / Relationships Email Templates ", 
				intro="Here you manage email templates used by member history, notes, and relationships.",
				gridext="#this.siteResourceID#_1",
				gridwidth=690,
				initGridOnLoad=false
			};
			local.strEmailTemplatesGrid = createObject("component","model.admin.emailTemplates.emailTemplateAdmin").manageEmailTemplates(strETData=local.strETData);
		</cfscript>
		
		<cfset local.sampleImportTemplate = buildCurrentLink(arguments.event,"sampleImportTemplate") & "&typeID=#local.typeID#&mode=stream">
		<cfif structKeyExists(arguments,"impData")>
			<cfset local.impData = arguments.impData> 
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfif arguments.event.getValue('mc_adminToolInfo.myRights.ViewRelationships',false)>
				<cfinclude template="dsp_memberHistory.cfm">
			<cfelse>
				<cfoutput>
					<h2>Not Allowed</h2>
					<p>You do not have appropriate permissions to perform this action.</p>
				</cfoutput>
			</cfif>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="listNotes" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="impData" type="string" required="false">
		
		<cfset var local = structNew()>
		
		<cfscript>
			local.HistoryAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='HistoryAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'));
			local.notesRights = buildRightAssignments(siteResourceID=local.HistoryAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			local.qryCategories = this.objMemberHistory.getParentChildCategories(siteID=arguments.event.getValue('mc_siteinfo.siteid'),siteResourceID=local.HistoryAdminSRID);
			local.historyTitle = "Notes";
			local.historyTitleSingular = "Note";
			local.typeID = 3;
			local.editMemberTab = 'notes';
			local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
			local.memSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list');
			local.grpSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups');
			local.mhAnalyzeMemberHistory= buildCurrentLink(arguments.event,"dspAnalyzeMemberHistory");
			local.mhEditLink = this.link.editMemberHistory & '&ret=all&mode=direct';
			local.mhAddLink = this.link.addMemberHistory & '&ret=all&mode=direct';
			local.mhReCategorizeLink = this.link.reCategorizeMemberHistory & '&ret=all&mode=direct';
			local.mhExportLink = this.link.exportMemberHistory & '&typeID=#local.typeID#&ret=all&mode=stream';
			local.mhDeleteItemLink = this.link.deleteMemberHistoryItem & '&typeID=#local.typeID#&ret=all&mode=stream';
			local.mhDeleteItemsLink = this.link.deleteMemberHistoryItems & '&typeID=#local.typeID#&ret=all&mode=stream';
			local.mhMassEmailLink = this.link.massEmailMemberHistory & "&typeID=#local.typeID#&mode=direct";
			local.historyListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberHistoryJSON&meth=getMemberHistoryEntries&typeID=#local.typeID#&chkAll=1&mode=stream";
			local.historyBackupLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberHistoryJSON&meth=getMemberHistoryBackup&typeID=#local.typeID#&mode=stream";
			local.startDateFrom = arguments.event.getValue('fDateFrom','');
			local.startDateTo = arguments.event.getValue('fDateTo','');
			local.permsGotoLink = buildLinkToTool(toolType='PermissionsAdmin',mca_ta='showPerms') & '&mode=direct';
			local.editCategoryLink = buildCurrentLink(arguments.event,"editCategory") & "&strFrom=#local.editMemberTab#&mode=direct";
			local.addCategoryMulti = buildCurrentLink(arguments.event,"addCategoryMulti") & "&strFrom=#local.editMemberTab#&mode=direct";

			local.categoryHeader = 'Note Category';
			local.categoryHeaderPlural = 'Note Categories';
			local.categoryItemText = 'Note items';
			local.canViewCategory = (local.notesRights.EditCategories OR local.notesRights.viewHistoryTypes);
			
			local.editCategoryLinkTitle = 'Edit Note Category';
			local.editCategoryLinkFunctionName = 'editCategory';
			
			local.deleteCategoryLinkTitle = 'Remove Note Category';
			local.deleteCategoryLinkFunctionName = 'removeCategory';

			local.checkModifyCategory = 1;

			local.categoriesLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=categoriesJSON&meth=getCategoryAndSubCategoryList&siteResourceID=#local.HistoryAdminSRID#&checkModifyCategory=#local.checkModifyCategory#&permResType=MemberHistoryCategory&mode=stream";

			local.filterKeyName = "MNANotes";
			local.tmpStr = { "fDateFrom": '', "fDateTo": '', "fEndDateFrom": '', "fEndDateTo": '', "fEntDateFrom":'', "fEntDateTo":'', "parentChildCategoryID":0, 
				"fKeyword":'', "fSelectMemberID":'', "fSelectMemberName":'', "fSelectMemberNum":'', "fAssignedMemberID":'', "fAssignedMemberName":'', "fAssignedMemberNum":'', 
				"fAssignedGroupID":'', "fAssignedGroupName":'', "fLinkedMemberID":'', "fLinkedMemberName":'', "fLinkedMemberNum":'', "fLinkedGroupID":'', "fLinkedGroupName":'' };
			local.strFilterData = application.objCommon.getToolFiltersData(keyname=local.filterKeyName, defaultFilterData=local.tmpStr);

			local.strETData = { 
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				treeCode='ETHISTORY',
				title="Member History / Notes / Relationships Email Templates ", 
				intro="Here you manage email templates used by member history, notes, and relationships.",
				gridext="#this.siteResourceID#_1",
				gridwidth=690,
				initGridOnLoad=false
			};
			local.strEmailTemplatesGrid = createObject("component","model.admin.emailTemplates.emailTemplateAdmin").manageEmailTemplates(strETData=local.strETData);
		</cfscript>
		
		<cfset local.sampleImportTemplate = buildCurrentLink(arguments.event,"sampleImportTemplate") & "&typeID=#local.typeID#&mode=stream">
		<cfif structKeyExists(arguments,"impData")>
			<cfset local.impData = arguments.impData> 
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfif arguments.event.getValue('mc_adminToolInfo.myRights.ViewNotes',false)>
				<cfinclude template="dsp_memberHistory.cfm">
			<cfelse>
				<cfoutput>
					<h2>Not Allowed</h2>
					<p>You do not have appropriate permissions to perform this action.</p>
				</cfoutput>
			</cfif>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addMemberHistory" access="public" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();
		local.qryMHData = { typeID=arguments.event.getTrimValue('typeID',1), parentChildCategoryID=0, historyID=0, dateEntered='', userDate='', 
			userEndDate='', quantity='', dollarAmt='', description='', memberID='', MemberName='', enteredByMemberName='', linkMemberID='', 
			linkedMemberName='', mhu_csrid=arguments.event.getTrimValue('mhu_csrid',''), mhu_rt=arguments.event.getTrimValue('mhu_rt',''), 
			mhu_ut=arguments.event.getTrimValue('mhu_ut',''), mhu_iID=arguments.event.getTrimValue('mhu_iID','') };
		</cfscript>

		<cfif local.qryMHData.typeID is 2>
			<cfset local.resourceTypeName = "RelationshipAdmin">
			<cfset local.useSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='#local.resourceTypeName#',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfelseif local.qryMHData.typeID is 3>
			<cfset local.resourceTypeName = "HistoryAdmin">
			<cfset local.useSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='#local.resourceTypeName#',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfelse>
			<cfset local.useSiteResourceID = this.siteResourceID>
			<cfset local.resourceTypeName = "MemberHistoryAdmin">
		</cfif>

		<cfscript>
		local.strMHCatWidget = CreateObject("component","model.admin.common.modules.memberHistoryCategorySelector.memberHistoryCategorySelector").getMemHistCategorySelector(
			siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType=local.resourceTypeName, selectorID='parentChildCategoryID', 
			selectedParentChildCategoryID=local.qryMHData.parentChildCategoryID, allowBlankOption=0, inlineLoadingSectionID="divMemberHistoryContainer");

		local.memSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list');
		local.formLink = this.link.insertMemberHistory & "&mode=stream";

		if (local.qryMHData.typeID is 1) {
			local.historyType = "History";
		} else if (local.qryMHData.typeID is 2) {
			local.historyType = "Relationship";
		} else if (local.qryMHData.typeID is 3) {
			local.historyType = "Note";
		}
		</cfscript>

		<cfif arguments.event.getValue('ret','all') neq 'all'>
			<cfset local.qryMHData.memberID = int(val(arguments.event.getValue('ret')))>
			<cfset local.qryMHData.userDate = now()>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_memberHistory.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="insertMemberHistory" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.dollarAmt = ReReplace(arguments.event.getValue('dollarAmt',''),'[^0-9\.\-]','','ALL');
		local.parentChildCategoryID = arguments.event.getValue('parentChildCategoryID','');
		local.sCategoryID = 0;
		local.sSubCategoryID = 0;
		if( listLen(local.parentChildCategoryID,'_') eq 2 ) {
			local.sCategoryID = val(getToken(local.parentChildCategoryID, 1,'_'));
			local.sSubCategoryID = val(getToken(local.parentChildCategoryID, 2,'_'));
		} else 
			local.sCategoryID = val(local.parentChildCategoryID);
		</cfscript>

		<cfif arguments.event.getValue('typeID') is 2>
			<cfset local.useSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='RelationshipAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfelseif arguments.event.getValue('typeID') is 3>
			<cfset local.useSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='HistoryAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfelse>
			<cfset local.useSiteResourceID = this.siteResourceID>
		</cfif>

		<cfset local.qryCategories = this.objMemberHistory.getParentChildCategories(siteID=arguments.event.getValue('mc_siteinfo.siteid'),
										siteResourceID=local.useSiteResourceID, checkPermission='AddEntry')>

		<cfif not listFindNoCase(valueList(local.qryCategories.parentChildCategoryID), local.parentChildCategoryID)>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<h2>Not Allowed</h2>
					<p>You do not have appropriate permissions to perform this action.</p>
				</cfoutput>
			</cfsavecontent>

			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>
		
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_addMemberHistory">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('typeID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('associatedMemberID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.sCategoryID#">
			<cfif local.sSubCategoryID eq 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.sSubCategoryID#">
			</cfif>
			<cfif len(arguments.event.getValue('userDate','')) eq 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" null="yes">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getValue('userDate')#">
			</cfif>
			<cfif len(arguments.event.getValue('userEndDate','')) eq 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" null="yes">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getValue('userEndDate')#">
			</cfif>
			<cfif val(arguments.event.getValue('quantity',0)) eq 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('quantity'))#">
			</cfif>
			<cfif len(local.dollarAmt)>
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.dollarAmt#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" null="yes">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('description')#">
			<cfif arguments.event.getValue('linkMemberID','') eq ''>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('linkMemberID')#">
			</cfif>
			<cfif len(arguments.event.getValue('mhu_csrid','')) and len(arguments.event.getValue('mhu_rt','')) and len(arguments.event.getValue('mhu_ut','')) and len(arguments.event.getValue('mhu_iID',''))>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mhu_csrid')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mhu_rt')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mhu_ut')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mhu_iID')#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.memHistoryID">
		</cfstoredproc>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">top.dofilterMH();top.MCModalUtils.hideModal();</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="reCategorizeMemberHistory" access="public" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();
		local.qryMHData = { typeID=arguments.event.getTrimValue('typeID',1), parentChildCategoryID=0 };
		</cfscript>

		<cfif local.qryMHData.typeID is 2>
			<cfset local.resourceTypeName = "RelationshipAdmin">
			<cfset local.useSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='#local.resourceTypeName#',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfelseif local.qryMHData.typeID is 3>
			<cfset local.resourceTypeName = "HistoryAdmin">
			<cfset local.useSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='#local.resourceTypeName#',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfelse>
			<cfset local.useSiteResourceID = this.siteResourceID>
			<cfset local.resourceTypeName = "MemberHistoryAdmin">
		</cfif>

		<cfscript>
		local.strMHCatWidget = CreateObject("component","model.admin.common.modules.memberHistoryCategorySelector.memberHistoryCategorySelector").getMemHistCategorySelector(
			siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType=local.resourceTypeName, selectorID='parentChildReCategorizeCategoryID', 
			selectedParentChildCategoryID=local.qryMHData.parentChildCategoryID, allowBlankOption=0, inlineLoadingSectionID="divReCategorizeMemberHistoryContainer");

		local.formLink = this.link.updateReCategorizeMemberHistory & "&mode=stream";
		local.recatMHEntryConfirmMsgLink = buildCurrentLink(arguments.event,"reCategorizeMemberHistoryConfirmMsg") & "&mode=stream";
		if (arguments.event.valueExists('limitToMemberID')) {
			local.formLink = "#local.formLink#&limitToMemberID=#arguments.event.getValue('limitToMemberID')#";
			local.recatMHEntryConfirmMsgLink = "#local.recatMHEntryConfirmMsgLink#&limitToMemberID=#arguments.event.getValue('limitToMemberID')#";
		}

		if (local.qryMHData.typeID is 1) {
			local.historyType = "History";
		} else if (local.qryMHData.typeID is 2) {
			local.historyType = "Relationship";
		} else if (local.qryMHData.typeID is 3) {
			local.historyType = "Note";
		}
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_reCategorizeMemberHistory.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="reCategorizeMemberHistoryConfirmMsg" access="public" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "entrycount":0, "msg":"" }>
		<cfset local.qryReCatMHEntries = this.objMemberHistory.getMemberHistoryFromFilters(event=arguments.event, mode='recategorizeMHEntryCount')>
		<cfset local.returnStruct.entrycount = val(local.qryReCatMHEntries.reCatMHEntryCount)>

		<cfswitch expression="#arguments.event.getValue('typeID',0)#">
			<cfcase value="1">
				<cfset local.historyType = "Member History">
			</cfcase>
			<cfcase value="2">
				<cfset local.historyType = "Relationship">
			</cfcase>
			<cfcase value="3">
				<cfset local.historyType = "Note">
			</cfcase>
		</cfswitch>

		<cfsavecontent variable="local.returnStruct.msg">
			<cfoutput>
				<cfif local.returnStruct.entrycount>
					<b>Confirmation Needed</b><br/>
					<p>Are you sure you want to recategorize <b>#local.returnStruct.entrycount#</b> #local.historyType# entries identified by the current filter?</p>
				<cfelse>
					<p>No #local.historyType# entries were found to be recategorized.</p>
				</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(serializeJSON(local.returnStruct),"echo")>
	</cffunction>

	<cffunction name="updateReCategorizeMemberHistory" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.parentChildCategoryID = arguments.event.getValue('parentChildReCategorizeCategoryID','');
		local.sCategoryID = 0;
		local.sSubCategoryID = 0;
		if( listLen(local.parentChildCategoryID,'_') eq 2 ) {
			local.sCategoryID = val(getToken(local.parentChildCategoryID, 1,'_'));
			local.sSubCategoryID = val(getToken(local.parentChildCategoryID, 2,'_'));
		} else 
			local.sCategoryID = val(local.parentChildCategoryID);
		</cfscript>

		<cfif arguments.event.getValue('typeID') is 2>
			<cfset local.useSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='RelationshipAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfelseif arguments.event.getValue('typeID') is 3>
			<cfset local.useSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='HistoryAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfelse>
			<cfset local.useSiteResourceID = this.siteResourceID>
		</cfif>

		<cfset local.qryCategories = this.objMemberHistory.getParentChildCategories(siteID=arguments.event.getValue('mc_siteinfo.siteid'),
										siteResourceID=local.useSiteResourceID, checkPermission='EditEntry')>

		<cfset local.data = ""/>
		<cfif not listFindNoCase(valueList(local.qryCategories.parentChildCategoryID), local.parentChildCategoryID)>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<script type="text/javascript">alert('Not Allowed - You do not have appropriate permissions to perform this action.');</script>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.qryMemHistory = this.objMemberHistory.getMemberHistoryFromFilters(event=arguments.event,mode='recategorizeMH')>
			
			<cfif ListLen(valueList(local.qryMemHistory.historyID)) gt 0>
				<cfset this.objMemberHistory.updateCategoriesMemberHistory(hIDList=valueList(local.qryMemHistory.historyID), categoryID=local.sCategoryID, subCategoryID=local.sSubCategoryID, 
						orgID=arguments.event.getValue('mc_siteinfo.orgID'), siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
			</cfif>
		</cfif>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editMemberHistory" access="public" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.qryMHData = this.objMemberHistory.getMemberHistoryData(siteID=arguments.event.getValue('mc_siteinfo.siteid'), orgID=arguments.event.getValue('mc_siteinfo.orgID'), historyID=arguments.event.getValue('hID',0))>
		
		<cfif local.qryMHData.recordcount is 0>
			<cflocation url="#this.link.message#&message=2&mode=direct" addtoken="no">
		</cfif>

		<cfif local.qryMHData.typeID is 2>
			<cfset local.resourceTypeName = "RelationshipAdmin">
			<cfset local.useSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='#local.resourceTypeName#',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfelseif local.qryMHData.typeID is 3>
			<cfset local.resourceTypeName = "HistoryAdmin">
			<cfset local.useSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='#local.resourceTypeName#',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfelse>
			<cfset local.resourceTypeName = "MemberHistoryAdmin">
			<cfset local.useSiteResourceID = this.siteResourceID>
		</cfif>

		<cfscript>
			local.strMHCatWidget = CreateObject("component","model.admin.common.modules.memberHistoryCategorySelector.memberHistoryCategorySelector").getMemHistCategorySelector(
				siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType=local.resourceTypeName, selectorID='parentChildCategoryID', 
				selectedParentChildCategoryID=local.qryMHData.parentChildCategoryID, allowBlankOption=0, inlineLoadingSectionID="divMemberHistoryContainer");

			local.memSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list');
			local.formLink = this.link.saveMemberHistory & "&mode=stream";

			if (local.qryMHData.typeID is 1) {
				local.historyType = "History";
			} else if (local.qryMHData.typeID is 2) {
				local.historyType = "Relationship";
			} else if (local.qryMHData.typeID is 3) {
				local.historyType = "Note";
			}
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_memberHistory.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveMemberHistory" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();
		local.data = "";
		local.dollarAmt = ReReplace(arguments.event.getValue('dollarAmt',''),'[^0-9\.\-]','','ALL');

		local.parentChildCategoryID = arguments.event.getValue('parentChildCategoryID','');
		local.sCategoryID = 0;
		local.sSubCategoryID = 0;
		if( listLen(local.parentChildCategoryID,'_') eq 2 ) {
			local.sCategoryID = val(getToken(local.parentChildCategoryID, 1,'_'));
			local.sSubCategoryID = val(getToken(local.parentChildCategoryID, 2,'_'));
		} else 
			local.sCategoryID = val(local.parentChildCategoryID);
		</cfscript>

		<cfif arguments.event.getValue('typeID') is 2>
			<cfset local.useSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='RelationshipAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfelseif arguments.event.getValue('typeID') is 3>
			<cfset local.useSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='HistoryAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfelse>
			<cfset local.useSiteResourceID = this.siteResourceID>
		</cfif>

		<cfset local.qryCategories = this.objMemberHistory.getParentChildCategories(siteID=arguments.event.getValue('mc_siteinfo.siteid'),
										siteResourceID=local.useSiteResourceID, checkPermission='EditEntry')>

		<cfif not listFindNoCase(valueList(local.qryCategories.parentChildCategoryID), local.parentChildCategoryID)>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<h2>Not Allowed</h2>
					<p>You do not have appropriate permissions to perform this action.</p>
				</cfoutput>
			</cfsavecontent>

			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>

		<cfset this.objMemberHistory.updateMemberHistory(historyID=arguments.event.getValue('hID'), memberID=arguments.event.getValue('associatedMemberID'), 
					categoryID=local.sCategoryID, subCategoryID=local.sSubCategoryID, userDate=arguments.event.getValue('userDate'),
					userEndDate=arguments.event.getValue('userEndDate'), description=arguments.event.getTrimValue('description'),
					quantity=val(arguments.event.getValue('quantity',0)), dollarAmt=local.dollarAmt,
					linkMemberID=val(arguments.event.getValue('linkMemberID',0)))>

		<cfsavecontent variable="local.data">
			<cfoutput>Entry has been saved.</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="deleteMemberHistoryItem" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = "The item could not be deleted. Contact MemberCentral for assistance.">
		<cfset local.DeleteEntryMemberHistoryCategoryRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="MemberHistoryCategory", functionName="DeleteEntry")>
			
		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemHistory">
				set nocount on;

				declare @siteID int, @memberID int, @functionID int, @groupPrintID int;

				set @siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteID')#" cfsqltype="CF_SQL_INTEGER">;
				set @memberID = <cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">;
				set @functionID = <cfqueryparam value="#local.DeleteEntryMemberHistoryCategoryRFID#" cfsqltype="CF_SQL_INTEGER">;
				select @groupPrintID = groupPrintID from dbo.ams_members where memberID = @memberID;

				select mh.historyID
				from dbo.ams_memberHistory as mh
				inner join dbo.cms_categories as cP on cP.categoryID = mh.categoryID
					and mh.historyID = <cfqueryparam value="#arguments.event.getValue('hid',0)#" cfsqltype="CF_SQL_INTEGER">
					and mh.siteID = @siteID
				inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID
					and srfrp.siteResourceID = cP.siteResourceID
					and srfrp.functionID = @functionID
				inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
					and gprp.rightPrintID = srfrp.rightPrintID
					and gprp.groupPrintID = @groupPrintID;
			</cfquery>

			<cfif local.qryMemHistory.recordcount>
				<cfset CreateObject('component','memberHistory').deleteMemberHistory(siteID=arguments.event.getValue('mc_siteinfo.siteID'), hIDList=arguments.event.getValue('hid',0))>

				<cfsavecontent variable="local.data">
					<cfoutput>
						<script type="text/javascript">doRemoveItemDone();</script>
					</cfoutput>
				</cfsavecontent>
			<cfelse>
				<cfset local.data = "You don't have permission to delete this entry item.">
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
		</cfcatch>
		</cftry>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="deleteMemberHistoryItems" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = "The items could not be deleted. Contact MemberCentral for assistance.">

		<cfset local.qryMemHistory = this.objMemberHistory.getMemberHistoryFromFilters(event=arguments.event,mode='deleteMH')>
		<cfif valueList(local.qryMemHistory.historyID) gt 0>
			<cfset this.objMemberHistory.deleteMemberHistory(siteID=arguments.event.getValue('mc_siteinfo.siteID'), hIDList=valueList(local.qryMemHistory.historyID))>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">doRemoveItemDone();dofilterMH();</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- CATEGORY FUNCTIONS --->
	<cffunction name="listCategories" access="public" output="false" returntype="struct" hint="list categories for member history">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();

			// security 
			// --- might need to send to message page that says they dont have right to view this page
			if (NOT arguments.event.getValue('mc_adminToolInfo.myRights.ViewMemberHistoryTypes'))
				application.objCommon.redirect("#this.link.message#&message=1");

			local.manageCategoriesURL = buildLinkToTool(toolType='MemberHistoryAdmin', mca_ta='listHistory') & '&tab=categories';
		</cfscript>
		<cfsavecontent variable="local.data">
			<div >
				<cfoutput>
					<p>Category Management has been moved to the new Categories tab in Member History.</p>		
					<a href="#local.manageCategoriesURL#">Click here to manage categories</a>
				</cfoutput>
			</div>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editCategory" access="public" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif NOT arguments.event.getValue('mc_adminToolInfo.myRights.EditCategories')>
			<cfset application.objCommon.redirect("#this.link.message#&message=1")>
		</cfif>
		<cfset local.strFrom = arguments.event.getValue('strFrom','memhistory')>
		<cfif local.strFrom EQ 'relationships'>
			<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='RelationshipAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
			<cfset local.resourceType = "RelationshipAdmin">
		<cfelseif local.strFrom EQ 'notes'>
			<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='HistoryAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
			<cfset local.resourceType = "HistoryAdmin">
		<cfelse>
			<cfset local.siteResourceID = this.siteResourceID>
			<cfset local.resourceType = "MemberHistoryAdmin">		
		</cfif>

		<cfset local.data = getCategoryFormBySiteResourceID(
			siteResourceID=local.siteResourceID,
			categoryID=arguments.event.getValue('categoryID',0),
			resourceType=local.resourceType,
			isModalContent=arguments.event.getValue('isModalContent', 1),
			usageMode=arguments.event.getValue('usageMode', 'MHCatAdmin'),
			uniqueWidgetSelectorID=arguments.event.getValue('uniqueWidgetSelectorID', '')
		)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getCategoryFormBySiteResourceID" access="public" returntype="string">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="resourceType" type="string" required="true">
		<cfargument name="isModalContent" type="numeric" required="false" default="1">
		<cfargument name="usageMode" type="string" required="false" default="">
		<cfargument name="uniqueWidgetSelectorID" type="string" required="false" default="">
		
		<cfset local = structNew()>
		<cfset local.objCategories = CreateObject('component','model.system.platform.category')>

		<cfset local.strCategory = local.objCategories.getCategoryData(arguments.categoryID).info>		
		<cfset local.qryCategories = local.objCategories.getCategories(siteResourceID=arguments.siteResourceID)>
		<cfset local.strCategoryLabels = getMemberHistoryCategoryLabels(resourceType=arguments.resourceType)>

		<cfquery name="local.qryCategoryTree" datasource="#application.dsn.membercentral.dsn#">
			select dbo.fn_getCategoryTreeIDForSiteResourceID(<cfqueryparam value="#arguments.siteResourceID#" cfsqltype="CF_SQL_INTEGER">) as categoryTreeID
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_category.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doSaveCategory" access="public" output="false" returntype="struct" hint="common save category function">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="resourceType" type="string" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="categoryName" type="string" required="true">
		<cfargument name="categoryDescription" type="string" required="true">
		<cfargument name="categoryCode" type="string" required="true">
		<cfargument name="parentCategoryID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { "success":true, "errmsg":"", "categoryid":arguments.categoryID }>
		<cfset local.objCategories = CreateObject('component','model.system.platform.category')>

		<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName=arguments.resourceType,siteID=arguments.mcproxy_siteID)>
		<cfset local.tmpRights = buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>

		<cftry>
			<cfif not structKeyExists(local.tmpRights,"EditCategories") or local.tmpRights.EditCategories is not 1>
				<cfthrow message="invalid request">
			</cfif>

			<cfif arguments.categoryID GT 0>
				<cfif NOT CreateObject('component','model.admin.memberHistory.memberHistory').checkModifyCategory(siteID=arguments.mcproxy_siteID, categoryID=arguments.categoryID)>
					<cfset application.objCommon.redirect("#this.link.message#&message=4&mode=direct")>
				</cfif>

				<cfset local.strUpdateResult = local.objCategories.updateCategory(categoryID=arguments.categoryID,
					categoryName=arguments.categoryName, categoryDescription=arguments.categoryDescription,
					categoryCode=arguments.categoryCode, parentCategoryID=arguments.parentCategoryID)>

				<cfset local.data.success = local.strUpdateResult.success>
				<cfset local.data.errmsg = local.strUpdateResult.errmsg>
			<cfelse>
				<cfset local.strAddResult = local.objCategories.addCategory(siteResourceID=local.siteResourceID,
					parentCategoryID=arguments.parentCategoryID, categoryName=arguments.categoryName,
					categoryDescription=arguments.categoryDescription, categoryCode=arguments.categoryCode)>

				<cfset local.data.categoryid = local.strAddResult.categoryID>
				<cfset local.data.success = local.strAddResult.success>
				<cfset local.data.errmsg = local.strAddResult.errmsg>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getMemberHistoryCategoryLabels" access="public" output="false" returntype="struct">
		<cfargument name="resourceType" type="string" required="true">

		<cfset var strCategoryLabels = {}>

		<cfswitch expression="#arguments.resourceType#">
			<cfcase value="RelationshipAdmin">
				<cfset strCategoryLabels = { "categoryLabel":"Relationship Category", "categoryLabelPlural":"Relationship Categories" }>
			</cfcase>
			<cfcase value="HistoryAdmin">
				<cfset strCategoryLabels = { "categoryLabel":"Note Category", "categoryLabelPlural":"Note Categories" }>
			</cfcase>
			<cfcase value="MemberHistoryAdmin">
				<cfset strCategoryLabels = { "categoryLabel":"Member History Category", "categoryLabelPlural":"Member History Categories" }>
			</cfcase>
		</cfswitch>

		<cfreturn strCategoryLabels>
	</cffunction>
	
	<cffunction name="deleteCategory" access="public" output="false" returntype="struct" hint="remove category">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="controllingSiteResourceID" type="numeric" required="true">

		<cfset var local = structNew()>
			
		<cfscript>
			local.tmpMHRights = buildRightAssignments(siteResourceID=arguments.controllingSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID);

			// security 
			// --- might need to send to message page that says they dont have right to view this page
			if (NOT local.tmpMHRights.DeleteCategories) {
				local.data.success = false;
			}
			else {
				// delete the category ---------------------------------------------------------------------
				local.data = CreateObject('component', 'model.system.platform.category').deleteCategory(categoryID=arguments.categoryID);
			}
		</cfscript>

		<cfreturn local.data>
	</cffunction>

	<!--- IMPORT FUNCTIONS --->
	<cffunction name="preProcessImport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		
		<cfset local.typeID = arguments.event.getValue('typeID',1)>
		<cfset local.strHistoryType = getHistoryTypeDetails(typeID=local.typeID)>

		<cfset local.strImport = { resourceType=local.strHistoryType.historyType, importTitle='', processImportLink=buildCurrentLink(arguments.event,"processImport") & "&tab=import", doAgainLink=local.strHistoryType.importLink }>

		<cfset local.strImport.importDesc = "Map the columns of your import file to the columns for this import.<br/>We have preselected the import column that matches the required column name.">

		<cfset local.strFormFields = { resourceType=local.strHistoryType.historyType, typeID=local.typeID }>

		<cfset local.qryImportColumns = getImportFieldDetails(typeID=local.typeID, mode='import')>
		
		<cfset local.impData = createObject("component","model.admin.common.modules.import.import").preProcessImport(event=arguments.event, strImport=local.strImport, 
									strFormFields=local.strFormFields, qryImportColumns=local.qryImportColumns, formFieldName='mhImportFileName')>

		<cfset local.data = showHistoryImportResults(event=arguments.event, impData=local.impData, mode='preprocess')>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processImport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfsetting requesttimeout="500">

		<cfset local.objMemberHistory = CreateObject("component","memberHistory")>
		<cfset local.typeID = arguments.event.getValue('typeID',1)>
		<cfset local.strHistoryType = getHistoryTypeDetails(typeID=local.typeID)>
		
		<cfset local.qryImportColumns = getImportFieldDetails(typeID=local.typeID, mode='importTemplate')>
		<cfset local.strImportResult = local.objMemberHistory.importHistory(event=arguments.event, qryImportColumns=local.qryImportColumns)>
				
		<cfif not local.strImportResult.success and structKeyExists(local.strImportResult,"strImportDetails") and arrayLen(local.strImportResult.strImportDetails.arrImportColumnDetails)>
			<cfset local.strImport = { resourceType=local.strHistoryType.historyType, importTitle='#local.strHistoryType.historyTitle# Import - Column Mapping', importDesc='', 
										processImportLink=buildCurrentLink(arguments.event,"processImport") & "&tab=import", doAgainLink=local.strHistoryType.importLink }>

			<cfset local.qryImportColumns = getImportFieldDetails(typeID=local.typeID, arrImportColumnDetails=local.strImportResult.strImportDetails.arrImportColumnDetails, mode='import')>

			<cfset local.strImportResult.previousMappingScreen = createObject("component","model.admin.common.modules.import.import").showImportProcessResults(strImport=local.strImport, strFormFields=local.strImportResult.strImportDetails.strFormFields, qryImportColumns=local.qryImportColumns)>
		</cfif>

		<cfset local.impData = local.objMemberHistory.showImportResults(strImportResult=local.strImportResult, doAgainURL=local.strHistoryType.importLink)>
		<cfset local.data = showHistoryImportResults(event=arguments.event, impData=local.impData, mode='process')>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="sampleImportTemplate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","memberHistory").generateImportTemplate(arguments.event.getValue('typeID',1))>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getImportFieldDetails" access="private" output="false" returntype="query">
		<cfargument name="typeID" type="numeric" required="true">
		<cfargument name="arrImportColumnDetails" type="array" required="false" default="#arrayNew(1)#">
		<cfargument name="mode" type="string" required="true" hint="import or importTemplate">

		<cfset var local = structNew()>
		<cfset local.columnNameRegex = "[^A-Za-z0-9_\-\&\(\)\:\/\s]">

		<cfif arguments.typeID is 1>
			<cfset local.importFieldList = "MemberNumber|STRING|1,Category|STRING|1,Subcategory|STRING|0,StartDate|Date|0,EndDate|Date|0,Description|STRING|0,Quantity|INTEGER|0,Amount|DECIMAL|0,LinkedMemberNumber|STRING|0">
		<cfelseif arguments.typeID is 2>
			<cfset local.importFieldList = "MemberNumber|STRING|1,Category|STRING|1,Subcategory|STRING|0,StartDate|Date|0,EndDate|Date|0,Description|STRING|0,LinkedMemberNumber|STRING|1">
		<cfelseif arguments.typeID is 3>
			<cfset local.importFieldList = "MemberNumber|STRING|1,Category|STRING|1,Subcategory|STRING|0,StartDate|Date|0,EndDate|Date|0,Description|STRING|0,LinkedMemberNumber|STRING|0">
		</cfif>

		<cfset local.strHistoryType = getHistoryTypeDetails(typeID=arguments.typeID)>
		
		<cfquery name="local.qryImportColumns" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			IF OBJECT_ID('tempdb..##tmpImportColumnHeaders') IS NOT NULL 
				DROP TABLE ##tmpImportColumnHeaders;
			IF OBJECT_ID('tempdb..##tmpImportColumns') IS NOT NULL 
				DROP TABLE ##tmpImportColumns;
			IF OBJECT_ID('tempdb..##tmpImportColumnDetails') IS NOT NULL 
				DROP TABLE ##tmpImportColumnDetails;
			
			CREATE TABLE ##tmpImportColumnHeaders (headerRowID int, header varchar(200));
			CREATE TABLE ##tmpImportColumns (columnID int IDENTITY(1,1), columnName varchar(500), dataTypeCode varchar(12), isRequired bit, headerRowID int, defaultColValue varchar(max));
			CREATE TABLE ##tmpImportColumnDetails (columnID int, mappedColValue varchar(500), mappedColOverrideValue varchar(max));
			
			INSERT INTO ##tmpImportColumnHeaders (headerRowID, header)
			select 	1, '#local.strHistoryType.historyTitle# Import - Column Mapping';

			<cfif arrayLen(arguments.arrImportColumnDetails)>
				<cfloop array="#arguments.arrImportColumnDetails#" index="local.thisColumn">
					INSERT INTO ##tmpImportColumnDetails (columnID, mappedColValue, mappedColOverrideValue)
					VALUES (#int(val(local.thisColumn.columnID))#,'#replace(local.thisColumn.mappedColValue,"'","''","ALL")#','#replace(local.thisColumn.mappedColOverrideValue,"'","''","ALL")#');
				</cfloop>
			</cfif>
			
			<cfloop list="#local.importFieldList#" index="local.thisField">
				INSERT INTO ##tmpImportColumns (columnName, dataTypeCode, isRequired, headerRowID)
				VALUES ('#listFirst(local.thisField,'|')#', '#listGetAt(local.thisField,2,'|')#', #listGetAt(local.thisField,3,'|')#, 1);
			</cfloop>

			<cfif arguments.mode is 'importTemplate'>
				select columnID, quotename(dbo.fn_regexReplace(columnName,'#local.columnNameRegex#','')) as columnName 
				from ##tmpImportColumns
				where defaultColValue is null
				order by columnID;
			<cfelseif arguments.mode is 'import'>
				select tmpH.header, tmpCol.columnID, dbo.fn_regexReplace(tmpCol.columnName,'#local.columnNameRegex#','') as columnName, tmpCol.dataTypeCode, 
					tmpCol.isRequired, tmpCol.defaultColValue, tmpColDetails.mappedColValue, tmpColDetails.mappedColOverrideValue 
				from ##tmpImportColumns as tmpCol 
				inner join ##tmpImportColumnHeaders as tmpH on tmpH.headerRowID = tmpCol.headerRowID
				left outer join ##tmpImportColumnDetails as tmpColDetails on tmpColDetails.columnID = tmpCol.columnID
				order by tmpCol.headerRowID, tmpCol.columnID;
			<cfelse>
				select columnID, columnName 
				from ##tmpImportColumns
				order by columnID;
			</cfif>

			IF OBJECT_ID('tempdb..##tmpImportColumnHeaders') IS NOT NULL 
				DROP TABLE ##tmpImportColumnHeaders;
			IF OBJECT_ID('tempdb..##tmpImportColumns') IS NOT NULL 
				DROP TABLE ##tmpImportColumns;
			IF OBJECT_ID('tempdb..##tmpImportColumnDetails') IS NOT NULL 
				DROP TABLE ##tmpImportColumnDetails;
		</cfquery>

		<cfreturn local.qryImportColumns>
	</cffunction>

	<cffunction name="showHistoryImportResults" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
		<cfargument name="impData" type="string" required="true">
		<cfargument name="mode" type="string" required="true">

		<cfset var local = structNew()>

		<cfset local.typeID = arguments.event.getValue('typeID',1)>
		<cfif local.typeID eq 1>
			<cfset local.result = listHistory(event=arguments.event, impData=arguments.impData)>
		<cfelseif local.typeID eq 2>
			<cfset local.result = listRelationships(event=arguments.event, impData=arguments.impData)>
		<cfelseif local.typeID eq 3>
			<cfset local.result = listNotes(event=arguments.event, impData=arguments.impData)>
		</cfif>

		<cfreturn local.result.data>
	</cffunction>

	<cffunction name="getHistoryTypeDetails" access="private" output="false" returntype="struct">
		<cfargument name="typeID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfif arguments.typeID is 2>
			<cfset local.historyType = "Relationships">
			<cfset local.historyTitle = "Relationships">
			<cfset local.importLink = this.link.listRelationships & "&tab=import">
		<cfelseif arguments.typeID is 3>
			<cfset local.historyType = "Notes">
			<cfset local.historyTitle = "Notes">
			<cfset local.importLink = this.link.listNotes & "&tab=import">
		<cfelse>
			<cfset local.historyType = "MemberHistory">
			<cfset local.historyTitle = "Member History">
			<cfset local.importLink = this.link.listHistory & "&tab=import">
		</cfif>

		<cfreturn local>
	</cffunction>
		
	<cffunction name="exportMemberHistory" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.data = "The export file could not be generated. Contact MemberCentral for assistance.">

		<cfset local.typeID = arguments.event.getValue('typeID',1)>
		<cfif local.typeID is 2>
			<cfset local.historyType = "Relationships">
			<cfset local.categoryName = "Category">
			<cfset local.userDateName = "Relationship">
		<cfelseif local.typeID is 3>
			<cfset local.historyType = "Notes">
			<cfset local.categoryName = "Category">
			<cfset local.userDateName = "Note">
		<cfelse>
			<cfset local.historyType = "MemberHistory">
			<cfset local.categoryName = "Category">
			<cfset local.userDateName = "History">
		</cfif>
		
		<!--- set vars --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "#local.historyType#-#DateFormat(Now(),'yyyymmdd')#.csv">

		<cfset local.qryMemberHistory = this.objMemberHistory.getMemberHistoryFromFilters(event=arguments.event, mode="exportMH")>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryExportMemHistory">
			set nocount on;

			IF OBJECT_ID('tempdb..##tmpMHExport') IS NOT NULL
				DROP TABLE ##tmpMHExport;
			IF OBJECT_ID('tempdb..##tblMemHistory') IS NOT NULL 
				DROP TABLE ##tblMemHistory;
			CREATE TABLE ##tblMemHistory (historyID int PRIMARY KEY);

			DECLARE @siteID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteID')#" cfsqltype="CF_SQL_INTEGER">;

			INSERT INTO ##tblMemHistory
			select historyID
			from dbo.ams_memberHistory
			where historyID in (0#valueList(local.qryMemberHistory.historyID)#)
			and siteID = @siteID;

			select ROW_NUMBER() OVER (ORDER BY mActive.lastName, mActive.firstName, mh.userDate desc, mh.historyID desc) as row,
				mh.dateEntered as [Date Entered],
				mActive.lastName + ', ' + mActive.firstName as [Member], mActive.memberNumber as [MemberNumber], mActive.company as [Member Company],
				cP.categoryName + isnull(' / '+cg2.categoryName,'') as [#local.categoryName#],
				mh.userDate as [#local.userDateName# Start Date],
				mh.userEndDate as [#local.userDateName# End Date],
				<cfif local.typeID is 1>
					mh.quantity as [Quantity], mh.dollarAmt as [Amount], 
				</cfif>
				mh.description as [Description], 
				mLinkActive.lastName + ', ' + mLinkActive.firstName as [Linked Member], mLinkActive.memberNumber as [Linked MemberNumber], mLinkActive.company as [Linked Member Company],
				mEnteredActive.lastName + ', ' + mEnteredActive.firstName + ' (' + mEnteredActive.memberNumber + ')' as [Entered By Member]
			into ##tmpMHExport
			from ##tblMemHistory as tmp
			inner join dbo.ams_memberHistory as mh on mh.historyID = tmp.historyID
				and mh.siteID = @siteID
			inner join dbo.cms_categories as cP on cP.categoryID = mh.categoryID
			inner join dbo.cms_categoryTrees as cPt on cPt.categoryTreeID = cP.categoryTreeID
			inner join dbo.ams_members as m on m.memberID = mh.memberID
			inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
			inner join dbo.ams_members as mEntered on mEntered.memberID = mh.enteredByMemberID
			inner join dbo.ams_members as mEnteredActive on mEnteredActive.memberID = mEntered.activeMemberID
			left outer join cms_categories cg2 on cg2.categoryID = mh.subCategoryID
			left outer join dbo.ams_members as mLink 
				inner join dbo.ams_members as mLinkActive on mLinkActive.memberID = mLink.activeMemberID
				on mLink.memberID = mh.linkMemberID;
			
			DECLARE @selectsql varchar(max) = '
				SELECT [Date Entered], [Member], [MemberNumber], [Member Company], [#local.categoryName#], [#local.userDateName# Start Date], 
					[#local.userDateName# End Date], <cfif local.typeID is 1>[Quantity], [Amount], </cfif> [Description], 
					[Linked Member], [Linked MemberNumber], [Linked Member Company], [Entered By Member], row as mcCSVorder 
				*FROM* ##tmpMHExport';
			EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

			IF OBJECT_ID('tempdb..##tmpMHExport') IS NOT NULL
				DROP TABLE ##tmpMHExport;
			IF OBJECT_ID('tempdb..##tblMemHistory') IS NOT NULL 
				DROP TABLE ##tblMemHistory;
		</cfquery>

		<!--- create download URL to send user to --->
		<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">doExportMH('#local.stDownloadURL#');</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- Get Messages for Application--->
	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry...</h4>
				<cfif arguments.event.valueExists('message')>
					<p>
						<cfswitch expression="#arguments.event.getValue('message')#">
							<cfcase value="1"><b>You do not have rights to this section.</b></cfcase>
							<cfcase value="2"><b>That History Item was not found.</b></cfcase>
							<cfcase value="3">
								<b>There was a problem saving this member's history information.</b><br/><br/>
								If you continue to see this error, contact Support for further assistance.
							</cfcase>
							<cfcase value="4"><b>You do not have permissions to modify this category.</b></cfcase>
						</cfswitch>
					</p>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addCategoryMulti" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();
		local.strFrom = arguments.event.getValue('strFrom','memhistory');
		if(local.strFrom EQ 'memhistory')
			local.siteResourceID = this.siteResourceID;
		else if (local.strFrom EQ 'relationships')
			local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='RelationshipAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'));
		else if (local.strFrom EQ 'notes')
			local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='HistoryAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'));

		local.formSubmit = this.link.insertMultiCategories &"&strFrom=#local.strFrom#";
		</cfscript>
		
		<cfquery name="local.qryCategoryTree" datasource="#application.dsn.membercentral.dsn#">
			select dbo.fn_getCategoryTreeIDForSiteResourceID(<cfqueryparam value="#local.siteResourceID#" cfsqltype="CF_SQL_INTEGER">) as categoryTreeID
		</cfquery>

		<cfset arguments.event.setValue('categoryTreeID',local.qryCategoryTree.categoryTreeID)>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_editCategoryMulti.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="insertMultiCategories" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.crlf = "#Chr(13)##Chr(10)#";
			
			// security 
			if (NOT arguments.event.getValue('mc_adminToolInfo.myRights.EditCategories'))
				 application.objCommon.redirect("#this.link.message#&message=1");

			local.strFrom = arguments.event.getValue('strFrom','memhistory');
			if(local.strFrom EQ 'memhistory')
				local.siteResourceID = this.siteResourceID;
			else if (local.strFrom EQ 'relationships')
				local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='RelationshipAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'));
			else if (local.strFrom EQ 'notes')
				local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='HistoryAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'));

			local.qryCategories = this.objMemberHistory.getParentCategories(siteID=arguments.event.getValue('mc_siteinfo.siteid'), siteResourceID=local.siteResourceID);
			local.modifyCategoryIDList = valueList(local.qryCategories.categoryID);
			local.arrCatValues = listToArray(arguments.event.getTrimValue('catValueList',''),local.crlf);
			for(local.i=1; local.i<=arrayLen(local.arrCatValues);local.i=local.i+1) {
				local.tempList = local.arrCatValues[local.i];
				local.pipeCount = len(REReplace(local.tempList, "[^\|]+", "", "ALL" ));

				if(listFind("0,1",local.pipeCount) AND find("|",local.arrCatValues[local.i]) NEQ 1) {
					local.arrTypeValues = listToArray(local.arrCatValues[local.i], '|');
					
					if(arrayLen(local.arrTypeValues) AND arrayIsDefined(local.arrTypeValues, 1) AND len(trim(local.arrTypeValues[1]))) {
						local.arrTypeValues[1] = trim(local.arrTypeValues[1]);

						local.parentCategory = this.objCategories.getCategoryIDFromName(categoryTreeID = arguments.event.getValue('categoryTreeID'),
							parentCategoryID = 0, categoryname = local.arrTypeValues[1]);
						local.categoryID = local.parentCategory.categoryID;
						
						if (local.categoryID is 0) {								
							local.addCat = this.objCategories.addCategory(siteResourceID=local.siteResourceID, parentCategoryID=0,
								categoryName=local.arrTypeValues[1], categoryDescription='', categoryCode='');
							if (local.addCat.success) {
								local.msgVal = 1;
								local.categoryID = local.addCat.categoryID;
								local.modifyCategoryIDList = listAppend(local.modifyCategoryIDList,local.categoryID);
							} else {
								local.msgVal = 3;
								local.categoryID = 0;
							}
						} else if (local.categoryID gt 0 and not listFindNocase(local.modifyCategoryIDList,local.categoryID)) {
							local.categoryID = 0;
						}
						
						if (local.categoryID gt 0) {
							if (arrayLen(local.arrTypeValues) EQ 2 AND len(trim(local.arrTypeValues[2]))) {
								local.arrTypeValues[2] = trim(local.arrTypeValues[2]);

								local.childCategory = this.objCategories.getCategoryIDFromName(categoryTreeID = arguments.event.getValue('categoryTreeID'),
									parentCategoryID = local.categoryID, categoryname = local.arrTypeValues[2]);
								local.childCategoryID = local.childCategory.categoryID;
								
								if(local.childCategoryID is 0) {
									local.addCat = this.objCategories.addCategory(siteResourceID=local.siteResourceID, parentCategoryID=local.categoryID,
										categoryName=local.arrTypeValues[2], categoryDescription='', categoryCode='');
								}
							}
						}
					}
				}
			}
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				parent.MCModalUtils.hideModal();
				parent.categoriesTable.draw();
			</script>
			</cfoutput>
		</cfsavecontent>
				
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="massEmailMemberHistory" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.typeID = arguments.event.getValue('typeID',1)>
		<cfif local.typeID is 2>
			<cfset local.historyType = "Relationships">
		<cfelseif local.typeID is 3>
			<cfset local.historyType = "Notes">
		<cfelse>
			<cfset local.historyType = "Member History">
		</cfif>

		<cfset local.strFilters = structNew()>
		<cfset structInsert(local.strFilters, 'typeID', local.typeID)>
		<cfset structInsert(local.strFilters, 'fDateFrom', arguments.event.getValue('fDateFrom',''))>
		<cfset structInsert(local.strFilters, 'fDateTo', arguments.event.getValue('fDateTo',''))>
		<cfset structInsert(local.strFilters, 'fEntDateFrom', arguments.event.getValue('fEntDateFrom',''))>
		<cfset structInsert(local.strFilters, 'fEntDateTo', arguments.event.getValue('fEntDateTo',''))>
		<cfset structInsert(local.strFilters, 'fEndDateFrom', arguments.event.getValue('fEndDateFrom',''))>
		<cfset structInsert(local.strFilters, 'fEndDateTo', arguments.event.getValue('fEndDateTo',''))>
		<cfset structInsert(local.strFilters, 'parentChildCategoryID', arguments.event.getValue('parentChildCategoryID',0))>
		<cfset structInsert(local.strFilters, 'fKeyword', arguments.event.getValue('fKeyword',''))>
		<cfset structInsert(local.strFilters, 'fQuantityFrom', arguments.event.getValue('fQuantityFrom',''))>
		<cfset structInsert(local.strFilters, 'fQuantityTo', arguments.event.getValue('fQuantityTo',''))>
		<cfset structInsert(local.strFilters, 'fAmtFrom', arguments.event.getValue('fAmtFrom',''))>
		<cfset structInsert(local.strFilters, 'fAmtTo', arguments.event.getValue('fAmtTo',''))>
		<cfset structInsert(local.strFilters, 'fSelectMemberID', arguments.event.getValue('fSelectMemberID',0))>
		<cfset structInsert(local.strFilters, 'fMHLink', arguments.event.getValue('fMHLink',''))>
		<cfset structInsert(local.strFilters, 'LimitToMemberID', arguments.event.getValue('LimitToMemberID',0))>
		<cfset structInsert(local.strFilters, 'fAssignedMemberID', arguments.event.getValue('fAssignedMemberID',0))>
		<cfset structInsert(local.strFilters, 'fAssignedGroupID', arguments.event.getValue('fAssignedGroupID',0))>
		<cfset structInsert(local.strFilters, 'fLinkedMemberID', arguments.event.getValue('fLinkedMemberID',0))>
		<cfset structInsert(local.strFilters, 'fLinkedGroupID', arguments.event.getValue('fLinkedGroupID',0))>
		<cfset structInsert(local.strFilters, 'chkAll', arguments.event.getValue('chkall',0))>
		<cfset structInsert(local.strFilters, 'historyIDList', arguments.event.getValue('historyIDList',''))>
		<cfset structInsert(local.strFilters, 'notHistoryIDList', arguments.event.getValue('notHistoryIDList',''))>

		<cfset local.arrRecipientModes = arrayNew(1)>
		<cfset local.tmpStr = { mode='both', desc='Both Sides', domEvent='onclick' }>
		<cfset arrayAppend(local.arrRecipientModes,local.tmpStr)>
		
		<cfset local.tmpStr = { mode='member', desc='Member Only', domEvent='onclick' }>
		<cfset arrayAppend(local.arrRecipientModes,local.tmpStr)>
		
		<cfset local.tmpStr = { mode='linked', desc='Linked Member Only', domEvent='onclick' }>
		<cfset arrayAppend(local.arrRecipientModes,local.tmpStr)>

		<cfset local.strResourceTitle = { resourceTitle=local.historyType, resourceTitleDesc='', templateEditorLabel='Compose your message.' }>
		
		<cfset local.argumentCollection = { siteID=arguments.event.getValue('mc_siteinfo.siteid'), siteCode=arguments.event.getValue('mc_siteinfo.sitecode'), resourceType=local.historyType, 
					recipientType='Members', strResourceTitle=local.strResourceTitle, strFilters=local.strFilters, arrRecipientModes=local.arrRecipientModes,
					mergeCodeInstructionsLink="#buildCurrentLink(arguments.event,'showMergeCodeInstructions')#&typeID=#local.typeID#&incMH=1&mode=stream", emailTemplateTreeCode="ETHISTORY" }>

		<cfset local.data = CreateObject("component","model.admin.common.modules.massEmails.massEmails").prepMassEmails(argumentCollection=local.argumentCollection)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
</cfcomponent>