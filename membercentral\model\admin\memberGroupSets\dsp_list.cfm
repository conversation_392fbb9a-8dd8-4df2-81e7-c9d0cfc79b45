<cfset local.selectedTab = event.getTrimValue("tab","GroupSet")>
<cfif event.getTrimValue("lockTab","false")>
	<cfset local.lockTab = local.selectedTab>
<cfelse>
	<cfset local.lockTab = "">
</cfif>
<cfsavecontent variable="local.GSListJS">
	<cfoutput>
		<script language="JavaScript">
			let gridInitArray = [{'importExportTab':false}], groupSetTable;
			
			function previewGroupset(gsID) {
				MCModalUtils.showModal({
					isslideout: true,
					size: 'lg',
					title: 'Preview Group Set',
					contenturl: '#this.link.previewGroupset#&gsID='+gsID,
					strmodalfooter: {
						showclose: true
					}
				});
			}
			function initializeGroupSetTable() {
				let domString = "<'row'<'col-sm-6 col-md-6'<'float-left mt-2'l>><'col-sm-6 col-md-6'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";
				groupSetTable = $('##groupSetTable').DataTable({
					"processing": true,
					"serverSide": true,
					"pageLength": 25,
					"lengthMenu": [ 25, 50, 100 ],
					"dom": domString,
					"language": {
						"lengthMenu": "_MENU_"
					},
					"ajax": { 
						"url": "#local.groupSetLink#",
						"type": "post",
						"data": function(d) {
							$.each($('##frmFilter').serializeArray(),function() {
								d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
							});
						}
					},
					"autoWidth": false,
					"columns": [
						{ "data": "GroupSetName" },
						{ 
							"data": "groupCount",
						  	"width": "15%", 
						  	"className": "text-center"
						},
						{ 
							"data": null,
							"render": function ( data, type, row, meta ) {
								let renderData = '';
								if (type === 'display') {
									renderData += '<a href="javascript:editGroupSet('+data.GroupsetID+');" class="btn btn-xs text-primary p-1 m-1" title="Edit Member Group Set"><i class="fa-solid fa-pencil"></i></a>';
									renderData += '<a href="javascript:previewGroupset('+data.GroupsetID+');" class="btn btn-xs text-primary p-1 m-1" title="Preview this Member Group Set"><i class="fa-solid fa-eye"></i></a>';
									if (data.GroupSetBeingUsed == 0) {
										renderData += '<a href="javascript:deleteGroupSet('+data.GroupsetID+');" id="btnDel'+data.GroupsetID+'" class="btn btn-xs text-danger p-1 m-1" title="Delete Member Group Set"><i class="fa-solid fa-trash-can"></i></a>';
									} else {
										renderData += '<a class="btn btn-xs p-1 m-1 invisible"><i class="fa-solid fa-trash-can"></i></a>';
									}
								}
								return type === 'display' ? renderData : data;
							},
							"width": "15%",
							"className": "text-center",
							"orderable": false
						}
					],
					"order": [[0, 'asc']],
					"searching": false,
					"pagingType": "simple"
				});
				mca_generateVerboseFilterMessage('frmFilter');
			}
			function editGroupSet(gsid) {
				MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: (gsid > 0 ? 'Edit' : 'Add') + ' Member Group Set',
				iframe: true,
				contenturl: '#this.link.edit#&gsID=' + gsid,
				strmodalfooter: {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.validateAndSaveGroupSet',
					extrabuttonlabel: 'Save',
				}
				});
			}
			function deleteGroupSet(gsid) {
				var removeItem = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){
						groupSetTable.draw(false);
					} else {
						delElement.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
						if(r.msg) alert(r.msg);
						else alert('We were unable to delete this Member Group Set. Try again.');
					}
				};

				let delElement = $('##btnDel'+gsid);
				mca_initConfirmButton(delElement, function(){
					var objParams = { gsid:gsid };
					TS_AJX('MEMBERGROUPSETS','doRemoveGroupSet',objParams,removeItem,removeItem,10000,removeItem);
				});
			}
			function gotoList() {
				top.location.href = '#this.link.list#';
			}
			function onTabChangeHandler(ActiveTab) {
				if (!gridInitArray[ActiveTab.id]) {
					gridInitArray[ActiveTab.id] = true;
					switch(ActiveTab.id) {
						case "groupSetTab":
							initializeGroupSetTable(); break;
					}
				}
			}	
			function loadGroupSetGrids_groupsetsadmin() {
				groupSetTable.draw();
			}
			function doFilterGroupSets() {
				mca_generateVerboseFilterMessage('frmFilter');
				loadGroupSetGrids_groupsetsadmin();
			}
			function clearFilterGroupSets() {
				$('##frmFilter')[0].reset();
				$('##frmFilter input[type="text"]').val('');
				doFilterGroupSets();
			}

			$(function() {
				mca_initNavPills('GroupSetPills', '#local.selectedTab#', '#local.lockTab#', onTabChangeHandler);
			});
		</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.GSListJS#">

<cfoutput>
<h4>Member Group Sets</h4>
<ul class="nav nav-pills nav-pills-dotted" id="GroupSetPills">
	<cfset local.thisTabID = "groupSetTab">
	<cfset local.thisTabName = "groupSet">
	<li class="nav-item">
		<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Member Group Sets</a>
	</li>
	<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.thisTabID = "importExportTab">
		<cfset local.thisTabName = "ex">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Import / Export</a>
		</li>
	</cfif>
</ul>

<div class="tab-content mc_tabcontent p-2 pb-0" id="pills-tabContent">
	<div class="tab-pane fade" id="pills-groupSetTab" role="tabpanel" aria-labelledby="groupSetTab">
		<!--- button bar --->
		<div class="toolButtonBar">
			<div><a href="javascript:editGroupSet(0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to add a new Member Group Set."><i class="fa-regular fa-circle-plus"></i> Add Member Group Set</a></div>
		</div>

		<div id="divFilterForm" class="my-3 grpToolBarItem" style="display:none;">
			<form name="frmFilter" id="frmFilter" onsubmit="doFilterGroupSets();return false;" data-filterwrapper="divFilterForm" data-verbosemsgwrapper="divMemberFieldSetFilterVerbose" data-filterkey="#local.filterKeyName#">
				<div class="card card-box mb-1">
					<div class="card-header py-1 bg-light">
						<div class="card-header--title font-weight-bold font-size-lg">
							Filter Member Group Sets
						</div>
					</div>
					<div class="card-body pb-3">
						<div class="form-row">
							<div class="col-sm-6">
								<div class="form-label-group mb-0">
									<input type="text" name="fGroupSetName" id="fGroupSetName" class="form-control" value="#local.MGSFilter.fGroupSetName#">
									<label for="fGroupSetName">Member Group Set Name Contains...</label>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-label-group mb-0">
									<input type="text" name="fGroupSetUID" id="fGroupSetUID" class="form-control" maxlength="60" value="#local.MGSFilter.fGroupSetUID#">
									<label for="fGroupSetUID">API ID</label>
								</div>
							</div>
						</div>			
					</div>
					<div class="card-footer p-2 text-right">
						<button type="button" name="btnResetFilterGroupSets" class="btn btn-sm btn-secondary" onclick="clearFilterGroupSets();">Clear Filters</button>
						<button type="submit" name="btnFilterGroupSets" class="btn btn-sm btn-primary">
							<i class="fa-light fa-filter"></i> Filter Member Group Sets
						</button>
						<button type="button" class="btnReApplyFilter d-none" onclick="loadGroupSetGrids_groupsetsadmin();"></button>
					</div>
				</div>
			</form>
		</div>
		
		<div id="divMemberFieldSetFilterVerbose" style="display:none;"></div>
		<div id="groupSetGrid">
			<table id="groupSetTable" class="table table-sm table-striped table-bordered" style="width:100%">
				<thead>
					<tr>
						<th>Member Group Set</th>
						<th>## Groups</th>
						<th>Actions</th>
					</tr>
				</thead>
			</table>
		</div>
	</div>
	<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<div class="tab-pane fade" id="pills-importExportTab" role="tabpanel" aria-labelledby="importExportTab">
			<cfinclude template="dsp_importexport.cfm">
		</div>
	</cfif>
</div>
</cfoutput>