<cfsavecontent variable="local.pageJS">
	<cfoutput>
	<script type="text/javascript">
		function gotoResultsPage(num) {
			$("##divSearchResultsContainer")
				.html('<div class="mt-4"><div class="text-center"><div class="spinner-border" role="status"></div><h4 class="mt-2">Please wait...</h4></div></div>')
				.load('#local.paging.link#&memPageNum='+num);
		}
		$(document).ready(function () {
			if (top.MCModalUtils.isShown()) {			
				top.$('##MCModalLabel').html('Member Search Results');				
				$('.h4Title').addClass('d-none');
			}else{
				$('.h4Title').removeClass('d-none');
			}
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">

<cfoutput>
<h4 class="h4Title">Member Search Results</h4>

<cfif len(arguments.event.getTrimValue('inGrp'))>
	<div class="p-2 alert alert-info">Results are limited to members of <i>#local.qryGroup.GroupPathExpanded#</i>.</div>
</cfif>
<cfif len(arguments.event.getTrimValue('notInGrp'))>
	<div class="p-2 alert alert-info">Results exclude members of <i>#local.qryGroup.GroupPathExpanded#</i>.</div>
</cfif>
<cfif len(arguments.event.getTrimValue('inEvReg'))>
	<div class="p-2 alert alert-info">Results are limited to registrants of <i>#EncodeForHTML(local.qryEvent.contentTitle)#</i>.</div>
</cfif>
<cfif len(arguments.event.getTrimValue('notInEvReg'))>
	<div class="p-2 alert alert-info">Results exclude registrants of <i>#EncodeForHTML(local.qryEvent.contentTitle)#</i>.</div>
</cfif>
<cfif len(arguments.event.getTrimValue('runMemNumContains',''))>
	<div class="p-2 alert alert-info">We didn't find an exact MemberNumber match, but we found the following partial match<cfif local.qryMembers.mc_totalMatches is not 1>es</cfif>.</div>
</cfif>

<div class="row mx-0">
<div class="col-12">

<div class="row d-flex px-0 mb-2">
	<cfif local.qryMembers.mc_totalMatches gt local.paging.rowsize>
		<div class="col-12 pl-0 pt-2 gridRNumDocs"><b>Page #local.paging.currpage#</b>&nbsp;&nbsp;(Showing #local.currCountStart# - #local.currCountStop# of #NumberFormat(local.qryMembers.mc_totalMatches)# matches found)</div>
	<cfelse>
		<div class="col-7 pl-0 pt-2 gridRNumDocs"><b>#NumberFormat(local.qryMembers.mc_totalMatches)# match<cfif local.qryMembers.mc_totalMatches is not 1>es</cfif> found</b></div>
		<div class="col-5 pr-0 text-right">
			<button class="btn btn-sm btn-primary" name="btnSearchForm" onclick="cancelSearchResults();" type="button" title="Search Again">
			<span class="btn-wrapper--icon"><i class="fa-regular fa-magnifying-glass"></i></span>
			<span class="btn-wrapper--label">Search Again</span>
			</button>
		</div>
	</cfif>	
</div>

<cfif local.paging.rowsize lt local.qryMembers.mc_totalMatches>
	<div class="card mt-1 mb-2 bg-grey row">
		<div class="card-body text-right p-2">
			<button class="btn btn-sm btn-primary" name="btnSearchForm" onclick="cancelSearchResults();" type="button" title="Search Again">
				<span class="btn-wrapper--icon"><i class="fa-regular fa-magnifying-glass"></i></span>
				<span class="btn-wrapper--label">Search Again</span>
			</button>

			<cfif local.paging.prevPage NEQ 0>
				<button  name="btnPrevResults" class="btn btn-sm btn-outline-primary" onclick="gotoResultsPage(#local.paging.prevPage#);" type="button" title="Previous Results">
					<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-chevron-left"></i></span>
					<span class="btn-wrapper--label">Previous</span>
				</button>
			<cfelse>
				<button name="btnPrevResults" class="btn btn-sm btn-outline-primary" disabled type="button" title="No Previous Results">
					<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-chevron-left"></i></span>
					<span class="btn-wrapper--label">Previous</span>
				</button>
			</cfif>
			<cfif (local.paging.currPage*local.paging.rowsize + 1) lte local.qryMembers.mc_totalMatches>
				<button name="btnNextResults" class="btn btn-sm btn-outline-primary"  onclick="gotoResultsPage(#local.paging.nextPage#);" type="button" title="Next Results">
					<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-chevron-right"></i></span>
					<span class="btn-wrapper--label">Next</span>
				</button>
			<cfelse>
				<button name="btnNextResults" class="btn btn-sm btn-outline-primary" disabled type="button" title="No Next Results">
					<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-chevron-right"></i></span>
					<span class="btn-wrapper--label">Next</span>
				</button>
			</cfif>
		</div>
	</div>
</cfif>

<div id="resultsTbl">
	<cfloop query="local.qryMembers">
		<cfset local.mc_combinedName = local.qryMembers['Extended MemberNumber'][local.qryMembers.currentrow]>
	
		<!--- combine address fields if there are any --->
		<cfset local.thisMem_mc_combinedAddresses = duplicate(local.mc_combinedAddresses)>
		<cfif StructCount(local.thisMem_mc_combinedAddresses)>
			<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
				<cfsavecontent variable="local.thisATFull">
					<cfif left(local.thisATID,1) eq "t">
						<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_">
						<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_">
					<cfelse>
						<cfset local.MAfcPrefix = "ma_#local.thisATID#_">
						<cfset local.MPfcPrefix = "mp_#local.thisATID#_">
					</cfif>
		
					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>
					<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#EncodeForHTML(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])#<br/> </cfif>
					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
					<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#EncodeForHTML(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])#<br/> </cfif>
					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
					<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#EncodeForHTML(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])#<br/></cfif>
					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
					<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#EncodeForHTML(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])# </cfif>
					<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
					<cfif arrayLen(local.tmp2) is 1 and len(local.qryMembers[local.tmp2[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>, #EncodeForHTML(local.qryMembers[local.tmp2[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])# </cfif>
					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
					<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])> #EncodeForHTML(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])#<br/></cfif>
					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#county']")>
					<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#EncodeForHTML(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])# County<br/></cfif>
					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#country']")>
					<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])> #EncodeForHTML(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])#<br/></cfif>
					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,#len(local.MPfcPrefix)#)='#local.MPfcPrefix#']")>
					<cfloop array="#local.tmp#" index="local.thisPT">
						<cfif len(local.qryMembers[local.thisPT.xmlAttributes.FieldLabel][local.qryMembers.currentrow])>
							<div>#local.thisPT.xmlAttributes.FieldLabel#: #EncodeForHTML(local.qryMembers[local.thisPT.xmlAttributes.FieldLabel][local.qryMembers.currentrow])#</div>
						</cfif>
					</cfloop>
				</cfsavecontent>
				<cfset local.thisATfull = trim(replace(replace(rereplace(local.thisATFull,'[\r\n\t]','','ALL'),'  ',' ','ALL'),' ,',',','ALL'))>
				<cfif left(local.thisATfull,2) eq ", ">
					<cfset local.thisATfull = right(local.thisATfull,len(local.thisATfull)-2)>
				</cfif>
				<cfif len(local.thisATfull)>
					<cfset local.thisMem_mc_combinedAddresses[local.thisATID]['addr'] = local.thisATfull>
				<cfelse>
					<cfset structDelete(local.thisMem_mc_combinedAddresses,local.thisATID,false)>
				</cfif>
			</cfloop>
		</cfif>

		<!--- get last login date if available --->
		<cfif arrayLen(local.LastLoginDateInFS) is 1>
			<cfif len(local.qryMembers[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>
				<cfset local.mc_dateLastLogin = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: #DateFormat(local.qryMembers[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow],"m/d/yy")#'>
			<cfelse>
				<cfset local.mc_dateLastLogin = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: <span class="text-muted"><i>none</i></span>'>
			</cfif>
		<cfelse>
			<cfset local.mc_dateLastLogin = "">
		</cfif>

		<!--- get recordtype if available --->
		<cfif arrayLen(local.RecordTypeInFS) is 1 and len(local.qryMembers[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>
			<cfset local.mc_recordType = local.qryMembers[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]>
		<cfelse>
			<cfset local.mc_recordType = "">
		</cfif>
		
		<!--- get membertypeid if available --->
		<cfif arrayLen(local.memberTypeInFS) is 1 and len(local.qryMembers[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>
			<cfset local.mc_memberType = local.qryMembers[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]>
		<cfelse>
			<cfset local.mc_memberType = "">
		</cfif>	
		
		<!--- get status if available --->
		<cfif arrayLen(local.memberStatusInFS) is 1 and len(local.qryMembers[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>
			<cfset local.mc_memberStatus = local.qryMembers[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]>
		<cfelse>
			<cfset local.mc_memberStatus = "">
		</cfif>

		<div class="card mb-3 row">
			<div class="card-body d-flex flex-row col-sm-12">
				<div class="pl-0 col-2">
					<cfif local.showMemberPhotosInSearchResults>
						<cfif local.qryMembers.hasMemberPhotoThumb>
							<img class="mc_memthumb" src="/memberphotosth/#LCASE(local.qryMembers.membernumber)#.jpg">
						<cfelse>
							<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
						</cfif>
					</cfif>
				</div>

				<div class="card-text pl-0 col-8">				
					<div>
						<div class="card-title">#EncodeForHTML(local.mc_combinedName)#</div>
						<cfif len(trim(local.qryMembers.company))>
							<div class="mb-2">#EncodeForHTML(local.qryMembers.company)#&nbsp;</div>
						</cfif>
						<cfif StructCount(local.thisMem_mc_combinedAddresses)>
							<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
								<div><h6 class="mr-3 font-weight-bold">#local.thisMem_mc_combinedAddresses[local.thisATID]['type']#</h6>#local.thisMem_mc_combinedAddresses[local.thisATID]['addr']#</div>
							</cfloop>
						</cfif>
						<cfif local.qryOutputFieldsForLoop.recordCount>
							<cfloop query="local.qryOutputFieldsForLoop">
								<cfset local.currValue = local.qryMembers[local.qryOutputFieldsForLoop.fieldLabel][local.qryMembers.currentrow]>
								<cfif len(local.currValue)>
									<div>
										#EncodeForHTML(local.qryOutputFieldsForLoop.fieldLabel)#: 
										<cfif left(local.qryOutputFieldsForLoop.fieldCode,13) eq 'acct_balance_'>
											#dollarFormat(local.currValue)#
										<cfelse>
											<cfswitch expression="#local.qryOutputFieldsForLoop.dataTypeCode#">
												<cfcase value="DATE">
													#dateFormat(local.currValue,"m/d/yyyy")#
												</cfcase>
												<cfcase value="STRING">
													#EncodeForHTML(local.currValue)#
												</cfcase>
												<cfcase value="DECIMAL2,INTEGER">
													#local.currValue#
												</cfcase>
												<cfcase value="BIT">
													#YesNoFormat(local.currValue)#
												</cfcase>
											</cfswitch>
										</cfif>
									</div>
								</cfif>
							</cfloop>
						</cfif>

						<div>
							<cfif len(local.mc_recordType)><span class="badge badge-dark">#local.mc_recordType#</span></cfif>
							<cfif len(trim(local.mc_memberType))><span class="badge badge-dark">#local.mc_memberType#</span></cfif>
							<cfif local.qryMembers.MCAccountStatus eq "I"><span class="badge badge-danger">ACCOUNT INACTIVE</span><cfelseif len(trim(local.mc_memberStatus))><span class="badge badge-info">#local.mc_memberStatus#</span></cfif>

							<cfif isArray(local.arrAllClassifications) and not arrayIsEmpty(local.arrAllClassifications)>
								<cfloop array="#local.arrAllClassifications#" index="local.currentClass">
									<cfset local.qryClass = local.currentClass.qryClass>
									<cfquery name="local.classifications" dbtype="query">
										select groupName
										from [local].qryClass
										where memberID = #local.qryMembers.memberid#
										order by groupName
									</cfquery>
									<cfif local.classifications.recordCount gt 0>					
										<h6 class="mt-2 font-weight-bold">#replace(local.currentClass.name,'_',' ','ALL')#</h6>
										<div>
											<cfloop query="local.classifications">
												<span class="badge badge-info">#local.classifications.groupName#</span>
											</cfloop>
										</div>
									</cfif>
								</cfloop>
							</cfif>
							<cfif len(local.mc_dateLastLogin)><div><span class="badge badge-secondary">#local.mc_dateLastLogin#</span></div></cfif>
						</div>
					</div>
				</div>

				<div class="ml-auto mt-auto mb-auto pl-0 pr-0 col-2">
					<button type="button" name="btnSelectMember" onclick="selectMember(#local.qryMembers.memberid#,'#local.qryMembers.membernumber#','#encodeForJavascript(local.qryMembers.firstname & ' ' & local.qryMembers.lastname)#');" class="btn btn-sm btn-outline-primary"><i class="fa-solid fa-user"></i> Select</button>
				</div>
			</div>
		</div>
	</cfloop>
</div>

<cfif local.paging.rowsize lt local.qryMembers.mc_totalMatches>
	<div class="card bg-grey row">
		<div class="card-body text-right p-2">
			<button class="btn btn-sm btn-primary" name="btnSearchForm" onclick="cancelSearchResults();" type="button" title="Search Again">
				<span class="btn-wrapper--icon"><i class="fa-regular fa-magnifying-glass"></i></span>
				<span class="btn-wrapper--label">Search Again</span>
			</button>

			<cfif local.paging.prevPage NEQ 0>
				<button  name="btnPrevResults" class="btn btn-sm btn-outline-primary" onclick="gotoResultsPage(#local.paging.prevPage#);" type="button" title="Previous Results">
					<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-chevron-left"></i></span>
					<span class="btn-wrapper--label">Previous</span>
				</button>
			<cfelse>
				<button name="btnPrevResults" class="btn btn-sm btn-outline-primary" disabled type="button" title="No Previous Results">
					<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-chevron-left"></i></span>
					<span class="btn-wrapper--label">Previous</span>
				</button>
			</cfif>
			<cfif (local.paging.currPage*local.paging.rowsize + 1) lte local.qryMembers.mc_totalMatches>
				<button name="btnNextResults" class="btn btn-sm btn-outline-primary"  onclick="gotoResultsPage(#local.paging.nextPage#);" type="button" title="Next Results">
					<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-chevron-right"></i></span>
					<span class="btn-wrapper--label">Next</span>
				</button>
			<cfelse>
				<button name="btnNextResults" class="btn btn-sm btn-outline-primary" disabled type="button" title="No Next Results">
					<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-chevron-right"></i></span>
					<span class="btn-wrapper--label">Next</span>
				</button>
			</cfif>
		</div>
	</div>
</cfif>

</div>
</div>
</cfoutput>