<cfsavecontent variable="local.js">
	<cfoutput>
	<script language="javascript">
		let subScheduleTable;

		function initSubScheduleTable(){
			let domString = "<'row'<'d-flex col-sm-5 col-md-5'<'mt-2'l><'d-flex flex-wrap p-1 m-2'>><'col-sm-7 col-md-7'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";

			subScheduleTable = $('##subScheduleTable').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 25,
				"lengthMenu": [ 25, 50, 100 ],
				"dom": domString,
				"language": {
					"lengthMenu": "_MENU_"
				},
				"ajax": { 
					"url": '#local.allSubscribersScheduleLink#',
					"type": "post"
				},
				"autoWidth": false,
				"columns": [
					{
						"data": null,
						"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<div><a href="javascript:editMember('+data.memberid+')" _target="blank">'+data.membername+'</a></div>';
								if(data.company.length) renderData += '<div class="text-dim small">'+data.company+'</div>';
							}
							return type === 'display' ? renderData : data;
						},
						"className": "align-top",
						"width": "20%"
					},
					{
						"data": null,
						"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								if (data.statuscode.toLowerCase() == 'd') renderData += '<span class="badge badge-danger">Deleted</span>';
								renderData += '<div><a href="javascript:showSubTree('+data.memberid+','+data.subscriberid+')">'+data.subscriptionname+'</a></div>';
								renderData += '<div>'+ data.statusname + '</div><div>'+ data.ratename + '</div>';
							}
							return type === 'display' ? renderData : data;
						},
						"className": "align-top",
						"width": "40%"
					},
					{ "data": "substartdate", "width": "10%", "className": "align-top" },
					{ "data": "subenddate", "width": "10%", "className": "align-top" },
					{ "data": "recogstartdate", "width": "10%", "className": "align-top" },
					{ "data": "recogenddate", "width": "10%", "className": "align-top" }
				],
				"order": [[0, 'asc']],
				"searching": false,
				"pagingType": "simple"
			});
			mca_generateVerboseFilterMessage('frmFilter3');
		}
		function clearFilterSchedulesGrid() {
			/* since reset() won't clear fields with default values */
			$('##frmFilter3 input[type="hidden"], ##frmFilter3 input[type="text"], ##fHasCardOnFile3').val('');
			$('##fSubPaymentStatus3, ##fFreq3, ##fSubType3, ##fSubscription3, ##fRevenueGL3').val(0);
			$('##fSubStatus3').val('0');
			$('##fRate3').empty().trigger('change.select2');
			$('##aClearAssocType3').click();
			filterSubGrid3();
		}
		function generateCustomSubSchedRadioFilterVerbose(filterField) {
			let label = "";
			let value = "";
			const fieldId = filterField.attr('id');

			if (filterField.is(':checked')) {
				switch (fieldId) {
					case 'assocTypeMember3':
						label = 'Associated With Member';
						value = $('##associatedMemberName3').val() + ' (' + $('##associatedMemberNum3').val() + ')';
					break;

					case 'assocTypeGroup3':
						label = 'Associated With Group';
						value = $('##associatedGroupName3').val();
					break;

					default:
						label = $(`label[for='${fieldId}']`).text().trim();
						value = filterField.val();
				}
			}

			return { label, value };
		}
		async function filterSubGrid3() {
			await mca_generateVerboseFilterMessage('frmFilter3');
			reloadSubSchedTable();
		}
		function reloadSubSchedTable() {
			subScheduleTable.draw();
		}
		function startExportSubs3() {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Export Subscribers',
				iframe: true,
				contenturl: '#this.link.startExportSubscriptionsSchedule#',
				strmodalfooter: {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: 'exportSubs3ActionButtonHandler',
					extrabuttonlabel: 'Export',
					extrabuttoniconclass: 'fa-light fa-file-csv'
				}
			});
		}
		function exportSubs3ActionButtonHandler(){
			$('##MCModalBodyIframe')[0].contentWindow.doExport();
		}
		function selectGroupInvFilter3() {
			var selhref = '#this.link.grpSelectGotoLink#&mode=direct&fldName=associatedGroupID3&retFunction=top.updateGroupField3&dispTitle=' + escape('Filter Subscribers by Group');
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Filter Subscribers by Group',
				iframe: true,
				contenturl: selhref,
				strmodalfooter: {
					classlist: 'd-none'
				}
			});
		}
		function selectMemberInvFilter3() {
			var selhref = '#this.link.memSelectGotoLink#&mode=direct&fldName=associatedMemberID3&retFunction=top.updateField3&dispTitle=';
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Filter Subscribers by Member',
				iframe: true,
				contenturl: selhref,
				strmodalfooter : {
					classlist: 'd-none',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '',
					extrabuttonlabel: 'Submit',
				}
			});
		}
		function updateField3(fldID, mID, mNum, mName) {
			var fld = $('##'+fldID);
			var fldName = $('##associatedVal3');
			fld.val(mID);
			if ((mName.length > 0) && (mNum.length > 0)) {
				$('##associatedMemberName3').val(mName);
				$('##associatedMemberNum3').val(mNum);
				fldName.html(mName + ' (' + mNum + ')');
				$('##associatedGroupID3').val(0);
				$('##divAssociatedVal3').show();
			} else {
				fldName.html('');
				$('##divAssociatedVal3').hide();
			}
		}
		function updateGroupField3(fldID,gID,gPath) {
			var fld = $('##'+fldID);
			var fldName = $('##associatedVal3');
			fld.val(gID);
			if (gPath.length > 0) {
				var newgPath = gPath.split("\\");
					newgPath.shift();
					newgPath = newgPath.join(" \\ ");
				$('##associatedGroupName3').val(newgPath);
				fldName.html(newgPath);
				$('##associatedMemberID3').val(0);
				$('##divAssociatedVal3').show();
			} else {
				fldName.html('');
				$('##divAssociatedVal3').hide();
			}
		}
		function loadSchedulesTab() {
			mca_setupDatePickerRangeFields('fTermStartFrom3','fTermStartTo3');
			mca_setupDatePickerRangeFields('fTermEndFrom3','fTermEndTo3');
			mca_setupCalendarIcons('frmFilter3');
			mca_setupSelect2();
			
			$('body').on('change', '##fSubType3', function(e) {
				mca_callChainedSelect('fSubType3', 'fSubscription3', link_adminHomeResource, 'subs', 'getSubscriptionsForSubType', 'typeid', 0, false, false);
				$('##fRate3').empty().trigger('change.select2');
			});

			$('body').on('change', '##fSubscription3', function(e) {
				mca_callChainedSelect('fSubscription3', 'fRate3', link_adminHomeResource, 'subs', 'getSubRatesForSub', 'subid', 0, true, false, [{name:'data-isrenewalrate', datafield:'isrenewalrate'}]);
			});

			if ($('##associatedMemberID3').val() == 0 && $('##associatedGroupID3').val() == 0) $("##divAssociatedVal3").hide();

			$(".assocType3").live("click",function(){
				var assocType = $('input:radio[name=assocType3]:checked').val();
				if (assocType != undefined) {
					if (assocType == "group") selectGroupInvFilter3();
					else selectMemberInvFilter3();
				}
			});

			$("##aClearAssocType3").live("click",function() {
				$(".assocType3").each(function(){
					$(this).attr("checked",false);
				});
				$('##associatedVal3').html("");
				$('##associatedMemberID3').val(0);
				$('##associatedGroupID3').val(0);
				$('##associatedMemberName3').val('');
				$('##associatedMemberNum3').val('');
				$('##associatedGroupName3').val('');
				$('##divAssociatedVal3').hide();
			});
		}
		async function initSubScheduleTab() {
			loadSchedulesTab();
			<cfif local.SubReportFilter.scheduleFilter.fSubType gt 0>
				await mca_callChainedSelect('fSubType3', 'fSubscription3', link_adminHomeResource, 'subs', 'getSubscriptionsForSubType', 'typeid', #local.SubReportFilter.scheduleFilter.fSubscription#, false, false);
				<cfif local.SubReportFilter.scheduleFilter.fSubscription gt 0>
					await mca_callChainedSelect('fSubscription3', 'fRate3', link_adminHomeResource, 'subs', 'getSubRatesForSub', 'subid', '#local.SubReportFilter.scheduleFilter.fRate#', true, false, [{name:'data-isrenewalrate', datafield:'isrenewalrate'}]);
				</cfif>
			</cfif>
			initSubScheduleTable();
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.js)#">

<cfoutput>
<div class="toolButtonBar">
	<button class="btn btn-link p-0 mr-2" onclick="startExportSubs3();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to export subscribers.">
		<i class="fa-regular fa-file-export"></i> Export Subscribers
	</button>
</div>

<div id="divFilterSubSchedForm" class="mb-3" style="display:none;">
	<form name="frmFilter3" id="frmFilter3" onsubmit="filterSubGrid3();return false;" data-filterwrapper="divFilterSubSchedForm" data-verbosemsgwrapper="divSubSchedFilterVerbose" data-customverbose-radio="generateCustomSubSchedRadioFilterVerbose" data-filterkey="schedulefilter">
	<div class="row mb-3">
		<div class="col">
			<div class="card card-box mb-1">
				<div class="card-header py-1 bg-light">
					<div class="card-header--title font-weight-bold font-size-md">
						Filter Subscribers
					</div>
				</div>
				<div class="card-body">
					<div class="row">
						<div class="col-xl-6 col-lg-12">
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="fSubType" id="fSubType3" class="form-control">
										<option value="0">All Subscription Types</option>
										<cfloop query="local.qrySubTypes">
											<option value="#local.qrySubTypes.typeID#"<cfif local.SubReportFilter.scheduleFilter.fSubType EQ local.qrySubTypes.typeID> selected</cfif>>#local.qrySubTypes.typeName#</option>
										</cfloop>
									</select>
									<label for="fSubType3">Subscription Type</label>
								</div>
							</div>
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="fSubscription" id="fSubscription3" class="form-control">
										<option value="0">All Subscriptions</option>
									</select>
									<label for="fSubscription3">Subscription</label>
								</div>
							</div>
							<div class="form-group">
								<div class="d-flex align-items-center mb-1">
									<span class="text-grey small mx-1">Quickly Select: </span>
									<a href="javascript:quickSelectSubRates('fRate3',0);" class="badge badge-neutral-second text-second mr-1">Join Rates</a>
									<a href="javascript:quickSelectSubRates('fRate3',1);" class="badge badge-neutral-second text-second">Renewal Rates</a>
								</div>
								<div class="form-label-group mb-2">
									<select name="fRate" id="fRate3" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2"></select>
									<label for="fRate3">Rate</label>
								</div>
							</div>
							<div class="form-row">
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fTermStartFrom" id="fTermStartFrom3" value="#local.SubReportFilter.scheduleFilter.fTermStartFrom#" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fTermStartFrom3"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermStartFrom3');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fTermStartFrom3">Start Date From</label>
											</div>
										</div>
									</div>
								</div>
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fTermStartTo" id="fTermStartTo3" value="#local.SubReportFilter.scheduleFilter.fTermStartTo#" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fTermStartTo3"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermStartTo3');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fTermStartTo3">Start Date To</label>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="form-row">
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fTermEndFrom" id="fTermEndFrom3" value="#local.SubReportFilter.scheduleFilter.fTermEndFrom#" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fTermEndFrom3"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermEndFrom3');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fTermEndFrom3">End Date From</label>
											</div>
										</div>
									</div>
								</div>
								<div class="col">
									<div class="form-group">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="fTermEndTo" id="fTermEndTo3" value="#local.SubReportFilter.scheduleFilter.fTermEndTo#" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fTermEndTo3"><i class="fa-solid fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermEndTo3');"><i class="fa-solid fa-circle-xmark"></i></a></span>
												</div>
												<label for="fTermEndTo3">End Date To</label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="col-xl-6 col-lg-12">
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="fSubStatus" id="fSubStatus3" class="form-control">
										<option value="0">All Statuses</option>
										<cfloop query="local.qryStatuses">
											<option value="#local.qryStatuses.statusCode#" <cfif local.SubReportFilter.scheduleFilter.fSubStatus eq local.qryStatuses.statusCode>selected</cfif>>#local.qryStatuses.statusName#</option>
										</cfloop>
									</select>
									<label for="fSubStatus3">Status</label>
								</div>
							</div>
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="fSubPaymentStatus" id="fSubPaymentStatus3" class="form-control">
										<option value="0">All Activation Options</option>
										<cfloop query="local.qryPaymentStatuses">
											<option value="#local.qryPaymentStatuses.statusCode#" <cfif local.SubReportFilter.scheduleFilter.fSubPaymentStatus eq local.qryPaymentStatuses.statusCode>selected</cfif>>#local.qryPaymentStatuses.statusName#</option>
										</cfloop>
									</select>
									<label for="fSubPaymentStatus3">Activation Option</label>
								</div>
							</div>
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="fFreq" id="fFreq3" class="form-control">
										<option value="0">All Frequencies</option>
										<cfloop query="local.qryFrequencies">
											<option value="#local.qryFrequencies.frequencyID#" <cfif local.SubReportFilter.scheduleFilter.fFreq eq local.qryFrequencies.frequencyID>selected</cfif>>#local.qryFrequencies.frequencyName#</option>
										</cfloop>
									</select>
									<label for="fFreq3">Frequency</label>
								</div>
							</div>
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="fHasCardOnFile" id="fHasCardOnFile3" class="form-control">
										<option value="">With or Without Pay Method Associated</option>
										<option value="Y" <cfif local.SubReportFilter.scheduleFilter.fHasCardOnFile eq "Y">selected</cfif>>With Pay Method Associated</option>
										<option value="N" <cfif local.SubReportFilter.scheduleFilter.fHasCardOnFile eq "N">selected</cfif>>With no Pay Method Associated</option>
									</select>
									<label for="fHasCardOnFile3">Pay Method</label>
								</div>
							</div>
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="fRevenueGL" id="fRevenueGL3" class="form-control">
										<option value="0">All Revenue GLs</option>
										<cfloop query="local.qryDeferredGLAccounts">
											<option value="#local.qryDeferredGLAccounts.GLAccountID#" <cfif local.SubReportFilter.scheduleFilter.fRevGL eq local.qryDeferredGLAccounts.GLAccountID>selected</cfif>>#local.qryDeferredGLAccounts.AccountName# <cfif len(local.qryDeferredGLAccounts.AccountCode)>(#local.qryDeferredGLAccounts.AccountCode#)</cfif></option>
										</cfloop>
									</select>
									<label for="fRevenueGL3">Revenue GL</label>
								</div>
							</div>
						</div>
					</div>
					<div class="form-group row">
						<div class="col-md-12">
							<div class="row">
								<div class="col-auto">
									Associated With:
								</div>
								<div class="col">
									<div class="form-check form-check-inline">
										<input type="radio" name="assocType3" id="assocTypeMember3" class="assocType3 form-check-input" value="member" <cfif local.SubReportFilter.scheduleFilter.associatedMemberID gt 0>checked</cfif>>
										<label  class="form-check-label" for="assocTypeMember3">A Specific Member</label>
									</div>
									<div class="form-check form-check-inline">
										<input type="radio" name="assocType3" id="assocTypeGroup3" class="assocType3 form-check-input" value="group" <cfif local.SubReportFilter.scheduleFilter.associatedGroupID gt 0>checked</cfif>>
										<label class="form-check-label" for="assocTypeGroup3">A Specific Group</label>
									</div>
									<div id="divAssociatedVal3">
										<span id="associatedVal3" class="font-weight-bold">
											<cfif local.SubReportFilter.scheduleFilter.associatedMemberID gt 0>
												#local.SubReportFilter.scheduleFilter.associatedMemberName# (#local.SubReportFilter.scheduleFilter.associatedMemberNum#)
											<cfelseif local.SubReportFilter.scheduleFilter.associatedGroupID gt 0>
												#local.SubReportFilter.scheduleFilter.associatedGroupName#
											</cfif>
										</span>
										<a href="##" id="aClearAssocType3" class="ml-2">clear</a>
									</div>
									<input type="hidden" name="associatedMemberID" id="associatedMemberID3" value="#local.SubReportFilter.scheduleFilter.associatedMemberID#">
									<input type="hidden" name="associatedMemberName" id="associatedMemberName3" value="#local.SubReportFilter.scheduleFilter.associatedMemberName#">
									<input type="hidden" name="associatedMemberNum" id="associatedMemberNum3" value="#local.SubReportFilter.scheduleFilter.associatedMemberNum#">
									<input type="hidden" name="associatedGroupID" id="associatedGroupID3" value="#local.SubReportFilter.scheduleFilter.associatedGroupID#">
									<input type="hidden" name="associatedGroupName" id="associatedGroupName3" value="#local.SubReportFilter.scheduleFilter.associatedGroupName#">
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="card-footer p-2 text-right">
					<button type="button" name="btnResetFilterSubs3" class="btn btn-sm btn-secondary" onclick="clearFilterSchedulesGrid();">Clear Filters</button>
					<button type="submit" name="btnFilterSubs3" class="btn btn-sm btn-primary">
						<i class="fa-light fa-filter"></i> Filter Subscriptions
					</button>
					<button type="button" class="btnReApplyFilter d-none" onclick="reloadSubSchedTable();"></button><!--- hidden button used by verbose fn to refresh datatable --->
				</div>
			</div>
		</div>
	</div>
	</form>
</div>

<div id="divSubSchedFilterVerbose" style="display:none;"></div>
<table id="subScheduleTable" class="table table-sm table-striped table-bordered" style="width:100%">
	<thead>
		<tr>
			<th>Name</th>
			<th>Subscription / Status / Rate</th>
			<th>Start</th>
			<th>End</th>
			<th>Rec Start</th>
			<th>Rec End</th>
		</tr>
	</thead>
</table>
</cfoutput>