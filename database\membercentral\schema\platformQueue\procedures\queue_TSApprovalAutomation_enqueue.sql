ALTER PROC dbo.queue_TSApprovalAutomation_enqueue
@depoDocumentID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @nowDate datetime = GETDATE();
	EXEC dbo.queue_getStatusIDbyType @queueType='TSApprovalAutomation', @queueStatus='readyForAttachCheck', @queueStatusID=@statusReady OUTPUT;

	IF NOT EXISTS (SELECT 1 FROM dbo.queue_TSApprovalAutomation WHERE depoDocumentID = @depoDocumentID) BEGIN
		INSERT INTO dbo.queue_TSApprovalAutomation (depoDocumentID, dateAdded, dateUpdated, statusID)
		VALUES (@depoDocumentID, @nowDate, @nowDate, @statusReady);

		EXEC membercentral.dbo.sched_resumeTask @name='Process TS Approvals Automation Queue', @engine='BERLinux';
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLL<PERSON><PERSON><PERSON> TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
