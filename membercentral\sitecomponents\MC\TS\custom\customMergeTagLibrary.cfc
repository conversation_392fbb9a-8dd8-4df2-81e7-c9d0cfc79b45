<cfcomponent output="no">
	<cffunction name="tsSubscriptionPlan" access="public" returntype="struct" hint="Displays TrialSmith subscription plan.">		
		<cfset var local = structNew()>

		<cfset local.data = structNew()>
		<cfset local.data["format"] = "html">
		<cfset local.data["jsonvariable"] = "">
		<cfset local.data["dataString"] = "">

		<cfset local.objPlans = CreateObject("component","model.system.platform.tsPlans")>
		<cfset local.qryPlans = local.objPlans.getPlans(application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='TLAMemberState'),session.cfcuser.memberdata.depoMemberDataID,application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='MemberType'))>
		<cfset local.theMemberTypeID = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='MemberType')>
		<cfloop query="local.qryPlans">
			<cfif local.qryPlans.membertypeid eq local.theMemberTypeID>
				<cfset local.data.dataString = local.qryPlans.membertype>
				<cfbreak>
			</cfif>		
		</cfloop>

		<cfreturn local.data>
	</cffunction>	
	
	<cffunction name="tsSubRenewalDate" access="public" returntype="struct" hint="Displays TrialSmith subscription expiration date.">		
		<cfset var local = structNew()>

		<cfset local.data = structNew()>
		<cfset local.data["format"] = "html">
		<cfset local.data["jsonvariable"] = "">
		<cfset local.data["dataString"] = "">
		
		<cfif structKeyExists(session.cfcuser, "subscriptionData") and isDate(session.cfcuser.subscriptionData[4].renewalDate)>
			<cfset local.data.dataString = dateformat(session.cfcuser.subscriptionData[4].renewalDate,"m/d/yyyy")>
		</cfif>

		<cfreturn local.data>
	</cffunction>	
	
	<cffunction name="tsUpgradeOptions" access="public" returntype="struct" hint="Displays TrialSmith subscription plan and upgrade options.">		
		<cfset var local = structNew()>
		
		<cfset local.data = structNew()>
		<cfset local.data["format"] = "html">
		<cfset local.data["jsonvariable"] = "">
		<cfset local.data["dataString"] = "">

		<cfset local.data.dataString = "<div id='tsUpgradeOptions'></div>">
		<cfreturn local.data>
	</cffunction>	

	<cffunction name="tsMemberAccountInfo" access="public" returntype="struct" hint="Displays TrialSmith's member account info.">		
		<cfset var local = structNew()>
		
		<cfset local.data = structNew()>
		<cfset local.data["format"] = "html">
		<cfset local.data["jsonvariable"] = "">
		<cfset local.data["dataString"] = "">

		<cfset local.qryMemberData = application.objMember.getTSMemberData(val(session.cfcuser.memberData.depomemberdataid))>

		<cfsavecontent variable="local.data.dataString">
			<cfoutput>
			<cfif local.qryMemberData.recordCount>
				#local.qryMemberData.FirstName# #local.qryMemberData.LastName#<br/>
				<cfif len(trim(local.qryMemberData.BillingFirm))>#local.qryMemberData.BillingFirm#<br/></cfif>
				<cfif len(trim(local.qryMemberData.BillingAddress))>#local.qryMemberData.BillingAddress#<br/></cfif>
				<cfif len(trim(local.qryMemberData.BillingAddress2))>#local.qryMemberData.BillingAddress2#<br/></cfif>
				#local.qryMemberData.BillingCity#, #local.qryMemberData.BillingState#  <cfif len(trim(local.qryMemberData.BillingZip))>#local.qryMemberData.BillingZip#</cfif><br/>
				<cfif len(trim(local.qryMemberData.Phone))>Tel: #local.qryMemberData.Phone#<br/></cfif>
				<cfif len(trim(local.qryMemberData.Fax))>Fax: #local.qryMemberData.Fax#<br/></cfif>
				Contact E-Mail: #local.qryMemberData.Email#<br/>
				Billing Contact: #local.qryMemberData.BillingContactName#<br/>
				Billing Contact E-mail:	#local.qryMemberData.BillingContactEmail#<br/>
			<cfelse>
				No record found.
			</cfif>
			</cfoutput>
		</cfsavecontent>		
		<cfset local.data.dataString = trim(local.data.dataString)>
		<cfreturn local.data>
	</cffunction>	

	<cffunction name="todayDate" access="public" returntype="struct" hint="Displays today's date in mm/dd/yyyy format.">
		<cfset var local = structNew()>

		<cfset local.data = structNew()>
		<cfset local.data["format"] = "html">
		<cfset local.data["jsonvariable"] = "">
		<cfset local.data["dataString"] = "">
		<cfset local.data.dataString = dateFormat(now(), "mm/dd/yyyy")>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="tsContactDepoName" access="public" returntype="struct" hint="Displays TrialSmith's member depositions contact name.">		
		<cfset var local = structNew()>
		
		<cfset local.data = structNew()>
		<cfset local.data["format"] = "html">
		<cfset local.data["jsonvariable"] = "">
		<cfset local.data["dataString"] = "">

		<cfset local.qryMemberData = application.objMember.getTSMemberData(val(session.cfcuser.memberData.depomemberdataid))>

		<cfsavecontent variable="local.data.dataString">
			<cfoutput>
			<cfif local.qryMemberData.recordCount>
				#local.qryMemberData.DepoContactFirstName# #local.qryMemberData.DepoContactLastName#<br/>
			<cfelse>
				No record found.
			</cfif>
			</cfoutput>
		</cfsavecontent>		
		<cfset local.data.dataString = trim(local.data.dataString)>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="tsContactDepoEmail" access="public" returntype="struct" hint="Displays TrialSmith's member depositions contact email.">		
		<cfset var local = structNew()>

		<cfset local.data = structNew()>
		<cfset local.data["format"] = "html">
		<cfset local.data["jsonvariable"] = "">
		<cfset local.data["dataString"] = "">

		<cfset local.qryMemberData = application.objMember.getTSMemberData(val(session.cfcuser.memberData.depomemberdataid))>

		<cfsavecontent variable="local.data.dataString">
			<cfoutput>
			<cfif local.qryMemberData.recordCount>
				#local.qryMemberData.DepoContactEmail#<br/>
			<cfelse>
				No record found.
			</cfif>
			</cfoutput>
		</cfsavecontent>		
		<cfset local.data.dataString = trim(local.data.dataString)>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="tsLinkedSites" access="public" returntype="struct" hint="Displays TrialSmith's member linked sites.">		
		<cfset var local = structNew()>

		<cfset local.data = structNew()>
		<cfset local.data["format"] = "html">
		<cfset local.data["jsonvariable"] = "">
		<cfset local.data["dataString"] = "">

		<cfset local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments)>

		<cfquery name="local.qryLinkedSites" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @loginNetworkID int;

			select @loginNetworkID = ns.networkID
			from membercentral.dbo.networkSites ns 
			where ns.isLoginNetwork = 1
			and ns.siteID =  <cfqueryparam value="#local.commonTagVars.siteID#" cfsqltype="CF_SQL_INTEGER">;

			select s.siteCode, tla.description
			from membercentral.dbo.ams_networkProfiles as np
			inner join membercentral.dbo.ams_membernetworkProfiles as mnp on mnp.profileID = np.profileID and mnp.status = 'A'
			inner join membercentral.dbo.sites as s on s.siteID = mnp.siteID
			inner join membercentral.dbo.networksites ns on ns.siteID = s.siteID and ns.isLoginNetwork = 1 and ns.networkID = @loginNetworkID
			inner join trialsmith.dbo.depotla tla on tla.state = s.sitecode
			where np.depomemberdataid = <cfqueryparam value="#val(session.cfcuser.memberData.depomemberdataid)#" cfsqltype="CF_SQL_INTEGER">
			AND np.status = 'A'
			order by tla.description;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfsavecontent variable="local.data.dataString">
			<cfoutput>
			<cfif local.qryLinkedSites.recordCount>
				<ul class="mc-mergeTagList mc-linkedsitesList">
					<cfloop query="local.qryLinkedSites">
						<cfset local.strSite = application.objSiteInfo.getSiteInfo(local.qryLinkedSites.siteCode)>
						<li class="mc-mergeTagListItem"><a href="#local.strSite.scheme#://#local.strSite.mainHostName#" target="_blank">#local.qryLinkedSites.description#</a></li>
					</cfloop>
				</ul>
			<cfelse>
				<div class="mc-mergeTagList mc-linkedsitesList mc-noDataMessageContainer">
					No partners found.
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>		
		<cfset local.data.dataString = trim(local.data.dataString)>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="tsMemberCertEmails" access="public" returntype="struct" hint="Displays TrialSmith's member certified emails.">		
		<cfset var local = structNew()>

		<cfset local.data = structNew()>
		<cfset local.data["format"] = "html">
		<cfset local.data["jsonvariable"] = "">
		<cfset local.data["dataString"] = "">

		<cfset local.qryMemberData = application.objMember.getMemberCertEmails(profileID=val(session.cfcuser.memberData.profileID))>

		<cfsavecontent variable="local.data.dataString">
			<cfoutput>
			<cfif local.qryMemberData.recordCount>
				<ul class="mc-mergeTagList mc-certemailsList">
					<cfloop query="local.qryMemberData">
						<li class="mc-mergeTagListItem">#local.qryMemberData.email#</li>
					</cfloop>
				</ul>
			<cfelse>
				<div class="mc-mergeTagList mc-certemailsList mc-noDataMessageContainer">
					No e-mails found.
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>		
		<cfset local.data.dataString = trim(local.data.dataString)>

		<cfreturn local.data>
	</cffunction>	

	<cffunction name="tsDocContributedCount" access="public" returntype="struct" hint="Displays TrialSmith's member document contributed count.">		
		<cfset var local = structNew()>

		<cfset local.data = structNew()>
		<cfset local.data["format"] = "html">
		<cfset local.data["jsonvariable"] = "">
		<cfset local.data["dataString"] = "">

		<cfquery name="local.qryCount" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(*) as docCount
			from dbo.depoDocuments d
			inner join dbo.depomemberdata as m on m.depomemberdataid = d.depomemberdataid 
			inner join dbo.depoDocumentTypes as t on t.TypeID = d.DocumentTypeID 
			inner join dbo.depoCaseTypes as ct on d.caseTypeId = ct.caseTypeID
			where m.depoMemberDataID = <cfqueryparam value="#val(session.cfcuser.memberData.depomemberdataid)#" cfsqltype="CF_SQL_INTEGER">
			and t.TypeID not in (2,6,9);

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data.dataString = val(local.qryCount.docCount)>

		<cfreturn local.data>
	</cffunction>		

	<cffunction name="tsDocPurchasedCount" access="public" returntype="struct" hint="Displays TrialSmith's member document purchased count.">
		<cfset var local = structNew()>

		<cfset local.data = structNew()>
		<cfset local.data["format"] = "html">
		<cfset local.data["jsonvariable"] = "">
		<cfset local.data["dataString"] = "">

		<cfquery name="local.qryCount" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
														
			DECLARE @depomemberdataid int = <cfqueryparam value="#val(session.cfcuser.memberdata.depomemberdataid)#" cfsqltype="CF_SQL_INTEGER">;
			declare @approvedStatusID int 
			select @approvedStatusID=statusID from trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

			SELECT count(*) as docCount
			FROM dbo.depoDocuments AS d 
			INNER JOIN dbo.depoDocumentStatusHistory as dsh on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
				and dsh.statusID = @approvedStatusID
			INNER JOIN dbo.depoPermissions AS p ON d.DocumentID = p.DocumentID 
			inner join dbo.depodocumenttypes as dt on d.documenttypeid = dt.typeid and (dt.acctcode between 3000 and 3999 or dt.acctcode in (5005,5006))
			inner join (
				select @depomemberdataid as depomemberdataid
				union
				select fpl.depoMemberDataID
				from dbo.tlaFirmPlanLink as fpl
				where fpl.firmPlanID = (select firmPlanID from dbo.tlaFirmPlanLink where depoMemberDataID = @depomemberdataid)
			) as acct on acct.depomemberdataid = p.depomemberdataid;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data.dataString = val(local.qryCount.docCount)>		

		<cfreturn local.data>
	</cffunction>		

	<cffunction name="tsMedlinePurchasedCount" access="public" returntype="struct" hint="Displays TrialSmith's Medline purchased count.">
		<cfset var local = structNew()>

		<cfset local.data = structNew()>
		<cfset local.data["format"] = "html">
		<cfset local.data["jsonvariable"] = "">
		<cfset local.data["dataString"] = "">

		<cfquery name="local.qryCount" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @purchasedDate datetime = CONVERT(VARCHAR, DATEADD(D,-31,getDate()),102);

			SELECT count(*) as docCount
			FROM dbo.depoTransactions			
			WHERE depomemberdataID = <cfqueryparam value="#val(session.cfcuser.memberData.depomemberdataid)#" cfsqltype="CF_SQL_INTEGER">
			AND accountCode = '5002'
			and DatePurchased > @purchasedDate;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data.dataString = val(local.qryCount.docCount)>		

		<cfreturn local.data>
	</cffunction>

</cfcomponent>