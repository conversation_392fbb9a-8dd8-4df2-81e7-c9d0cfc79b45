<cfcomponent output="false">

	<cffunction name="getCertMergeCodes" access="public" output="false" returntype="struct">
		<cfscript>
			var strCertMergeCodes = {
				"member": "firstname,lastname,Pennsylvania_licenseNumber"
			};
			return strCertMergeCodes;
		</cfscript>
	</cffunction>

	<cffunction name="generateCertBody" access="public" output="false" returntype="string">
		<cfargument name="strCertMergeCodes" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.totalHours = 0>
		<cfset local.substantiveEventHours = 0>
		<cfset local.ethicsEventHours = 0>
		<cfset local.roleCategoryNameList = "">
		<cfset local.strEventDetails = "">

		<!--- Roles --->
		<cfquery name="local.qRoles" dbtype="query">
			SELECT CATEGORYNAME
			FROM [arguments.strCertMergeCodes].registrant.QRYEVENTROLES
		</cfquery>
		<cfif local.qRoles.recordCount>
			<cfset local.roleCategoryNameList = valueList(local.qRoles.CATEGORYNAME)>
		</cfif>

		<!--- TYPE OF LAW --->
		<cfif structKeyExists(arguments.strCertMergeCodes.event, "qryEventCustomFields") 
			AND structKeyExists(arguments.strCertMergeCodes.event.qryEventCustomFields, "customvalue")>
			<cfquery name="local.qType" dbtype="query">
				SELECT customvalue AS eventDetails
				FROM arguments.strCertMergeCodes.event.qryEventCustomFields
				WHERE fieldReference = 'typeoflaw'
			</cfquery>
			<cfif local.qType.recordCount>
				<cfset local.strEventDetails = local.qType.eventDetails>
			</cfif>
		</cfif>
		<!--- Hours --->
		<cfloop query="arguments.strCertMergeCodes.credit.qrySpecificCertficatesToGenerate">
			<cfif originalCreditType EQ 'Substantive'>
				<cfset local.substantiveEventHours += creditValueAwarded>
			<cfelseif originalCreditType EQ 'Legal Ethics'>
				<cfset local.ethicsEventHours += creditValueAwarded>
			</cfif>
		</cfloop>
		<cfset local.totalHours = local.substantiveEventHours + local.ethicsEventHours>
		<cfset local.fmt = function(n){ return NumberFormat(n, "____.__"); }>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<html>
				<head>
					<title>Certificate of Completion</title>
					<style>
						html, body { width:8.5in; margin:0; padding:0;}
						table { font-family: Helvetica, Arial, sans-serif; font-size:14px; }

						/* Credits table */
						.cle-credits { width:100%; border-collapse:collapse; table-layout:fixed; }
						.cle-credits th, .cle-credits td { padding:8px 8px; }
						.cle-credits .label { width:55%; text-align:right; white-space:nowrap; }
						.cle-credits .value { width:45%; text-align:left;
							font-variant-numeric: tabular-nums;
							-moz-font-feature-settings:"tnum";
							font-feature-settings:"tnum";
						}
						.cle-credits .total .label,
						.cle-credits .total .value { font-weight:800; }
						.cle-credits .heading th { font-weight:800; text-align:right; padding:6px 8px; }
					</style>
				</head>
				<body>
					<!-- OUTER WRAPPER TABLE -->
					<table width="100%" cellpadding="0" cellspacing="0" border="0" style="padding:30px 36px 20px;">
						<tr>
							<td>
								<!-- HEADER TABLE -->
								<table width="100%" cellpadding="0" cellspacing="0" border="0">
									<tr>
										<td align="center" style="font-size:16px;">Continuing Legal Education</td>
									</tr>
									<tr>
										<td align="center" style="font-weight:800;font-size:26px;padding-top:8px;">
											Certificate of Completion
										</td>
									</tr>
									<tr>
										<td>
											<table width="100%" cellpadding="0" cellspacing="0" border="0" style="margin:22px 0 40px;">
												<tr><td style="height:14px;background:##283a8d;"></td></tr>
											</table>
										</td>
									</tr>
								</table>

								<!-- BODY COPY TABLE -->
								<table width="100%" cellpadding="0" cellspacing="0" border="0">
									<tr>
										<td align="center" style="color:##777;"><i>This certificate is presented to</i></td>
									</tr>
									<tr>
										<td align="center" style="font-size:28px;font-weight:800;color:##1f4ca0;padding-top:10px;">
											#htmlEditFormat(arguments.strCertMergeCodes.member.firstName)# 
											#htmlEditFormat(arguments.strCertMergeCodes.member.lastName)#
										</td>
									</tr>
									<tr>
										<td align="center" style="color:##777;padding-top:10px;">
											PA BAR ID:
											<cfif structKeyExists(arguments.strCertMergeCodes.member,"Pennsylvania_licenseNumber") 
												AND len(arguments.strCertMergeCodes.member["Pennsylvania_licenseNumber"])>
												#htmlEditFormat(arguments.strCertMergeCodes.member["Pennsylvania_licenseNumber"])#
											</cfif>
										</td>
									</tr>
									<tr>
										<td align="center" style="color:##000;padding-top:20px;">
											<i>For successfully completing the Westmoreland Bar Association CLE Program:</i>
										</td>
									</tr>
									<tr>
										<td align="center" style="font-size:18px;font-weight:800;padding-top:12px;">
											#htmlEditFormat(arguments.strCertMergeCodes.event.qryEventMeta.eventContentTitle)#
										</td>
									</tr>
									<tr>
										<td align="center" style="color:##9c9c9c;padding-top:10px;">
											TYPE OF LAW:
											<cfif len(trim(local.strEventDetails))>
												#htmlEditFormat(local.strEventDetails)#
											<cfelse>
												Course Practice Area to be here
											</cfif>
										</td>
									</tr>
									<cfif len(trim(local.roleCategoryNameList))>
										<tr>
											<td align="center" style="color:##9c9c9c;padding-top:6px;">
												<b>Role: #htmlEditFormat(local.roleCategoryNameList)#</b>
											</td>
										</tr>
									</cfif>
								</table>

								<!-- CREDITS BLOCK -->
								
								<table width="100%" style="margin-top:50px; ">
								<tr><td></td><td nowrap width="1%">
								<table class="cle-credits">
									<tr class="heading">
										<th colspan="2">CREDITS AWARDED</th>
									</tr>
									<tr>
										<td class="label">Substantive Credits:</td>
										<td class="value">#local.fmt(local.substantiveEventHours)#</td>
									</tr>
									<tr>
										<td class="label">Ethics Credits:</td>
										<td class="value">#local.fmt(local.ethicsEventHours)#</td>
									</tr>
									<tr class="total">
										<td class="label">TOTAL CLE CREDITS:</td>
										<td class="value">#local.fmt(local.totalHours)#</td>
									</tr>
								</table>
								</td><td></td></tr>
								</table>

								<!-- FOOTER LOGO -->
								<table width="100%" cellpadding="0" cellspacing="0" border="0" style="margin-top:30px;">
									<tr>
										<td align="center">
											<img src="#arguments.strCertMergeCodes.certificate.imagesurl#cert_logo.png" 
												alt="Westmoreland Bar Association" style="width:300px;">
										</td>
									</tr>
								</table>
							</td>
                        </tr>
					</table>
				</body>
				</html>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

</cfcomponent>
