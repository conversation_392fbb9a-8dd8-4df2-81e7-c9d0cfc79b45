<cfsavecontent variable="local.scheduledTaskJS">
	<cfoutput>
	<script language="JavaScript">
		var #toScript(local.participantID, 'participantID')#
		function showAllowRegistrantInstructions(){
			if ($('##allowRegistrantInstructions').is(':checked')) {
				$('##allowRegistrantInstructionsHolder').removeClass("d-none");
			} else {
				$('##allowRegistrantInstructionsHolder').addClass("d-none");
			}
		} 

		function showAllowSpeakerInstructions(){
			if ($('##allowSpeakerInstructions').is(':checked')) {
				$('##allowSpeakerInstructionsHolder').removeClass("d-none");
			} else {
				$('##allowSpeakerInstructionsHolder').addClass("d-none");
			}
		}

		function showAllowWebinarMaterial(){
			if ($('##allowWebinarMaterial').is(':checked')) {
				$('##allowWebinarMaterialHolder').removeClass("d-none");
			} else {
				$('##allowWebinarMaterialHolder').addClass("d-none");
			}
		}

		function loadSWLScheduledTaskTab() {
			mca_setupSelect2ByID('registrantSelectedTimeframes');
			mca_setupSelect2ByID('speakerSelectedTimeframes'); 
			mca_setupSelect2ByID('webinarMaterialSelectedTimeframes');
		}
		function saveSWLScheduledTask() {
			mca_hideAlert('err_scheduledTask');	
			var arrReq = [];
			if ($('##allowRegistrantInstructions').is(':checked')) {
				var registrantSelectedTimeframes = $('##registrantSelectedTimeframes').val();
				if (!registrantSelectedTimeframes || !registrantSelectedTimeframes.length) {
					arrReq[arrReq.length] = "Select at least one timeframe to Email registrants their webinar connection instructions.";
				}
			}
			if ($('##allowSpeakerInstructions').is(':checked')) {
				var speakerSelectedTimeframes = $('##speakerSelectedTimeframes').val();
				if (!speakerSelectedTimeframes || !speakerSelectedTimeframes.length) {
					arrReq[arrReq.length] = "Select at least one timeframe to Email speakers their webinar connection instructions.";
				}
			}
			if ($('##allowWebinarMaterial').is(':checked')) {
				var webinarMaterialSelectedTimeframes = $('##webinarMaterialSelectedTimeframes').val();
				if (!webinarMaterialSelectedTimeframes || !webinarMaterialSelectedTimeframes.length) {
					arrReq[arrReq.length] = "Select at least one timeframe to Email materials to registrants before the webinar.";
				}
			}
			if (arrReq.length) {
				mca_showAlert('err_scheduledTask', arrReq.join('<br/>'));
			} else {
				$('##btnUpdateScheduledTask').prop('disabled',true);
				$('##frmSWLScheduledTask, ##divSWLScheduledTaskSaveLoading').toggle();
				var saveScheduledTaskResult = function(r) {
					$('##btnUpdateScheduledTask').prop('disabled',false);
					if (r.success && r.success.toLowerCase() == 'true') {
						$('##frmSWLScheduledTask, ##divSWLScheduledTaskSaveLoading').toggle();
						$('##saveResponse').html('<span class="badge badge-success">SAVED SUCCESSFULLY</span>').addClass('text-success').show().fadeOut(5000);
					} else {
						alert('We were unable to save Scheduled Task Details. Try again.');
					}
				}
				var objParams = { 
					participantID: participantID, 
					isRegistrantInstructionsEnabled : $('##allowRegistrantInstructions').prop('checked') ? 1 : 0,
					registrantSelectedTimeframes: $('##allowRegistrantInstructions').prop('checked') ? $('##registrantSelectedTimeframes').val().join(',') : '', 
					isSpeakerInstructionsEnabled : $('##allowSpeakerInstructions').prop('checked') ? 1 : 0,
					speakerSelectedTimeframes: $('##allowSpeakerInstructions').prop('checked') ? $('##speakerSelectedTimeframes').val().join(',') : '', 
					isWebinarMaterialEnabled : $('##allowWebinarMaterial').prop('checked') ? 1 : 0,
					webinarMaterialSelectedTimeframes: $('##allowWebinarMaterial').prop('checked') ? $('##webinarMaterialSelectedTimeframes').val().join(',') : ''};
				TS_AJX('ADMINSWL','updateScheduledTaskDetails',objParams,saveScheduledTaskResult,saveScheduledTaskResult,10000,saveScheduledTaskResult);
			}
		}
	</script>

	<style>
		.form-rows {
			display: flex;
			flex-direction: column;
		}
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.scheduledTaskJS#">

<cfoutput>
	<div id="err_scheduledTask" class="alert alert-danger mb-2 mt-2 d-none"></div>
	<div class="alert d-flex align-items-center pl-2 py-1 align-content-center alert-info" role="alert">
		<span class="font-size-lg d-block d-40 mr-1 text-center"><i class="fa-solid fa-circle-info"></i></span>
		<span>Scheduled tasks are performed for active programs.</span>
	</div>
	<form name="frmSWLScheduledTask" id="frmSWLScheduledTask" method="post" autocomplete="off">
		<div class="custom-control custom-switch mb-3">
			<input type="checkbox" name="allowRegistrantInstructions" id="allowRegistrantInstructions" onclick="showAllowRegistrantInstructions();" class="custom-control-input" <cfif local.scheduledTaskDetails.isRegistrantInstructionsEnabled EQ 1>checked</cfif>>
			<label class="custom-control-label" for="allowRegistrantInstructions">
				<b>Registrant Connection Instructions:</b> Email registrants their webinar connection instructions. <span data-tooltip-class="tooltip-secondary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Connection instructions are always emailed at the time of registration."><i class="fa-solid fa-question-circle pl-1"></i></span>
			</label>
		</div>
		<div class="form-group my-2 <cfif local.scheduledTaskDetails.isRegistrantInstructionsEnabled NEQ 1>d-none</cfif>" id="allowRegistrantInstructionsHolder">
			<div class="form-group mt-2 mb-3 pl-5" id="registrantInstructionsMultiSelect">
				<div class="form-label-group">
					<div class="input-group flex-nowrap">
						<select name="registrantSelectedTimeframes" id="registrantSelectedTimeframes" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" data-allowclear="false" placeholder="No Time Frames">
							<option value="7d" <cfif local.scheduledTaskDetails.isRegistrantInstructionsEnabled EQ 1 AND listFind(local.scheduledTaskDetails.registrantSelectedTimeframes, '7d')>selected</cfif>>7 days before</option>
							<option value="2d" <cfif local.scheduledTaskDetails.isRegistrantInstructionsEnabled EQ 1 AND listFind(local.scheduledTaskDetails.registrantSelectedTimeframes, '2d')>selected</cfif>>2 days before</option>
							<option value="1d" <cfif local.scheduledTaskDetails.isRegistrantInstructionsEnabled EQ 1 AND listFind(local.scheduledTaskDetails.registrantSelectedTimeframes, '1d')>selected</cfif>>1 day before</option>
							<option value="1h" <cfif local.scheduledTaskDetails.isRegistrantInstructionsEnabled EQ 1 AND listFind(local.scheduledTaskDetails.registrantSelectedTimeframes, '1h')>selected</cfif>>1 hour before</option>
						</select>
						<label for="registrantSelectedTimeframes">Select Timeframes</label>
					</div>
				</div>
			</div>
		</div>
		<div class="custom-control custom-switch mb-3">
			<input type="checkbox" name="allowSpeakerInstructions" id="allowSpeakerInstructions" onclick="showAllowSpeakerInstructions();" class="custom-control-input" <cfif local.scheduledTaskDetails.isSpeakerInstructionsEnabled EQ 1>checked</cfif>>
			<label class="custom-control-label" for="allowSpeakerInstructions">
				<b>Speaker Connection Instructions:</b> Email speakers their webinar connection instructions.
			</label>
		</div>
		<div class="form-group my-2 <cfif local.scheduledTaskDetails.isSpeakerInstructionsEnabled NEQ 1>d-none</cfif>" id="allowSpeakerInstructionsHolder">
			<div class="form-group mt-2 mb-3 pl-5" id="speakerInstructionsMultiSelect">
				<div class="form-label-group">
					<div class="input-group flex-nowrap">
						<select name="speakerSelectedTimeframes" id="speakerSelectedTimeframes" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" data-allowclear="false" placeholder="No Time Frames">
							<option value="7d" <cfif local.scheduledTaskDetails.isSpeakerInstructionsEnabled EQ 1 AND listFind(local.scheduledTaskDetails.speakerSelectedTimeframes, '7d')>selected</cfif>>7 days before</option>
							<option value="2d" <cfif local.scheduledTaskDetails.isSpeakerInstructionsEnabled EQ 1 AND listFind(local.scheduledTaskDetails.speakerSelectedTimeframes, '2d')>selected</cfif>>2 days before</option>
							<option value="1d" <cfif local.scheduledTaskDetails.isSpeakerInstructionsEnabled EQ 1 AND listFind(local.scheduledTaskDetails.speakerSelectedTimeframes, '1d')>selected</cfif>>1 day before</option>
							<option value="1h" <cfif local.scheduledTaskDetails.isSpeakerInstructionsEnabled EQ 1 AND listFind(local.scheduledTaskDetails.speakerSelectedTimeframes, '1h')>selected</cfif>>1 hour before</option>
						</select>
						<label for="speakerSelectedTimeframes">Select Timeframes</label>
					</div>
				</div>
			</div>
		</div>
		<div class="custom-control custom-switch mb-3"> 
			<input type="checkbox" name="allowWebinarMaterial" id="allowWebinarMaterial" onclick="showAllowWebinarMaterial();" class="custom-control-input" <cfif local.scheduledTaskDetails.isWebinarMaterialEnabled EQ 1>checked</cfif>>
			<label class="custom-control-label" for="allowWebinarMaterial">
				<b>Webinar Materials:</b> Email materials to registrants before the webinar. (if applicable)
			</label>
		</div>
		<div class="form-group my-2 <cfif local.scheduledTaskDetails.isWebinarMaterialEnabled NEQ 1>d-none</cfif>" id="allowWebinarMaterialHolder">
			<div class="form-group mt-2 mb-3 pl-5" id="webinarMaterialMultiSelect">
				<div class="form-label-group">
					<div class="input-group flex-nowrap">
						<select name="webinarMaterialSelectedTimeframes" id="webinarMaterialSelectedTimeframes" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" data-allowclear="false" placeholder="No Time Frames">
							<option value="7d" <cfif local.scheduledTaskDetails.isWebinarMaterialEnabled EQ 1 AND listFind(local.scheduledTaskDetails.webinarMaterialSelectedTimeframes, '7d')>selected</cfif>>7 days before</option>
							<option value="2d" <cfif local.scheduledTaskDetails.isWebinarMaterialEnabled EQ 1 AND listFind(local.scheduledTaskDetails.webinarMaterialSelectedTimeframes, '2d')>selected</cfif>>2 days before</option>
							<option value="1d" <cfif local.scheduledTaskDetails.isWebinarMaterialEnabled EQ 1 AND listFind(local.scheduledTaskDetails.webinarMaterialSelectedTimeframes, '1d')>selected</cfif>>1 day before</option>
							<option value="1h" <cfif local.scheduledTaskDetails.isWebinarMaterialEnabled EQ 1 AND listFind(local.scheduledTaskDetails.webinarMaterialSelectedTimeframes, '1h')>selected</cfif>>1 hour before</option>
						</select>
						<label for="webinarMaterialSelectedTimeframes">Select Timeframes</label>
					</div>
				</div>
			</div>
		</div>
		<div class="form-group row mt-2">
			<div class="col text-right">
				<span id="saveResponse"></span>
				<button type="button" name="btnUpdateScheduledTask" id="btnUpdateScheduledTask" onclick="saveSWLScheduledTask()" class="btn btn-sm btn-primary">Save Details</button>
			</div>
		</div>
	</form>
</cfoutput>

<div id="divSWLScheduledTaskSaveLoading" style="display:none;">
	<div class="text-center">
		<br/>
		<i class="fa-light fa-circle-notch fa-spin fa-3x"></i>
		<br/><br/>
		<b>Please wait while we save these changes.</b>
		<br/>
	</div>
</div>