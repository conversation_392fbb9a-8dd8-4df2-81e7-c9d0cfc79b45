<cfinclude template="../commonJS.cfm">

<cfoutput>
<div class="tsAppHeading"><b>#local.cfcuser_firstname# #local.cfcuser_lastname#</b>, Upload Your Depositions and Documents</div><hr/>
<cfif structKeyExists(local,"notAllowedMessage") and len(local.notAllowedMessage)>
	#local.notAllowedMessage#
<cfelse>
	<div id="docUploadDocFormContainer">
		<table class="tsAppBodyText" style="width:100%;">
			<tr>
				<td style="vertical-align:top;">
					<form name="frmUploadDoc" id="frmUploadDoc">
						<table class="tsAppBodyText">
							<tr>
								<td colspan="2">
									<b>#local.cfcuser_firstname# #local.cfcuser_lastname#</b>
								</td>
								<td>Credits:</td>
								<td>#dollarFormat(local.qryTotalCreditsAndDepositions.PCTotal)#</td>
								<td>Total Depositions:</td>
								<td>#local.qryTotalCreditsAndDepositions.totalDepositions#</td>
							</tr>
						</table>
						<div class="docUpdTool docUploadStepsSummary" style="display:none;">
							<table class="tsAppBodyText">
								<tr>
									<td>Award:</td>
									<td id="awardCredType"></td>
									<td>
										<button type="button" name="btnEditAwardType" class="tsAppBodyButton" onclick="showUploadSteps();" title="Edit">
											<i class="fa-solid fa-pencil" style="color: ##007bff;"></i>
										</button>
									</td>
								</tr>
								<tr>
									<td>Organization:</td>
									<td id="depoDocOrgDisp"></td>
									<td>
										<button type="button" name="btnEditDocOrg" class="tsAppBodyButton" onclick="showUploadSteps();" title="Edit">
											<i class="fa-solid fa-pencil" style="color: ##007bff;"></i>
										</button>
									</td>
								</tr>
							</table>
						</div>
						<div id="docUploadSteps1And2Container" class="docUploadSteps" style="margin:20px 0;">
							<h3>Step 1: Which trial lawyer group gets Credit for your depositions?</h3>
							<div style="padding-top:10px;padding-left:10px;">
								<select name="docState" id="docState" class="tsAppBodyText" onchange="onChangeDepoDocStates();">
									<option value="">Select a Trial Lawyers Association/Litigation Group</option>
									<cfloop query="local.qryDepoStates">
										<option value="#local.qryDepoStates.State#" #((local.defaultSelectedAssociation eq local.qryDepoStates.State) ? "selected" : "")#>#local.qryDepoStates.Description#</option>
									</cfloop>
									<option value="TS">My Association or Group is Not Listed</option>
								</select>
							</div>
							<cfif val(local.qryDepoDocumentSettings.DepoAmazonBucks) AND val(local.qryDepoDocumentSettings.DepoAmazonBucksCredit)>
								<div style="margin:20px 0;">
									<h3>Step 2: What type of Credit do you want for your depositions?</h3>
									<div style="padding-left:10px;">
										<label style="margin:10px 0;">
											<input type="radio" name="credType" id="credTypeDepo" value="depo" onclick="onClickCreditPreference(this.value);" checked>
											I prefer Deposition Purchase Credits which I can use at a later date without penalty, up to $30 per depo no limit (best value)
										</label>
										<br/>
										<label style="margin:10px 0;">
											<input type="radio" name="credType" id="credTypeAmazonBucks" value="amazon" onclick="onClickCreditPreference(this.value);">
											I prefer Amazon Bonus Cash of up to $10 per deposition, which will be awarded to me in the form of an Amazon Gift Card.
										</label>
										<div id="depoDocAmazonBucksInfo" style="display:none;">
											<table class="tsAppBodyText">
												<tr>
													<td><label for="depoDocAmazonBucksFullName">Full Name for Amazon Gift Card Recipient</label></td>
													<td><input type="text" id="depoDocAmazonBucksFullName" name="depoDocAmazonBucksFullName" value="#local.cfcuser_firstname# #local.cfcuser_lastname#" placeholder="Full Name" maxlength="550" autocomplete="off" onblur="validateUploadDocsForm('prevalidate');"></td>
												</tr>
												<tr>
													<td><label for="depoDocAmazonBucksEmail">Email for Amazon Gift Card Recipient</label></td>
													<td><input type="text" id="depoDocAmazonBucksEmail" name="depoDocAmazonBucksEmail" value="#local.cfcuser_email#" placeholder="Email" maxlength="255" autocomplete="off" onblur="validateUploadDocsForm('prevalidate');"></td>
												</tr>
											</table>
										</div>
									</div>
								</div>
							<cfelse>
								<input type="radio" name="credType" id="credTypeDepo" value="depo" style="display:none;" checked>
							</cfif>

							<cfif application.mcEnvironment neq "production">
								<div class="tsAppBodyText" style="margin:20px 0; background:##fff6bf url(/assets/common/images/checkmark.gif) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##0f0; border-bottom:2px solid ##0f0;">
									<b>New TrialSmith AI Beta Promotion!</b> We'll automatically provide you with a Deposition Insights Summary for each transcript that you contribute. 
								</div>
							</cfif>
						</div>
						<div id="depoDocUploader" class="docUpdTool" style="display:none;">
							<p>Your browser does not have HTML5 support.</p>
						</div>
						<div id="depoDocUploaderError" style="display:none;margin:20px 0; background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00;"></div>
						<div class="docUploadSteps" style="margin-top:20px;text-align:right;">
							<button type="button" name="btnContinue" class="tsAppBodyButton" onclick="showUploadSection();">
								Continue to Upload Your Depositions
							</button>
							<button type="button" name="btnLearnMore" class="tsAppBodyButton" onclick="showLearnMoreSection();">
								Learn More / FAQ
							</button>
						</div>
						<div id="depoDocUploaderButtonContainer" class="docUpdTool" style="display:none;margin-top:20px;">
							<button type="button" name="btnUploadDocs" id="btnUploadDocs" class="tsAppBodyButton" onclick="startUploadDocs();" disabled>Start Upload of Files</button>
						</div>
					</form>
					<div id="docUploadDocConf" class="tsAppBodyText" style="display:none;padding:20px;"></div>
				</td>
			</tr>
			<tr style="display:none;" id="learnMoreSection">
				<td style="vertical-align:top;">
					<div style="margin-bottom:20px;">#local.qryContentUploadDocumentsStep2Info.rawContent#</div>
					<div style="margin-bottom:20px;">
						<h3>Earn Document Purchase credits</h3>
						<cfset local.aboutCreditsContent = local.qryContentUploadDocumentsStep2.rawContent>
						<cfset local.aboutCreditsContent = replaceNoCase(local.aboutCreditsContent,'[[TSEncodedDepoMemberName]]',encodeForHTMLAttribute("#local.cfcuser_firstname# #local.cfcuser_lastname#"), 'all')>
						<cfset local.aboutCreditsContent = replaceNoCase(local.aboutCreditsContent,'[[TSDepoMemberDataID]]',local.cfcuser_depomemberdataid, 'all')>
						<cfif val(local.qryDepoDocumentSettings.DepoAmazonBucks) AND val(local.qryDepoDocumentSettings.DepoAmazonBucksCredit)>
							<cfset local.aboutCreditsContent = replaceNoCase(local.aboutCreditsContent,'[[DepoDocAmazonCredits]]',"Earn Amazon credits of up to $10 per transcript", 'all')>
						<cfelse>
							<cfset local.aboutCreditsContent = replaceNoCase(local.aboutCreditsContent,'<li>[[DepoDocAmazonCredits]]</li>',"", 'all')>
						</cfif>
						#local.aboutCreditsContent#
					</div>
					<div style="margin:10px 0;">
						<h3>Terms of Agreement with transcript contributions</h3>
						<div>#local.qryContentUploadDocumentsStep2Terms.rawContent#</div>
					</div>
					<div style="margin:10px 0;">
						<h3>About Depo Database</h3>
						<div>#local.qryContentUploadDocumentsStep2FAQ.rawContent#</div>
					</div>
				</td>
			</tr>
		</table>
	</div>
</cfif>
</cfoutput>