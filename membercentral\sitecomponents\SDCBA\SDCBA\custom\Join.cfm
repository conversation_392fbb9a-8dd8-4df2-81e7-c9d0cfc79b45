<cfscript>
	variables.applicationReservedURLParams 	= "TestMode";
	local.customPage.baseURL				= "/?#getBaseQueryString(false)#";

	// SET PAGE DEFAULTS: ----------------------------------------------------------------------------------------------------
	local.arrCustomFields = [];
	local.tmpField = { name="FormNameDispaly", type="STRING", desc="Form Name Display", value="" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="OrgEmailRecipient", type="STRING", desc="Organization Recipient Email", value="<EMAIL>" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="memberEmailFrom", type="STRING", desc="Member Email From", value="<EMAIL>" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="AccountLookUpTitle", type="STRING", desc="Account look up title", value="Account Lookup / Create New Account" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="AccountLookUpButton", type="STRING", desc="Account look up Button Name", value="Account Lookup" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="AccountLookUpContent", type="CONTENTOBJ", desc="Account look up Content", value="Click the Account Look Up Button Above" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="memberTypeUID", type="STRING", desc="Member Type UID", value="D46DFDEA-6E46-4043-BA8F-F4AF38DE1B21" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="sectionTypeUID", type="STRING", desc="Session Type UID", value="061D8C07-2443-4424-B032-8BD7D6D3860C" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="sectionSetUID", type="STRING", desc="Session Set UID", value="BD55E654-DD14-4095-B84D-7F1E0E4C943C" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="MCLESubscriptionUID", type="STRING", desc="MCLE Pass Subscription UID", value="BE5185FE-ED5A-482A-B9F7-084B146435B5" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="MCLEStatement", type="CONTENTOBJ", desc="MCLE Pass Statement", value="Please select if you wish to purchase the MCLE Annual Pass" };
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="MCLEFull", type="STRING", desc="MCLE Full Rate", value="100" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="caAttyMembershipAllUID", type="STRING", desc="Caatty Mmbership All UID", value="E1ED7014-FC69-4909-BC32-543DCAA9C636" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="afMembershipAllUID", type="STRING", desc="AF Membership All UID", value="7622C440-F775-4E73-9CBB-FA0576A05FA7" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="lcMembershipAll", type="STRING", desc="LC Membership All UID", value="31FC0F73-758E-4F1F-9E66-D1A04E618DCD" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="donationSubUID", type="STRING", desc="Donation Sub UID", value="A740B467-29EB-4AD0-BCBC-C24AB5A5DB19" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="donationSubStandardRateUID", type="STRING", desc="Donation Sub Standard Rate UID", value="DF17A924-402E-403C-8EB3-AC123461AB5A" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="donationSubBillableHourRateUID", type="STRING", desc="Donation Sub Billable Hour Rate UID", value="D7C584CE-BCEC-4FF2-8D98-8DAA0328656B" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="donationSubOtherRateUID", type="STRING", desc="Donation Sub Other Rate UID", value="BC6EDEA0-4532-4F9D-859A-BAEF79E472C0" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="forumEmergingLawyersUID", type="STRING", desc="Forum Emerging Lawyers UID", value="DB27C2C3-B906-4E49-A16F-537746F5DC09" };  	
	arrayAppend(local.arrCustomFields, local.tmpField);
	/*half year rates*/
	local.tmpField = { name="Attorney0to1HalfYearAmt", type="STRING", desc="Attorney in practice 0 through 1 year half year amount", value="0" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Attorney2to3HalfYearAmt", type="STRING", desc="Attorney in practice 2 through 3 years half year amount", value="50" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Attorney4to6HalfYearAmt", type="STRING", desc="Attorney in practice 4 through 6 years half year amount", value="75" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Attorney7to12HalfYearAmt", type="STRING", desc="Attorney in practice 7 through 12 years half year amount", value="100" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Attorney13PlusHalfYearAmt", type="STRING", desc="Attorney in practice 13 or more years half year amount", value="110" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="FacultyLicensedHalfYearAmt", type="STRING", desc="Law School Faculty half year amount", value="53" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Graduate0to2HalfYearAmt", type="STRING", desc="Law School Graduate (within the first two years and not admitted to practice in any jurisdiction) half year amount", value="0" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Graduate2PlusHalfYearAmt", type="STRING", desc="Law School Graduate (JD, not licensed in any jurisdiction and 2 or more years since graduation) half year amount", value="53" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ExecutiveDirectorsAndStaffHalfYearAmt", type="STRING", desc="Executive Directors and staff of San Diego legal community organizations who are not attorneys or law school graduates/JD half year amount", value="63" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="AssociateMemberHalfYearAmt", type="STRING", desc="Associate Member (Paralegals, legal community professionals) half year amount", value="63" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="FacultyNonLicensedHalfYearAmt", type="STRING", desc="Law School Faculty (who are not an Active licensed status with any licensing jurisdiction) half year amount", value="43" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="VendorsHalfYearAmt", type="STRING", desc="Vendors supplying products and services to the legal community and who have never been licensed to practice in any jurisdiction or have an inactive status license half year amount", value="110" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	/*full year rates*/
	local.tmpField = { name="Attorney0to1FullYearAmt", type="STRING", desc="Attorney in practice 0 through 1 year full year amount", value="0" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Attorney2to3FullYearAmt", type="STRING", desc="Attorney in practice 2 through 3 years full year amount", value="110" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Attorney4to6FullYearAmt", type="STRING", desc="Attorney in practice 4 through 6 years full year amount", value="185" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Attorney7to12FullYearAmt", type="STRING", desc="Attorney in practice 7 through 12 years full year amount", value="245" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Attorney13PlusFullYearAmt", type="STRING", desc="Attorney in practice 13 or more years full year amount", value="265" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="FacultyLicensedFullYearAmt", type="STRING", desc="Law School Faculty full year amount", value="135" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Graduate0to2FullYearAmt", type="STRING", desc="Law School Graduate (within the first two years and not admitted to practice in any jurisdiction) full year amount", value="135" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Graduate2PlusFullYearAmt", type="STRING", desc="Law School Graduate (JD, not licensed in any jurisdiction and 2 or more years since graduation) full year amount", value="135" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ExecutiveDirectorsAndStaffFullYearAmt", type="STRING", desc="Executive Directors and staff of San Diego legal community organizations who are not attorneys or law school graduates/JD full year amount", value="135" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="AssociateMemberFullYearAmt", type="STRING", desc="Associate Member (Paralegals, legal community professionals) full year amount", value="135" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="FacultyNonLicensedFullYearAmt", type="STRING", desc="Law School Faculty (who are not an Active licensed status with any licensing jurisdiction) full year amount", value="135" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="VendorsFullYearAmt", type="STRING", desc="Vendors supplying products and services to the legal community and who have never been licensed to practice in any jurisdiction or have an inactive status license full year amount", value="275" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	/*late rates*/
	local.tmpField = { name="Attorney0to1LateAmt", type="STRING", desc="Attorney in practice 0 through 1 year late amount", value="0" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Attorney2to3LateAmt", type="STRING", desc="Attorney in practice 2 through 3 years late amount", value="110" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Attorney4to6LateAmt", type="STRING", desc="Attorney in practice 4 through 6 years late amount", value="185" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Attorney7to12LateAmt", type="STRING", desc="Attorney in practice 7 through 12 years late amount", value="245" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Attorney13PlusLateAmt", type="STRING", desc="Attorney in practice 13 or more years late amount", value="265" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="FacultyLicensedLateAmt", type="STRING", desc="Law School Faculty late amount", value="135" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Graduate0to2LateAmt", type="STRING", desc="Law School Graduate (within the first two years and not admitted to practice in any jurisdiction) late amount", value="135" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Graduate2PlusLateAmt", type="STRING", desc="Law School Graduate (JD, not licensed in any jurisdiction and 2 or more years since graduation) late amount", value="135" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ExecutiveDirectorsAndStaffLateAmt", type="STRING", desc="Executive Directors and staff of San Diego legal community organizations who are not attorneys or law school graduates/JD late amount", value="135" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="AssociateMemberLateAmt", type="STRING", desc="Associate Member (Paralegals, legal community professionals) late amount", value="135" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="FacultyNonLicensedLateAmt", type="STRING", desc="Law School Faculty (who are not an Active licensed status with any licensing jurisdiction)  late amount", value="135" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="VendorsLateAmt", type="STRING", desc="Vendors supplying products and services to the legal community and who have never been licensed to practice in any jurisdiction or have an inactive status license late amount", value="275" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	/*donation amounts*/
	local.tmpField = { name="FoundationDonationAmt1", type="STRING", desc="Foundation Donation Amount 1", value="25" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="FoundationDonationAmt2", type="STRING", desc="Foundation Donation Amount 2", value="100" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="FoundationDonationAmt3", type="STRING", desc="Foundation Donation Amount 2", value="250" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="FoundationDonationAmt4", type="STRING", desc="Foundation Donation Amount 2", value="500" }; 
	arrayAppend(local.arrCustomFields, local.tmpField);

	local.tmpField = { name="MembershipDuesStatement", type="CONTENTOBJ", desc="Membership Dues Statement", value="Basic Membership dues for attorneys with active status license from any US jurisdiction varies by years in practice from date of first licensure. <a href='/?pg=JoinSDCBA' target='_blank' class='bodytext'>Click here</a> to view the price schedule."};
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="AssociateMember", type="CONTENTOBJ", desc="Associate Member", value="Associate Member <small>(Paralegals and other legal community professionals not licensed to practice law in any US jurisdiction)</small>"};
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Graduate0to2", type="CONTENTOBJ", desc="Graduate 0 to 2 years", value="Law School Graduate <small>(Within 2 years or less of graduating from a JD Program and not licensed to practice law in any US jurisdiction)</small>"};
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Graduate2Plus", type="CONTENTOBJ", desc="Graduate 2 or more years", value="Law School Graduate JD <small>(JDs not licensed in to practice law in any US jurisdiction and 2 or more years since graduation from JD program)</small>"};
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ExecutiveDirectorsAndStaff", type="CONTENTOBJ", desc="Executive Directors And Staff", value="Executive <small>(Executive Directors and staff of SD legal community organizations who are not licensed to practice law in any US jurisdiction or with a JD)</small>"};
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="FacultyNonLicensed", type="CONTENTOBJ", desc="Non licensed Law School Faculty", value="Law School Faculty <small>(who are not an Active licensed status with any licensing jurisdiction)</small>"};
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="Vendors", type="CONTENTOBJ", desc="Vendors", value="Vendor <small>(Vendors supplying products and services to the legal community and not licensed to practice law in any US jurisdiction. Formerly Affiliate.)</small>"};
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="CertificationStatement", type="CONTENTOBJ", desc="Certification Statement", value="I certify that the information provided in this SDCBA Member Application is true and correct in all material respects, and that I meet the relevant member eligibility requirements.  I agree and acknowledge that if the SDCBA in its discretion deems me ineligible for membership and/or if any of the information provided in this application is false, the membership will be void from the outset."};
	arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="FoundationDonationStatement", type="CONTENTOBJ", desc="Foundation Donation section statement", value="San Diego County Bar Foundation (SDCBF): The San Diego County Bar Foundation is the charitable arm of the San Diego County Bar Association and strives to provide access to justice by investing in sustainable results and advocacy for people and communities in our region that are impacted by poverty, abuse and discrimination. The Bar Foundation is a 501(c)(3) tax-exempt public charity and your contribution may be tax deductible.  Please consult with your tax advisor. The Bar Foundation''s tax identification number is 95-3366651. To learn more about the Foundation, visit sdcbf.org. Please consider a voluntary donation to the Foundation below."};
	arrayAppend(local.arrCustomFields, local.tmpField);
	
	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);

	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=arguments.event,
		formName='frmFB35',
		formNameDisplay='#local.strPageFields.FormNameDispaly#',
		orgEmailTo='#local.strPageFields.OrgEmailRecipient#',
		orgEmailFrom='<EMAIL>',
		memberEmailFrom='#local.strPageFields.memberEmailFrom#'
	));
	/* THESE MUST BE SET CORRECTLY OR ELSE */
	/* We should strongly consider automating when these roll over */
		local.useHalfYearRates	= false;
		local.useLateRates			= true;
	/*-------------------------------------*/
	
	local.emailSubject			= 'Membership Application';
	
	local.mainhostName 			= event.getValue('mc_siteInfo.mainhostName');
	local.scheme 				= event.getValue('mc_siteInfo.scheme');
	local.USStates 					= application.objCustomPageUtils.mem_getStatesByCountry('United States');

	local.memberTypeUID					= '#local.strPageFields.memberTypeUID#';
	local.sectionTypeUID				= '#local.strPageFields.sectionTypeUID#';
	local.sectionSetUID					= '#local.strPageFields.sectionSetUID#';
	local.caAttyMembershipAllUID 		= '#local.strPageFields.caAttyMembershipAllUID#';
	local.afMembershipAllUID 			= '#local.strPageFields.afMembershipAllUID#';
	local.lcMembershipAll 				= '#local.strPageFields.lcMembershipAll#';
	local.donationSubUID				= '#local.strPageFields.donationSubUID#';
	local.donationSubStandardRateUID 	= '#local.strPageFields.donationSubStandardRateUID#';
	local.donationSubBillableHourRateUID = '#local.strPageFields.donationSubBillableHourRateUID#';
	local.donationSubOtherRateUID 		= '#local.strPageFields.donationSubOtherRateUID#';

	local.objSubs						= CreateObject('component','model.admin.subscriptions.subscriptions');
	
	local.forumQualifyNumOfDays 		= 1826;
	local.forumEmergingLawyersUID 		= "#local.strPageFields.forumEmergingLawyersUID#";
	local.qryMemberPL	 				= application.objMember.getMemberProfLicenses(local.memberid,local.orgID,'California');	

	local.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname=local.formName);

	local.arrDonationAmts = ArrayNew(1);
	local.arrDonationAmts[1] = {checked=1,text='I''d like to make a $#local.strPageFields.FoundationDonationAmt1# voluntary donation',amount=local.strPageFields.FoundationDonationAmt1};
	local.arrDonationAmts[2] = {checked=0,text='I''d like to make a $#local.strPageFields.FoundationDonationAmt2# voluntary donation',amount=local.strPageFields.FoundationDonationAmt2};
	local.arrDonationAmts[3] = {checked=0,text='I''d like to make a $#local.strPageFields.FoundationDonationAmt3# voluntary donation',amount=local.strPageFields.FoundationDonationAmt3};
	local.arrDonationAmts[4] = {checked=0,text='I''d like to make a $#local.strPageFields.FoundationDonationAmt4# voluntary donation',amount=local.strPageFields.FoundationDonationAmt4};
</cfscript>

<cfquery name="local.qryGetCaliforniaLicense" datasource="#application.dsn.membercentral.dsn#">
	select PLTypeID, PLName from dbo.ams_memberProfessionalLicenseTypes where plname = 'California' and  orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
</cfquery>

<cfset local.membershipDuesTypeID = application.objCustomPageUtils.sub_getTypeFromUID(siteID=local.siteID, typeUID=local.memberTypeUID)>

<cfset local.hasSub = false>
<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)/>
        
<cfif (NOT local.isSuperUser AND int(local.useMID) GT 0)>
	<cfset local.hasSub = application.objCustomPageUtils.sub_hasSubsciptionInType(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), mcproxy_siteID=local.siteID, memberID=local.useMID, typeID=local.membershipDuesTypeID)>
</cfif>

<cfoutput>
	<cfsavecontent variable="local.pageCSS">
		<style type="text/css">
			.customPage{font-family: Verdana, Arial, Helvetica, sans-serif;}
			
			.frmContent{ padding:10px; background:##ddd }
			.frmRow1{ background:##fff; }
			.frmRow2{ background:##eaeaea; }
			.frmRow3{ background:##999999;}
			.frmText{ font-size:8pt; font-weight:none; color:##666;}
			.frmButtons{ padding:5px 0; border-top:3px solid ##520e0d; border-bottom:3px solid ##520e0d; }
			
			<!--- background:url(/assets/common/images/interior_titleBG_Tan.jpg) repeat-x; --->
			.TitleText {  font-size:16pt; color:##4f6550; font-weight:bold;}
			.CPSection{  margin-bottom:15px; }
			a.calnavright,a.calnavleft{ text-decoration:none; }
			.CPSectionTitle { font-size:10pt; font-weight:bold; color:##fff; padding:8px; background-color:##0E568D; }
			.CPSectionContent{ padding:0 10px; }
			.subCPSectionArea1 { padding:5px; background-color:##cccccc; }
			.subCPSectionArea2 { padding:5px; background-color:##dbdedf; }
			.subCPSectionArea3 { padding:5px; background-color:##aaa;}
			.subCPSectionArea4 { padding:5px; background-color:##eaeaea;}
			.subCPSectionTitle { font-size:9pt; font-weight:bold;}
			.subCPSectionText { font-size:8.5pt;}
			
			.info{ font-style:italic; font-size:8pt; color:##555; font-weight:normal;}
			.small{ font-size:7.5pt;}
			
			.r { text-align:right; }
			.l { text-align:left; }
			.cent { text-align:center; }
			.i { font-style:italic; }
			.bld { font-weight:bold; }
			
			.P{padding:10px;}
			.PL{padding-left:10px;}
			.PR{padding-right:10px;}
			.PB{padding-bottom:10px;}
			.PT{padding-top:10px;}
			
			.BB { border-bottom:1px solid ##666; }
			.BL { border-left:1px solid ##666; }
			.BT { border-top:1px solid ##666; }
			.block { display:block; }
			.black{ color:##000000; }
			.red{ color:##ff0000; }
			<!--- EMAIL CSS: ------------------------------------------------------------------------------------------------ --->
			.msgHeader{background:##0E568D; color:##ffffff; font-weight:Bold; text-transform:uppercase; padding:5px;}
			.msgSubHeader{background:##dddddd;}
			
			<!---.bodytext { color:##03608b;}--->
			select.bodytext{color:##666;}
			
			.alert{ background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
			.paymentGateway{ background-color:##ededed; padding:10px; }
			##memberNumber{ display:inline-block; width:140px; }
			
			.totalsContent{ width:450px; }
			.totalsItem{ border-bottom:1px solid ##777777; }
			.grandTotal{ font-weight:bold; }
			.totalsText{ display:inline-block; width:70%;padding:3px 0 0 5px; }
			.totalsValue{ display:inline-block; width:25%; text-align:right; padding:3px 5px 0 0; }
			##date_bar, ##date_birth, ##date_graduation { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:95% 50%; background-repeat:no-repeat; }

			.bardate { 
				background-image:url("/assets/common/images/calendar/monthView.gif"); 
				background-position:right center; background-repeat:no-repeat;
			}
			##vendorDescription td textarea{ width : 200% !important; }
			##membershipDescription>td>table>tbody>tr:first-child td {
				background-color: ##0E568D !important;
				color: ##ffffff !important;
				font-weight: Bold !important;
				text-transform: uppercase;
				padding: 5px !important;
				border-bottom: 0px !important;
				font: bold 16px Verdana,Helvetica,Arial,sans-serif !important;
			}
			##membershipDescription>td>table>tbody>tr:nth-child(2) td table {
				border: 1px solid !important;
				width: 100%;
			}
			##membershipDescription>td>table>tbody>tr:nth-child(2)>td {
				padding: 0px !important;
			}
			##membershipDescription>td>table>tbody>tr:nth-child(2) td table tbody tr td:first-child {
				border-right: 1px solid;
				font-size: 8pt !important;
				font-weight: bold !important;
				color: ##666 !important;
				width: 22% !important;
			}
			##membershipDescription>td>table>tbody>tr:nth-child(2) td table tbody tr td:nth-child(2) {
				font-size: 8pt !important;
				color: ##666 !important;
			}
			##membershipDescription>td>table {
				margin-bottom: -14px !important;
			}
		</style>
	</cfsavecontent>
	
	<cfsavecontent variable="local.pageJS">
		<script type='text/javascript' src='/assets/common/javascript/date.js'></script>
		<script type="text/javascript">
			function _FB_hasValue(obj, obj_type){
				if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){ tmp = obj.value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length == 0) return false; else return true; }
				else if (obj_type == 'SELECT'){ for (var i=0; i < obj.length; i++) { if (obj.options[i].selected){ tmp = obj.options[i].value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length > 0) return true; } } return false; } 
				else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){ if (obj.checked) return true; else return false;	} 
				else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){ if (obj.length == undefined && obj.checked) return true; else{for (var i=0; i < obj.length; i++){ if (obj[i].checked) return true; }} return false; }
				else{ return true; }
			}
			function _FB_withinResponseLimit(obj, obj_type, optRespLimit) {
				var cnt = 0;
				for (var i=0; i < obj.length; i++) {
					if (obj_type == 'SELECT' && obj.options[i].selected) cnt++;
					else if (obj_type == 'CHECKBOX' && obj[i].checked) cnt++;
				}					
				if (cnt <= optRespLimit) return true;
				else return false;
			}
			function formatCurrency(num) {
				num = num.toString().replace(/\$|\,/g,'');
				if(isNaN(num)) num = "0";
				num = Math.abs(num);	// added by tl 5/16/2006. force positive values only
				sign = (num == (num = Math.abs(num)));
				num = Math.floor(num*100+0.50000000001);
				cents = num%100;
				num = Math.floor(num/100).toString();
				if(cents<10) cents = "0" + cents;
				for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+
				num.substring(num.length-(4*i+3));
				return (((sign)?'':'-') + num + '.' + cents);
			}
			function getSelectedRadio(buttonGroup) {
				if (buttonGroup[0]) {
					for (var i=0; i<buttonGroup.length; i++) { if (buttonGroup[i].checked) return i }
				} else { if (buttonGroup.checked) return 0; }
				return -1;
			}
			function selectMember() {
				$.colorbox( {innerWidth:550, innerHeight:400, href:'/?pg=accountLocator&autoClose=0&retFunc=addMember&innerWidth=550&innerHeight=400', iframe:true, overlayClose:false} );
			}
			function resizeBox(newW,newH) { $.colorbox.resize({innerWidth:newW,innerHeight:newH});}
			function addMember(memObj) {
				$.colorbox.close();
				assignMemberData(memObj);
				$('##joinFormAccountLookup').hide();
			}
		</script>
	</cfsavecontent>
	
	#local.pageJS#
	#local.pageCSS#
	
	<div id="customPage">
		<cfif local.isSuperUser> 
			<div class="bodyText" >
				This form is not available when logged in as a SuperUser.
			</div>
		<cfelseif local.hasSub>
			<!--- Renewal form is not Open --->
			<div class="bodyText" >
				It looks like you might already be a member! Thank you and please contact SDCBA at (619) 231-0781 or <a href="mailto:<EMAIL>"><EMAIL></a> for information about your membership or renewing.
			</div>
		<cfelse>
			<cfswitch expression="#event.getValue('isSubmitted', 0)#">
			
				<cfcase value="0">
					<!---Resetting Captcha flag, so that an user access the join form for the first time is presented with captcha--->
					<cfif structKeyExists(session, "captchaEntered")>
						<cfset structDelete(session, "captchaEntered")>
					</cfif> 
							
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.sections">
						set nocount on
						declare @typeUID varchar(max), @siteID int, @setUID uniqueIdentifier
						
						select @typeUID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.sectionTypeUID#">
						select @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
						select @setUID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.sectionSetUID#">
						
						select subs.uid, REPLACE(subscriptionName, ' Section', '') as subscriptionName
						from dbo.sub_sets s
						inner join dbo.sub_subscriptionSets ss
							on ss.setID = s.setID
							and s.uid = @setUID
						inner join dbo.sub_subscriptions subs
							on subs.subscriptionID = ss.subscriptionID
						inner join dbo.sub_types t
							on t.typeID = subs.typeID
							and t.uid = @typeUID
							and t.siteID = @siteID
						order by subs.subscriptionName
						set nocount off
					</cfquery>
					
					<cfscript>
						local.section.recordCount		= local.sections.recordCount;
						local.section.column1			= round((local.section.recordCount + .49) / 2); 
					</cfscript>

					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryLawSchools">
						select mdcv.valueID, mdcv.columnValueString
						from dbo.ams_memberDataColumnValues mdcv
						inner join dbo.ams_memberDataColumns mdc 
							on mdc.columnID = mdcv.columnID
							and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
							and columnName = 'law_school'
						order by mdcv.columnValueString
					</cfquery>

					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryProfessionalLicenseTypes">
						select mplt.[PLTypeID], mplt.PLName
						from membercentral.dbo.ams_memberProfessionalLicenseTypes mplt
						where mplt.PLName not like 'CA Specialist%'
							and mplt.PLName not in ('Canadian Attorney License','Mexican Attorney License','Other Intl Attorney License')
							and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
						order by ordernum
					</cfquery>
					
					<cfset local.strData = {}>
					<cfset local.strData.memberID = 0>
					<cfset local.strData.orgID = local.orgID>
					<cfset local.strData.siteID = local.siteID>
					<cfset local.vendorDescriptionFieldSet = application.objCustomPageUtils.renderFieldSet(uid='996A83D8-D9D4-441B-B703-4822E02467FC', mode="collection", strData=local.strData)>

					<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
					<script type="text/javascript">
						var existingLicenseTemplate = null;
						var newLicenseTemplate = null;
						var compiledExistingLicenseTemplate = null;
						var compiledNewLicenseTemplate = null;
						var oldestBarDate = null;
						var licensesFound = false;
						var existingLicenseArray = [];

						function licenseChangeHandler(){
							//get oldest date
							var barDateArray = [];
							//Any active licenses?
							var foundActive = false;
							var allInactive = true;
							licensesFound = false;

							if (existingLicenseArray.length) {
								for (var i=0;i<existingLicenseArray.length;i++) {
									if (existingLicenseArray[i].activedate.length) {
										barDateArray.push(parseInt(moment(existingLicenseArray[i].activedate,"M/D/YYYY").format("YYYYMMDD")));
									}
									if (existingLicenseArray[i].statusname !== 'Inactive') {
										allInactive = false;
									}
									if (existingLicenseArray[i].licensenumber.length && existingLicenseArray[i].statusname === 'Active') {
										foundActive = true;
										licensesFound = true;
									} else if (existingLicenseArray[i].licensenumber.length) {
										licensesFound = true;
									}
								}
							}

							$('.bardate').each( function (index,thisItem){
								if (thisItem.value.length) {
									barDateArray.push(parseInt(moment(thisItem.value,"M/D/YYYY").format("YYYYMMDD")));
								}
							});
							if (barDateArray.length) {
								oldestBarDate = moment(Math.min.apply(Math,barDateArray).toString(),"YYYYMMDD");
								$('##bardate')[0].value = moment(oldestBarDate).format('MM/DD/YYYY');
							} else {
								oldestBarDate = null;
							}

							$('.newLicenseRow').each( function (index,thisItem){
								thisBardate = $(thisItem).find('.bardate')[0];
								thisBarnumber = $(thisItem).find('.barnumber')[0];
								thisBarstatus = $(thisItem).find('.barstatus')[0];

								if (thisBarstatus.value !== 'Inactive') {
									allInactive = false;
								}
								if (thisBarstatus.value === 'Active') {
									foundActive = true;
									licensesFound = true;
								} else if (thisBarnumber.value.length) {
									licensesFound = true;
								}
							});
							if(!licensesFound){
								allInactive = false;
							}
							$('##hasActiveLicense')[0].value = foundActive;
							$('##allInactiveLicense')[0].value = allInactive;
							showYears();
						}
						function assignMemberData(memObj){
							var thisForm = document.forms["#local.formName#"];
							var er_changeErr = function(r) {
								alert('error');
							};
							
							var er_change = function(r) {
								var results = r;
								if( results.success ){

									thisForm['memberNumber'].value 	= results.membernumber;
									thisForm['memberID'].value 			= results.memberid;

									thisForm['name'].value = results.firstname + ' ' + results.lastname;
									
									document.getElementById('divName').innerHTML = results.firstname + ' ' + results.lastname;

									thisForm['firm'].value = results.company;
									document.getElementById('divFirm').innerHTML = results.company;
									
									var dispAddress = results.address1;
									if (results.address2.length > 0)
										dispAddress += '<br>' + results.address2;
									
									thisForm['address'].value = dispAddress;
									document.getElementById('divAddress').innerHTML = dispAddress;
	
									thisForm['city'].value = results.city;
									document.getElementById('divCity').innerHTML = results.city;
									
									thisForm['state'].value = results.statecode;
									document.getElementById('divState').innerHTML = results.statecode;
									
									thisForm['zip'].value = results.postalcode;
									document.getElementById('divZip').innerHTML = results.postalcode;
									
									thisForm['phone'].value = results.phone;
									document.getElementById('divPhone').innerHTML = results.phone;
									
									thisForm['email'].value = results.email;
									document.getElementById('divEmail').innerHTML = results.email;
									
									thisForm['graddate'].value = results.law_school_date.toString('M/d/yyyy');
	
									var lawschool = thisForm['lawschool'];
									for (var i=0; i <= lawschool.length-1; i++) {
										if (lawschool[i].text == results.law_school) lawschool[i].selected = true;
									}

									existingLicenseTemplate = $('##existingLicenseTemplate').html();
									compiledExistingLicenseTemplate = Handlebars.compile(existingLicenseTemplate);

									newLicenseTemplate = $('##newLicenseTemplate').html();
									compiledNewLicenseTemplate = Handlebars.compile(newLicenseTemplate);

									//existingLicenses
									if (r.licenses.length) {
										existingLicenseArray = r.licenses;
										for (var i=0;i<r.licenses.length;i++) {
											$('##existingLicenses').append(compiledExistingLicenseTemplate(r.licenses[i]));
											$("##professionalLicenseDropdown option[value='" + r.licenses[i].pltypeid + "']").remove();
										}
										$('##existingLicenseSection').show();
									}
									$("##professionalLicenseDropdown").multiselect({ header: false, noneSelectedText: 'Choose Licensed Jurisdictions', selectedList: 5 });
									$('##professionalLicenseDropdown').change(function(event) {
										var selectedValues = [];
										//make sure that all currently selected items have fields listed in the new license section
										$(this).find("option:selected").each( function (index,thisItem){
											selectedValues.push(thisItem.value);
											if (!$("##newLicenses").has($("##pl" + thisItem.value)).length) {
												if ($("##pl" + thisItem.value).length) {
													$('##newLicenses').append($("##pl" + thisItem.value));
												} else {
													var newLicenseData = { plname: thisItem.text, plid: thisItem.value };
													$('##newLicenses').append(compiledNewLicenseTemplate(newLicenseData));
													mca_setupDatePickerField('pl' + thisItem.value + '_licensedate');
													$('##btnClearpl' + thisItem.value + '_licensedate').click( function(e) { mca_clearDateRangeField('pl' + thisItem.value + '_licensedate');return false;} );
												}

												$('##pl' + thisItem.value + '_licensenumber').on('change',licenseChangeHandler);
												$('##pl' + thisItem.value + '_licensedate').on('change',licenseChangeHandler);
												$('##pl' + thisItem.value + '_licensestatus').on('change',licenseChangeHandler);
											}
										});

										//show table if any selections are made
										if (selectedValues.length) {
											$('##newLicensesTable').show();
										} else {
											$('##newLicensesTable').hide();
										}

										//see if any previously checked licenses have been deleted from list

										if (selectedValues.length) {
											var selectedLicenseIDsRegex = new RegExp('pl(' + selectedValues.join('|') + ')');
											$('##newLicenses tr').each( function (index, thisLicenseRow) {
												if (thisLicenseRow.id.search(selectedLicenseIDsRegex) === -1) {
													$('##' + thisLicenseRow.id + '_licensenumber').off();
													$('##' + thisLicenseRow.id + '_licensedate').off();
													$('##' + thisLicenseRow.id + '_licensestatus').off();
													$(thisLicenseRow).remove();
												}
											});
										} else  {
											$('##newLicenses tr').each( function (index, thisLicenseRow) {
												$(thisLicenseRow).remove();
											});
										}
									});

									// un hide form   
									if (r.types['#LCase(local.memberTypeUID)#'] != 1)
										document.getElementById('memberInfo').style.display 		= '';
									else
										document.getElementById('memberExisting').style.display = '';
								} else { alert('not success'); }
								licenseChangeHandler();
							};
	
							var customFields = [];
							customFields[0] = 'law_school';
							customFields[1] = 'law_school_date';
							var typeUIDs = '#local.memberTypeUID#'.split(',');

							$('##newLicensesTable').hide();
							$('##isNewRecord').val(memObj.isNewRecord);
							var objParams = { memberNumber:memObj.memberNumber, typeUIDs:typeUIDs, customFields:customFields };
							TS_AJX('SUBS','getMemberDataByMemberNumberWithSubTypes',objParams,er_change,er_changeErr,20000,er_changeErr);
							showCaptcha();
						}
						function loadMember(memNumber){
							var objParams = { memberNumber:memNumber };
							assignMemberData(objParams);
						}
						function checkCaptchaAndValidate(){
							var thisForm = document.forms["#local.formName#"];
							var status = false;
							var captcha_callback = function(captcha_response){
								if (captcha_response.response && captcha_response.response != 'success') {
									status = false;
								} else {
									status = true;
								}
							}
							if (!_FB_hasValue(thisForm['captcha'], 'TEXT')) {
								alert('Please enter the correct code shown in the graphic.');
								return false;
							} else {
								#local.captchaDetails.jsvalidationcode#
							}
							if(status){
								return _FB_validateJoinForm();
							} else {
								alert('Please enter the correct code shown in the graphic.');
								return false;
							}
						}
						function showYears() {
							var thisForm = document.forms["#local.formName#"];
							
							var dateRegEx = /^[01]?[0-9]\/[0-3]?[0-9]\/[12][90][0-9][0-9]$/;
							if (dateRegEx.test(thisForm['bardate'].value)) {
								var monthPart = parseInt(thisForm['bardate'].value.split("/")[0]);
								var dayPart = parseInt(thisForm['bardate'].value.split("/")[1]);
								var yearPart = parseInt(thisForm['bardate'].value.split("/")[2]);
	
								if ((monthPart != NaN) && (dayPart != NaN) && (yearPart != NaN)) {
									var now = new Date();
									var barDate = new Date(yearPart, monthPart-1, dayPart);
									
									if (now > barDate) {
										var currYear = now.getFullYear();
										var memAllow = 0;
										
										if ((yearPart == currYear) || ((yearPart == (currYear - 1)) && (monthPart >= 10))) {
											memAllow = 1;
										} else if ((yearPart == (currYear - 2)) ||
														((yearPart == (currYear - 1)) && (monthPart <= 9)) ||
														((yearPart == (currYear - 3)) && (monthPart >= 10))) {
											memAllow = 2;
										} else if (((yearPart <= (currYear - 4)) && (yearPart >= (currYear - 5))) ||
														((yearPart == (currYear - 3)) && (monthPart <= 9)) ||
														((yearPart == (currYear - 6)) && (monthPart >= 10))) {
											memAllow = 3;
										} else if (((yearPart <= (currYear - 7)) && (yearPart >= (currYear - 11))) ||
														((yearPart == (currYear - 6)) && (monthPart <= 9)) ||
														((yearPart == (currYear - 12)) && (monthPart >= 10))) {
											memAllow = 4;
										} else if ((yearPart <= (currYear - 13)) || 
														((yearPart == (currYear - 12)) && (monthPart <= 9))) {
											memAllow = 5;
										}
									}
								}
							}
	
							//law school grad year
							var currentTimestamp = moment();
							//moment object parse uses zero-indexed array for months
							var octoberFirstOfThisYear = moment({ y:currentTimestamp.year(), M:9, d:1});
							var mostRecentOctoberFirst;
							var glsCutoffDate;
							if (currentTimestamp.isBefore(octoberFirstOfThisYear)) {
								mostRecentOctoberFirst = octoberFirstOfThisYear.subtract(1,'years');
							} else {
								mostRecentOctoberFirst = octoberFirstOfThisYear;
							}

							glsCutoffDate = mostRecentOctoberFirst.subtract(2,'years');

							if (dateRegEx.test($('##graddate')[0].value)) {
								var graddate = moment($('##graddate')[0].value);
							}

							var membership = thisForm['membership'];
							for (var i=0; i <= membership.length-1; i++) {
								switch(membership[i].value) {
									case "1": case "2": case "3": case "4": case "5":
									//active attorney years in practice rates
										if (membership[i].value == memAllow && $('##hasActiveLicense')[0].value == 'true') {
											membership[i].disabled = false;
										}
										else {
											membership[i].disabled = true;
											membership[i].checked = false;
										}
									break;
									case "6":
									//law school faculty licensed anywhere regardless of status
										if (licensesFound && $('##allInactiveLicense')[0].value == 'false') {
											membership[i].disabled = false;
										}
										else {
											membership[i].disabled = true;
											membership[i].checked = false;
										}
									break;
									case "7":
									//grad from law school within last 2 years with no bar licenses
										if ((!licensesFound || (licensesFound && $('##allInactiveLicense')[0].value == 'true')) && graddate && graddate.isAfter(glsCutoffDate)) {
											membership[i].disabled = false;
										}
										else {
											membership[i].disabled = true;
											membership[i].checked = false;
										}
									break;
									case "8":
									//grad from law school over 2 years ago with no bar licenses
										if ((!licensesFound || (licensesFound && $('##allInactiveLicense')[0].value == 'true')) && graddate && graddate.isBefore(glsCutoffDate)) {
											membership[i].disabled = false;
										}
										else {
											membership[i].disabled = true;
											membership[i].checked = false;
										}
									break;
									case "9": case "10": case "11": case "12":
										if ((!licensesFound || (licensesFound && $('##allInactiveLicense')[0].value == 'true')) && !graddate) {
											membership[i].disabled = false;
										}
										else {
											membership[i].disabled = true;
											membership[i].checked = false;
										}
									break;
								}
							}
							getTotalDue();
	
							// validate it is a good date - check
							// validate that is is in the past - check
							// disable the invalid years
							// and deselect if invalid is selected
						}
						function getTotalDue(){
							var thisForm = document.forms["frmFB35"];
							var mLevel 	= 0;
							var sCount 	= 0;				
							var mTotal 	= 0;
							var sTotal 	= 0;
							var dTotal 	= 0;
							var total = 0;				
							for (var i=0; i < thisForm.membership.length; i++){
								if (thisForm.membership[i].checked) mLevel = thisForm.membership[i].value;
							}
							
							if(mLevel == 12){
								$("##vendorDescription").css('display','block');
								$("##vendorDescription tr td:nth-child(2)").removeClass('tsAppBodyText');
							} else {
								$("##vendorDescription").css('display','none');
							}  
							<!--- membership total --->
							switch (mLevel){
							<cfif local.useHalfYearRates>
								case "1":	mTotal = #local.strPageFields.Attorney0to1HalfYearAmt#;		break;
								case "2":	mTotal =  #local.strPageFields.Attorney2to3HalfYearAmt#;	break;
								case "3":	mTotal =  #local.strPageFields.Attorney4to6HalfYearAmt#;	break;
								case "4": 	mTotal =  #local.strPageFields.Attorney7to12HalfYearAmt#;	break;
								case "5": 	mTotal =  #local.strPageFields.Attorney13PlusHalfYearAmt#;	break;
								case "6": 	mTotal =  #local.strPageFields.FacultyLicensedHalfYearAmt#;	break;
								case "7": 	mTotal =  #local.strPageFields.Graduate0to2HalfYearAmt#;	break;
								case "8": 	mTotal =  #local.strPageFields.Graduate2PlusHalfYearAmt#;	break;
								case "9": 	mTotal =  #local.strPageFields.ExecutiveDirectorsAndStaffHalfYearAmt#;	break;
								case "10": 	mTotal =  #local.strPageFields.AssociateMemberHalfYearAmt#;	break;
								case "11":	mTotal =  #local.strPageFields.FacultyNonLicensedHalfYearAmt#;	break;
								case "12":	mTotal =  #local.strPageFields.VendorsHalfYearAmt#;	break;
							<cfelseif local.useLateRates>
								case "1": 	mTotal = #local.strPageFields.Attorney0to1LateAmt#; 	break;
								case "2": 	mTotal = #local.strPageFields.Attorney2to3LateAmt#; 	break;
								case "3": 	mTotal = #local.strPageFields.Attorney4to6LateAmt#; 	break;
								case "4": 	mTotal = #local.strPageFields.Attorney7to12LateAmt#; 	break;
								case "5": 	mTotal = #local.strPageFields.Attorney13PlusLateAmt#; 	break;
								case "6": 	mTotal = #local.strPageFields.FacultyLicensedLateAmt#; 	break;
			
								case "7": 	mTotal = #local.strPageFields.Graduate0to2LateAmt#; 	break;
								case "8": 	mTotal = #local.strPageFields.Graduate2PlusLateAmt#; 	break;
								case "9": 	mTotal = #local.strPageFields.ExecutiveDirectorsAndStaffLateAmt#; 	break;
								case "10": 	mTotal = #local.strPageFields.AssociateMemberLateAmt#; 	break;
								case "11":	mTotal = #local.strPageFields.FacultyNonLicensedLateAmt#; 	break;
			
								case "12":	mTotal = #local.strPageFields.VendorsLateAmt#; 	break;
							<cfelse>
								case "1": 	mTotal =  #local.strPageFields.Attorney0to1FullYearAmt#;  	break;
								case "2": 	mTotal =  #local.strPageFields.Attorney2to3FullYearAmt#;	break;
								case "3": 	mTotal =  #local.strPageFields.Attorney4to6FullYearAmt#;	break;
								case "4": 	mTotal =  #local.strPageFields.Attorney7to12FullYearAmt#;	break;
								case "5": 	mTotal =  #local.strPageFields.Attorney13PlusFullYearAmt#;	break;
								case "6": 	mTotal =  #local.strPageFields.FacultyLicensedFullYearAmt#;	break;
			
								case "7": 	mTotal =  #local.strPageFields.Graduate0to2FullYearAmt#;	break;
								case "8": 	mTotal =  #local.strPageFields.Graduate2PlusFullYearAmt#;	break;
								case "9": 	mTotal =  #local.strPageFields.ExecutiveDirectorsAndStaffFullYearAmt#;	break;
								case "10": 	mTotal =  #local.strPageFields.AssociateMemberFullYearAmt#;	break;
								case "11":	mTotal =  #local.strPageFields.FacultyNonLicensedFullYearAmt#;	break;
			
								case "12":	mTotal =  #local.strPageFields.VendorsFullYearAmt#;	break;
							</cfif>		
								default:	mTotal = 0;
							}				
							$('##membershipAmount').val(mTotal);
							<!--- section total --->
							sTotal = 0;
								
							<!--- donation total --->
							dTotal = 0;
							switch ($(thisForm.donation).filter(':CHECKED')[0].value){
								case "donationOther":
									if ($.isNumeric($('##donationOtherAmount')[0].value))
										dTotal = parseFloat($('##donationOtherAmount')[0].value);
									else
										dTotal = 0;
									break;
								case "donationDecline":
									dTotal = 0; break;
								default:
									dTotal = parseFloat($(thisForm.donation).filter(':CHECKED')[0].value);
									
							}
							if($(thisForm.mcleSubs).filter(':CHECKED').length)
								mcleTotal =  parseFloat(#local.strPageFields.MCLEFull#);
							else
								mcleTotal = 0;
							<!--- totalDue --->
							total = mTotal + sTotal + dTotal + mcleTotal;
							document.getElementById('memberShipTotal').innerHTML = mTotal;
							document.getElementById('sectionTotal').innerHTML = sTotal;
							document.getElementById('donationTotal').innerHTML = dTotal;
							document.getElementById('mcleTotal').innerHTML = mcleTotal;
							document.getElementById('totalDue').innerHTML = total;
						}		
						function _FB_validateJoinForm() {
							var thisForm = document.forms["#local.formName#"];
							var _CF_this = document.forms["#local.formName#"];
							var arrReq = new Array();
							var lastMtrxErr = '';

							if (!_FB_hasValue(thisForm['memberNumber'], 'TEXT')) arrReq[arrReq.length] 		= 'Please Create an Account';

							if (!_FB_hasValue(thisForm['directory'], 'RADIO')) arrReq[arrReq.length] 	= 'SDCBA Online Directory';
							if (!_FB_hasValue(thisForm['firmtype'], 'RADIO')) arrReq[arrReq.length] 	= 'Firm Profile';
							if (!_FB_hasValue(thisForm['membership'], 'RADIO')) {
								arrReq[arrReq.length] 		= 'Dues Schedule';
							} else {
								var selMembership = '';
								var membership = thisForm['membership'];
								for (var i=0; i <= membership.length-1; i++) {
									if (membership[i].checked) {
										selMembership = membership[i].value;
										if(selMembership == 12){
											#local.vendorDescriptionFieldSet.jsValidation#
										}
									}
								}
								var _arLicenses = $('##professionalLicenseDropdown').val();
								var _CANmbr = '';
								if(_arLicenses != null &&_arLicenses.length){
									<cfif local.qryGetCaliforniaLicense.recordCount>
									if(_arLicenses.indexOf('#local.qryGetCaliforniaLicense.PLTypeID#') > 0){
										_CANmbr = $('##pl#local.qryGetCaliforniaLicense.PLTypeID#_licensenumber').val();
										if(membership != 1 && membership != 2 && membership != 3 && membership != 4 && membership != 5 &&membership != 6){
											arrReq[arrReq.length] 	= 'One Attorney Members category should be selected.';
										}
									}
									</cfif>
								}
								switch (thisForm['donation'].value) {
									case "donationOther":
										if (!($('##donationOtherAmount')[0].value.length) || !($.isNumeric($('##donationOtherAmount')[0].value))) {
											arrReq[arrReq.length] 	= 'Foundation Donation Amount';
										}
									break;
								}
							}
							if (!_FB_hasValue(thisForm['membershipCertification'], 'RADIO')) arrReq[arrReq.length] 	= 'Membership Certification';
							if (arrReq.length > 0) {
								var msg = 'The following questions are required:\n\n';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
								alert(msg);
								return false;
							}
							$("button[type='submit'],input[type='submit']", _CF_this).html("Please Wait...").attr('disabled', 'disabled');
							return true;
						}
						function disableNonAttorneyRates(x){
							var nonMembershipRadios = $('.nonAttorney');
							
							if ( x.value != '' ){
								nonMembershipRadios.attr('disabled',true);
							} else {
								nonMembershipRadios.attr('disabled',false);
							}
							
						}						

						$(document).ready(function(){	
						
							$("##forumEmergingLawyersSub").hide();
							
							$("input[name=membership]:radio").change(function(){
								if(!$('.radioAttorney').is(':checked')) {
									$('##forumEmergingLawyersInd').attr('checked', false); 
									$("##forumEmergingLawyersSub").hide();
								} else {
									if($.trim($("##bardate").val()).length > 0){
										var start = moment("#dateFormat(now(),"mm/dd/yyyy")#");
										var end = moment($("##bardate").val());
										var dayDiff = start.diff(end, "days");		
										if(dayDiff < #local.forumQualifyNumOfDays#)						
											$("##forumEmergingLawyersSub").show();
									}									
								}
							});
							
							$("##bardate").change(function(){
								$("##forumEmergingLawyersSub").hide();
								if(!$('.radioAttorney').is(':checked')) {
									$('##forumEmergingLawyersInd').attr('checked', false); 
									$("##forumEmergingLawyersSub").hide();
								} else {
									if($.trim($("##bardate").val()).length > 0){
										var start = moment("#dateFormat(now(),"mm/dd/yyyy")#");
										var end = moment($("##bardate").val());
										var dayDiff = start.diff(end, "days");		
										if(dayDiff < #local.forumQualifyNumOfDays#)						
											$("##forumEmergingLawyersSub").show();
									}									
								}
							});					
						});							
					</script>

					<script id="existingLicenseTemplate" type="text/x-handlebars-template">
						<tr>
							<td class="bodytext">{{plname}}</td>
							<td class="bodytext optionsText">{{licensenumber}}</td>
							<td class="bodytext optionsText">{{activedate}}</td>
							<td class="bodytext optionsText">{{statusname}}</td>
						</tr>
					</script>
					<script id="newLicenseTemplate" type="text/x-handlebars-template">
						<tr id="pl{{{plid}}}" class="newLicenseRow">
							<td class="bodytext">{{plname}}</td>
							<td class="bodytext optionsText">
								<input id="pl{{{plid}}}_licensenumber" name="pl{{{plid}}}_licensenumber" type="text" class="input-small bodytext barnumber" autocomplete="off" size="16" />
							</td>
							<td class="bodytext optionsText">
								<input id="pl{{{plid}}}_licensedate" name="pl{{{plid}}}_licensedate" type="text" class="input-small bodytext bardate" autocomplete="off" size="10" />
								<input type="button" class="tsAppBodyButton" name="btnClearpl{{{plid}}}_licensedate"  id="btnClearpl{{{plid}}}_licensedate" value="clear" />
							</td>
							<td class="bodytext optionsText">
								<select id="pl{{{plid}}}_licensestatus" name="pl{{{plid}}}_licensestatus" type="text" class="input-medium bodytext barstatus" autocomplete="off">
									<option value="">Select Status</option>
									<option value="Active">Active</option>
									<option value="Inactive">Inactive</option>
								</select>
							</td>
						</tr>
					</script>
					<div class="form" >
						<span class="bodytext formIntro"><em>*<small>Denotes required field</small></em></span>
						<br /><br />
						<cfform name="frmFB35" id="frmFB35" method="POST" action="/?#cgi.QUERY_STRING#" onsubmit="return checkCaptchaAndValidate();">
							<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="1" />
							<cfinput type="hidden" name="memberID"  id="memberID" value="#session.cfcUser.memberData.memberID#">
							<cfinput type="hidden" name="memberNumber" id="memberNumber" value="#session.cfcUser.memberData.memberNumber#" size="30" />
							<input type="hidden" name="bardate"  id="bardate" value=""/>
							<input type="hidden" name="hasActiveLicense"  id="hasActiveLicense" value=""/>
							<input type="hidden" name="allInactiveLicense"  id="allInactiveLicense" value=""/>
							<input type="hidden" name="membershipAmount"  id="membershipAmount" value=""/>
							<cfinput type="hidden" name="isNewRecord" id="isNewRecord" value="">
							<input type="checkbox" name="iAgree" id="iAgree" value="" style="height: 0;width: 0;position: absolute;top: 0;z-index: -1;">
						<cfoutput>
						<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
							<div class="CPSection" id="joinFormAccountLookup">
								<div class="CPSectionTitle BB">#local.strPageFields.AccountLookUpTitle#</div>
								<div class="frmRow1" style="padding:10px;">
									<div id="associatedMemberIDSelect" style="margin-right: 5px;margin-bottom:10px;">
										<button name="btnAddAssoc" type="button" onClick="selectMember()">#local.strPageFields.AccountLookUpButton#</button>
									</div>
									#local.strPageFields.AccountLookUpContent#
								</div>
							</div>
						</cfif>
						</cfoutput>
						
							<div id="memberInfo" name="studentInfo" style="display:none;">			
	
								<div class="page">					
									<div class="section">
										<div class="tsAppLegendTitle sectionTitle">Member Information</div>
										<table cellspacing="0" cellpadding="2" border="0">
											<tr valign="top">
												<td class="bodytext questionNumber"></td>
												<td class="bodytext questionText" align="right">Full Name:</td>
												<td class="bodytext optionsInline">
													<div id="divName" name="divName"></div>
													<cfinput value="" name="name"  id="name" type="hidden" />
												</td>
											</tr>
											<tr valign="top">
												<td class="bodytext questionNumber"></td>
												<td class="bodytext questionText" align="right">Firm/Business Name:</td>
												<td class="bodytext optionsInline">
													<div id="divFirm" name="divFirm"></div>
													<cfinput value="" name="firm"  id="firm" type="hidden" />
												</td>
											</tr>
											<tr valign="top">
												<td class="bodytext questionNumber"></td>
												<td class="bodytext questionText" align="right">Firm/Business Address:</td>
												<td class="bodytext optionsInline">
													<div id="divAddress" name="divAddress"></div>
													<cfinput value="" name="address"  id="address" type="hidden" />
												</td>
											</tr>
											<tr valign="top">
												<td class="bodytext questionNumber"></td>
												<td class="bodytext questionText" align="right">City:</td>
												<td class="bodytext optionsInline">
													<div id="divCity" name="divCity"></div>
													<cfinput value="" name="city"  id="city" type="hidden" />
												</td>
											</tr>
											<tr valign="top">
												<td class="bodytext questionNumber"></td>
												<td class="bodytext questionText" align="right">State:</td>
												<td class="bodytext optionsInline">
													<div id="divState" name="divState"></div>
													<cfinput value="" name="state"  id="state" type="hidden" />
												</td>
											</tr>
											<tr valign="top">
												<td class="bodytext questionNumber"></td>
												<td class="bodytext questionText" align="right">Zip:</td>
												<td class="bodytext optionsInline">
													<div id="divZip" name="divZip"></div>
													<cfinput value="" name="zip"  id="zip" type="hidden" />
												</td>
											</tr>
											<tr valign="top">
												<td class="bodytext questionNumber"></td>
												<td class="bodytext questionText" align="right">Phone:</td>
												<td class="bodytext optionsInline">
													<div id="divPhone" name="divPhone"></div>
													<cfinput value="" name="phone"  id="phone" type="hidden" />
												</td>
											</tr>
											<tr valign="top">
												<td class="bodytext questionNumber"></td>
												<td class="bodytext questionText" align="right">Email:</td>
												<td class="bodytext optionsInline">
													<div id="divEmail" name="divEmail"></div>
													<cfinput value="" name="email"  id="email" type="hidden" />
												</td>
											</tr>
										</table>
									</div>
									<br />

									<div class="section">
										<div class="tsAppLegendTitle sectionTitle">Licensure Information</div>
										<p class="bodytext">
											Please choose the jurisdictions in which you are licensed, if any, and then enter their respective bar number(s), licensure date(s), and current status.
										</p>
										<strong>In which U.S. juridictions are you licensed:</strong>
										<select id="professionalLicenseDropdown" name="professionalLicenseDropdown" multiple="multiple">
											<cfloop query="local.qryProfessionalLicenseTypes">
												<option value="#local.qryProfessionalLicenseTypes.pltypeid#">#local.qryProfessionalLicenseTypes.PLName#</option>
											</cfloop>
										</select>
										<br />
										<table style="width:600px;" id="newLicensesTable">
											<tr>
												<th class="bodytext text-left">Jursidiction</th>
												<th class="bodytext text-left">Bar Number</th>
												<th class="bodytext text-left">Date Admitted</th>
												<th class="bodytext text-left">License Status</th>
											</tr>
											<tbody id="newLicenses">
												<!--- Populated by HandleBars Template --->
											</tbody>
										</table>

										<div id="existingLicenseSection" style="display:none;">
											<br />
											<p class="bodytext">
												The following licensure information is already associated with your account. If any are incorrect or need to have their status changed, please contact the SDCBA Member Services Department at <a href="mailto:<EMAIL>"><EMAIL></a> or 619.231.0781 ext. 3505.
											</p>
											<table style="width:600px;">
												<tr>
													<th class="bodytext text-left">Jursidiction</th>
													<th class="bodytext text-left">Bar Number</th>
													<th class="bodytext text-left">Date Admitted</th>
													<th class="bodytext text-left">License Status</th>
												</tr>
												<tbody id="existingLicenses">
													<!--- Populated by HandleBars Template --->
												</tbody>
											</table>
										</div>

									</div>
									<br />

									<div class="section">
										<div class="tsAppLegendTitle sectionTitle">SDCBA Online Directory (select your preference)</div>
										<br/>
										<table cellspacing="0" cellpadding="2" border="0">

											<tr>
												<td class="bodytext questionNumber"></td>
												<td colspan="2" class="bodytext optionsVertical">
													<table>
														<tr>
															<td class="bodytext"><cfinput value="Yes" class="bodytext optionsRadio" name="directory"  id="directory" type="radio"></td>
															<td class="bodytext optionsText">Yes, please include my info.</td>
														</tr>
														<tr>
															<td class="bodytext"><cfinput value="No" class="bodytext optionsRadio" name="directory"  id="directory" type="radio"></td>
															<td class="bodytext optionsText">No, please exclude my info.</td>
														</tr>
													</table>
												</td>
											</tr>
											<tr valign="top">
												<td class="bodytext questionNumber"></td><td class="bodytext questionText" colspan="2">If yes, please email a photo to <a href="mailto:<EMAIL>"><EMAIL></a>.  Please include your name and Jurisdiction where licensed and license number In the email.</td>
											</tr>
										</table>
									</div>
									<br />
									<div class="section">
										<div class="tsAppLegendTitle sectionTitle">Demographic Information</div>
										<br/>
										<table cellspacing="0" cellpadding="2" border="0">
										
											<tr valign="top">
												<td class="bodytext questionText" align="right">Law School:</td>
												<td class="bodytext optionsInline" colspan="3">
													<cfselect class="bodytext largeBox" name="lawschool" id="lawschool" >
														<option value = '0'>--Select a Law School</option>
														<cfloop query="local.qryLawSchools">
															<option value="#local.qryLawSchools.valueID#">#local.qryLawSchools.columnValueString#</option>
														</cfloop>
													</cfselect>
												</td>
											</tr>
											<tr valign="top">
												<td class="bodytext questionText" align="right">Graduation Date:</td>
												<td class="bodytext optionsInline" colspan="3">
													<cfinput class="bodytext" type="text" name="graddate"  id="graddate" value="" autocomplete="off" size="16" onchange="showYears()">
													<cfinput type="button" class="tsAppBodyButton" name="btnCleargraddate"  id="btnCleargraddate" value="clear">
													<cfsavecontent variable="local.datejs">
														<script language="javascript">
															$(function() { 
																mca_setupDatePickerField('graddate');
																$("##btnCleargraddate").click( function(e) { mca_clearDateRangeField('graddate');return false; } );
																mca_setupDatePickerField('otherBarDate');
																$("##btnClearOtherBarDate").click( function(e) { mca_clearDateRangeField('otherBarDate');return false;} );	
															});
														</script>
														<style type="text/css">
														##graddate, ##otherBarDate { 
															background-image:url("/assets/common/images/calendar/monthView.gif"); 
															background-position:right center; background-repeat:no-repeat; }
														</style>
													</cfsavecontent>
													<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">	
												</td>
											</tr>
										</table>
									</div>	
									<br />				
									<div class="section">
										<div class="tsAppLegendTitle sectionTitle">Firm Profile *</div>
										<table cellspacing="0" cellpadding="2" border="0">
											<tr valign="top">
												<td class="bodytext questionNumber"></td>
												<td class="bodytext optionsInline">
													<table>
														<tr>
															<td class="bodytext"><cfinput class="bodytext optionsCheckbox" name="firmtype"  id="firmtype" type="radio" value="Solo attorney" required="yes" message="Please select your Firm Profile." /></td>
															<td class="bodytext optionsText">Solo</td>
														</tr>
														<tr>
															<td class="bodytext"><cfinput class="bodytext optionsCheckbox" name="firmtype"  id="firmtype" type="radio" value="2-4 attorneys" /></td>
															<td class="bodytext optionsText">Firm of 2-4 Attorneys</td>
														</tr>
														<tr>
															<td class="bodytext"><cfinput class="bodytext optionsCheckbox" name="firmtype"  id="firmtype" type="radio" value="5-9 attorneys" /></td>
															<td class="bodytext optionsText">Firm of 5-9 Attorneys</td>
														</tr>
														<tr>
															<td class="bodytext"><cfinput class="bodytext optionsCheckbox" name="firmtype"  id="firmtype" type="radio" value="10-14 attorneys" /></td>
															<td class="bodytext optionsText">Firm of 10-14 attorneys</td>
														</tr>
														<tr>
															<td class="bodytext"><cfinput class="bodytext optionsCheckbox" name="firmtype"  id="firmtype" type="radio" value="15-19 attorneys" /></td>
															<td class="bodytext optionsText">Firm of 15-19 attorneys</td>
														</tr>
														<tr>
															<td class="bodytext"><cfinput class="bodytext optionsCheckbox" name="firmtype"  id="firmtype" type="radio" value="20-24 attorneys" /></td>
															<td class="bodytext optionsText">Firm of 20-24 attorneys</td>
														</tr>
														<tr>
															<td class="bodytext"><cfinput class="bodytext optionsCheckbox" name="firmtype"  id="firmtype" type="radio" value="25+ attorneys" /></td>
															<td class="bodytext optionsText">Firm of 25+ attorneys</td>
														</tr>
														<tr>
															<td class="bodytext"><cfinput class="bodytext optionsCheckbox" name="firmtype"  id="firmtype" type="radio" value="Public/Gov't" /></td>
															<td class="bodytext optionsText">Public/Government</td>
														</tr>
														<tr>
															<td class="bodytext"><cfinput class="bodytext optionsCheckbox" name="firmtype"  id="firmtype" type="radio" value="Corporate/In-House" /></td>
															<td class="bodytext optionsText">Corporate/In-House Counsel</td>
														</tr>
														<tr>
															<td class="bodytext"><cfinput class="bodytext optionsCheckbox" name="firmtype"  id="firmtype" type="radio" value="Nonprofit" /></td>
															<td class="bodytext optionsText">Non Profit</td>
														</tr>
														<tr>
															<td class="bodytext"><cfinput class="bodytext optionsCheckbox" name="firmtype"  id="firmtype" type="radio" value="Federal Court" /></td>
															<td class="bodytext optionsText">Federal Court</td>
														</tr>
														<tr>
															<td class="bodytext"><cfinput class="bodytext optionsCheckbox" name="firmtype"  id="firmtype" type="radio" value="State Court" /></td>
															<td class="bodytext optionsText">State Court</td>
														</tr>
														<tr>
															<td class="bodytext"><cfinput class="bodytext optionsCheckbox" name="firmtype"  id="firmtype" type="radio" value="Vendor Company" /></td>
															<td class="bodytext optionsText">Vendor Company</td>
														</tr>
														<tr>
															<td class="bodytext"><cfinput class="bodytext optionsCheckbox" name="firmtype"  id="firmtype" type="radio" value="Other" /></td>
															<td class="bodytext optionsText">Other (please explain)<cfinput type="text" name="firmtype_txt"  id="firmtype_txt" value="" /></td>
														</tr>
													</table>
												</td>
											</tr>
										</table>
									</div>	
									<br /><br />				
									<div class="section">
										<div class="tsAppLegendTitle sectionTitle">Membership Dues Schedule *</div>
										<div class="bodytext sectionDesc"></div>
										<table cellspacing="0" cellpadding="2">
										<tr valign="top">
										<tr valign="top">
											#local.strPageFields.MembershipDuesStatement#
										</tr>
										<tr>
											<td colspan="2" class="bodytext optionsInline">
												<br/>
												<div class="bodytext"><b>Attorney Members</b><br/></div>
												<div class="bodytext" style="margin-left:20px;">
													<cfinput class="bodytext optionsRadio radioAttorney" name="membership"  id="membership" type="radio" value="1" onclick="getTotalDue();" disabled required="yes" message="Please select a Membership Dues level." /> Attorney in practice 0 through 1 year<br />
													<cfinput class="bodytext optionsRadio radioAttorney" name="membership"  id="membership" type="radio" value="2" onclick="getTotalDue();" disabled> Attorney in practice 2 through 3 years<br />
													<cfinput class="bodytext optionsRadio radioAttorney" name="membership"  id="membership" type="radio" value="3" onclick="getTotalDue();" disabled> Attorney in practice 4 through 6 years<br />
													<cfinput class="bodytext optionsRadio" name="membership"  id="membership" type="radio" value="4" onclick="getTotalDue();" disabled> Attorney in practice 7 through 12 years<br />
													<cfinput class="bodytext optionsRadio" name="membership"  id="membership" type="radio" value="5" onclick="getTotalDue();" disabled> Attorney in practice 13 or more years<br />
													<cfinput class="bodytext optionsRadio" name="membership"  id="membership" type="radio" value="6" onclick="getTotalDue();"> Law School Faculty (Active status license from any US jurisdiction)<br />
												</div>
												<br />
												<div id="forumEmergingLawyersSub" class="bodytext" style="margin-left:20px;">
													<cfinput type="checkbox" name="forumEmergingLawyersInd" id="forumEmergingLawyersInd" value="#local.forumEmergingLawyersUID#">
													I wish to join the <b>Forum for Emerging Lawyers</b> (Attorneys in their first four years of practice--no fee for this division)
												</div>												
												<br/>
												<div class="bodytext"><b>Legal Community Members</b> (Nonvoting members)<br/></div>
												<div class="bodytext" style="margin-left:20px;">
												<cfinput class="bodytext" name="membership_licensed"  id="membership_licensed" type="checkbox" value="Yes">Please check here if you have ever been licensed to practice law in any US jurisdiction.<br /><br />
												</div>
												<p>Select one of the options below.</p>
												<div class="bodytext" style="margin-left:20px;">
													<cfinput class="bodytext optionsRadio nonAttorney" name="membership"  id="membership" type="radio" value="7" onclick="getTotalDue();">#ReReplace(ReReplace(local.strPageFields.Graduate0to2,'<p>',''),'</p>','')#<br />
													<cfinput class="bodytext optionsRadio nonAttorney" name="membership"  id="membership" type="radio" value="8" onclick="getTotalDue();">#ReReplace(ReReplace(local.strPageFields.Graduate2Plus,'<p>',''),'</p>','')#<br />
													<cfinput class="bodytext optionsRadio nonAttorney" name="membership"  id="membership" type="radio" value="9" onclick="getTotalDue();">#ReReplace(ReReplace(local.strPageFields.ExecutiveDirectorsAndStaff,'<p>',''),'</p>','')#<br />
													<cfinput class="bodytext optionsRadio nonAttorney" name="membership"  id="membership" type="radio" value="10" onclick="getTotalDue();">#ReReplace(ReReplace(local.strPageFields.AssociateMember,'<p>',''),'</p>','')#<br />
													<cfinput class="bodytext optionsRadio nonAttorney" name="membership"  id="membership" type="radio" value="11" onclick="getTotalDue();">#ReReplace(ReReplace(local.strPageFields.FacultyNonLicensed,'<p>',''),'</p>','')#<br />
													<cfinput class="bodytext optionsRadio nonAttorney" name="membership"  id="membership" type="radio" value="12" onclick="getTotalDue();">#ReReplace(ReReplace(local.strPageFields.Vendors,'<p>',''),'</p>','')#<br />
													<div id="vendorDescription" style="display:none;">
														#local.vendorDescriptionFieldSet.fieldSetContent#
													</div>
												</div>
											</td>
										</tr>
										</table>
									</div>
									<br/><br/>
									<div id="mclesubsCheckbox">
										<br />
										<div class="section">
											<div class="tsAppLegendTitle sectionTitle">SDCBA CLE All-Access Pass</div>
											<table cellspacing="0" cellpadding="2" border="0" width="600">
												<tr valign="top">
													<td class="bodytext questionText" colspan="3" id="sectionsCheckboxText">#local.strPageFields.MCLEStatement#</td>
												</tr>
												
												<tr>
													<td class="bodytext questionNumber" width="10"></td>
													<td colspan="2" class="bodytext optionsVertical">
														<table width="100%" cellspacing="0" cellpadding="0" border="0">
															<tr>
																<td class="bodytext optionsText" width="50%" valign="top">
																	<input class="bodytext optionsCheckbox" name="mcleSubs" type="checkbox" onClick="getTotalDue()" value="#local.strPageFields.MCLESubscriptionUID#">Add the CLE All-Access Pass
																</td>
																<td class="bodytext optionsText" width="50%" valign="top">
																</td>
															</tr>
														</table>
													</td>
												</tr>
											</table>
										</div>
									</div>
									<br/><br/>
									<div id="sectionsCheckbox">
										<br />
										<div class="section">
											<div class="tsAppLegendTitle sectionTitle">SDCBA Sections</div>
											<table cellspacing="0" cellpadding="2" border="0" width="600">
												<tr valign="top">
													<td class="bodytext questionText" colspan="3" id="sectionsCheckboxText">Sections are formed by the SDCBA for the presentation, discussion and study of subjects by an interested group of members.  Now with your basic membership, dues for section membership is free. </td>
												</tr>
												
												<tr>
													<td class="bodytext questionNumber" width="10"></td>
													<td colspan="2" class="bodytext optionsVertical">
														<table width="100%" cellspacing="0" cellpadding="0" border="0">
															<tr>
																<td class="bodytext optionsText" width="50%" valign="top">
																	<cfloop query="local.sections" startRow="1" endRow="#local.section.column1#">
																		<input class="bodytext optionsCheckbox" name="sections" type="checkbox" onClick="getTotalDue()" id="#local.sections.subscriptionName#" value="#local.sections.uid#">#local.sections.subscriptionName#<cfif local.sections.subscriptionName eq "Carmel Valley" OR local.sections.subscriptionName eq "Financial Professional Liaison" OR local.sections.subscriptionName eq "Law Practice Management & Marketing">&nbsp;(Free)</cfif><br />
																	</cfloop>
																</td>
																<td class="bodytext optionsText" width="50%" valign="top">
																	<cfloop query="local.sections" startRow="#local.section.column1 + 1#" endRow="#local.section.recordCount#">
																		<input class="bodytext optionsCheckbox" name="sections" type="checkbox" onClick="getTotalDue()" id="#local.sections.subscriptionName#" value="#local.sections.uid#">#local.sections.subscriptionName#<cfif local.sections.subscriptionName eq "Carmel Valley" OR local.sections.subscriptionName eq "Financial Professional Liaison" OR local.sections.subscriptionName eq "Law Practice Management & Marketing">&nbsp;(Free)</cfif><br />
																	</cfloop>
																</td>
															</tr>
														</table>
													</td>
												</tr>
											</table>
										</div>
									</div>
									<div id="sectionsRadio" style="display:none;">
										<br />
										<div class="section">
											<div class="tsAppLegendTitle sectionTitle">SDCBA Sections</div>
											<table cellspacing="0" cellpadding="2" border="0" width="600">
												<tr valign="top">
													<td class="bodytext questionText" colspan="3">Affiliate Section Members pay $65 and can only select 1 section.</td>
												</tr>
												<tr>
													<td class="bodytext questionNumber" width="10"></td>
													<td colspan="2" class="bodytext optionsVertical">
														<table width="100%" cellspacing="0" cellpadding="0" border="0">
															<tr>
																<td class="bodytext optionsText" width="50%" valign="top">
																	<cfloop query="local.sections" startRow="1" endRow="#local.section.column1#">
																		<input class="bodytext optionsRadio" name="sections" type="radio" onClick="getTotalDue()" id="#local.sections.subscriptionName#" value="#local.sections.uid#">#local.sections.subscriptionName#<cfif local.sections.subscriptionName eq "Carmel Valley" OR local.sections.subscriptionName eq "Financial Professional Liaison" OR local.sections.subscriptionName eq "Law Practice Management & Marketing">&nbsp;(Free)</cfif><br />
																	</cfloop>
																</td>
																<td class="bodytext optionsText" width="50%" valign="top">
																	<cfloop query="local.sections" startRow="#local.section.column1 + 1#" endRow="#local.section.recordCount#">
																		<input class="bodytext optionsRadio" name="sections" type="radio" onClick="getTotalDue()" id="#local.sections.subscriptionName#" value="#local.sections.uid#">#local.sections.subscriptionName#<cfif local.sections.subscriptionName eq "Carmel Valley" OR local.sections.subscriptionName eq "Financial Professional Liaison" OR local.sections.subscriptionName eq "Law Practice Management & Marketing">&nbsp;(Free)</cfif><br />
																	</cfloop>
																</td>
															</tr>
														</table>
													</td>
												</tr>
											</table>
										</div>
									</div>
									<br />
									<div class="section">
										<div class="tsAppLegendTitle sectionTitle">Foundation Donation</div>
										<table cellspacing="0" cellpadding="2">
											<tr valign="top">
												<td class="bodytext questionNumber" valign="top"></td>
												<td class="bodytext questionText" colspan="2" valign="top">
													#local.strPageFields.FoundationDonationStatement#
													<br /><br />
													Please select the donation level you'd like to contribute:
													<br/>

													<div class="bodytext" style="margin-left:20px;">
														<cfloop from="1" to="#arrayLen(local.arrDonationAmts)#" index="local.row">
															<input class="bodytext optionsRadio foundationDonation" name="donation"  id="donationStandard#local.row#" type="radio" value="#local.arrDonationAmts[local.row].amount#" onclick="getTotalDue();" <cfif local.arrDonationAmts[local.row].checked>checked="checked"</cfif>> #local.arrDonationAmts[local.row].text#<br />
														</cfloop>

														<input class="bodytext optionsRadio foundationDonation" name="donation"  id="donationOther" type="radio" value="donationOther" onclick="getTotalDue();">  I'd like to donate another amount. Specify Amount: $<input type="text" class="input-mini" name="donationOtherAmount" id="donationOtherAmount" value="" onKeyUp="getTotalDue();" onChange="getTotalDue();" onfocus="$('##donationOther')[0].click();"/><br />
														<input class="bodytext optionsRadio foundationDonation" name="donation"  id="donationDecline" type="radio" value="donationDecline" onclick="getTotalDue();">  I do not wish to donate at this time.<br />
													</div>
												</td>
											</tr>
										</table>
									</div>
									<br />
	
									<div class="section">
										<div class="tsAppLegendTitle sectionTitle">Payment Information</div>
										<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
											<tr>
												<td class="bodytext">
													<table border="0">
														<tr>
															<td class="bodytext" width="125">Membership Dues:</td>
															<td class="bodytext" width="75" align="right">$<span id="memberShipTotal"></span></td>
														</tr>
														<tr>
															<td class="bodytext">Sections:</td>
															<td class="bodytext" align="right">$<span id="sectionTotal"></span></td>
														</tr>
														<tr>
															<td class="bodytext">Donation:</td>
															<td class="bodytext" align="right">$<span id="donationTotal"></span></td>
														</tr>
														<tr>
															<td class="bodytext">SDCBA CLE All-Access Pass:</td>
															<td class="bodytext" align="right">$<span id="mcleTotal"></span></td>
														</tr>
														<tr style="border-top:1px solid ##ccc;">
															<td class="bodytext"><strong>Total Amount Due:</strong></td>
															<td class="bodytext" align="right"><strong>$<span id="totalDue"></span></strong></td>
														</tr>
													</table>
												</td>
											</tr>
										</table>
									</div>
									
									<br />
									<div class="bodytext formClose">SDCBA is a tax-exempt 501(c)(6) organization.  Dues are not tax deductible as a charitable contribution for federal income tax purposes but may be tax deductible under other provisions of the Internal Revenue Service Code.  The SDCBA estimates that up to 1.0% of your membership dues may support trial court funding lobbying efforts and therefore is not deductible.   For more information, please consult your tax advisor.</div>
									<br />
									<div>
										<input type="checkbox" name="membershipCertification" id="membershipCertification" value="1">#local.strPageFields.CertificationStatement#
									</div>

									<div class="section">
										<div class="frmText" style="padding:10px;">
											#local.captchaDetails.htmlContent#
										</div>
									</div>
									<div>
										<input type="hidden" name="FBFormID" value="35">
										<input type="hidden" name="FBAction" value="postForm">
										<input type="Submit" name="btnSubmit" class="bodytext formButton" value="Submit">
									</div>
								</div>
							</div>
							<div id="memberExisting" name="memberExisting" style="display:none;">
								<div class="bodytext formClose">
									You currently have a membership subscription.  If you believe that you should be able to sign up for a Membership, please contact SDCBA.
								</div>
							</div>
						</cfform>

					</div>
					<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<script>loadMember('#session.cfcUser.memberData.memberNumber#');</script>
					</cfif>
					<script>getTotalDue();</script>
				</cfcase>

				<cfcase value="1">
					
					<cfif structKeyExists(event.getCollection(), 'iAgree') OR (
						(NOT structKeyExists(session, "captchaEntered")) AND ((NOT(structKeyExists(event.getCollection(),'captcha') AND len(event.getValue('captcha') ))) 
							OR application.objCustomPageUtils.validateCaptcha(code=event.getValue('captcha'),captcha=event.getValue('captcha_check')).response NEQ "success"))>
						<cflocation url="#local.customPage.baseURL#&isSubmitted=100" addtoken="no">
					</cfif>
					
					<!--- Build the subscriptions, status Billed and redirect to Front end subscription payment	--->
					<cfset local.showErrorMsg = false>
					<cfset local.membership = event.getValue('membership')>

					<!--- save member record --->
					<cftry>
						
						<!--- Setting Captcha submitted flag --->
						<cfset session.captchaEntered = 1>

						<!--- set local.memberID = 0 for already existing user selected via account locator for others use their memberid --->
						<cfif NOT val(event.getValue('isNewRecord')) AND NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
							<cfset local.memberID = 0>
						<cfelse>
							<cfset local.memberID = event.getValue('memberID')>
						</cfif>

						<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=event.getValue('memberID'))>

						<cfif ListFind("1,2,3,4,5,6",local.membership)>
							<cfset local.objSaveMember.setCustomField(field='Member_Type', value='MBR')>
						<cfelseif ListFind("7,8,9,10,11,12",local.membership)>
							<cfset local.objSaveMember.setCustomField(field='Member_Type', value='LC')>
						</cfif>
						<cfif ListFind("1,2,3,4,5",local.membership)>
							<cfset local.objSaveMember.setCustomField(field='Member_Category', value='MBR')>
						<cfelseif local.membership eq 6>
							<cfset local.objSaveMember.setCustomField(field='Member_Category', value='FAC')>
						<cfelseif local.membership eq 7>
							<cfset local.objSaveMember.setCustomField(field='Member_Category', value='LC|GLS')>
						<cfelseif local.membership eq 8>
							<cfset local.objSaveMember.setCustomField(field='Member_Category', value='JD')>
						<cfelseif local.membership eq 9>
							<cfset local.objSaveMember.setCustomField(field='Member_Category', value='Exec')>
						<cfelseif local.membership eq 10>
							<cfset local.objSaveMember.setCustomField(field='Member_Category', value='LC|AM')>
						<cfelseif local.membership eq 11>
							<cfset local.objSaveMember.setCustomField(field='Member_Category', value='LC|FAC')>
						<cfelseif local.membership eq 12>
							<cfset local.objSaveMember.setCustomField(field='Member_Category', value='Vendor')>
						</cfif>

						<cfif event.getValue('directory','') eq "Yes">
							<cfset local.objSaveMember.setCustomField(field='Display in Online Directory', value='Display my Listing')>
							<cfset local.objSaveMember.setCustomField(field='Display Photo if Available', value='Display my Photo if Available')>
						<cfelse>
							<cfset local.objSaveMember.setCustomField(field='Display in Online Directory', value='Do Not Display my Listing')>
							<cfset local.objSaveMember.setCustomField(field='Display Photo if Available', value='Do Not Display my Photo')>
						</cfif>

						<cfif event.getValue('lawschool','0') neq "0">
							<cfset local.objSaveMember.setCustomField(field='law_school', valueID=val(event.getValue('lawschool')))>
						</cfif>
						
						<cfif event.getValue('graddate','') neq "">
							<cfset local.objSaveMember.setCustomField(field='law_school_date', value=dateFormat(event.getValue('graddate'),"mm/dd/yyyy"))>
						</cfif>

						<cfif (Len(event.getValue('firmtype','')) gt 0) AND (event.getValue('firmtype','') neq "Other")>
							<cfset local.objSaveMember.setCustomField(field='Firm Profile', value=event.getValue('firmtype'))>
						</cfif>

						<cfset local.objSaveMember.setCustomField(field = 'Membership Certification', value = arguments.event.getValue('membershipCertification',0))>
						<cfset local.objSaveMember.setMemberType(memberType='User')>

						<cfset local.newMemberNumber = "">
						<cfset local.isCaliforniaNumberSet = false>
						<cfif (Len(event.getValue('professionalLicenseDropdown','')) gt 0)>
							<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPLStatuses">
								select plstatusID, statusName
								from dbo.ams_memberProfessionalLicenseStatuses
								where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
								and statusName in ('Active','Inactive')
							</cfquery>
							<cfset local.statuses = structnew()/>
							<cfloop query="local.qryPLstatuses">
								<cfset local.statuses[local.qryPLstatuses.statusName] = local.qryPLstatuses.plstatusID />
							</cfloop>
							<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryProfessionalLicenseTypes">
								select mplt.[PLTypeID], mplt.PLName
								from dbo.ams_memberProfessionalLicenseTypes mplt
								where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
								order by ordernum
							</cfquery>
							<cfset local.licensesNames = ArrayNew(1)>
							<cfloop query="local.qryProfessionalLicenseTypes">
								<cfset local.licensesNames[local.qryProfessionalLicenseTypes.pltypeid] = local.qryProfessionalLicenseTypes.PLName>	
							</cfloop>
							
							<cfloop list="#event.getValue('professionalLicenseDropdown')#" index="local.thisItem">
								<cfset local.thisNumber = event.getValue('pl' & local.thisItem & '_licensenumber','')>
								<cfset local.thisDate = event.getValue('pl' & local.thisItem & '_licensedate','')>
								<cfset local.thisStatus = event.getValue('pl' & local.thisItem & '_licensestatus','')>
								
								<cfset local.objSaveMember.setProLicense(name=local.licensesNames[local.thisItem], status=local.thisStatus, license=local.thisNumber, date=local.thisDate)>
								<cfif local.licensesNames[local.thisItem] EQ 'California' AND len(local.thisNumber)>
									<cfset local.isCaliforniaNumberSet = true>
								</cfif>
							</cfloop>
						</cfif>

						<cfset local.objMemberAdmin = CreateObject("component","model.admin.members.members")>
						<cfif local.isCaliforniaNumberSet>
							
							<cfset local.strCheckMemberNumber = local.objMemberAdmin.checkMemberNumber(mcproxy_orgID=local.orgID, memberID=local.memberID, memberNumber=trim(local.thisNumber))>						
							<cfif local.strCheckMemberNumber.success is 1>
								<cfset local.newMemberNumber = trim(local.thisNumber)>
							<cfelse>
								<cfset local.strCheckMemberNumber = local.objMemberAdmin.checkMemberNumber(mcproxy_orgID=local.orgID, memberID=local.memberID, memberNumber='AT' & trim(local.thisNumber))>							
								<cfif local.strCheckMemberNumber.success is 1>
									<cfset local.newMemberNumber = IIf((Left(trim(local.thisNumber),2) NEQ 'AT'), DE('AT' & trim(local.thisNumber)) , DE(trim(local.thisNumber)))>
								<cfelse>
									<cfset local.newMemberNumber = IIf((Left(event.getValue('memberNumber'),2) NEQ 'AT'), DE('AT' & event.getValue('memberNumber')) , DE(event.getValue('memberNumber')))>
								</cfif>
							</cfif>
						<cfelse>
							<cfif ListFind("1,2,3,4,5,6",local.membership)>
								<cfset local.newMemberNumber = IIf((Left(event.getValue('memberNumber'),2) NEQ 'AT'), DE('AT' & event.getValue('memberNumber')) , DE(event.getValue('memberNumber')))>
							<cfelseif ListFind("7,8,9,10,11,12",local.membership)>
								<cfset local.newMemberNumber = IIf((Left(event.getValue('memberNumber'),2) NEQ 'LC'), DE('LC' & event.getValue('memberNumber')) , DE(event.getValue('memberNumber')))>
							</cfif>
						</cfif>

						<cfset local.strCheckMemberNumber = local.objMemberAdmin.checkMemberNumber(mcproxy_orgID=local.orgID, memberID=local.memberID, memberNumber=trim(local.newMemberNumber))>
						
						<cfif len(local.newMemberNumber) and local.strCheckMemberNumber.success is 1>
							<cfset local.objSaveMember.setDemo(membernumber=local.newMemberNumber)>
						</cfif>
						
						<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
						<cfif NOT local.strResult.success>
							<cfthrow message="Unable to save member record.">
						</cfif>
					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
						<cfset local.showErrorMsg = true>
						<cfset local.errCode = 4>
					</cfcatch>
					</cftry>

					<cfif local.showErrorMsg>
						<cfoutput>
							There was an error processing your application.  Please contact your association for assistance.
						</cfoutput>
					</cfif>

					<cfoutput>
						<script>
							function updateMember(){
								var er_changeErr = function(r) {
									document.getElementById('waitDiv').style.display 	= 'none'; 
									document.getElementById('errDiv').style.display 	= ''; 
								};
									
								var er_change = function(r) {
									var results = r;
									if( results.success ){
										$("##btnSubmit").trigger('click');
									} else { 
										document.getElementById('waitDiv').style.display 	= 'none'; 
										document.getElementById('errDiv').style.display 	= ''; 
									}
								};
		
								var objParams = { memberID:#event.getValue('memberID')# };
								TS_AJX('SUBS','updateGroupsForMember',objParams,er_change,er_changeErr,1000000,er_changeErr);
							}
						</script>
						<div id="waitDiv" name="waitDiv">
						<h4>Updating member record and processing application.</h4>
						<img src="/assets/common/images/loading-dots.gif">
						</div>
						<div id="errDiv" name="errDiv" style="display:none;">
						<h4>There was an error processing your application.  Please contact your association for assistance.</h4>
						</div>
						<cfform name="#local.formName#"  id="#local.formName#" method="POST" action="/?#cgi.QUERY_STRING#" style="visibility:hidden">
							<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
							<cfloop collection="#arguments.event.getCollection()#" item="local.key">
								<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
									and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
									and left(local.key,4) neq "fld_">
									<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
								</cfif>
							</cfloop>
							<button type="submit" class="tsAppBodyButton" name="btnSubmit" id="btnSubmit">Submit</button>						
						</cfform>
						<script>
							updateMember();
						</script>
					</cfoutput>
				</cfcase>

				<cfcase value="2">
					<cfset local.showErrorMsg = false>

					<cfset local.timeStamp 				= now() />
					<cfset local.showErrorMsg = false>
					<cfset local.membership = event.getValue('membership')>
					<cfset local.licenses = application.objMember.getMemberProfLicenses(memberID=event.getValue('memberID'),orgID=local.orgID) />

					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryLawSchoolName">
						select mdcv.columnValueString
						from dbo.ams_memberDataColumnValues mdcv
						inner join dbo.ams_memberDataColumns mdc 
							on mdc.columnID = mdcv.columnID
							and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
							and columnName = 'law_school'
							and mdcv.valueID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('lawschool','0')#">
						order by mdcv.columnValueString
					</cfquery>

					<cfset local.vendorDescriptionFieldSetConfirmation = application.objCustomPageUtils.renderFieldSet(uid='996A83D8-D9D4-441B-B703-4822E02467FC', mode="confirmation", strData=event.getCollection())>
					<cfsavecontent variable="local.invoice">
						#local.pageCSS#
						<!-- @msg@ -->
						<p>#local.emailSubject# submitted on #dateformat(local.timeStamp,"dddd, m/d/yyyy")# #timeformat(local.timeStamp,"h:mm tt")#.</p>
						<table cellpadding="2" cellspacing=  "0" width="100%" border="1" class="customPage">					
						<tr class="msgHeader"><td colspan="2" class="b">Membership Information</td></tr>
						<tr>
							<td colspan="2">
								<table width="100%">
									<tr>
										<td width="48%" valign="top">
											<table  cellpadding="2" cellspacing="0" width="100%" border="1">
												<tr><td class="frmText bld">Name: </td><td class="frmText">#event.getValue('name','')#&nbsp;</td></tr>
												<tr><td class="frmText bld">Please check here if you have ever been licensed to practice law in any US jurisdiction: </td><td class="frmText">#event.getValue('membership_licensed','')#&nbsp;</td></tr>
												<tr>
													<td class="frmText bld">Membership: </td>
													<td class="frmText">
														<cfswitch expression="#local.membership#">
															<cfcase value="1">
																Attorney in practice 0 through 1 year
															</cfcase>
															<cfcase value="2">
																Attorney in practice 2 through 3 years
															</cfcase>
															<cfcase value="3">
																Attorney in practice 4 through 6 years
															</cfcase>
															<cfcase value="4">
																Attorney in practice 7 through 12 years
															</cfcase>
															<cfcase value="5">
																Attorney in practice 13 or more years
															</cfcase>
															<cfcase value="6">
																Law School Faculty (Active status license from any US jurisdiction)
															</cfcase>
															<cfcase value="7">
																#local.strPageFields.Graduate0to2#
															</cfcase>
															<cfcase value="8">
																#local.strPageFields.Graduate2Plus#
															</cfcase>
															<cfcase value="9">
																#local.strPageFields.ExecutiveDirectorsAndStaff#
															</cfcase>
															<cfcase value="10">
																#local.strPageFields.AssociateMember#
															</cfcase>
															<cfcase value="11">
																#local.strPageFields.FacultyNonLicensed#
															</cfcase>
															<cfcase value="12">
																#local.strPageFields.Vendors#
															</cfcase>
														</cfswitch>
													</td>
												</tr>
												
												<tr><td class="frmText bld">Email: </td><td class="frmText">#event.getValue('email','')#&nbsp;</td></tr>
												<tr><td class="frmText bld">Law School: </td><td class="frmText">#local.qryLawSchoolName.columnValueString#&nbsp;</td></tr>
												<tr><td class="frmText bld">Graduation Date: </td><td class="frmText">#event.getValue('graddate','')#&nbsp;</td></tr>

											</table>
										</td>
										<td width="2%"></td>
										<td width="48%" valign="top">
											<table  cellpadding="2" cellspacing="0" width="100%" border="1">
												<tr><td class="frmText bld">Address: </td><td class="frmText">#event.getValue('address','')#&nbsp;</td></tr>
												<tr><td class="frmText bld">City: </td><td class="frmText">#event.getValue('city','')#&nbsp;</td></tr>
												<tr><td class="frmText bld">State: </td><td class="frmText">#event.getValue('state','')#&nbsp;</td></tr>
												<tr><td class="frmText bld">Zip Code: </td><td class="frmText">#event.getValue('zip','')#&nbsp;</td></tr>
												<tr><td class="frmText bld">Phone: </td><td class="frmText">#event.getValue('phone','')#&nbsp;</td></tr>
											</table>
										</td>
									</tr>
								</table>
							</td>
						</tr>					
						<tr>
							<td colspan="2">
								<table width="100%">
									<tr>
										<td width="48%" valign="top">
											<cfif local.licenses.recordcount>
												<table  cellpadding="2" cellspacing="0" width="100%" border="1">
													<tr>
														<td class="frmText bld">Jurisdiction</td>
														<td class="frmText bld">#local.strProfLicenseLabels.profLicenseNumberLabel#</td>
														<td class="frmText bld">#local.strProfLicenseLabels.profLicenseDateLabel#</td>
														<td class="frmText bld">#local.strProfLicenseLabels.profLicenseStatusLabel#</td>
													</tr>
													<cfloop query="local.licenses">
														<tr>
															<td class="frmText">#local.licenses.PLName#</td>
															<td class="frmText">#local.licenses.LicenseNumber#</td>
															<td class="frmText">#local.licenses.ActiveDate#</td>
															<td class="frmText">#local.licenses.statusName#</td>
														</tr>
													</cfloop>
												</table>
											</cfif>
										</td>
										<td width="2%"></td>
										<td width="48%" valign="top">
											<table  cellpadding="2" cellspacing="0" width="100%" border="1">
												<tr><td class="frmText bld">Firm/Business Name: </td><td class="frmText">#event.getValue('firm','')#&nbsp;</td></tr>
												<tr><td class="frmText bld">Firm Profile: </td><td class="frmText">#event.getValue('firmtype','')#&nbsp;</td></tr>
												<cfif (event.getValue('firmtype','') eq 'Other') AND (len(event.getValue('firmtype_txt','')) gt 0)>
													<tr><td class="frmText bld">Firm Profile Other: </td><td class="frmText">#event.getValue('firmtype_txt','')#&nbsp;</td></tr>
												</cfif>
												<tr><td class="frmText bld">Include in directory: </td><td class="frmText">#event.getValue('directory','')#&nbsp;</td></tr>
												<tr>
													<td class="frmText bld">Donation: </td>
													<td class="frmText">
														<cfif len(event.getValue('donation','')) AND event.getValue('donation','') NEQ 'donationDecline'>
															<cfswitch expression="#event.getValue('donation','')#">
																<cfcase value="donationOther">
																	Other Donation - $#event.getValue('donationOtherAmount',0)#
																</cfcase>
																<cfdefaultcase>
																	$#val(event.getValue('donation',''))# Donation
																</cfdefaultcase>
															</cfswitch>
														<cfelse>
															No
														</cfif>
													</td>
												</tr>

												<tr>
													<td class="frmText bld">SDCBA CLE All-Access Pass: </td>
													<td class="frmText">
														<cfif len(event.getValue('mcleSubs',''))>
															$#local.strPageFields.MCLEFull#
														<cfelse>
															$0.00
														</cfif>
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</td>
						</tr>
						<cfif local.membership EQ 12>
							<tr>
								<td colspan="2">
									<table width="100%">
										<tr id="membershipDescription">
											<td width="100%" valign="top">
												#local.vendorDescriptionFieldSetConfirmation.fieldSetContent#
											</td>
										</tr>
									</table>
								</td>
							</tr>
						</cfif>
						<tr>
							<td colspan="2">
								<table width="100%">
									<tr>
										<td width="48%" valign="top">
											<table  cellpadding="2" cellspacing="0" width="100%" border="1">
												<tr class="msgHeader"><td colspan="2" class="b">SECTIONS</td></tr>
											<cfif listlen(event.getValue('sections','')) gt 0>
												<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySectionNames">
													select subscriptionName
													from dbo.sub_subscriptions
													where uid in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" list="yes" value="#event.getValue('sections')#">)
													order by subscriptionName
												</cfquery>
												<cfloop query="local.qrySectionNames">
													<tr><td class="frmText bld" colspan="2">#local.qrySectionNames.subscriptionName#</td></tr>
												</cfloop>
											<cfelse>
												<tr><td class="frmText bld" colspan="2">No sections selected.</td></tr>
											</cfif>
											</table>
										</td>
										<td width="2%"></td>
										<td width="48%" valign="top">
											<table  cellpadding="2" cellspacing="0" width="100%" border="1">
												<tr class="msgHeader"><td colspan="2" class="b">DIVISIONS</td></tr>
											<cfif listlen(event.getValue('forumEmergingLawyersInd','')) gt 0>
												<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDivisionNames">
													select subs.subscriptionName
													from dbo.sub_subscriptions subs				
													where subs.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.forumEmergingLawyersUID#">				
												</cfquery>
												<cfloop query="local.qryDivisionNames">
													<tr><td class="frmText bld" colspan="2">#local.qryDivisionNames.subscriptionName#</td></tr>
												</cfloop>
											<cfelse>
												<tr><td class="frmText bld" colspan="2">No Divisions selected.</td></tr>
											</cfif>
											</table>
										</td>
									</tr>
									
								</table>
							</td>
						</tr>
						<tr>
							<td colspan="2">
								<table width="100%">
									<tr>
										<td width="100%" valign="top">
											<table  cellpadding="2" cellspacing="0" width="100%" border="1">
												<tr>
													<td class="frmText b" style="width:97%;">#local.strPageFields.CertificationStatement#</td>
													<cfif event.getValue('membershipCertification',0) EQ 1>
														<td class="frmText l">Yes</td>
													<cfelse>
														<td class="frmText l">No</td>
													</cfif>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</td>
						</tr>
						</table>
					</cfsavecontent>
					
					<!--- CREATE SUBSCRIPTION ----------------------------------------------------------------------------- --->
					<cfset local.subStruct = structNew()>
					<cfset local.subStruct.children = arrayNew(1)>
					<cfset local.membership = event.getValue('membership')>
					<cfif (local.membership eq "1") OR (local.membership eq "2") OR (local.membership eq "3") OR
								(local.membership eq "4") OR (local.membership eq "5") or (local.membership eq "6")>
					
						<cfset local.subStruct.uid = local.caAttyMembershipAllUID>
						<cfset local.subStruct.rateOverride = event.getValue('membershipAmount',0)>
					<cfelseif (local.membership eq "7") OR (local.membership eq "8") OR (local.membership eq "9") OR
										(local.membership eq "10") OR (local.membership eq "11") OR (local.membership eq "12")>
										
						<cfset local.subStruct.uid = local.lcMembershipAll>
						<cfset local.subStruct.rateOverride = event.getValue('membershipAmount',0)>
					</cfif>

					<!--- Get Subscription Rate --->
					<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.rootSubRate">
						set nocount on;

						declare @subscriptionID int, @memberID int, @isRenewalRate bit, @FID int;
						set @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.useMID#">;
						set @isRenewalRate = 0;
						set @FID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;

						select @subscriptionID = subscriptionID
						from dbo.sub_subscriptions 
						where uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.subStruct.uid#">;

						select uid
						from dbo.fn_sub_getMostExclusiveRateInfo(@memberID,@subscriptionID,@isRenewalRate,@FID);
					</cfquery>

					<cfset local.subStruct.rateUID = local.rootSubRate.uid>
					<cfloop list="#event.getValue('sections','')#" index="local.thisSectionUID">
						<cfset local.childStruct = structNew()>
						<cfset local.childStruct.uid = local.thisSectionUID>
						<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
					</cfloop>
					
					<cfif len(trim(event.getValue('forumEmergingLawyersInd','')))>
						<cfset local.childStruct = structNew()>
						<cfset local.childStruct.uid = event.getValue('forumEmergingLawyersInd')>
						<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
					</cfif>				

					<cfif len(event.getValue('donation','')) and event.getValue('donation','') NEQ 'donationDecline'>
						<cfset local.childStruct = structNew()>
						<cfset local.childStruct.uid = local.donationSubUID>
						<cfif event.getValue('donation','') EQ 'donationOther'>
							<cfset local.childStruct.rateUID = local.donationSubOtherRateUID />
							<cfset local.childStruct.rateOverride = event.getValue('donationOtherAmount',0) />
						<cfelse>
							<cfset local.childStruct.rateUID = local.donationSubStandardRateUID />
							<cfset local.childStruct.rateOverride = val(event.getValue('donation','')) />
						</cfif>
						<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
					</cfif>

					<cfif len(trim(event.getValue('mcleSubs','')))>
						<cfset local.qryRates = application.objCustomPageUtils.sub_getEligibleRates(siteID=local.siteID, memberID=local.useMID, subscriptionUID=local.strPageFields.MCLESubscriptionUID, isRenewal=false)>
						<cfset local.childStruct = structNew()>
						<cfset local.childStruct.uid = event.getValue('mcleSubs')>
						<cfset local.childStruct.rateUID = local.qryRates.rateUID />
						<cfset local.childStruct.rateOverride = #local.strPageFields.MCLEFull# />
						<cfset ArrayAppend(local.subStruct.children, local.childStruct)>
					</cfif>	

					<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>
					<cfset local.subReturn = local.objSubReg.autoSubscribe(event=event, memberID=local.useMID, subStruct=local.subStruct, newAsBilled=true)>
					<cfif local.subReturn.success eq false>
						<!--- email association ----------------------------------------------------------------------------------------- --->

						<cfsavecontent variable="local.mailContent">
							<cfoutput>
								The system was unable to create the subscriptions for this application and #event.getValue('name','')# was not sent an email confirmation.<br />
									<hr />
								#local.invoice#	
							</cfoutput>
						</cfsavecontent>

						<cfscript>
							local.arrEmailTo = [];
							local.toEmailArr = listToArray(replace(local.ORGEmail.to,",",";","all"),';');
							for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
								local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
							}

							if (arrayLen(local.arrEmailTo)) {
								local.responseStruct = application.objEmailWrapper.sendMailESQ(
									emailfrom={ name="", email=local.ORGEmail.from },
									emailto=local.arrEmailTo,
									emailreplyto=local.ORGEmail.from,
									emailsubject=local.ORGEmail.SUBJECT,
									emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - Join",
									emailhtmlcontent=local.mailContent,
									siteID=arguments.event.getTrimValue('mc_siteinfo.siteID'),
									memberID=arguments.event.getTrimValue('mc_siteinfo.sysMemberID'),
									messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
									sendingSiteResourceID=this.siteResourceID
								);
							}
						</cfscript>
						<cfset local.showErrorMsg = true>
						<cfset local.errCode = 3>
					<cfelse>
						
						<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDirectLinkCode">
							select directLinkCode
							from dbo.sub_subscribers
							where subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subReturn.rootSubscriberID#">
						</cfquery>

						<!--- email member ---------------------------------------------------------------------------------------------- --->
						
						<cfset local.emailSentToUser = false />
						<cfif structKeyExists(local,"overrideMemberEmailTo")>
							<cfset local.useremail = local.overrideMemberEmailTo />
						<cfelse>
							<cfset local.useremail = event.getTrimValue('email','') />
						</cfif>
						<cfif len(local.useremail)>
							<cfsavecontent variable="local.mailContent">
								<cfoutput>
									<p>Thank you for submitting your application!</p>
									<p>Below are your initial selections for your membership application. </p>
									<p>If you have NOT already done so, please <a href="#local.scheme#://#local.mainhostName#/renewsub/#local.qryDirectLinkCode.directLinkCode#">click here</a> 
											to pay and finalize your membership. 
									</p>
									
									<p>
									If the link does not work for you, please copy the following link and paste into the address bar of your browser:<br>
									#local.scheme#://#local.mainhostName#/renewsub/#local.qryDirectLinkCode.directLinkCode#
									</p> 
									<p>
									If you have any questions, please contact the SDCBA Member Services Department at 619.231.0781. 
									</p> 
									<hr />
									#local.invoice#	
								</cfoutput>
							</cfsavecontent>

							<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name="", email=local.memberEmail.from },
								emailto=[{ name="", email=local.useremail }],
								emailreplyto=local.ORGEmail.to,
								emailsubject=local.memberEmail.SUBJECT,
								emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
								emailhtmlcontent=local.mailContent,
								siteID=local.siteID,
								memberID=val(local.useMID),
								messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
								sendingSiteResourceID=this.siteResourceID
							)>

							<cfset local.emailSentToUser = local.responseStruct.success>

						</cfif>

						<!--- email association ----------------------------------------------------------------------------------------- --->
						<cfsavecontent variable="local.orgEmailContent">
							<cfif NOT local.emailSentToUser>
								#event.getValue('name','')# was not sent an email confirmation due to bad Data.<br />
								Please contact them and let them know.<br>
								Their confirmation link is: #local.scheme#://#local.mainhostName#/renewsub/#local.qryDirectLinkCode.directLinkCode#
								<hr />
							</cfif>
							#local.invoice#
						</cfsavecontent>

						<cfsavecontent variable="local.mailContent">
							<cfoutput>
								#local.orgEmailContent#
							</cfoutput>
						</cfsavecontent>

						<cfscript>
							local.arrEmailTo = [];
							local.toEmailArr = listToArray(replace(local.ORGEmail.to,",",";","all"),';');
							for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
								local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
							}

							if (arrayLen(local.arrEmailTo)) {
								local.responseStruct = application.objEmailWrapper.sendMailESQ(
									emailfrom={ name="", email=local.ORGEmail.from },
									emailto=local.arrEmailTo,
									emailreplyto=local.ORGEmail.from,
									emailsubject=local.ORGEmail.SUBJECT,
									emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - Join",
									emailhtmlcontent=local.mailContent,
									siteID=arguments.event.getTrimValue('mc_siteinfo.siteID'),
									memberID=arguments.event.getTrimValue('mc_siteinfo.sysMemberID'),
									messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
									sendingSiteResourceID=this.siteResourceID
								);
							}
						</cfscript>
						<!---begin: create pdf and put on member's record --->
						<cfset local.qryMember = application.objMember.getMemberInfo(memberID=local.useMID, orgID=local.orgID)>
						
						<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.orgCode)>
						<cfset local.uid = createuuid()>
						<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
							<cfoutput>
								<html>
								<head>
								#local.pageCSS#
								</head>
								<body>
								#local.orgEmailContent#
								</body>
								</html>
							</cfoutput>
						</cfdocument>
				
						<cfset local.strPDF = structNew()>
						<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
						<cfset local.strPDF['serverFile'] = "Membership_#local.qryMember.membernumber#_#DateFormat(now(),'MMDDYYYY')#.pdf">
						
						<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
						
						<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=local.useMID, strPDF=local.strPDF, siteID=local.siteID, docTitle="Membership_#local.qryMember.membernumber#_#DateFormat(now(),'MMDDYYYY')#")>
						
						<cflocation url="/renewsub/#local.qryDirectLinkCode.directLinkCode#" addtoken="no">
					</cfif>
					<cfif local.showErrorMsg>
						<cfoutput>
							There was an error processing your application.  Please contact your association for assistance. (#local.subReturn.errReason#)
						</cfoutput>
					</cfif>

				</cfcase>				
				<!--- SPAM MESSAGE: ================================================================================================================================= --->
				<cfcase value="100">
					<div>
						Error! you Can't Post Here.
					</div>
				</cfcase>
				
			</cfswitch>
		</cfif>
	</div>
</cfoutput>