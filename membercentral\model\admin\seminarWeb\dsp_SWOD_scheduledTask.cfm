<cfsavecontent variable="local.scheduledTaskJS">
	<cfoutput>
	<script language="JavaScript">
		var #toScript(local.participantID, 'participantID')#
		function showAllowCompletionReminder(){
			if ($('##allowCompletionReminder').is(':checked')) {
				$('##allowCompletionReminderHolder').removeClass("d-none");
			} else {
				$('##allowCompletionReminderHolder').addClass("d-none");
			}
		}

		function showEnrollmentMultiSelect() {
			$('##enrollmentMultiSelect').removeClass('d-none');
			$('##creditMultiSelect').addClass('d-none');
		}

		function showCreditMultiSelect() {
			$('##enrollmentMultiSelect').addClass('d-none');
			$('##creditMultiSelect').removeClass('d-none');
		}
		function loadSWODScheduledTaskTab() {
			mca_setupSelect2ByID('enrollmentTimeframes');
			mca_setupSelect2ByID('creditTimeframes');
		}
		function saveSWODScheduledTask() {
			mca_hideAlert('err_scheduledTask');	
			var arrReq = [];
			if ($('##allowCompletionReminder').is(':checked')) {
				if (!$('input[name="emailDays"]:checked').val()) {
					arrReq[arrReq.length] = 'Select an email option.';
				}

				if ($('##emailDays_1').is(':checked')) {
					var enrollmentTimeframes = $('##enrollmentTimeframes').val();
					if (!enrollmentTimeframes || !enrollmentTimeframes.length) {
						arrReq[arrReq.length] = "Select at least one timeframe for 'Send email after the initial enrollment date'.";
					}
				}

				if ($('##emailDays_2').is(':checked')) {
					var creditTimeframes = $('##creditTimeframes').val();
					if (!creditTimeframes || !creditTimeframes.length) {
						arrReq[arrReq.length] = "Select at least one timeframe for 'Send email before credit complete by date'.";
					}
				}

				if (!$('##emailSubject').val().trim()) {
					arrReq[arrReq.length] = "Enter an email subject.";
				}
			}
			if (arrReq.length) {
				mca_showAlert('err_scheduledTask', arrReq.join('<br/>'));
			} else {
				$('##btnUpdateScheduledTask').prop('disabled',true);
				$('##frmSWODScheduledTask, ##divSWODScheduledTaskSaveLoading').toggle();
				var saveScheduledTaskResult = function(r) {
					$('##btnUpdateScheduledTask').prop('disabled',false);
					if (r.success && r.success.toLowerCase() == 'true') {
						$('##frmSWODScheduledTask, ##divSWODScheduledTaskSaveLoading').toggle();
						$('##saveResponse').html('<span class="badge badge-success">SAVED SUCCESSFULLY</span>').addClass('text-success').show().fadeOut(5000);
					} else {
						alert('We were unable to save Scheduled Task Details. Try again.');
					}
				}
				var objParams = { 
					participantID: participantID, 
					isCompletionReminderEnabled: $('##allowCompletionReminder').prop('checked') ? 1 : 0, 
					emailOption: $('##emailDays_1').is(':checked') ? 1 : 2, 
					selectedTimeframes: $('##emailDays_1').is(':checked') ? $('##enrollmentTimeframes').val().join(',') : $('##creditTimeframes').val().join(','), 
					emailSubject:$('##emailSubject').val()};
				TS_AJX('ADMINSWOD','updateScheduledTaskDetails',objParams,saveScheduledTaskResult,saveScheduledTaskResult,10000,saveScheduledTaskResult);
			}
		}
	</script>

	<style>
		.form-rows {
			display: flex;
			flex-direction: column;
		}
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.scheduledTaskJS#">

<cfoutput>
	<div id="err_scheduledTask" class="alert alert-danger mb-2 mt-2 d-none"></div>
	<div class="alert d-flex align-items-center pl-2 py-1 align-content-center alert-info" role="alert">
		<span class="font-size-lg d-block d-40 mr-1 text-center"><i class="fa-solid fa-circle-info"></i></span>
		<span>Scheduled tasks are performed for active programs.</span>
	</div>
	<form name="frmSWODScheduledTask" id="frmSWODScheduledTask" method="post" autocomplete="off">
		<div class="form-group mt-2 mb-3">
			<div class="custom-control custom-switch">
				<input type="checkbox" name="allowCompletionReminder" id="allowCompletionReminder" onclick="showAllowCompletionReminder();" class="custom-control-input" <cfif local.scheduledTaskDetails.isCompletionReminderEnabled EQ 1>checked</cfif>>
				<label class="custom-control-label" for="allowCompletionReminder">
					<b>Program Completion Reminder:</b> Email registrants a reminder to complete their program.
				</label>
			</div>
			<div class="form-group my-2 <cfif NOT local.scheduledTaskDetails.isCompletionReminderEnabled EQ 1>d-none</cfif>" id="allowCompletionReminderHolder">
				<div class="mt-2 mb-3 pl-5">
					<div class="form-rows">
						<div class="mb-0">
							<div class="form-input">
								<input type="radio" name="emailDays" id="emailDays_1" value="1" class="form-check-input" <cfif local.scheduledTaskDetails.emailOption EQ 1>checked</cfif> onclick="showEnrollmentMultiSelect()">
								<label for="emailDays_1" class="form-check-label">Send email after the initial enrollment date</label>
							</div>
						</div>
					</div>
					<div class="form-rows">
						<div class="mb-0">
							<div class="form-input">
								<input type="radio" name="emailDays" id="emailDays_2" value="2" class="form-check-input" <cfif local.scheduledTaskDetails.emailOption EQ 2>checked</cfif> onclick="showCreditMultiSelect()">
								<label for="emailDays_2" class="form-check-label">Send email before credit complete by date</label>
							</div>
						</div>
					</div>
				</div>
				<div class="form-group mt-2 mb-3 pl-5 <cfif local.scheduledTaskDetails.emailOption EQ 2>d-none</cfif>" id="enrollmentMultiSelect">
					<div class="form-label-group">
						<div class="input-group flex-nowrap">
							<select name="enrollmentTimeframes" id="enrollmentTimeframes" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" data-allowclear="false" placeholder="No Time Frames">
								<option value="7" <cfif local.scheduledTaskDetails.emailOption EQ 1 AND listFind(local.scheduledTaskDetails.selectedTimeframes, '7')>selected</cfif>>7 days after</option>
								<option value="15" <cfif local.scheduledTaskDetails.emailOption EQ 1 AND listFind(local.scheduledTaskDetails.selectedTimeframes, '15')>selected</cfif>>15 days after</option>
								<option value="30" <cfif local.scheduledTaskDetails.emailOption EQ 1 AND listFind(local.scheduledTaskDetails.selectedTimeframes, '30')>selected</cfif>>30 days after</option>
								<option value="45" <cfif local.scheduledTaskDetails.emailOption EQ 1 AND listFind(local.scheduledTaskDetails.selectedTimeframes, '45')>selected</cfif>>45 days after</option>
								<option value="60" <cfif local.scheduledTaskDetails.emailOption EQ 1 AND listFind(local.scheduledTaskDetails.selectedTimeframes, '60')>selected</cfif>>60 days after</option>
								<option value="75" <cfif local.scheduledTaskDetails.emailOption EQ 1 AND listFind(local.scheduledTaskDetails.selectedTimeframes, '75')>selected</cfif>>75 days after</option>
							</select>
							<label for="enrollmentTimeframes">Select Timeframes After Initial Enrollment Date</label>
						</div>
					</div>
				</div>
				<div class="form-group mt-2 mb-3 pl-5 <cfif local.scheduledTaskDetails.emailOption EQ 1>d-none</cfif>" id="creditMultiSelect">
					<div class="form-label-group">
						<div class="input-group flex-nowrap">
							<select name="creditTimeframes" id="creditTimeframes" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" data-allowclear="false" placeholder="No Time Frames">
								<option value="2" <cfif local.scheduledTaskDetails.emailOption EQ 2 AND listFind(local.scheduledTaskDetails.selectedTimeframes, '2')>selected</cfif>>2 days before</option>
								<option value="7" <cfif local.scheduledTaskDetails.emailOption EQ 2 AND listFind(local.scheduledTaskDetails.selectedTimeframes, '7')>selected</cfif>>7 days before</option>
								<option value="15" <cfif local.scheduledTaskDetails.emailOption EQ 2 AND listFind(local.scheduledTaskDetails.selectedTimeframes, '15')>selected</cfif>>15 days before</option>
								<option value="30" <cfif local.scheduledTaskDetails.emailOption EQ 2 AND listFind(local.scheduledTaskDetails.selectedTimeframes, '30')>selected</cfif>>30 days before</option>
								<option value="60" <cfif local.scheduledTaskDetails.emailOption EQ 2 AND listFind(local.scheduledTaskDetails.selectedTimeframes, '60')>selected</cfif>>60 days before</option>
							</select>
							<label for="creditTimeframes">Select Timeframes Before Completion Date</label>
						</div>
					</div>
				</div>
				<div class="form-group mt-2 mb-3 pl-5 ">
					<div class="form-label-group">
						<div class="form-label-group">
							<input type="text" name="emailSubject" id="emailSubject" class="form-control" maxlength="250" autocomplete="off" value="#local.scheduledTaskDetails.emailSubject#">
							<label for="emailSubject">Email Subject <span class="text-danger">*</span></label>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="form-group row mt-2">
			<div class="col text-right">
				<span id="saveResponse"></span>
				<button type="button" name="btnUpdateScheduledTask" id="btnUpdateScheduledTask" onclick="saveSWODScheduledTask()" class="btn btn-sm btn-primary">Save Details</button>
			</div>
		</div>
	</form>
</cfoutput>

<div id="divSWODScheduledTaskSaveLoading" style="display:none;">
	<div class="text-center">
		<br/>
		<i class="fa-light fa-circle-notch fa-spin fa-3x"></i>
		<br/><br/>
		<b>Please wait while we save these changes.</b>
		<br/>
	</div>
</div>