<cfoutput>
<mjml>
	<mj-head>
		<cfif len(arguments.preheaderText)>
			<mj-preview>#arguments.preheaderText#</mj-preview>
		</cfif>
		<mj-attributes>
			<mj-all font-family="Helvetica, sans-serif" color="##5a5a5a" font-size="17px" line-height="1.25em"></mj-all>
			<mj-class name="presentedBy" color="##A7A2A9" align="center" font-size="16px"></mj-class>
			<mj-class name="secHead" font-size="15px" font-family="georgia, serif" color="##fff"></mj-class>
			<mj-class name="secBorder" border-color="##e3e3e3" border-width="3px" padding="0"></mj-class>
		</mj-attributes>
		<mj-style inline="inline">
			.head {font-weight: bold; font-family: Georgia, serif; font-size: 26px; line-height: 28px; margin-bottom: 5px; margin-top: 0;}
			.subHead { font-family: Georgia, serif; font-size: 18px; margin: 0; }
			.fullCatalog {text-decoration: none !important; color: ##A7A2A9; font-family: Helvetica, sans-serif; text-transform: uppercase; font-size: 10px;}
			<cfif local.strAssociation.qryAssociation.hasSWProgramImageConfiguration and local.programLevelFeaturedImageCount>
				.title {min-height: 105px;}
				.checks {padding-left: 20px; text-align: left;}
				.checks li {list-style: square; font-size:20px; line-height: 18px; color: ##cfcfcf;}
			<cfelse>
				.title {text-align: center; max-width: 475px; margin: 0 auto; display: block; }
				.checks {text-align: left; display: block; padding-left: 0;}
				.checks li {list-style: square; font-size:20px; line-height: 18px; color: ##cfcfcf; list-style-position: inside; margin-bottom: 10px;}
			</cfif>
			.checks li span {font-size: 14px; color: ##5a5a5a; }
			.checks p {margin: 0 0 10px 0;}
			.register {width: 100px; border: ##6B9E39 2px solid; border-radius: 5px; text-align: center; border-spacing: 0; margin-left: 0; font-size: 12px;}
			.learn {width: 100px; text-align: center; border-spacing: 0; margin-left: 0; font-size: 11px; }
			.register a, .learn a {text-transform: uppercase; color: ##6B9E39; text-decoration: none; }
			.evDate {font-size: 13px; line-height: 15px; margin: 8px 0 6px 0;}
			.upEvent {color: ##1A3958; text-decoration: none;}
			.credit {font-size: 13px; }
			.green {color: ##6B9E39;}
		</mj-style>
		<mj-style>
			@media only screen and (max-width: 479px) {
				.evDate, .head, .subHead, .credit {text-align: center !important;}
			}
		</mj-style>
	</mj-head>
	<mj-body>
		<mj-section padding="10px 25px">
			<mj-column><mj-text mj-class="presentedBy" padding="10px 40px">#local.strAssociation.qryAssociation.description#</mj-text></mj-column>
		</mj-section>
		<mj-section padding="20px 0 7px 0" border-bottom="##1A3958 solid 1px">
			<mj-column>
				<mj-text mj-class="secHead" padding="0">
					<div style="padding: 10px 25px; background-color: ##1A3958; display: inline; border-radius: 3px 3px 0 0;">#local.strAssociation.qryAssociation.brandSWLTab#</div><span><a href="#local.programsArr[1].browsePageLink#" class="fullCatalog">&nbsp;&nbsp;&nbsp;view all programs</a></span>
				</mj-text>
			</mj-column>
		</mj-section>

		<cfloop array="#local.programsArr#" index="local.thisProgram">
			<mj-section padding="15px 25px 0 25px">
				<cfif local.programLevelFeaturedImageCount and len(local.thisProgram.featuredImagePath)>
					<mj-column width="33%">
						<mj-image src="#local.thisProgram.featuredImagePath#" title="Go to program details page." href="#local.thisProgram.programDetailPageLink#"></mj-image>
					</mj-column>
				</cfif>
				<mj-column width="<cfif local.programLevelFeaturedImageCount and len(local.thisProgram.featuredImagePath)>67<cfelse>100</cfif>%">
					<mj-text padding-bottom="10px">
					<div class="title">
						<p class="head"><a href="#local.thisProgram.programDetailPageLink#" class="upEvent" title="Go to program details page.">#encodeForHTML(local.thisProgram.strSeminar.qrySeminar.seminarName)#</a></p>
						<cfif len(local.thisProgram.strSeminar.qrySeminar.seminarSubTitle)>
							<p class="subHead"><a href="#local.thisProgram.programDetailPageLink#" class="upEvent" title="Go to program details page.">#encodeForHTML(local.thisProgram.strSeminar.qrySeminar.seminarSubTitle)#</a></p>
						</cfif>
						<p class="evDate">
							<cfif dateDiff("d", local.thisProgram.strSeminar.qrySeminar.dspStartDate, local.thisProgram.strSeminar.qrySeminar.dspEndDate) gt 0>
								#DateFormat(local.thisProgram.strSeminar.qrySeminar.dspStartDate,'ddd, mmmm d')# - #DateFormat(local.thisProgram.strSeminar.qrySeminar.dspEndDate,'ddd, mmm d')#
							<cfelse>
								<cfif TimeFormat(local.thisProgram.strSeminar.qrySeminar.dspStartDate,'TT') neq TimeFormat(local.thisProgram.strSeminar.qrySeminar.dspEndDate,'TT')>
									<cfset local.startTimeFormat = "h:mm TT">
								<cfelse>
									<cfset local.startTimeFormat = "h:mm">
								</cfif>
								#DateFormat(local.thisProgram.strSeminar.qrySeminar.dspStartDate,'ddd, mmmm d')# / #replace(TimeFormat(local.thisProgram.strSeminar.qrySeminar.dspStartDate, local.startTimeFormat),":00","")# - #replace(TimeFormat(local.thisProgram.strSeminar.qrySeminar.dspEndDate,'h:mm TT'),":00 "," ")# #UCASE(local.thisProgram.strSeminar.qrySeminar.dspTZAbbr)#
							</cfif>
						</p>
						<cfif local.thisProgram.strSeminar.qrySeminar.offerCredit and local.thisProgram.creditAuthorityCount>
							<p class="credit" style="margin-top: 5px;">
								<img src="https://www.membercentral.com/userassets/mc/mc/userimages/semweb%20marketing%20emails/cleanicons_bookmark_32px_1a3958.png" style="width: 14px; position: relative; top: 3px; margin-right: 5px;">
								<cfif local.thisProgram.creditAuthorityCount eq 1>
									#local.thisProgram.dspCredits#
								<cfelse>
									Credit Offered: #local.thisProgram.dspCredits#
								</cfif>
							</p>
						</cfif>
					</div>
					</mj-text>
				</mj-column>
			</mj-section>
			<mj-section padding="0">
				<mj-column>
					<mj-text padding-top="0">
						<div style="padding: 0 20px;">
							<cfif local.thisProgram.strSeminar.qryLearningObjectives.recordCount>
								<ul class="checks">
									<cfloop query="local.thisProgram.strSeminar.qryLearningObjectives">
										<li><span>#local.thisProgram.strSeminar.qryLearningObjectives.objective#</span></li>
									</cfloop>
								</ul>
							<cfelse>
								<p>#local.thisProgram.trimmedProgramDesc#</p>
							</cfif>
							<table align="<cfif local.programLevelFeaturedImageCount and len(local.thisProgram.featuredImagePath)>right<cfelse>center</cfif>" style="margin: 10px auto 0;">
							<tr>
								<td class="learn"><a style="" href="#local.thisProgram.programDetailPageLink#">Learn More</a></td>
								<td class="register"><a style="" href="#local.thisProgram.programDetailPageLink#">Register</a></td>
							</tr>
							</table>
						</div>
					</mj-text>
					<mj-spacer></mj-spacer>
					<mj-divider mj-class="secBorder"></mj-divider>
				</mj-column>
			</mj-section>
			<mj-section padding="0">
				<mj-column>
					<mj-spacer></mj-spacer>
				</mj-column>
			</mj-section>
		</cfloop>
		<mj-section padding-top="0">
			<mj-column padding="0 25px" width="100%">
				<mj-text align="center" font-size="13px" color="##A7A2A9">
					<p style="margin:5px;">
						This promotional message was sent by SeminarWeb on behalf of <a href="#local.strAssociation.qryAssociation.CatalogURL#" style="color: ##A7A2A9;">#local.qryOrgIdentity.organizationName#</a><br>
						13359 N Hwy 183 ##406-1220, Austin, TX 78750 | (737) 201-2059
					</p>
					<!--- chr(7) inserted before unsub address done on purpose to ensure that Lyris merge code is processed correctly. it is replaced with a CRLF after HTML is compacted --->
					<p style="margin:5px;">#chr(7)# <a href="mailto:$subst('Email.UnSub')?Subject=Unsubscribe%20Request&Body=Please%20unsubscribe%20me%20from%20this%20list." style="color: ##A7A2A9; font-size: 13px;">UNSUBSCRIBE</a></p>
					<IMG ALT="" SRC="http://lists.trialsmith.com/db/%%outmail.messageid%%/%%memberid%%/1.gif" WIDTH="1" HEIGHT="1">
				</mj-text>
			</mj-column>
		</mj-section>
	</mj-body>
</mjml>
</cfoutput>