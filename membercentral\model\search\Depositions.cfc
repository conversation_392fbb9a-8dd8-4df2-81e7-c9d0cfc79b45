<cfcomponent extends="Bucket">

	<cfset variables.thisBucketTypeID = 5>
	<cfset variables.thisBucketType = "depositions">
	<cfset variables.thisBucketCartItemTypeID = 1>
	<cfset variables.thisBucketMaxPerPage = 10>
	<cfset variables.thisBucketMaxShown = 10000>

	<cffunction name="showHeader" access="private" output="false" returntype="string">
		<cfargument name="bucketName" type="string" required="yes">
		<cfargument name="bucketSettings" type="xml" required="no" default="<settings/>">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var local = StructNew()>
		<cfset local.settingsStruct = prepSettings(arguments.bucketSettings)>

		<cfsavecontent variable="local.header">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/depositions/header.cfm">
		</cfsavecontent>
		
		<cfreturn local.header>
	</cffunction>

	<cffunction name="showSearchForm" access="public" output="false" returntype="string">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="searchID" required="no" type="numeric" default="0">
		<cfargument name="viewDirectory" type="string" required="no" default="default">
		
		<cfset var local = StructNew()>
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>
		
		<!--- load search if passed in --->
		<cfset local.strSearchForm = prepSearchForSearchForm(searchID=arguments.searchID, bucketID=arguments.bucketID)>

		<!--- get states --->
		<cfquery name="local.qryStates" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SELECT distinct s.orderpref, s.code, s.name
			from dbo.depodocuments as d
			inner join dbo.states as s on s.code = d.jurisdiction
			order by s.orderpref, s.name		
		</cfquery>
		
		<!--- show common JS --->
		<cfset showCommonJS(bucket=variables.thisBucketType, bucketID=arguments.bucketID, viewDirectory=arguments.viewDirectory)>

		<cfsavecontent variable="local.data">
			<cfinclude template="/views/search/commonSearchFormJS.cfm">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/depositions/searchForm.cfm">
		</cfsavecontent>
		
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="prepSearchForSearchForm" access="private" returntype="struct" output="no" hint="parses the searchXML and populates search form">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
	
		<cfset var local = structNew()>
		
		<cfscript>
		local.returnStruct = StructNew();

		if (arguments.searchID gt 0) {
			// convert origin searchXML to bucket searchXML
			local.searchXML = application.objSearchTranslate.convertSearchXML(arguments.searchID,variables.thisBucketTypeID);

			// read/clean from xml
			local.returnStruct.s_fname = replace(local.searchXML.search["s_fname"].xmlText,chr(34),'','ALL');
			local.returnStruct.s_lname = replace(local.searchXML.search["s_lname"].xmlText,chr(34),'','ALL');
			local.returnStruct.s_casename = local.searchXML.search["s_casename"].xmlText;
			local.returnStruct.s_jurisdiction = local.searchXML.search["s_jurisdiction"].xmlText;
			local.returnStruct.s_depodatefrom = prepareSearchDate(local.searchXML.search["s_depodatefrom"].xmlText);
			local.returnStruct.s_depodateto = prepareSearchDate(local.searchXML.search["s_depodateto"].xmlText);
			local.returnStruct.s_key_all = local.searchXML.search["s_key_all"].xmlText;
			local.returnStruct.s_key_one = local.searchXML.search["s_key_one"].xmlText;
			local.returnStruct.s_key_phrase = local.searchXML.search["s_key_phrase"].xmlText;
			local.returnStruct.s_key_x = local.searchXML.search["s_key_x"].xmlText;
		} else {
			local.returnStruct.s_fname = '';
			local.returnStruct.s_lname = '';
			local.returnStruct.s_casename = '';
			local.returnStruct.s_jurisdiction = '';
			local.returnStruct.s_depodatefrom = '';
			local.returnStruct.s_depodateto = '';
			local.returnStruct.s_key_all = '';
			local.returnStruct.s_key_one = '';
			local.returnStruct.s_key_phrase = '';
			local.returnStruct.s_key_x = '';
		}

		return local.returnStruct;
		</cfscript>
	</cffunction>


	<cffunction name="prepSearch" access="private" returntype="struct" output="no" hint="parses the searchXML and prepares search criteria">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">

		<cfset var searchXML = application.objSearchTranslate.convertSearchXML(arguments.searchID,variables.thisBucketTypeID)>
		
		<cfreturn prepSearchFromXML(siteID=arguments.siteID,bucketID=arguments.bucketID,searchXML=searchXML)>
	</cffunction>


	<cffunction name="prepSearchFromXML" access="private" returntype="struct" output="no" hint="parses the searchXML and prepares search criteria">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="searchXML" required="yes" type="xml">
	
		<cfset var local = structNew()>
		
		<cfscript>
		// get bucket info to get any restrictions
		local.qryBucketInfo = getBucketInfo(arguments.bucketID);

		// read/clean from xml
		local.s_fname = preparePhraseString(arguments.searchXML.search["s_fname"].xmlText);
		local.s_lname = preparePhraseString(arguments.searchXML.search["s_lname"].xmlText);
		local.s_casename = prepareSearchString(arguments.searchXML.search["s_casename"].xmlText);
		local.s_jurisdiction = arguments.searchXML.search["s_jurisdiction"].xmlText;
		local.s_depodatefrom = prepareSearchDate(arguments.searchXML.search["s_depodatefrom"].xmlText);
		local.s_depodateto = prepareSearchDate(arguments.searchXML.search["s_depodateto"].xmlText);
		
		// Fileshare 2 translation. bypass prepareSearchString
		local.s_docflags = "";
		if ( structKeyExists(arguments.searchXML.search, "s_docflags") ){
			local.s_docflags = arguments.searchXML.search["s_docflags"].xmlText;
		}

		local.s_key_all = prepareSearchString(arguments.searchXML.search["s_key_all"].xmlText);
		if (local.s_docflags NEQ '' AND local.s_key_all NEQ '')
			local.s_key_all = local.s_key_all & " AND " & local.s_docflags;
		else if (local.s_docflags NEQ '')
			local.s_key_all = local.s_docflags;
		
		local.s_key_one = prepareSearchString(arguments.searchXML.search["s_key_one"].xmlText,true);
		local.s_key_phrase = preparePhraseString(arguments.searchXML.search["s_key_phrase"].xmlText);
		local.s_key_x = prepareSearchString(arguments.searchXML.search["s_key_x"].xmlText);

		//prepare expertname keywords
		local.keywordsexpertname = "";
		if (Len(local.s_fname))
			local.keywordsexpertname = listAppend(local.keywordsexpertname,local.s_fname,chr(7));
		if (Len(local.s_lname))
			local.keywordsexpertname = listAppend(local.keywordsexpertname,local.s_lname,chr(7));
		local.keywordsexpertname = Replace(local.keywordsexpertname,chr(7)," and ","ALL");
		local.keywordscasename = local.s_casename;

		// prepare keywords
		local.keywordsInclude = "";
		if (Len(local.s_key_all))
			local.keywordsInclude = listAppend(local.keywordsInclude,local.s_key_all,chr(7));
		if (Len(local.s_key_one))
			local.keywordsInclude = listAppend(local.keywordsInclude,local.s_key_one,chr(7));
		if (Len(local.s_key_phrase))
			local.keywordsInclude = listAppend(local.keywordsInclude,local.s_key_phrase,chr(7));
		local.keywordsInclude = Replace(local.keywordsInclude,chr(7)," and ","ALL");

		local.keywordsExclude = "";
		if (Len(local.s_key_x))
			local.keywordsExclude = replaceNoCase(local.s_key_x," and "," or ","all");
		if (len(local.keywordsExclude) and len(local.keywordsInclude)) 
			local.finalKeywords = local.keywordsInclude & " and not (" & local.keywordsExclude & ")";
		else if (len(local.keywordsInclude))
			local.finalKeywords = local.keywordsInclude;
		else if (len(local.keywordsExclude))
			local.finalKeywords = "a and not (" & local.keywordsExclude & ")";
		else 
			local.finalKeywords = "";

		local.settingsStruct = prepSettings(local.qryBucketInfo.bucketSettings);
		if (local.settingsStruct.search.relatedtestimonyonly) {
			local.relatedexpertkeywords = prepareFullTextExpertKeywords(local.s_fname,local.s_lname);
			if (len(local.finalKeywords))
				local.finalKeywords = local.finalKeywords & " and (" & local.relatedexpertkeywords & ")";
			else
				local.finalKeywords = local.relatedexpertkeywords;
		}
		
		// return search struct
		local.returnStruct = structNew();
		structInsert(local.returnStruct,"expertFName",replace(local.s_fname,chr(34),"","ALL"));
		structInsert(local.returnStruct,"expertLName",replace(local.s_lname,chr(34),"","ALL"));
		structInsert(local.returnStruct,"keywords",local.finalKeywords);
		structInsert(local.returnStruct,"expertname",local.keywordsexpertname);
		structInsert(local.returnStruct,"casename",local.keywordscasename);
		structInsert(local.returnStruct,"jurisdiction",local.s_jurisdiction);
		structInsert(local.returnStruct,"fromdate",local.s_depodatefrom);
		structInsert(local.returnStruct,"todate",local.s_depodateto);
		structInsert(local.returnStruct,"strSettings",local.settingsStruct);

		// do i have enough criteria to run a search?
		if (local.returnStruct.strSettings.search.relatedtestimonyonly)
			structInsert(local.returnStruct,"searchAccepted",len(local.keywordsexpertname) gt 0);
		else if (local.returnStruct.strSettings.search.deposedexpertonly)
			structInsert(local.returnStruct,"searchAccepted",len(local.s_fname) and len(local.s_lname));
		else if (not len(local.finalKeywords) and not len(local.keywordsexpertname) and not len(local.keywordscasename) and not len(local.s_jurisdiction) and not len(local.s_depodatefrom) and not len(local.s_depodateto))
			structInsert(local.returnStruct,"searchAccepted",false);
		else
			structInsert(local.returnStruct,"searchAccepted",true);
		</cfscript>

		<cfset local.fulltextsql = "">
		<cfset local.rankExpressionList = "">
		<cfset local.CRLF = chr(13) & chr(10)>
		<cfif local.returnStruct.strSettings.search.deposedexpertonly and local.returnStruct.searchAccepted>
			<cfset local.fulltextsql = local.fulltextsql & local.CRLF & "inner join searchMC.dbo.ts_subscriberDepositions as sd on sd.depoDocumentID = docs.documentid">
		</cfif>
		<cfif len(local.returnStruct.expertname) and not local.returnStruct.strSettings.search.relatedtestimonyonly>
			<cfset local.fulltextsql = local.fulltextsql & local.CRLF & "inner join containstable(trialsmith.dbo.depodocuments,expertname,@expertsearchterms) as expertsearch on expertsearch.[key] = docs.documentid">
			<cfset local.rankExpressionList = listappend(local.rankExpressionList,"expertsearch.[rank]")>
		</cfif>
		<cfif len(local.returnStruct.casename)>
			<cfset local.fulltextsql = local.fulltextsql & local.CRLF & "inner join containstable(trialsmith.dbo.depodocuments,style,@casenamesearchterms) as casesearch on casesearch.[key] = docs.documentid">
			<cfset local.rankExpressionList = listappend(local.rankExpressionList,"casesearch.[rank]")>
		</cfif>
		<cfif len(local.returnStruct.keywords)>
			<cfsavecontent variable="local.fulltextsql">
				<cfoutput>#local.fulltextsql#</cfoutput>
				inner join (
					select [key], max(rank) as rank from
					(				
						select dsearch.[key] as [key], rank from containstable(trialsmith.dbo.depodocuments,*,@keywordsearchterms) dsearch
						union
						(
							select sd.documentid as [key], rank from search.dbo.depodocuments sd
							inner join containstable(search.dbo.depodocuments,searchtext,@keywordsearchterms) as sdsearch on sdsearch.[key] = sd.id
						)
					) fsearch group by [key]
				) as keywordsearch on keywordsearch.[key] = docs.documentid
			</cfsavecontent>
			<cfset local.rankExpressionList = listappend(local.rankExpressionList,"keywordsearch.[rank]")>
		</cfif>
		<cfif local.returnStruct.strSettings.search.relatedtestimonyonly and len(local.returnStruct.expertname)>
			<cfset local.fulltextsql = local.fulltextsql & local.CRLF & "left outer join containstable(trialsmith.dbo.depodocuments,expertname,@expertsearchterms) as excludeexpertsearch on excludeexpertsearch.[key] = docs.documentid where excludeexpertsearch.[key] is null">
		</cfif>

		<cfif len(local.fulltextsql)>
			<cfsavecontent variable="local.fulltextsql">
				select distinct docs.documentid, (<cfoutput>#replacenocase(local.rankExpressionList,","," + ","all")#</cfoutput>) as rank
				from dbo.depoDocuments as docs
				<cfoutput>#local.fulltextsql#</cfoutput>;
			</cfsavecontent>
		</cfif>
	
		<cfset structInsert(local.returnStruct,"fulltextsql",local.fulltextsql)>
		<cfset structInsert(local.returnStruct,"rankExpressionList",local.rankExpressionList)>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="prepSettings" access="private" returntype="struct" output="no" hint="parses the settingsXML into a standardized struct">
		<cfargument name="bucketSettings" required="yes" type="xml">
		
		<cfset var local = StructNew()>
		
		<cfscript>
			// standardize restrictions
			local.settingsStruct = StructNew();
			local.bucketSettingsXML = XMLParse(arguments.bucketSettings);
			if (StructKeyExists(local.bucketSettingsXML.settings,"restrict_orgcode") and StructKeyExists(local.bucketSettingsXML.settings["restrict_orgcode"].XmlAttributes,"action")) {
				local.settingsStruct.restrictorgcode = structnew();
				local.settingsStruct.restrictorgcode.value = local.bucketSettingsXML.settings["restrict_orgcode"].xmlText;
	
				//override parameter not currently supported
				if (StructKeyExists(local.bucketSettingsXML.settings["restrict_orgcode"].XmlAttributes,"override") and local.bucketSettingsXML.settings["restrict_orgcode"].XmlAttributes.override eq "false")
					local.settingsStruct.restrictorgcode.override = false;
				else 
					local.settingsStruct.restrictorgcode.override = true;
	
				if (local.bucketSettingsXML.settings["restrict_orgcode"].XmlAttributes.action eq "include")
					local.settingsStruct.restrictorgcode.action = "include";
				else if (local.bucketSettingsXML.settings["restrict_orgcode"].XmlAttributes.action eq "exclude")
					local.settingsStruct.restrictorgcode.action = "exclude";
				else // no action defined and there is no default so cancel restriction
					structdelete(local.settingsStruct,"orgcode");
			}
			if (StructKeyExists(local.bucketSettingsXML.settings,"restrict_bankid") and StructKeyExists(local.bucketSettingsXML.settings["restrict_bankid"].XmlAttributes,"action")) {
				local.settingsStruct.restrictbankid = structnew();
				local.settingsStruct.restrictbankid.value = local.bucketSettingsXML.settings["restrict_bankid"].xmlText;
				
				//override parameter not currently supported
				if (StructKeyExists(local.bucketSettingsXML.settings["restrict_bankid"].XmlAttributes,"override") and local.bucketSettingsXML.settings["restrict_bankid"].XmlAttributes.override eq "false")
					local.settingsStruct.restrictbankid.override = false;
				else 
					local.settingsStruct.restrictbankid.override = true;
	
				if (local.bucketSettingsXML.settings["restrict_bankid"].XmlAttributes.action eq "include")
					local.settingsStruct.restrictbankid.action = "include";
				else if (local.bucketSettingsXML.settings["restrict_bankid"].XmlAttributes.action eq "exclude")
					local.settingsStruct.restrictbankid.action = "exclude";
				else // no action defined and there is no default so cancel restriction
					structdelete(local.settingsStruct,"bankid");
			}

			if (StructKeyExists(local.bucketSettingsXML.settings,"search")) {
				local.settingsStruct.search = structnew();

				if (StructKeyExists(local.bucketSettingsXML.settings["search"].XmlAttributes,"deposedexpertonly") and local.bucketSettingsXML.settings["search"].XmlAttributes.deposedexpertonly eq "true")
					local.settingsStruct.search.deposedexpertonly = true;
				else 
					local.settingsStruct.search.deposedexpertonly = false;

				if (StructKeyExists(local.bucketSettingsXML.settings["search"].XmlAttributes,"relatedtestimonyonly") and local.bucketSettingsXML.settings["search"].XmlAttributes.relatedtestimonyonly eq "true")
					local.settingsStruct.search.relatedtestimonyonly = true;
				else 
					local.settingsStruct.search.relatedtestimonyonly = false;
			} else {
				local.settingsStruct.search = structnew();
				local.settingsStruct.search.deposedexpertonly = false;
				local.settingsStruct.search.relatedtestimonyonly = false;
			}

			if (StructKeyExists(local.bucketSettingsXML.settings,"searchoverride_guest")) {
				local.settingsStruct.searchoverrideguest = structnew();
				if (StructKeyExists(local.bucketSettingsXML.settings["searchoverride_guest"].XmlAttributes,"bankids") and len(trim(local.bucketSettingsXML.settings["searchoverride_guest"].XmlAttributes.bankids)))
					local.settingsStruct.searchoverrideguest.bankids = local.bucketSettingsXML.settings["searchoverride_guest"].XmlAttributes.bankids;
			}
		</cfscript>
		<cfreturn local.settingsStruct>
	</cffunction>

	<cffunction name="getResultsCount" access="public" returntype="struct" output="no" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="mcproxy_siteID" required="no">
		<cfargument name="searchID" required="no">
		<cfargument name="bucketID" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["itemcount"] = 'N/A'>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>
		
		<cfif 
			(NOT StructKeyExists(arguments, "mcproxy_siteID") OR NOT IsNumeric(arguments.mcproxy_siteID)) OR 
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID)) OR 
			(NOT StructKeyExists(arguments, "bucketID") OR NOT IsNumeric(arguments.bucketID))>
				<cfset local.isValidArguments = false>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = doGetResultsCount(siteID=arguments.mcproxy_siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doGetResultsCount" access="private" returntype="struct" output="no" hint="searches and returns a count">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">

		<cfset var local = StructNew()>

		<cfset local.strSearch = prepSearch(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfset local.returnStruct = StructNew()>

		<cfif NOT local.strSearch.searchAccepted>
			<cfset StructInsert(local.returnStruct,"itemcount",'N/A')>
			<cfset StructInsert(local.returnStruct,"success",true)>
			<cfset saveBucketCount(searchID=arguments.searchID,bucketID=arguments.bucketID,itemCount=-1)>
		<cfelse>
			<cfset local.cachedItemCount = getCachedBucketCount(searchID=arguments.searchID,bucketID=arguments.bucketID)>
			<cfif local.cachedItemCount gte 0>
				<cfset StructInsert(local.returnStruct,"itemcount",local.cachedItemCount)>
				<cfset StructInsert(local.returnStruct,"success",true)>
			<cfelse>
				<cfif local.strSearch.strSettings.search.deposedexpertonly>
					<cfset local.metric = "deposed.getResultsCount">
				<cfelseif local.strSearch.strSettings.search.relatedtestimonyonly>
					<cfset local.metric = "deposrelated.getResultsCount">
				<cfelse>
					<cfset local.metric = "#variables.thisBucketType#.getResultsCount">
				</cfif>
				<cfset local.returnStruct = runResultsCount(siteID=arguments.siteID,bucketID=arguments.bucketID,strSearch=local.strSearch)>
				<cfset saveStats(searchID=arguments.searchID, metric=local.metric, ms=local.returnStruct.ExecutionTime, itemCount=local.returnStruct.itemCount)>
				<cfset saveBucketCount(searchID=arguments.searchID,bucketID=arguments.bucketID,itemCount=local.returnStruct.itemCount)>
				<!--- Remove structkeys not expected by caller --->
				<cfset structDelete(local.returnStruct, "ExecutionTime")/>
			</cfif>
		</cfif>
		<cfreturn local.returnStruct/>
	</cffunction>
	<cffunction name="getResultsCountForSearchIndex" access="public" output="no" returntype="struct">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="firstName" required="true" type="string">
		<cfargument name="lastName" required="true" type="string">

		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.searchXML">
			<cfoutput>
			<search xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2">
				<bid>#val(arguments.bucketID)#</bid>
				<s_casename></s_casename>
				<s_depodatefrom xsi:nil="true" />
				<s_depodateto xsi:nil="true" />
				<s_fname>#xmlFormat(trim(replace(arguments.firstName,chr(34),'','ALL')))#</s_fname>
				<s_jurisdiction expanded=""></s_jurisdiction>
				<s_lname>#xmlFormat(trim(replace(arguments.lastName,chr(34),'','ALL')))#</s_lname>
				<s_key_all></s_key_all>
				<s_key_one></s_key_one>
				<s_key_phrase></s_key_phrase>
				<s_key_x></s_key_x>
			</search>
			</cfoutput>
		</cfsavecontent>
		<cfset local.searchXML = XMLParse(local.searchXML)>
		
		<cfset local.strSearch = prepSearchFromXML(siteID=arguments.siteID,bucketID=arguments.bucketID,searchXML=local.searchXML)>
		<cfset local.returnStruct = StructNew()>
		<cfif NOT local.strSearch.searchAccepted>
			<cfset StructInsert(local.returnStruct,"itemcount",'N/A')>
			<cfset StructInsert(local.returnStruct,"success",true)>
		<cfelse>
			<cfset local.returnStruct = runResultsCount(siteID=arguments.siteID,bucketID=arguments.bucketID,strSearch=local.strSearch)>
		</cfif>
		<cfreturn local.returnStruct/>		
	</cffunction>
	<cffunction name="runResultsCount" access="private" returntype="struct" output="no" hint="searches and returns a count">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="strSearch" required="yes" type="struct">

		<cfset var local = StructNew()>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>
		
		<cfset local.returnStruct = StructNew()>

		<cfset local.memberGroups = variables.cfcuser_TSgroups>
		<cfset local.qJoinedAndPublicGroups = cleanGroups()>
		<cfset local.stringJoinedAndPublicGroups = listprepend(valuelist(local.qJoinedAndPublicGroups.groupid),0)>
		<cfset local.stringJoinedGroupsOnly = listprepend(local.memberGroups,0)>
		<cfif not variables.cfcuser_isLoggedIn and StructKeyExists(arguments.strSearch.strSettings,"searchoverrideguest") and StructKeyExists(arguments.strSearch.strSettings.searchoverrideguest,"bankids")>
			<cfset local.stringJoinedAndPublicGroups = listappend(valuelist(local.qJoinedAndPublicGroups.groupid),arguments.strSearch.strSettings.searchoverrideguest.bankids)>
			<cfset local.stringJoinedGroupsOnly = listappend(local.memberGroups,arguments.strSearch.strSettings.searchoverrideguest.bankids)>
		</cfif>

		<cfset local.strResults = getSearchResultsCount(strSearch=arguments.strSearch, stringJoinedAndPublicGroups=local.stringJoinedAndPublicGroups, bucketType="depositions")>
		<cfset StructInsert(local.returnStruct,"executionTime",local.strResults.qryStat.ExecutionTime)>
		<cfset StructInsert(local.returnStruct,"itemcount",local.strResults.qryResults.itemCount)>
		<cfset StructInsert(local.returnStruct,"success",true)>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSearchResultsCount" access="public" output="false" returntype="struct">
		<cfargument name="strSearch" required="yes" type="struct">
		<cfargument name="stringJoinedAndPublicGroups" required="yes" type="string">
		<cfargument name="bucketType" required="yes" type="string">

		<cfset var qryResults = "">
		<cfset var qryStat = "">

		<cfquery name="qryResults" datasource="#application.dsn.tlasites_trialsmith.dsn#" result="qryStat">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @keywordsearchterms varchar(8000), @expertsearchterms varchar(8000), @casenamesearchterms varchar(8000);
			declare @tblGroups TABLE (groupID int PRIMARY KEY);
			set @keywordsearchterms = <cfqueryparam value="#arguments.strSearch.keywords#" cfsqltype="CF_SQL_LONGVARCHAR">;
			set @expertsearchterms = <cfqueryparam value="#arguments.strSearch.expertname#" cfsqltype="CF_SQL_LONGVARCHAR">;
			<cfif arguments.strSearch.keyExists("casename")>
				set @casenamesearchterms = <cfqueryparam value="#arguments.strSearch.casename#" cfsqltype="CF_SQL_LONGVARCHAR">;
			</cfif>

			declare @approvedStatusID int 
			select @approvedStatusID=statusID from  depoDocumentStatuses where statusName = 'Approved';

			IF OBJECT_ID('tempdb..##tmpDocumentIDs') IS NOT NULL
				DROP TABLE ##tmpDocumentIDs;
			CREATE TABLE ##tmpDocumentIDs (documentID int PRIMARY KEY);

			<cfif len(arguments.strSearch.fulltextsql)>
				IF OBJECT_ID('tempdb..##tmpDocumentsFTS') IS NOT NULL
					DROP TABLE ##tmpDocumentsFTS;
				CREATE TABLE ##tmpDocumentsFTS (documentID int PRIMARY KEY, rank int);

				INSERT INTO ##tmpDocumentsFTS (documentID, rank)
				#arguments.strSearch.fulltextsql#
			</cfif>

			INSERT INTO @tblGroups (groupID)
			SELECT distinct listitem
			FROM membercentral.dbo.fn_intListToTable(<cfqueryparam value="#arguments.stringJoinedAndPublicGroups#" cfsqltype="CF_SQL_VARCHAR">,',');

			INSERT INTO ##tmpDocumentIDs (documentID)
			select distinct d.documentID
			from dbo.depoDocuments as d 
			inner join dbo.depoDocumentStatusHistory as dsh 
					on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
					and dsh.statusID = @approvedStatusID
			inner join dbo.depogroups as g on d.groupid = g.groupid
			inner join @tblGroups as tblg on tblg.groupID = g.groupID
			<cfif len(arguments.strSearch.fulltextsql)>
				inner join ##tmpDocumentsFTS as tmpd on tmpd.documentID = d.documentID
			</cfif>
			where d.documenttypeid = 1

			<cfif arguments.strSearch.keyExists("jurisdiction") AND len(arguments.strSearch.jurisdiction)>
				and d.jurisdiction = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.strSearch.jurisdiction#">
			</cfif>
			<cfif arguments.strSearch.keyExists("fromdate") AND len(arguments.strSearch.fromdate)>
				and d.documentdate >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.strSearch.fromdate#">
			</cfif>
			<cfif arguments.strSearch.keyExists("todate") AND len(arguments.strSearch.todate)>
				and d.documentdate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.strSearch.todate# 23:59:59.997">
			</cfif>
			<cfif StructKeyExists(arguments.strSearch.strSettings,"restrictorgcode") and len(arguments.strSearch.strSettings.restrictorgcode.value)>
				<cfswitch expression="#arguments.strSearch.strSettings.restrictorgcode.action#">
					<cfcase value="include">
						and g.orgCode in (<cfqueryparam cfsqltype="cf_sql_varchar" list="yes" value="#arguments.strSearch.strSettings.restrictorgcode.value#">)
					</cfcase>
					<cfcase value="exclude">
						and g.orgCode not in (<cfqueryparam cfsqltype="cf_sql_varchar" list="yes" value="#arguments.strSearch.strSettings.restrictorgcode.value#">)
					</cfcase>
				</cfswitch>
			</cfif>
			<cfif StructKeyExists(arguments.strSearch.strSettings,"restrictbankid") and len(arguments.strSearch.strSettings.restrictbankid.value)>
				<cfswitch expression="#arguments.strSearch.strSettings.restrictbankid.action#">
					<cfcase value="include">
						and g.groupid in (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#arguments.strSearch.strSettings.restrictbankid.value#">)
					</cfcase>
					<cfcase value="exclude">
						and g.groupid not in (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#arguments.strSearch.strSettings.restrictbankid.value#">)
					</cfcase>
				</cfswitch>
			</cfif>;

			<cfswitch expression="#arguments.bucketType#">
				<cfcase value="depositions">
					<cfif arguments.strSearch.strSettings.search.deposedexpertonly>
						SELECT COUNT(distinct sd.depoMemberDataID) as itemCount
						FROM ##tmpDocumentIDs as tmp
						INNER JOIN searchMC.dbo.ts_subscriberDepositions as sd ON sd.depoDocumentID = tmp.documentID;
					<cfelse>
						SELECT COUNT(*) AS itemCount 
						FROM ##tmpDocumentIDs;
					</cfif>
				</cfcase>
				<cfcase value="expertConnect">
					DECLARE @nowDate datetime = getdate(), @dtTenYearsBack datetime, @dtFiveYearsBack datetime;
					SET @dtTenYearsBack = DATEADD(year, -10, @nowDate);
					SET @dtFiveYearsBack = DATEADD(year, -5, @nowDate);

					-- who have deposed an expert
					SELECT DISTINCT sd.depoMemberDataID
					FROM ##tmpDocumentIDs as tmp
					INNER JOIN dbo.depoDocuments as d ON d.documentID = tmp.documentID
					INNER JOIN searchMC.dbo.ts_subscriberDepositions as sd ON sd.depoDocumentID = tmp.documentID
					WHERE d.documentDate >= @dtTenYearsBack
						UNION
					-- contributed the matching depositions
					SELECT DISTINCT d.depoMemberDataID
					FROM ##tmpDocumentIDs as tmp
					INNER JOIN dbo.depoDocuments as d ON d.documentID = tmp.documentID
					WHERE d.dateEntered >= @dtTenYearsBack
						UNION
					-- who have viewed or downloaded that deposition
					SELECT DISTINCT d.depoMemberDataID
					FROM ##tmpDocumentIDs as tmp
					INNER JOIN platformStats.dbo.statsTSDocumentViews as d ON d.documentID = tmp.documentID
					WHERE d.dateEntered >= @dtFiveYearsBack
						UNION
					-- who have purchased that deposition
					SELECT DISTINCT dt.depoMemberDataID
					FROM ##tmpDocumentIDs as tmp
					INNER JOIN dbo.depoTransactions as dt ON dt.documentID = tmp.documentID
					WHERE dt.DatePurchased >= @dtTenYearsBack;
				</cfcase>
				<cfdefaultcase>
					SELECT COUNT(*) AS itemCount 
					FROM ##tmpDocumentIDs;
				</cfdefaultcase>
			</cfswitch>

			IF OBJECT_ID('tempdb..##tmpDocumentIDs') IS NOT NULL  
				DROP TABLE ##tmpDocumentIDs;
			<cfif len(arguments.strSearch.fulltextsql)>
				IF OBJECT_ID('tempdb..##tmpDocumentsFTS') IS NOT NULL  
					DROP TABLE ##tmpDocumentsFTS;
			</cfif>

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn { "qryResults":qryResults, "qryStat":qryStat }>
	</cffunction>

	<cffunction name="getNotLoggedInResults" access="private" output="false" returntype="struct" hint="searches and returns not logged in text">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="searchID" type="numeric" required="yes">
		<cfargument name="bucketID" type="numeric" required="yes">
		<cfargument name="strSearch" type="struct" required="yes">
		<cfargument name="viewDirectory" type="string" required="no" default="default">
		
		<cfset var local = StructNew()>
		<cfset local.objSearch = CreateObject("component","model.search.search")>

		<cfset local.strResultsCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>
		
		<cfset local.qrySearchVerbose = local.objSearch.getSearchVerbose(searchID=arguments.searchID)>
		<cfset local.strVerbose = application.objSearchTranslate.printVerboseString(local.qrySearchVerbose.searchVerbose,' ','; ')>
		<cfset local.stsearchVerboseNoName = trim(rereplacenocase(local.strVerbose,"Name:[^;]+;","","one"))>
		<cfif val(local.strResultsCount.itemCount)>
			<cfset local.strSearchSummary = getSearchSummary(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, 
				deposedExpertOnly=arguments.strSearch.strSettings.search.deposedexpertonly, relatedtestimonyonly=arguments.strSearch.strSettings.search.relatedtestimonyonly)>
		</cfif>

		<cfset local.strSearch = arguments.strSearch>
		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/depositions/notLoggedInResults.cfm">
		</cfsavecontent>
		<cfset local.stResults = ReReplace(local.stResults,'\s{2,}',' ','ALL')>		

		<cfset local.returnStruct = StructNew()>
		<cfset StructInsert(local.returnStruct,"resultmode","summary")>
		<cfif val(local.strResultsCount.itemCount)>
			<cfset StructInsert(local.returnStruct,"strchart",local.strSearchSummary.strchart)>
		<cfelse>
			<cfset StructInsert(local.returnStruct,"strchart",{})>
		</cfif>
		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfset StructInsert(local.returnStruct,"itemcount",local.strResultsCount.itemCount)>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="showNotAllowed" access="private" output="false" returntype="struct">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="bucketName" required="yes" type="string">
		<cfargument name="accessDeniedMessage" required="yes" type="string">
		<cfargument name="includeBucketCount" required="no" type="boolean" default="true">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>

		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/depositions/notAllowed.cfm">
		</cfsavecontent>

		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfif arguments.includeBucketCount>
			<cfset local.strCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
			<cfset StructInsert(local.returnStruct,"itemcount",local.strCount.itemCount)>
		<cfelse>
			<cfset StructInsert(local.returnStruct,"itemcount",-1)>
		</cfif>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getUserNoAccessResults" access="private" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="searchID" type="numeric" required="yes">
		<cfargument name="bucketID" type="numeric" required="yes">
		<cfargument name="strSearch" type="struct" required="yes">
		<cfargument name="viewDirectory" type="string" required="no" default="default">
		
		<cfset var local = StructNew()>
		<cfset local.objSearch = CreateObject("component","model.search.search")>

		<cfset local.strResultsCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>
		
		<cfset local.qrySearchVerbose = local.objSearch.getSearchVerbose(searchID=arguments.searchID)>
		<cfset local.strVerbose = application.objSearchTranslate.printVerboseString(local.qrySearchVerbose.searchVerbose,' ','; ')>
		<cfset local.stsearchVerboseNoName = trim(rereplacenocase(local.strVerbose,"Name:[^;]+;","","one"))>
		<cfif val(local.strResultsCount.itemCount)>
			<cfset local.strSearchSummary = getSearchSummary(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID,
				deposedExpertOnly=arguments.strSearch.strSettings.search.deposedexpertonly, 
				relatedtestimonyonly=arguments.strSearch.strSettings.search.relatedtestimonyonly)>
		</cfif>

		<cfsavecontent variable="local.inactiveUserInfo">
			<cfif variables.cfcuser_TrialSmithAllowed is not 1>
				<cfoutput>#showTrialsmithNotAllowed(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID).resulthtml#</cfoutput>
			<cfelseif variables.cfcuser_TrialSmithPending is 1>
				<cfoutput>#showTrialsmithPending(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID).resulthtml#</cfoutput>
			<cfelseif variables.cfcuser_TrialSmithExpired is 1>
				<cfoutput>#showTrialsmithExpired(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID).resulthtml#</cfoutput>
			<cfelseif variables.cfcuser_TrialSmithNoPlan is not 0>
				<cfoutput>#showTrialsmithNoPlan(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID).resulthtml#</cfoutput>
			<cfelseif arguments.strSearch.strSettings.search.deposedexpertonly AND variables.cfcuser_TSRights LT 3>
				<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/depositions/upgradeUserPlan.cfm">
			</cfif>
		</cfsavecontent>

		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/depositions/userNoAccessResults.cfm">
		</cfsavecontent>
		<cfset local.stResults = ReReplace(local.stResults,'\s{2,}',' ','ALL')>		

		<cfset local.returnStruct = StructNew()>
		<cfset StructInsert(local.returnStruct,"resultmode","summary")>
		<cfif val(local.strResultsCount.itemCount)>
			<cfset StructInsert(local.returnStruct,"strchart",local.strSearchSummary.strchart)>
		<cfelse>
			<cfset StructInsert(local.returnStruct,"strchart",{})>
		</cfif>
		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfset StructInsert(local.returnStruct,"itemcount",local.strResultsCount.itemCount)>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getResults" access="public" returntype="struct" output="no" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="mcproxy_siteID" required="no">
		<cfargument name="searchID" required="no">
		<cfargument name="bucketID" required="no">
		<cfargument name="startRow" required="no">
		<cfargument name="sortType" required="no">
		<cfargument name="filter" required="no" default="">
		<cfargument name="queryOnly" required="no" default="0">
		<cfargument name="viewDirectory" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["errmsg"] = 'Invalid Request'>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>
		
		<cfif 
			(NOT StructKeyExists(arguments, "mcproxy_siteID") OR NOT IsNumeric(arguments.mcproxy_siteID)) OR
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID)) OR
			(NOT StructKeyExists(arguments, "bucketID") OR NOT IsNumeric(arguments.bucketID)) OR
			(NOT StructKeyExists(arguments, "startRow") OR NOT IsNumeric(arguments.startRow)) OR
			NOT StructKeyExists(arguments, "sortType") OR
			(StructKeyExists(arguments, "queryOnly") AND NOT IsBoolean(arguments.queryOnly)) OR
			(NOT StructKeyExists(arguments, "viewDirectory") OR NOT ListFind(variables.viewDirectories, arguments.viewDirectory))>
				<cfset local.isValidArguments = false>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = doGetResults(siteID=arguments.mcproxy_siteID, searchID=arguments.searchID,
				bucketID=arguments.bucketID, startrow=arguments.startrow, sortType=arguments.sortType,
				filter=arguments.filter, queryOnly=arguments.queryOnly, viewDirectory=arguments.viewDirectory)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doGetResults" access="private" returntype="struct" output="no" hint="searches and returns a query result">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="startRow" required="yes" type="numeric">
		<cfargument name="sortType" required="yes" type="string">
		<cfargument name="filter" required="yes" type="string">
		<cfargument name="queryOnly" required="yes" type="boolean">
		<cfargument name="viewDirectory" required="yes" type="string">

		<cfset var local = StructNew()>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfset local.strSearch = prepSearch(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfset local.qryBucketInfo = getBucketInfo(bucketID=arguments.bucketID)>
		<cfset local.returnStruct = StructNew()>

		<!--- checks.
		1. logged in?
		2. RestrictToGroup?
		3. search accepted?
		4. TrialSmithAllowed?
		5. TrialSmithDisabled?
		6. TrialSmithPending?
		7. TrialSmithExpired?
		8. TrialSmithNoPlan?
		9. Pro-Plan or higher for Deposed Experts?
		--->
		<cfif NOT variables.cfcuser_isLoggedIn>
			<cfreturn getNotLoggedInResults(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, strSearch=local.strSearch, viewDirectory=arguments.viewDirectory)>
		<cfelseif NOT variables.cfcuser_isSiteAdmin AND val(local.qryBucketInfo.restrictToGroupID) GT 0 AND local.qryBucketInfo.isMemberInRestrictedGroup NEQ 1>
			<cfreturn showNotAllowed(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, bucketName=local.qryBucketInfo.bucketName, 
				accessDeniedMessage=local.qryBucketInfo.accessDeniedMessage, viewDirectory=arguments.viewDirectory)>
		<cfelseif NOT local.strSearch.searchAccepted>
			<cfreturn showSearchNotAccepted(searchID=arguments.searchID, bucketID=arguments.bucketID, bucketName=local.qryBucketInfo.bucketName, bucketSettings=local.qryBucketInfo.bucketSettings, viewDirectory=arguments.viewDirectory)>
		<cfelseif variables.cfcuser_TrialSmithAllowed is not 1>
			<cfreturn getUserNoAccessResults(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, strSearch=local.strSearch, viewDirectory=arguments.viewDirectory)>
		<cfelseif variables.cfcuser_TrialSmithDisabled is 1>
			<cfreturn showTrialsmithDisabled(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		<cfelseif variables.cfcuser_TrialSmithPending is 1 
					OR variables.cfcuser_TrialSmithExpired is 1 
					OR variables.cfcuser_TrialSmithNoPlan is not 0
					OR (local.strSearch.strSettings.search.deposedexpertonly AND variables.cfcuser_TSRights LT 3)>
			<cfreturn getUserNoAccessResults(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, strSearch=local.strSearch, viewDirectory=arguments.viewDirectory)>
		<cfelse>
			<cfset local.memberGroups = variables.cfcuser_TSgroups>
			<cfset local.qJoinedAndPublicGroups = cleanGroups()>
			<cfset local.stringJoinedAndPublicGroups = listprepend(valuelist(local.qJoinedAndPublicGroups.groupid),0)>
			<cfset local.stringJoinedGroupsOnly = listprepend(local.memberGroups,0)>
			<cfif not variables.cfcuser_isLoggedIn and StructKeyExists(local.strSearch.strSettings,"searchoverrideguest") and StructKeyExists(local.strSearch.strSettings.searchoverrideguest,"bankids")>
				<cfset local.stringJoinedAndPublicGroups = listappend(valuelist(local.qJoinedAndPublicGroups.groupid),local.strSearch.strSettings.searchoverrideguest.bankids)>
				<cfset local.stringJoinedGroupsOnly = listappend(local.memberGroups,local.strSearch.strSettings.searchoverrideguest.bankids)>
			</cfif>
			
			<cfset local.strCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>

			<cfquery name="local.qryResults" datasource="#application.dsn.tlasites_trialsmith.dsn#" result="local.qryStat">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @depomemberdataid int, @keywordsearchterms varchar(8000), @expertsearchterms varchar(8000), @casenamesearchterms varchar(8000),
					@depoDocumentIDList varchar(max), @itemCount int;
				DECLARE @tblGroups TABLE (groupID int PRIMARY KEY);
				DECLARE @tmpResults TABLE (documentid int, rank int, row int);
				set @depomemberdataid = <cfqueryparam value="#variables.cfcuser_depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;
				set @keywordsearchterms = <cfqueryparam value="#local.strSearch.keywords#" cfsqltype="CF_SQL_LONGVARCHAR">;
				set @expertsearchterms = <cfqueryparam value="#local.strSearch.expertname#" cfsqltype="CF_SQL_LONGVARCHAR">;
				set @casenamesearchterms = <cfqueryparam value="#local.strSearch.casename#" cfsqltype="CF_SQL_LONGVARCHAR">;

				declare @approvedStatusID int 
				select @approvedStatusID=statusID from  depoDocumentStatuses where statusName = 'Approved';

				IF OBJECT_ID('tempdb..##tmpDocumentsSearch') IS NOT NULL  
					DROP TABLE ##tmpDocumentsSearch;
				CREATE TABLE ##tmpDocumentsSearch (documentID int PRIMARY KEY, rank int, row int);
	
				<cfif len(local.strSearch.fulltextsql)>
					IF OBJECT_ID('tempdb..##tmpDocumentsFTS') IS NOT NULL  
						DROP TABLE ##tmpDocumentsFTS;
					CREATE TABLE ##tmpDocumentsFTS (documentID int PRIMARY KEY, rank int);

					INSERT INTO ##tmpDocumentsFTS (documentID, rank)
					#local.strSearch.fulltextsql#
				</cfif>

				INSERT INTO @tblGroups (groupID)
				SELECT distinct listitem
				FROM membercentral.dbo.fn_intListToTable(<cfqueryparam value="#local.stringJoinedAndPublicGroups#" cfsqltype="CF_SQL_VARCHAR">,',');

				INSERT INTO ##tmpDocumentsSearch (documentID, rank, row)
				select d.documentid, <cfif len(local.strSearch.keywords & local.strSearch.expertname & local.strSearch.casename)>tmpd.rank<cfelse>1000</cfif> as rank,
					ROW_NUMBER() OVER (ORDER BY 
						<cfswitch expression="#arguments.sortType#">
							<cfcase value="date">
								d.DocumentDate desc
							</cfcase>
							<cfcase value="witness">
								d.ExpertName
							</cfcase>
							<cfcase value="case">
								d.style
							</cfcase>
							<cfdefaultcase>
								<cfset arguments.sortType = "rank">
								<cfif len(local.strSearch.keywords & local.strSearch.expertname & local.strSearch.casename)>tmpd.rank desc, </cfif>d.DocumentDate desc
							</cfdefaultcase>
						</cfswitch>
						) as row
				from dbo.depoDocuments as d 
				inner join dbo.depoDocumentStatusHistory as dsh 
					on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
					and dsh.statusID = @approvedStatusID
				inner join dbo.depogroups as g on d.groupid = g.groupid
				inner join @tblGroups as tblg on tblg.groupID = g.groupID
				<cfif len(local.strSearch.fulltextsql)>
					inner join ##tmpDocumentsFTS as tmpd on tmpd.documentID = d.documentID
				</cfif>
				where d.documenttypeid = 1
				<cfif len(local.strSearch.jurisdiction)>
					and d.jurisdiction = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.strSearch.jurisdiction#">
				</cfif>
				<cfif len(local.strSearch.fromdate)>
					and d.documentdate >= <cfqueryparam cfsqltype="cf_sql_date" value="#local.strSearch.fromdate#">
				</cfif>
				<cfif len(local.strSearch.todate)>
					and d.documentdate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.strSearch.todate# 23:59:59.997">
				</cfif>
				<cfif StructKeyExists(local.strSearch.strSettings,"restrictorgcode") and len(local.strSearch.strSettings.restrictorgcode.value)>
					<cfswitch expression="#local.strSearch.strSettings.restrictorgcode.action#">
						<cfcase value="include">
							and g.orgCode in (<cfqueryparam cfsqltype="cf_sql_varchar" list="yes" value="#local.strSearch.strSettings.restrictorgcode.value#">)
						</cfcase>
						<cfcase value="exclude">
							and g.orgCode not in (<cfqueryparam cfsqltype="cf_sql_varchar" list="yes" value="#local.strSearch.strSettings.restrictorgcode.value#">)
						</cfcase>
					</cfswitch>
				</cfif>
				<cfif StructKeyExists(local.strSearch.strSettings,"restrictbankid") and len(local.strSearch.strSettings.restrictbankid.value)>
					<cfswitch expression="#local.strSearch.strSettings.restrictbankid.action#">
						<cfcase value="include">
							and g.groupid in (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#local.strSearch.strSettings.restrictbankid.value#">)
						</cfcase>
						<cfcase value="exclude">
							and g.groupid not in (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#local.strSearch.strSettings.restrictbankid.value#">)
						</cfcase>
					</cfswitch>
				</cfif>;

				SET @itemCount = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strCount.itemcount#">;

				<cfif len(local.strSearch.fulltextsql)>
					IF OBJECT_ID('tempdb..##tmpDocumentsFTS') IS NOT NULL  
						DROP TABLE ##tmpDocumentsFTS;
				</cfif>

				<cfif local.strSearch.strSettings.search.deposedexpertonly>
					IF OBJECT_ID('tempdb..##tmpDepoMembers') IS NOT NULL  
						DROP TABLE ##tmpDepoMembers;
					CREATE TABLE ##tmpDepoMembers (depomemberDataID int PRIMARY KEY, docCount int, docList varchar(max), row int);

					INSERT INTO ##tmpDepoMembers (depomemberDataID, docCount, docList, row)
					SELECT depoMemberDataID, docCount, docList, ROW_NUMBER() OVER (ORDER by docCount desc, depoMemberDataID asc) as row
					FROM (
						SELECT depoMemberDataID, count(depoDocumentID) as docCount, STRING_AGG(depoDocumentID,'|') as docList
						from (
							SELECT distinct sd.depoMemberDataID, sd.depoDocumentID
							FROM ##tmpDocumentsSearch as tmp
							INNER JOIN searchMC.dbo.ts_subscriberDepositions as sd ON sd.depoDocumentID = tmp.documentID
						) as tmp
						GROUP BY depoMemberDataID
					) as outertmp;

					-- return top x
					select TOP (<cfqueryparam value="#variables.thisBucketMaxPerPage#" cfsqltype="CF_SQL_INTEGER">) @itemCount as itemCount, 
						m.depoMemberDataID, m.firstname, m.lastname, m.billingfirm, m.email, m.phone, 
						(select Name from States where code = m.billingState) as stateName,
						membercentral.dbo.fn_RegExReplace(m.phone,'[^0-9]','') as phoneNumOnly, 
						m.billingCity, m.billingState, tmp.docCount, tmp.docList
					FROM ##tmpDepoMembers as tmp
					INNER JOIN dbo.depomemberdata AS m on m.depoMemberDataID = tmp.depoMemberDataID
					WHERE tmp.row >= <cfqueryparam value="#arguments.startrow#" cfsqltype="CF_SQL_INTEGER">
					ORDER BY tmp.row;

					IF OBJECT_ID('tempdb..##tmpDepoMembers') IS NOT NULL  
						DROP TABLE ##tmpDepoMembers;
				<cfelse>
					INSERT INTO @tmpResults (documentid, rank, row)
					Select TOP (<cfqueryparam value="#min(arguments.startrow + variables.thisBucketMaxPerPage - 1,variables.thisBucketMaxShown)#" cfsqltype="CF_SQL_INTEGER">) 
						documentID, rank, row
					FROM ##tmpDocumentsSearch
					ORDER BY row;

					-- return top x
					select TOP (<cfqueryparam value="#variables.thisBucketMaxPerPage#" cfsqltype="CF_SQL_INTEGER">) @itemCount as itemCount, 
						d.documentid, d.pages, d.expertname, d.documentdate, d.style, d.state, ct.description as causedesc, d.notes, d.jurisdiction,
						dfo.dateLastModified as uploadpdfdate, m.email, m.firstname, m.lastname, m.billingfirm, m.BillingCity, m.BillingState, m.phone,
						(select Name from States where code = d.jurisdiction) as stateName,
						owned = CASE
							WHEN d.depomemberdataid = @depomemberdataid then 1
							WHEN EXISTS (select documentID from dbo.depoPermissions where depomemberdataid = @depomemberdataid and documentid = d.documentid) then 1
							WHEN dbo.fn_Documents_hasContributed(d.documentid,@depomemberdataid) = 1 THEN 1
							WHEN dbo.fn_Documents_checkPermissionsToDoc(d.documentid,@depomemberdataid) = 1 THEN 1
							ELSE 0
							END,
						contributed = CASE WHEN dbo.fn_Documents_hasContributed(d.documentid,@depomemberdataid) = 1 THEN 1 ELSE 0 END,
						d.depomemberdataid,
						inCart = CASE
							WHEN EXISTS (select documentid from dbo.documentcart where documentid = d.documentid and depomemberdataid = @depomemberdataid and itemTypeID = #variables.thisBucketCartItemTypeID#) then 1
							ELSE 0
							END
						<cfif local.strSearch.strSettings.search.deposedexpertonly>, @depoDocumentIDList AS depoDocumentIDList</cfif>
					from @tmpResults as tmp
					inner join dbo.depodocuments AS d on d.documentid = tmp.documentid
					inner join dbo.depoCaseTypes as ct on d.caseTypeId = ct.caseTypeID
					inner join dbo.depomemberdata as m on m.depomemberdataid = d.depomemberdataid
					left outer join dbo.depoDocumentsFilesOnline as dfo on dfo.documentID = d.documentID and dfo.fileType = 'pdf'
					where tmp.row >= <cfqueryparam value="#arguments.startrow#" cfsqltype="CF_SQL_INTEGER">
					ORDER BY tmp.row;
				</cfif>

				IF OBJECT_ID('tempdb..##tmpDocumentsSearch') IS NOT NULL  
					DROP TABLE ##tmpDocumentsSearch;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif local.strSearch.strSettings.search.deposedexpertonly>
				<cfset local.metric = "deposed.getResults">
			<cfelseif local.strSearch.strSettings.search.relatedtestimonyonly>
				<cfset local.metric = "deposrelated.getResults">
			<cfelse>
				<cfset local.metric = "#variables.thisBucketType#.getResults">
			</cfif>
			<cfset saveStats(searchID=arguments.searchID, metric=local.metric, ms=local.qryStat.ExecutionTime)>

			<cfset local.dropboxAppKey = "">
			<cfif structKeyExists(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode), "dropboxappkey") and len(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).dropboxappkey)>
				<cfset local.dropboxAppKey = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).dropboxappkey>
			</cfif>

			<cfif arguments.queryOnly eq 0>
				<!--- adjust maxperpage based on actual data if necessary and get page variables --->
				<cfscript>
				local.MaxPerPage = iif(local.qryResults.recordcount gt variables.thisBucketMaxPerPage,variables.thisBucketMaxPerPage,local.qryResults.recordcount);
				if (local.MaxPerPage gt 0) {
					local.NumTotalPages = Ceiling(local.qryResults.itemCount / variables.thisBucketMaxPerPage);
					local.NumCurrentPage = int((int(arguments.startrow) + variables.thisBucketMaxPerPage - 1) / variables.thisBucketMaxPerPage);
				} else {
					local.NumTotalPages = 0;
					local.NumCurrentPage = 0;
				}
				</cfscript>
				
				<!--- return content --->
				<cfsavecontent variable="local.stResults">
					<cfif local.strSearch.strSettings.search.deposedexpertonly>
						<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/depositions/listExperts.cfm">
					<cfelse>
						<cfif local.strSearch.strSettings.search.relatedtestimonyonly AND variables.cfcuser_membertype EQ 1 >
							<cfif val(local.strCount.itemCount)>
								<cfset local.strSearchSummary = getSearchSummary(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, 
																	deposedExpertOnly=local.strSearch.strSettings.search.deposedexpertonly, relatedtestimonyonly=local.strSearch.strSettings.search.relatedtestimonyonly)>
							</cfif>
							<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/depositions/showBasicPlanResults.cfm">
						<cfelse>
							<cfquery name="local.qPurchaseCredit" datasource="#application.dsn.tlasites_trialsmith.dsn#">
								SET NOCOUNT ON;
								SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

								select firmplan 
								from dbo.fn_Documents_getPurchaseCredits(<cfqueryparam value="#variables.cfcuser_depomemberdataid#" cfsqltype="CF_SQL_INTEGER">);

								SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
							</cfquery>

							<cfquery name="local.qryPCAmount" datasource="#application.dsn.tlasites_trialsmith.dsn#">
								SET NOCOUNT ON;
								SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

								SELECT ISNULL(SUM(pc.PurchaseCreditAmount), 0) as pctotal
								FROM dbo.PurchaseCredits as PC
								INNER JOIN dbo.depomemberdata as M ON PC.DepoMemberDataID = M.DepoMemberDataID
								WHERE 
								<cfif len(local.qPurchaseCredit.firmplan)>
									PC.depoMemberDataID IN (
										SELECT fpl2.depoMemberDataID
										FROM dbo.tlaFirmPlanLink AS fpl2 
										INNER JOIN dbo.tlaFirmPlanLink AS fpl ON fpl2.firmPlanID = fpl.firmPlanID
										WHERE fpl.depoMemberDataID = <cfqueryparam value="#variables.cfcuser_depomemberdataid#" cfsqltype="CF_SQL_INTEGER">
									)
								<cfelse>
									PC.DepoMemberDataID = <cfqueryparam value="#variables.cfcuser_depomemberdataid#" cfsqltype="CF_SQL_INTEGER">
								</cfif>;

								SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
							</cfquery>
							
							<cfset local.qryCartCount = CreateObject("component","model.viewcart.viewcart").getDocuments(
								depomemberdataid=variables.cfcuser_depomemberdataid,
								membertype=val(application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='memberType')),
								billingstate=application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='billingState'),
								billingzip=application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='billingZip'),
								orgcode=application.objsiteInfo.getSiteInfo(session.mcStruct.siteCode).siteID)>
								
							<cfset local.totalCartPrice = 0>
							<cfif local.qryCartCount.recordCount>
								<cfquery name="local.qrySum" dbtype="query">
									SELECT SUM(pPrice) AS totalCartPrice
									FROM [local].qryCartCount
								</cfquery>
								<cfset local.totalCartPrice = local.qrySum.totalCartPrice>
							</cfif>
							
							<cfset local.pcRemainingtotal = (local.qryPCAmount.pctotal - local.totalCartPrice)>
							<cfset local.pcRemainingtotal = (local.pcRemainingtotal LT 0 ? 0 : local.pcRemainingtotal)>
							
							<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/depositions/results.cfm">
						</cfif>
					</cfif>
				</cfsavecontent>
			<cfelse>
				<cfset local.stResults = "">
			</cfif>

			<cfset local.stResults = ReReplace(local.stResults,'\s{2,}',' ','ALL')>
			<cfif local.strSearch.strSettings.search.relatedtestimonyonly AND variables.cfcuser_membertype EQ 1 >
				<cfset StructInsert(local.returnStruct,"resultmode","summary")>
				<cfif val(local.strCount.itemCount)>
					<cfset StructInsert(local.returnStruct,"strchart",local.strSearchSummary.strchart)>
				<cfelse>
					<cfset StructInsert(local.returnStruct,"strchart",{})>
				</cfif>
			</cfif>	
			<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
			<cfif arguments.queryOnly eq 1>
				<cfset StructInsert(local.returnStruct,"resultqry",local.qryResults)>
			<cfelse>
				<cfset StructInsert(local.returnStruct,"numTotalPages",val(local.NumTotalPages))>
				<cfset StructInsert(local.returnStruct,"numCurrentPage",val(local.NumCurrentPage))>
			</cfif>
			<cfset StructInsert(local.returnStruct,"thisBucketCartItemTypeID",variables.thisBucketCartItemTypeID)>
			<cfset StructInsert(local.returnStruct,"itemcount",val(local.qryResults.itemCount))>
			<cfset StructInsert(local.returnStruct,"success",true)>
		
			<cfreturn local.returnStruct>
		</cfif>
	</cffunction>

	<cffunction name="saveSearchForm" access="public" output="no" returntype="numeric" hint="saves the form vars to a search and returns the searchid">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="formvars" required="yes" type="struct">

		<cfset var local = structNew()>
		
		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfif StructKeyExists(arguments.formvars,"s_jurisdiction") and listlen(arguments.formvars.s_jurisdiction)>
			<cfquery name="local.statesLookup" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				select name 
				from dbo.states
				where code in (<cfqueryparam cfsqltype="cf_sql_varchar" list="yes" value="#trim(arguments.formvars.s_jurisdiction)#">)
			</cfquery>
			<cfset local.expandedStates = xmlFormat(valuelist(local.statesLookup.name,"^"))>
			<cfset local.xmlStates = xmlFormat(trim(arguments.formvars.s_jurisdiction))>
		<cfelse>
			<cfset local.expandedStates = "">
			<cfset local.xmlStates = "">
		</cfif>

		<cfsavecontent variable="local.xmlSearch">
			<cfoutput>
			<search xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2">
				<bid><cfif StructKeyExists(arguments.formvars,"bid")>#val(arguments.formvars.bid)#</cfif></bid>
				<s_casename><cfif StructKeyExists(arguments.formvars,"s_casename")>#xmlFormat(trim(arguments.formvars.s_casename))#</cfif></s_casename>
				<cfif StructKeyExists(arguments.formvars,"s_depodatefrom") and isValid("date",arguments.formvars.s_depodatefrom)>
					<s_depodatefrom>#DateFormat(arguments.formvars.s_depodatefrom,"yyyy-mm-dd")#Z</s_depodatefrom>
				<cfelse>
					<s_depodatefrom xsi:nil="true" />
				</cfif>
				<cfif StructKeyExists(arguments.formvars,"s_depodateto") and isValid("date",arguments.formvars.s_depodateto)>
					<s_depodateto>#DateFormat(arguments.formvars.s_depodateto,"yyyy-mm-dd")#Z</s_depodateto>
				<cfelse>
					<s_depodateto xsi:nil="true" />
				</cfif>
				<s_fname><cfif StructKeyExists(arguments.formvars,"s_fname")>#xmlFormat(trim(replace(arguments.formvars.s_fname,chr(34),'','ALL')))#</cfif></s_fname>
				<s_jurisdiction expanded="#local.expandedStates#">#local.xmlStates#</s_jurisdiction>
				<s_lname><cfif StructKeyExists(arguments.formvars,"s_lname")>#xmlFormat(trim(replace(arguments.formvars.s_lname,chr(34),'','ALL')))#</cfif></s_lname>
				<s_key_all><cfif StructKeyExists(arguments.formvars,"s_key_all")>#xmlFormat(trim(arguments.formvars.s_key_all))#</cfif></s_key_all>
				<s_key_one><cfif StructKeyExists(arguments.formvars,"s_key_one")>#xmlFormat(trim(arguments.formvars.s_key_one))#</cfif></s_key_one>
				<s_key_phrase><cfif StructKeyExists(arguments.formvars,"s_key_phrase")>#xmlFormat(trim(arguments.formvars.s_key_phrase))#</cfif></s_key_phrase>
				<s_key_x><cfif StructKeyExists(arguments.formvars,"s_key_x")>#xmlFormat(trim(arguments.formvars.s_key_x))#</cfif></s_key_x>
			</search>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.searchID = saveSearchXML(val(arguments.formvars.bid),local.xmlSearch)>
		
		<cfreturn local.searchID>
	</cffunction>

	<cffunction name="cleanGroups" access="private" returntype="query" output="no" hint="returns query of TS groups user is authorized to search">
		<cfset var local = StructNew()>
		<cfquery name="local.qryGroups" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			select groupid, description
			from dbo.depogroups
		</cfquery>
		<cfreturn local.qryGroups>
	</cffunction>

	<cffunction name="getSearchSummary" access="private" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="searchID" type="numeric" required="true">
		<cfargument name="bucketID" type="numeric" required="true">
		<cfargument name="deposedExpertOnly" type="boolean" required="true">
		<cfargument name="relatedtestimonyonly" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "strchart":{} }>
		
		<cfif NOT variables.cfcuser_isLoggedIn 
			OR (variables.cfcuser_TrialSmithPending IS 1 
				OR variables.cfcuser_TrialSmithExpired IS 1 
				OR variables.cfcuser_TrialSmithNoPlan IS NOT 0
				OR (arguments.deposedExpertOnly AND variables.cfcuser_TSRights LT 3)
				OR (arguments.relatedtestimonyonly AND variables.cfcuser_membertype EQ 1))>
			
			<cfset local.strSearch = prepSearch(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
			<cfset local.memberGroups = variables.cfcuser_TSgroups>
			<cfset local.qJoinedAndPublicGroups = cleanGroups()>
			<cfset local.stringJoinedAndPublicGroups = listprepend(valuelist(local.qJoinedAndPublicGroups.groupid),0)>
			<cfset local.stringJoinedGroupsOnly = listprepend(local.memberGroups,0)>
			<cfif structKeyExists(local.strSearch.strSettings,"searchoverrideguest") and structKeyExists(local.strSearch.strSettings.searchoverrideguest,"bankids")>
				<cfset local.stringJoinedAndPublicGroups = listappend(valuelist(local.qJoinedAndPublicGroups.groupid),local.strSearch.strSettings.searchoverrideguest.bankids)>
				<cfset local.stringJoinedGroupsOnly = listappend(local.memberGroups,local.strSearch.strSettings.searchoverrideguest.bankids)>
			</cfif>

			<cfquery name="local.qryResults" datasource="#application.dsn.tlasites_trialsmith.dsn#" result="local.qryStat">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpDocuments') IS NOT NULL  
					DROP TABLE ##tmpDocuments;
				IF OBJECT_ID('tempdb..##tmpItemStates') IS NOT NULL  
					DROP TABLE ##tmpItemStates;
				CREATE TABLE ##tmpDocuments (documentID int PRIMARY KEY, documentDate datetime);
				CREATE TABLE ##tmpItemStates (stateCode varchar(2), itemCount int);

				declare @approvedStatusID int 
				select @approvedStatusID=statusID from trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

				DECLARE @depomemberdataid int, @keywordsearchterms varchar(8000), @expertsearchterms varchar(8000), @casenamesearchterms varchar(8000), 
					@locationDataXML xml, @itemLocDataXML xml, @USStatesXML xml, @totalCount int;
				DECLARE @tmpUSStates TABLE (stateCode varchar(4), stateName varchar(40), orderPref int);
				DECLARE @tblGroups TABLE (groupID int PRIMARY KEY);
				
				SET @depomemberdataid = <cfqueryparam value="#variables.cfcuser_depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;
				SET @keywordsearchterms = <cfqueryparam value="#local.strSearch.keywords#" cfsqltype="CF_SQL_LONGVARCHAR">;
				SET @expertsearchterms = <cfqueryparam value="#local.strSearch.expertname#" cfsqltype="CF_SQL_LONGVARCHAR">;
				SET @casenamesearchterms = <cfqueryparam value="#local.strSearch.casename#" cfsqltype="CF_SQL_LONGVARCHAR">;

				INSERT INTO @tmpUSStates (stateCode, stateName, orderPref)
				select [code], [Name], orderPref
				from memberCentral.dbo.ams_states
				where countryID = 1;

				<cfif len(local.strSearch.fulltextsql)>
					IF OBJECT_ID('tempdb..##tmpDocumentsFTS') IS NOT NULL  
						DROP TABLE ##tmpDocumentsFTS;
					CREATE TABLE ##tmpDocumentsFTS (documentID int PRIMARY KEY, rank int);

					INSERT INTO ##tmpDocumentsFTS (documentID, rank)
					#local.strSearch.fulltextsql#
				</cfif>

				INSERT INTO @tblGroups (groupID)
				SELECT distinct listitem
				FROM membercentral.dbo.fn_intListToTable(<cfqueryparam value="#local.stringJoinedAndPublicGroups#" cfsqltype="CF_SQL_VARCHAR">,',');
	
				INSERT INTO ##tmpDocuments (documentID, documentDate)
				select d.documentID, d.documentDate
				from dbo.depoDocuments as d 
				inner join dbo.depoDocumentStatusHistory as dsh on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
					and dsh.statusID = @approvedStatusID
				inner join dbo.depogroups as g on d.groupid = g.groupid
				inner join @tblGroups as tblg on tblg.groupID = g.groupID
				<cfif len(local.strSearch.fulltextsql)>
					inner join ##tmpDocumentsFTS as tmpd on tmpd.documentID = d.documentID
				</cfif>
				where d.documenttypeid = 1
				<cfif len(local.strSearch.jurisdiction)>
					and d.jurisdiction = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSearch.jurisdiction#">
				</cfif>
				<cfif len(local.strSearch.fromdate)>
					and d.documentDate >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.strSearch.fromdate#">
				</cfif>
				<cfif len(local.strSearch.todate)>
					and d.documentDate <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.strSearch.todate# 23:59:59.997">
				</cfif>
				<cfif StructKeyExists(local.strSearch.strSettings,"restrictorgcode") and len(local.strSearch.strSettings.restrictorgcode.value)>
					<cfswitch expression="#local.strSearch.strSettings.restrictorgcode.action#">
						<cfcase value="include">
							and g.orgCode in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" list="yes" value="#local.strSearch.strSettings.restrictorgcode.value#">)
						</cfcase>
						<cfcase value="exclude">
							and g.orgCode not in (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" list="yes" value="#local.strSearch.strSettings.restrictorgcode.value#">)
						</cfcase>
					</cfswitch>
				</cfif>
				<cfif StructKeyExists(local.strSearch.strSettings,"restrictbankid") and len(local.strSearch.strSettings.restrictbankid.value)>
					<cfswitch expression="#local.strSearch.strSettings.restrictbankid.action#">
						<cfcase value="include">
							and g.groupid in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#local.strSearch.strSettings.restrictbankid.value#">)
						</cfcase>
						<cfcase value="exclude">
							and g.groupid not in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#local.strSearch.strSettings.restrictbankid.value#">)
						</cfcase>
					</cfswitch>
				</cfif>;

				<cfif len(local.strSearch.fulltextsql)>
					IF OBJECT_ID('tempdb..##tmpDocumentsFTS') IS NOT NULL  
						DROP TABLE ##tmpDocumentsFTS;
				</cfif>

				<cfif arguments.deposedExpertOnly>
					IF OBJECT_ID('tempdb..##tmpDeposedExperts') IS NOT NULL  
						DROP TABLE ##tmpDeposedExperts;
					CREATE TABLE ##tmpDeposedExperts (depoMemberDataID int, billingState varchar(50));

					INSERT INTO ##tmpDeposedExperts (depoMemberDataID, billingState)
					SELECT DISTINCT m.depoMemberDataID, m.billingState
					FROM dbo.depomemberdata AS m
					INNER JOIN searchMC.dbo.ts_subscriberDepositions AS sd ON sd.depoMemberDataID = m.depoMemberDataID
					INNER JOIN ##tmpDocuments AS tmp ON tmp.documentID = sd.depoDocumentID

					SET @totalCount = @@ROWCOUNT;

					INSERT INTO ##tmpItemStates (stateCode, itemCount)
					select s.stateCode, count(tmp.depoMemberDataID)
					from ##tmpDeposedExperts as tmp
					inner join @tmpUSStates as s on s.stateCode = tmp.billingState
					group by s.stateCode;

					IF OBJECT_ID('tempdb..##tmpDeposedExperts') IS NOT NULL  
						DROP TABLE ##tmpDeposedExperts;
				<cfelse>
					SELECT @totalCount = COUNT(documentID)
					FROM ##tmpDocuments;

					INSERT INTO ##tmpItemStates (stateCode, itemCount)
					select s.stateCode, count(tmp.documentID)
					from ##tmpDocuments as tmp
					inner join dbo.depoDocuments as d on d.DocumentID = tmp.documentID
					inner join @tmpUSStates as s on s.stateCode = d.jurisdiction
					group by s.stateCode;
				</cfif>

				SELECT @itemLocDataXML = ISNULL((
					select statecode, itemcount
					from ##tmpItemStates as rowdata
					order by statecode
					FOR XML AUTO, ELEMENTS, ROOT('itemlocation')
				),'<itemlocation/>');

				SELECT @USStatesXML = ISNULL((
					select statecode, statename
					from @tmpUSStates as rowdata
					order by orderPref, statecode
					FOR XML AUTO, ELEMENTS, ROOT('usstates')
				),'<usstates/>');

				SELECT @locationDataXML = ISNULL((
					select @itemLocDataXML, @USStatesXML 
					for xml path ('locdata')
				),'<locdata/>');

				SELECT @totalCount as totalCount, @locationDataXML as locationDataXML;
				
				IF OBJECT_ID('tempdb..##tmpDocuments') IS NOT NULL  
					DROP TABLE ##tmpDocuments;
				IF OBJECT_ID('tempdb..##tmpItemStates') IS NOT NULL  
					DROP TABLE ##tmpItemStates;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif local.qryResults.recordCount>
				<cfset local.returnStruct = {
					"strchart": { 
						"arrlocation":[],
						"bucket":"depositions",
						"totalcount":local.qryResults.totalCount,
						"locationobjtitle": arguments.deposedExpertOnly
											? "Location of Attorneys (#numberFormat(local.qryResults.totalCount,",")# Total)"
											: "Location of Depositions (#numberFormat(local.qryResults.totalCount,",")# Total)",
						"arrusstates":[]
					}
				}>
				<cfif len(local.qryResults.locationDataXML) AND arrayLen(XMLSearch(local.qryResults.locationDataXML,'/locdata/child::node()'))>
					<cfset var arrStateCodes = XMLSearch(local.qryResults.locationDataXML,'/locdata/itemlocation/rowdata/statecode')>
					<cfset var arrStateItemCount = XMLSearch(local.qryResults.locationDataXML,'/locdata/itemlocation/rowdata/itemcount')>
					<cfset local.returnStruct['strchart']['arrlocation'] =  arrStateCodes.map(
						function(item,index) { 
							return { "statecode":arguments.item.xmlText, "value":val(arrStateItemCount[arguments.index].xmlText) }; 
						}
					)>
					<cfset var arrUSStateCodes = XMLSearch(local.qryResults.locationDataXML,'/locdata/usstates/rowdata/statecode')>
					<cfset var arrUSStateNames = XMLSearch(local.qryResults.locationDataXML,'/locdata/usstates/rowdata/statename')>
					<cfset local.returnStruct['strchart']['arrusstates'] =  arrUSStateCodes.map(
						function(item,index) { 
							return { "statecode":arguments.item.xmlText, "statename":arrUSStateNames[arguments.index].xmlText }; 
						}
					)>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getExpertDepositions" access="public" returntype="struct" output="no" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="encodedString" required="false">
		<cfargument name="startRow" required="false">
		<cfargument name="searchID" required="false">
		<cfargument name="viewDirectory" required="false">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["errmsg"] = 'Invalid Request'>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>
		
		<cfif (NOT StructKeyExists(arguments, "encodedString") OR NOT len(arguments.encodedString)) OR
			(NOT StructKeyExists(arguments, "startRow") OR NOT IsNumeric(arguments.startRow)) OR
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID)) OR
			(NOT StructKeyExists(arguments, "viewDirectory") OR NOT ListFind(variables.viewDirectories, arguments.viewDirectory))>
				<cfset local.isValidArguments = false>
		<cfelse>
			<cftry>
				<cfset local.decryptedString = decrypt(toString(toBinary(arguments.encodedString)),"M3m18eR_CenTR@l")>
				<cfif listLen(local.decryptedString,'^~~^') NEQ 2>
					<cfthrow>
				</cfif>
				<cfset local.depoMemberDataID = getToken(local.decryptedString,1,'^~~^')>
				<cfset local.docList = getToken(local.decryptedString,2,'^~~^').replace('|',',','all')>
				<cfif NOT isNumeric(local.depoMemberDataID) OR NOT listLen(local.docList)>
					<cfset local.isValidArguments = false>
				</cfif>
			<cfcatch>
				<cfset local.isValidArguments = false>
			</cfcatch>
			</cftry>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = doGetExpertDepositions(depoMemberDataID=local.depoMemberDataID, docList=local.docList, startRow=arguments.startRow, searchID=arguments.searchID, viewDirectory=arguments.viewDirectory)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doGetExpertDepositions" access="private" output="false" returntype="struct">
		<cfargument name="depoMemberDataID" type="numeric" required="true">
		<cfargument name="docList" type="string" required="true">
		<cfargument name="startRow" type="numeric" required="true">
		<cfargument name="searchID" type="numeric" required="true">
		<cfargument name="viewDirectory" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 
			"success":true,
			"depositionshtml":"",
			"startPos":arguments.startRow, 
			"count":variables.thisBucketMaxPerPage,
			"totalCount":0, 
			"numTotalPages":0, 
			"numCurrentPage":0, 
			"startPage":1, 
			"endPage":1, 
			"maxPage":5 
		}>

		<cfset local.objDocument = CreateObject('component','model.system.platform.tsDocument')>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfquery name="local.qryDepositions" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpDocumentIDs') IS NOT NULL  
				DROP TABLE ##tmpDocumentIDs;
			IF OBJECT_ID('tempdb..##tmpDocuments') IS NOT NULL  
				DROP TABLE ##tmpDocuments;
			CREATE TABLE ##tmpDocumentIDs (documentID int PRIMARY KEY);
			CREATE TABLE ##tmpDocuments (documentID int PRIMARY KEY, row int);

			declare @approvedStatusID int 
			select @approvedStatusID=statusID from trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

			DECLARE @depomemberdataid int, @loggedInDepomemberdataID int, @totalCount int;
			SET @depomemberdataid = <cfqueryparam value="#arguments.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">;
			SET @loggedInDepomemberdataID = <cfqueryparam value="#variables.cfcuser_depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;

			INSERT INTO ##tmpDocumentIDs (documentID)
			SELECT DISTINCT listitem
			FROM membercentral.dbo.fn_intListToTable(<cfqueryparam value="#arguments.docList#" cfsqltype="CF_SQL_LONGVARCHAR">,',');

			INSERT INTO ##tmpDocuments (documentID, row)
			SELECT d.documentID, ROW_NUMBER() OVER (ORDER BY d.documentDate DESC)
			FROM dbo.depoDocuments as d 
			inner join dbo.depoDocumentStatusHistory as dsh on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
				and dsh.statusID = @approvedStatusID
			INNER JOIN ##tmpDocumentIDs as tmpd on tmpd.documentID = d.documentID
			INNER JOIN searchMC.dbo.ts_subscriberDepositions AS sd ON sd.depoDocumentID = d.documentID
			WHERE sd.depoMemberDataID = @depomemberdataid
			AND d.documenttypeid = 1
			group by d.documentID, d.documentDate;

			SET @totalCount = @@ROWCOUNT;

			SELECT TOP (<cfqueryparam value="#variables.thisBucketMaxPerPage#" cfsqltype="CF_SQL_INTEGER">) @totalCount as totalCount, 
				d.documentid, d.pages, d.expertname, d.documentdate, d.style, d.state, ct.description as causedesc, d.notes, d.jurisdiction,
				dfo.dateLastModified as uploadpdfdate, m.email, m.firstname, m.lastname, m.billingfirm, m.BillingCity, m.BillingState, m.phone,
				(select Name from States where code = d.jurisdiction) as stateName,
				owned = CASE
					WHEN d.depomemberdataid = @loggedInDepomemberdataID then 1
					WHEN EXISTS (select documentID from dbo.depoPermissions where depomemberdataid = @loggedInDepomemberdataID and documentid = d.documentid) then 1
					WHEN dbo.fn_Documents_hasContributed(d.documentid,@loggedInDepomemberdataID) = 1 THEN 1
					WHEN dbo.fn_Documents_checkPermissionsToDoc(d.documentid,@loggedInDepomemberdataID) = 1 THEN 1
					ELSE 0
					END,
				contributed = CASE WHEN dbo.fn_Documents_hasContributed(d.documentid,@loggedInDepomemberdataID) = 1 THEN 1 ELSE 0 END,
				d.depomemberdataid,
				inCart = CASE
					WHEN EXISTS (select documentid from dbo.documentcart where documentid = d.documentid and depomemberdataid = @loggedInDepomemberdataID and itemTypeID = #variables.thisBucketCartItemTypeID#) then 1
					ELSE 0
					END
			FROM ##tmpDocuments as tmp
			INNER JOIN dbo.depodocuments AS d on d.documentid = tmp.documentid
			INNER JOIN dbo.depoCaseTypes as ct on d.caseTypeId = ct.caseTypeID
			INNER JOIN dbo.depomemberdata as m on m.depomemberdataid = d.depomemberdataid
			left outer join dbo.depoDocumentsFilesOnline as dfo on dfo.documentID = d.documentID and dfo.fileType = 'pdf'
			WHERE tmp.row >= <cfqueryparam value="#arguments.startrow#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY tmp.row;
			
			IF OBJECT_ID('tempdb..##tmpDocumentIDs') IS NOT NULL  
				DROP TABLE ##tmpDocumentIDs;
			IF OBJECT_ID('tempdb..##tmpDocuments') IS NOT NULL  
				DROP TABLE ##tmpDocuments;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryDepositions.recordCount>
			<cfset local.returnStruct.totalCount = local.qryDepositions.totalCount>
			<cfset local.returnStruct.numTotalPages = Ceiling(local.qryDepositions.totalCount / local.returnStruct.count)>
			<cfset local.returnStruct.numCurrentPage = int((int(local.returnStruct.startPos) + local.returnStruct.count - 1) / local.returnStruct.count)>

			<cfif local.returnStruct.numCurrentPage gt 1 and (Ceiling((local.returnStruct.numCurrentPage + (local.returnStruct.maxPage / 2))) gt local.returnStruct.maxPage)>
				<cfset local.returnStruct.endPage = Ceiling(local.returnStruct.numCurrentPage + (local.returnStruct.maxPage / 2))>
				<cfset local.returnStruct.startPage = local.returnStruct.endPage - (local.returnStruct.maxPage - 1)>
				<cfif local.returnStruct.endPage gte local.returnStruct.numTotalPages>
					<cfset local.returnStruct.endPage = local.returnStruct.numTotalPages>
					<cfif local.returnStruct.endPage lte local.returnStruct.maxPage>
						<cfset local.returnStruct.startPage = 1>
					<cfelse>
						<cfset local.returnStruct.startPage = local.returnStruct.endPage - (local.returnStruct.maxPage - 1)>
					</cfif>
				</cfif>
			<cfelseif local.returnStruct.numCurrentPage gte 1 and local.returnStruct.numCurrentPage lte local.returnStruct.maxPage and local.returnStruct.maxPage lte local.returnStruct.numTotalPages>
				<cfset local.returnStruct.endPage = local.returnStruct.maxPage>
			<cfelseif local.returnStruct.numTotalPages lte local.returnStruct.maxPage>
				<cfset local.returnStruct.endPage = local.returnStruct.numTotalPages>
			</cfif>

			<cfset local.uniqueID = CreateUniqueId()>

			<cfset local.dropboxAppKey = "">
			<cfif structKeyExists(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode), "dropboxappkey") and len(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).dropboxappkey)>
				<cfset local.dropboxAppKey = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).dropboxappkey>
			</cfif>

			<cfsavecontent variable="local.returnStruct.depositionshtml">
				<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/depositions/listDepositions.cfm">
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.returnStruct.depositionshtml">
				<cfoutput>
					<div class="alert">No Depositions found!</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getBucketSettingsHelpText" access="public" output="false" returntype="struct">
		
		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>

		<cfsavecontent variable="local.returnStruct.helpText">
			<cfoutput>
				<div class="alert align-items-center pl-2 align-content-center alert-primary show mb-2 alertSection" role="alert">
					<ul>
						<li>override = true or false -- if true then override restriction</li>
						<li>action = include or exclude </li>
						<li>deposedexpertonly = true or false -- if true then bucket only searches deposed searches only, DEFAULT False</li>
						<li>relatedtestimonyonly = true or false -- if true then bucket only searches relatedtestimony only, DEFAULT False</li>
						<li>bankids -- list of bank ids /li>
					</ul>
					<p class="ml-3 mb-0">Notes:</p>
					<ul>
						<li>override parameter not currently supported</li>

						<li>if no action defined and there is no default so cancel restriction</li>
					</ul>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnStruct>
	</cffunction>
</cfcomponent>