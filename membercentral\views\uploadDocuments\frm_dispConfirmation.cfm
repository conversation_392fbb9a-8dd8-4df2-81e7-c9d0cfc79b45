<cfoutput>
Thank you! We received #arguments.event.getValue('docsCount')# #local.docLabel# that you uploaded on #DateFormat(now(),'m/d/yyyy')#.<br/><br/>
Your #local.docLabel# will be analyzed and approved as quickly as possible.<br/><br/>
After approval by our team, you'll receive a notice of applicable document purchase credits you've earned for each new deposition.<br/><br/>
Don't forget, we'll take any expert deposition transcript you find, whether you took the deposition or found a copy. Every transcript earns credits for your account and helps other plaintiff lawyers in their expert research.<br/><br/>
<button type="button" class="<cfif arguments.event.getValue('viewDirectory') EQ 'responsive'>btn btn-primary<cfelse>tsAppBodyButton</cfif> mc_blinkerbtn" onclick="uploadDepositionsAgain();">
	Upload More Depositions
</button>
<cfif application.mcEnvironment neq "production">
	<br/><br/>
	<b>New TrialSmith AI Beta Promotion!</b> We'll automatically provide you with a Deposition Insights Summary for each transcript that you contribute. You'll receive an email confirmation when they are ready for you to download from the My Documents screen.<br/><br/>
</cfif>
</cfoutput>