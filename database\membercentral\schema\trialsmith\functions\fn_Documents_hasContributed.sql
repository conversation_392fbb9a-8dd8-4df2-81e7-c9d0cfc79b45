ALTER FUNCTION [dbo].[fn_Documents_hasContributed] (
	@documentID int,
	@depomemberdataid int
) 
returns bit
AS
begin

	declare @hasContributed bit
	
	-- am I in a firm plan? If so, contributed for anyone in plan

	IF EXISTS (
		SELECT d.DocumentID
		FROM dbo.depoDocuments AS d 
		inner join dbo.depoDocumentStatusHistory as dsh 
			on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
			and d.documentID = @documentID
		inner join dbo.depoDocumentStatuses as ds on ds.statusID = dsh.statusID
			and ds.statusName = 'Approved'
		inner join dbo.depodocumenttypes as dt on d.documenttypeid = dt.typeid and dt.acctcode between 3000 and 3999
		inner join (
			select @depomemberdataid as depomemberdataid
			union
			select fpl.depoMemberDataID
			from dbo.tlaFirmPlanLink as fpl
			where fpl.firmPlanID = (select firmPlanID from dbo.tlaFirmPlanLink where depoMemberDataID = @depomemberdataid)
		) as acct on acct.depomemberdataid = d.depomemberdataid


	) 
		SELECT @hasContributed = 1
	else
		SELECT @hasContributed = 0

	return @hasContributed
end
GO
