ALTER PROC dbo.ts_importDepoDocumentFromFS2Queue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @readyToProcessStatusID int, @processingStatusID int, @doneStatusID int, 
		@documentVersionID int, @depoMemberDataID int, @docOrgCode varchar(10), @fileExt varchar(20), 
		@s3prefix varchar(30), @depoDocumentID int, @MCDocumentObjectkey varchar(200), 
		@TSDocumentObjectkey varchar(200), @nowDate datetime = GETDATE(), @s3CopyReadyStatusID int,
		@contributeDate date; 

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='fileShare2toDepoDocs', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processing', @queueStatusID=@processingStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='done', @queueStatusID=@doneStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Copy', @queueStatus='readyToProcess', @queueStatusID=@s3CopyReadyStatusID OUTPUT;
	
	-- get info from queue item
	select @documentVersionID=documentVersionID, @depoMemberDataID=depoMemberDataID, @docOrgCode=docOrgCode, @fileExt=fileExt,
		@s3prefix=s3prefix, @contributeDate=contributeDate
	from platformQueue.dbo.queue_fileShare2toDepoDocs
	where itemID = @itemID
	and statusID = @readyToProcessStatusID
	and fileExt not in ('zip','rar');

	IF @documentVersionID IS NULL
		goto on_done;

	update platformQueue.dbo.queue_fileShare2toDepoDocs
	set statusID = @processingStatusID,
		dateUpdated = getdate()
	where itemID = @itemID;

	SET @MCDocumentObjectkey = lower(@s3prefix + right('0000' + cast(@documentVersionID % 1000 as varchar(4)),4) + '/' + cast(@documentVersionID as varchar(10)) + '.' + @fileExt);
	
	BEGIN TRAN;
		EXEC dbo.ts_addDepoDocument @depomemberdataID=@depoMemberDataID, @docOrgCode=@docOrgCode, @originalExt=@fileExt, 
			@contributeDate=@contributeDate, @DepoAmazonBucks=0, @DepoAmazonBucksFullName=null, @DepoAmazonBucksEmail=null, 
			@enteredByDepomemberdataID=@depoMemberDataID, @documentID=@depoDocumentID OUTPUT;

		SET @TSDocumentObjectkey = lower('depos/original/' + right('0000' + cast(@depoDocumentID % 1000 as varchar(4)),4) + '/' + cast(@depoDocumentID as varchar(10)) + '.' + @fileExt);

		-- we have to just do the s3copy here since queue_TSApprovalAutomation requires the file be downloaded to mcfile01 already.
		-- and we cant just trigger reprocessing attachments because the file isnt in depos/original yet.
		-- so we will copy to depos/original, and use the lambda notifier to trigger reprocessing of the pdf
		INSERT INTO platformQueue.dbo.queue_S3Copy (s3bucketName, objectkey, newS3bucketName, newObjectKey, dateAdded, dateUpdated, nextAttemptDate, statusID)
		VALUES ('membercentralcdn', @MCDocumentObjectkey, 'trialsmith-depos', @TSDocumentObjectkey, @nowDate, @nowDate, @nowDate, @s3CopyReadyStatusID);
	COMMIT TRAN;
	
	update platformQueue.dbo.queue_fileShare2toDepoDocs
	set statusID = @doneStatusID,
		dateUpdated = getdate()
	where itemID = @itemID;

	delete from platformQueue.dbo.queue_fileShare2toDepoDocs
	where itemID = @itemID;
	
	on_done:
	RETURN 0;
	
END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
