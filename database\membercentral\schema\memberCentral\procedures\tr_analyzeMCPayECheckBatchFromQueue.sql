ALTER PROC dbo.tr_analyzeMCPayECheckBatchFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpUSIOPaymentTransactions') IS NOT NULL 
		DROP TABLE #tmpUSIOPaymentTransactions;
	IF OBJECT_ID('tempdb..#tmpMoveBatchTransactions') IS NOT NULL 
		DROP TABLE #tmpMoveBatchTransactions;
	CREATE TABLE #tmpUSIOPaymentTransactions (transactionID int, amount decimal(18,2), batchID int, depositDate datetime);
	CREATE TABLE #tmpMoveBatchTransactions (transactionID int, fromBatchID int, toBatchCode varchar(40), toBatchName varchar(400));

	DECLARE @queueTypeID int, @itemStatus int, @statusReady int, @statusProcessing int, @orgID int, @depositDate date, 
		@MPProfileID int, @MPProfileCode varchar(20), @minTransactionID int, @systemMemberID int, @siteID int, 
		@fromBatchID int, @toBatchCode varchar(40), @toBatchName varchar(400), @toBatchID int, @fromBatchStatusID int, 
		@toBatchStatusID int, @openStatusID int, @openForModificationStatusID int, @closedStatusID int, @postedStatusID int, 
		@batchIncrement int, @exceptionBatchTypeID int, @batchFound bit, @useBatchCode varchar(40), @statusID int, 
		@fromBatchUpdateStatusID int, @toBatchUpdateStatusID int;

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='analyzeMCPayECheckBatches', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;	

	SELECT @itemStatus = statusID, @siteID = siteID, @orgID = orgID, @depositDate = depositDate, @MPProfileID = MPProfileID
	FROM platformQueue.dbo.queue_analyzeMCPayECheckBatches
	WHERE itemID = @itemID;

	select @systemMemberID = dbo.fn_ams_getMCSystemMemberID();
	select @openStatusID = statusID from dbo.tr_batchStatuses where [status] = 'Open';
	select @openForModificationStatusID = statusID from dbo.tr_batchStatuses where [status] = 'Open for Modification';
	select @closedStatusID = statusID from dbo.tr_batchStatuses where [status] = 'Closed';
	select @postedStatusID = statusID from dbo.tr_batchStatuses where [status] = 'Posted';
	select @exceptionBatchTypeID = batchTypeID from dbo.tr_batchTypes where batchType = 'Exceptions';

	SELECT @MPProfileCode = profileCode
	FROM dbo.mp_profiles
	WHERE siteID = @siteID
	AND profileID = @MPProfileID;
	
	-- if itemID is not readyToProcess, kick out now
	IF @itemStatus IS NULL OR @itemStatus <> @statusReady
		GOTO on_done;

	-- update status
	UPDATE platformQueue.dbo.queue_analyzeMCPayECheckBatches
	SET statusID = @statusProcessing,
		dateUpdated = getdate()
	WHERE itemID = @itemID;

	-- get ach batch transactions (payments and refunds)
	INSERT INTO #tmpUSIOPaymentTransactions (transactionID, amount, batchID, depositDate)
	SELECT DISTINCT t.transactionID, t.amount, mcb.batchID, mcb.depositDate
	FROM dbo.tr_MCPayECheckBatches AS b
	INNER JOIN dbo.tr_MCPayECheckTransactions AS bt ON bt.siteID = @siteID
		AND bt.MCPayECheckBatchID = b.MCPayECheckBatchID
	INNER JOIN dbo.tr_transactions AS t ON t.ownedByOrgID = @orgID
		AND t.transactionID = bt.transactionID
		AND t.statusID = 1
	INNER JOIN dbo.tr_batchTransactions AS mcbt ON mcbt.orgID = @orgID
		AND mcbt.transactionID = t.transactionID
	INNER JOIN dbo.tr_batches AS mcb ON mcb.orgID = @orgID
		AND mcb.batchID = mcbt.batchID
	WHERE b.siteID = @siteID
	AND b.MPProfileID = @MPProfileID
	AND b.depositDate = @depositDate;

	-- batch transactions with deposit date mismatches
	INSERT INTO #tmpMoveBatchTransactions (transactionID, fromBatchID, toBatchCode, toBatchName)
	SELECT t.transactionID, pt.batchID, 
		toBatchCode = CONVERT(CHAR(8),@depositDate,112) + '_' + cast(@MPProfileID as varchar(10)) + '_' + cast(CASE t.typeID WHEN 2 THEN t.debitGLAccountID WHEN 4 THEN t.creditGLAccountID END as varchar(10)),
		toBatchName = CONVERT(CHAR(8),@depositDate,112) + ' ' + @MPProfileCode + ' ' + CASE t.typeID WHEN 2 THEN debitGL.accountName WHEN 4 THEN creditGL.accountName END
	FROM #tmpUSIOPaymentTransactions AS pt
	INNER JOIN dbo.tr_transactions AS t ON t.ownedByOrgID = @orgID
		AND t.transactionID = pt.transactionID
	INNER JOIN dbo.tr_glAccounts AS debitGL ON debitGL.orgID = @orgID
		AND debitGL.GLAccountID = t.debitGLAccountID
	INNER JOIN dbo.tr_glAccounts AS creditGL ON creditGL.orgID = @orgID
		AND creditGL.GLAccountID = t.creditGLAccountID
	WHERE pt.depositDate <> @depositDate;

	SELECT @minTransactionID = MIN(transactionID) FROM #tmpMoveBatchTransactions;
	WHILE @minTransactionID IS NOT NULL BEGIN
		SELECT @fromBatchID = NULL, @toBatchID = NULL, @fromBatchStatusID = NULL, @toBatchCode = NULL, @toBatchName = NULL, 
			@fromBatchUpdateStatusID = NULL, @toBatchUpdateStatusID = NULL, @statusID = NULL, @toBatchStatusID = NULL;

		SELECT @fromBatchID = fromBatchID, @toBatchCode = toBatchCode, @toBatchName = toBatchName
		FROM #tmpMoveBatchTransactions
		WHERE transactionID = @minTransactionID;

		SELECT @toBatchID = batchID
		FROM dbo.tr_batches
		WHERE orgID = @orgID
		AND batchCode = @toBatchCode
		AND payProfileID = @MPProfileID
		AND depositDate = @depositDate;

		IF @toBatchID IS NULL
			EXEC dbo.tr_createBatch @orgID=@orgID, @payProfileID=@MPProfileID, @batchTypeID=1, @batchCode=@toBatchCode, 
				@batchName=@toBatchName, @controlAmt=0, @controlCount=0, @depositDate=@depositDate, @isSystemCreated=1, 
				@createdByMemberID=@systemMemberID, @batchID=@toBatchID OUTPUT;

		SELECT @fromBatchStatusID = statusID
		FROM dbo.tr_batches
		WHERE orgID = @orgID
		AND batchID = @fromBatchID;

		IF @fromBatchStatusID IN (@openForModificationStatusID,@closedStatusID) BEGIN
			-- reopen batch
			UPDATE dbo.tr_batches
			SET statusID = @openStatusID
			WHERE orgID = @orgID
			AND batchID = @fromBatchID;
			
			INSERT INTO dbo.tr_batchStatusHistory (batchID, updateDate, statusID, oldStatusID, enteredByMemberID)
			VALUES (@fromBatchID, GETDATE(), @openStatusID, @fromBatchStatusID, @systemMemberID);

			SET @fromBatchUpdateStatusID = @fromBatchStatusID;
		END

		SELECT @toBatchStatusID = statusID
		FROM dbo.tr_batches
		WHERE orgID = @orgID
		AND batchID = @toBatchID;

		IF @toBatchStatusID IN (@openForModificationStatusID,@closedStatusID) BEGIN
			-- reopen batch
			UPDATE dbo.tr_batches
			SET statusID = @openStatusID
			WHERE orgID = @orgID
			AND batchID = @toBatchID;
			
			INSERT INTO dbo.tr_batchStatusHistory (batchID, updateDate, statusID, oldStatusID, enteredByMemberID)
			VALUES (@toBatchID, GETDATE(), @openStatusID, @toBatchStatusID, @systemMemberID);

			SET @toBatchUpdateStatusID = @toBatchStatusID;
		END
		
		IF @toBatchStatusID = @postedStatusID BEGIN
			SET @batchIncrement = 0;
			SET @toBatchCode = @toBatchCode + '_EX';
			SET @toBatchName = @toBatchName + ' Exceptions';
		
			SET @batchFound = 0;
			WHILE @batchFound = 0 BEGIN
				SELECT @toBatchID = NULL, @statusID = NULL;
				SET @batchIncrement = @batchIncrement + 1;

				IF @batchIncrement = 1
					SET @useBatchCode = @toBatchCode;
				ELSE
					SET @useBatchCode = @toBatchCode + '_' + cast(@batchIncrement as varchar(3));

				SELECT @toBatchID = batchID, @statusID = statusID
				FROM dbo.tr_batches
				WHERE orgID = @orgID
				AND batchTypeID = @exceptionBatchTypeID
				AND batchCode = @useBatchCode
				AND payProfileID = @MPProfileID;

				IF @toBatchID IS NOT NULL AND @statusID = @openStatusID
					SET @batchFound = 1;
				IF @toBatchID IS NULL BEGIN
					EXEC dbo.tr_createBatch @orgID=@orgID, @payProfileID=@MPProfileID, @batchTypeID=@exceptionBatchTypeID, 
						@batchCode=@useBatchCode, @batchName=@toBatchName, @controlAmt=0, @controlCount=0, @depositDate=@depositDate, 
						@isSystemCreated=1, @createdByMemberID=@systemMemberID, @batchID=@toBatchID OUTPUT;
					SET @batchFound = 1;
					SET @toBatchUpdateStatusID = @closedStatusID;
				end
			end
		END

		-- move batch transaction
		EXEC dbo.tr_moveBatchTransaction @orgID=@orgID, @transactionID=@minTransactionID, @fromBatchID=@fromBatchID, 
			@toBatchID=@toBatchID, @skipSysCreatedChecks=1;

		-- update batch statusID
		IF @fromBatchUpdateStatusID IS NOT NULL BEGIN
			select @statusID = statusID
			from dbo.tr_batches
			where orgID = @orgID
			and batchID = @fromBatchID;
			
			IF @fromBatchUpdateStatusID <> @statusID BEGIN
				UPDATE dbo.tr_batches
				SET statusID = @fromBatchUpdateStatusID
				WHERE orgID = @orgID
				AND batchID = @fromBatchID;

				INSERT INTO dbo.tr_batchStatusHistory (batchID, updateDate, statusID, oldStatusID, enteredByMemberID)
				VALUES (@fromBatchID, GETDATE(), @fromBatchUpdateStatusID, @statusID, @systemMemberID);
			END
		END

		IF @toBatchUpdateStatusID IS NOT NULL BEGIN
			select @statusID = statusID
			from dbo.tr_batches
			where orgID = @orgID
			and batchID = @toBatchID;
			
			IF @toBatchUpdateStatusID <> @statusID BEGIN
				UPDATE dbo.tr_batches
				SET statusID = @toBatchUpdateStatusID
				WHERE orgID = @orgID
				AND batchID = @toBatchID;

				INSERT INTO dbo.tr_batchStatusHistory (batchID, updateDate, statusID, oldStatusID, enteredByMemberID)
				VALUES (@toBatchID, GETDATE(), @toBatchUpdateStatusID, @statusID, @systemMemberID);
			END
		END

		SELECT @minTransactionID = MIN(transactionID) FROM #tmpMoveBatchTransactions WHERE transactionID > @minTransactionID;
	END

	-- verify and post closed batches
	EXEC dbo.tr_verifyAndPostMCPayECheckBatches @orgID=@orgID, @MPProfileID=@MPProfileID, @depositDate=@depositDate;

	-- delete queue item
	DELETE FROM platformQueue.dbo.queue_analyzeMCPayECheckBatches
	WHERE itemID = @itemID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpUSIOPaymentTransactions') IS NOT NULL 
		DROP TABLE #tmpUSIOPaymentTransactions;
	IF OBJECT_ID('tempdb..#tmpMoveBatchTransactions') IS NOT NULL 
		DROP TABLE #tmpMoveBatchTransactions;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
