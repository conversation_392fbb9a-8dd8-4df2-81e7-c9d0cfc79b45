use platformQueue
GO

update dbo.tblQueueTypes
set queueType = 'TSApprovalAutomation',
	checkProc = 'queue_TSApprovalAutomation_check',
	clearProc = 'queue_TSApprovalAutomation_clear',
	downloadProc = 'queue_TSApprovalAutomation_download',
	summaryProc = 'queue_TSApprovalAutomation_summary'
where queueType = 'depoDocumentsAttach'
GO

DROP PROC dbo.queue_depoDocumentsAttach_check
GO

DROP TABLE dbo.queue_depoDocumentsAttach
GO

CREATE TABLE [dbo].[queue_TSApprovalAutomation](
	[itemID] [int] IDENTITY(1,1) NOT NULL,
	[depoDocumentID] [int] NOT NULL,
	[errorMessage] [varchar](max) NULL,
	[isNotified] [bit] NULL,
	[statusID] [int] NOT NULL,
	[dateAdded] [datetime] NOT NULL,
	[dateUpdated] [datetime] NOT NULL,
 CONSTRAINT [PK_queue_TSApprovalAutomation] PRIMARY KEY CLUSTERED 
(
	[itemID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

CREATE PROC dbo.queue_TSApprovalAutomation_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @queueTypeID INT, @processingStatusID INT, @readyStatusID INT, 
		@grabProcessingStatusID int, @failedStatusID int, @tier varchar(12), @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	SELECT @tier = tier from membercentral.dbo.fn_getServerSettings();
	EXEC dbo.queue_getQueueTypeID @queueType='TSApprovalAutomation', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForProcessing', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@processingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='failed', @queueStatusID=@failedStatusID OUTPUT;

	-- We dont upload when not in production, so delete the queue when not in prod
	IF @tier = 'Production' BEGIN
		-- TSApprovalAutomation / processingItem autoreset to readyToProcess
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(minute, -10, GETDATE());
		SELECT @issueCount = count(itemID) FROM dbo.queue_TSApprovalAutomation WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
		IF @issueCount > 0 BEGIN
			UPDATE dbo.queue_TSApprovalAutomation
			SET statusID = @readyStatusID, 
				dateUpdated = GETDATE()
			WHERE statusID = @processingStatusID 
			AND dateUpdated < @timeToUse;

			SET @errorTitle = 'TSApprovalAutomation Queue Issue';
			SET @errorSubject = 'TSApprovalAutomation queue moved items from processingItem to readyToProcess';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END

		-- TSApprovalAutomation / grabbedForProcessing autoreset to readyToProcess
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(minute, -10, GETDATE());
		SELECT @issueCount = count(itemID) FROM dbo.queue_TSApprovalAutomation WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
		IF @issueCount > 0 BEGIN
			UPDATE dbo.queue_TSApprovalAutomation
			SET statusID = @readyStatusID, 
				dateUpdated = GETDATE()
			WHERE statusID = @grabProcessingStatusID 
			AND dateUpdated < @timeToUse;

			SET @errorTitle = 'TSApprovalAutomation Queue Issue';
			SET @errorSubject = 'TSApprovalAutomation queue moved items from grabbedForProcessing to readyToProcess';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END

		-- TSApprovalAutomation readyToProcess notify
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(itemID) FROM dbo.queue_TSApprovalAutomation WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
		IF @issueCount > 0 BEGIN
			SET @errorTitle = 'TSApprovalAutomation Queue Issue';
			SET @errorSubject = 'TSApprovalAutomation queue has items in readyToProcess with dateUpdated older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END

		-- TSApprovalAutomation failed notify. Use DateAdded since we change dateupdated upon fail but still keep trying to process it.
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(itemID) FROM dbo.queue_TSApprovalAutomation WHERE statusID = @failedStatusID AND dateAdded < @timeToUse AND isNotified = 0;
		IF @issueCount > 0 BEGIN
			UPDATE dbo.queue_TSApprovalAutomation
			SET isNotified = 1 
			WHERE statusID = @failedStatusID 
			AND dateAdded < @timeToUse 
			AND isNotified = 0;

			SET @errorTitle = 'TSApprovalAutomation Queue Issue';
			SET @errorSubject = 'TSApprovalAutomation queue has items in failed status with dateAdded older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END
	END ELSE BEGIN
		SELECT @issueCount = count(itemID) FROM dbo.queue_TSApprovalAutomation;
		IF @issueCount > 0
			DELETE FROM dbo.queue_TSApprovalAutomation;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

DROP PROC dbo.queue_depoDocumentsAttach_clear
GO

CREATE PROC dbo.queue_TSApprovalAutomation_clear
@status varchar(60)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DELETE qi
	FROM dbo.queue_TSApprovalAutomation AS qi
	INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID and qs.queueStatus = @status;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

DROP PROC dbo.queue_depoDocumentsAttach_download
GO

CREATE PROC dbo.queue_TSApprovalAutomation_download
@status varchar(60),
@csvfilename varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @selectsql varchar(max) = '';

	SELECT @selectsql = 'SELECT ''TSApprovalAutomation'' as queueType, qs.queueStatus, qi.itemID, qi.depoDocumentID, qi.errorMessage, qi.isNotified,
		qi.dateAdded, qi.dateUpdated, ROW_NUMBER() OVER(order by qi.dateAdded, qi.itemID) as mcCSVorder 
		*FROM* platformQueue.dbo.queue_TSApprovalAutomation as qi
		INNER JOIN platformQueue.dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID and qs.queueStatus = ''' + @status + '''';

	EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@csvfilename, @returnColumns=0;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

DROP PROC dbo.queue_depoDocumentsAttach_grabForProcessing
GO

CREATE PROC dbo.queue_TSApprovalAutomation_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems(itemID int);

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @batchSize tinyint = 100;
	EXEC dbo.queue_getQueueTypeID @queueType='TSApprovalAutomation', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	-- dequeue
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID INTO #tmpQueueItems
	FROM dbo.queue_TSApprovalAutomation as qi
	INNER JOIN (
		SELECT top (@batchSize) qi2.itemID 
		FROM dbo.queue_TSApprovalAutomation as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	SELECT qid.itemID, qid.depoDocumentID
	FROM #tmpQueueItems as qi
	INNER JOIN dbo.queue_TSApprovalAutomation as qid on qid.itemID = qi.itemID;
	
	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO

DROP PROC dbo.queue_depoDocumentsAttach_summary
GO

CREATE PROC dbo.queue_TSApprovalAutomation_summary

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	insert into #tmpQueueDataCount (queueType, queueStatus, numItems, minDateAdded, offerDelete)
	select 'TSApprovalAutomation', qs.queueStatus, count(qi.itemid), min(qi.dateUpdated), 1
	from dbo.queue_TSApprovalAutomation as qi
	inner join dbo.tblQueueStatuses as qs on qs.queuestatusID = qi.statusID
	group by qs.queueStatus;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

use trialsmith
GO

ALTER PROC dbo.ts_addDepoDocumentToAttachQueue
@depoDocumentID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @nowDate datetime = GETDATE();
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='TSApprovalAutomation', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	IF NOT EXISTS (SELECT 1 FROM platformQueue.dbo.queue_TSApprovalAutomation WHERE depoDocumentID = @depoDocumentID) BEGIN
		INSERT INTO platformQueue.dbo.queue_TSApprovalAutomation (depoDocumentID, dateAdded, dateUpdated, statusID)
		VALUES (@depoDocumentID, @nowDate, @nowDate, @statusReady);

		EXEC membercentral.dbo.sched_resumeTask @name='Process TS Approvals Automation Queue', @engine='BERLinux';
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.ts_importDepoDocumentFromFS2Queue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @readyToProcessStatusID int, @processingStatusID int, @doneStatusID int, 
		@documentVersionID int, @depoMemberDataID int, @docOrgCode varchar(10), @fileExt varchar(20), 
		@s3prefix varchar(30), @depoDocumentID int, @MCDocumentObjectkey varchar(200), 
		@TSDocumentObjectkey varchar(200), @nowDate datetime = GETDATE(), @s3CopyReadyStatusID int,
		@contributeDate date; 

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='fileShare2toDepoDocs', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processing', @queueStatusID=@processingStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='done', @queueStatusID=@doneStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Copy', @queueStatus='readyToProcess', @queueStatusID=@s3CopyReadyStatusID OUTPUT;
	
	-- get info from queue item
	select @documentVersionID=documentVersionID, @depoMemberDataID=depoMemberDataID, @docOrgCode=docOrgCode, @fileExt=fileExt,
		@s3prefix=s3prefix, @contributeDate=contributeDate
	from platformQueue.dbo.queue_fileShare2toDepoDocs
	where itemID = @itemID
	and statusID = @readyToProcessStatusID
	and fileExt not in ('zip','rar');

	IF @documentVersionID IS NULL
		goto on_done;

	update platformQueue.dbo.queue_fileShare2toDepoDocs
	set statusID = @processingStatusID,
		dateUpdated = getdate()
	where itemID = @itemID;

	SET @MCDocumentObjectkey = lower(@s3prefix + right('0000' + cast(@documentVersionID % 1000 as varchar(4)),4) + '/' + cast(@documentVersionID as varchar(10)) + '.' + @fileExt);
	
	BEGIN TRAN;
		EXEC dbo.ts_addDepoDocument @depomemberdataID=@depoMemberDataID, @docOrgCode=@docOrgCode, @originalExt=@fileExt, 
			@contributeDate=@contributeDate, @DepoAmazonBucks=0, @DepoAmazonBucksFullName=null, @DepoAmazonBucksEmail=null, 
			@enteredByDepomemberdataID=@depoMemberDataID, @documentID=@depoDocumentID OUTPUT;

		SET @TSDocumentObjectkey = lower('depos/original/' + right('0000' + cast(@depoDocumentID % 1000 as varchar(4)),4) + '/' + cast(@depoDocumentID as varchar(10)) + '.' + @fileExt);

		-- we have to just do the s3copy here since queue_TSApprovalAutomation requires the file be downloaded to mcfile01 already.
		-- and we cant just trigger reprocessing attachments because the file isnt in depos/original yet.
		-- so we will copy to depos/original, and use the lambda notifier to trigger reprocessing of the pdf
		INSERT INTO platformQueue.dbo.queue_S3Copy (s3bucketName, objectkey, newS3bucketName, newObjectKey, dateAdded, dateUpdated, nextAttemptDate, statusID)
		VALUES ('membercentralcdn', @MCDocumentObjectkey, 'trialsmith-depos', @TSDocumentObjectkey, @nowDate, @nowDate, @nowDate, @s3CopyReadyStatusID);
	COMMIT TRAN;
	
	update platformQueue.dbo.queue_fileShare2toDepoDocs
	set statusID = @doneStatusID,
		dateUpdated = getdate()
	where itemID = @itemID;

	delete from platformQueue.dbo.queue_fileShare2toDepoDocs
	where itemID = @itemID;
	
	on_done:
	RETURN 0;
	
END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

use membercentral
GO

update dbo.scheduledTasks
set [name] = 'Process TS Approvals Automation Queue',
	taskCFC = 'depoDocuments.processTSApprovalAutomationQueue'
where taskID = 193
GO

use platformMail
GO

UPDATE dbo.email_messageTypes
SET messageTypeCode = 'PROCTSAPPAUTO',
	messageType = 'Process TS Approval Automation Queue'
WHERE messageTypeCode = 'PROCESSDOCATTCH'
GO

use trialsmith
GO

DROP PROC dbo.ts_addDepoDocumentToAttachQueue
GO

use platformQueue
GO

CREATE PROC dbo.queue_TSApprovalAutomation_enqueue
@depoDocumentID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @nowDate datetime = GETDATE();
	EXEC dbo.queue_getStatusIDbyType @queueType='TSApprovalAutomation', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	IF NOT EXISTS (SELECT 1 FROM dbo.queue_TSApprovalAutomation WHERE depoDocumentID = @depoDocumentID) BEGIN
		INSERT INTO dbo.queue_TSApprovalAutomation (depoDocumentID, dateAdded, dateUpdated, statusID)
		VALUES (@depoDocumentID, @nowDate, @nowDate, @statusReady);

		EXEC membercentral.dbo.sched_resumeTask @name='Process TS Approvals Automation Queue', @engine='BERLinux';
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

use platformQueue
GO

DROP PROC dbo.queue_TSApprovalAutomation_grabForProcessing
GO

CREATE PROC dbo.queue_TSApprovalAutomation_grabbedForAttachmentCheck

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems(itemID int);

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @batchSize tinyint = 100;
	EXEC dbo.queue_getQueueTypeID @queueType='TSApprovalAutomation', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='ReadyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='GrabbedForProcessing', @queueStatusID=@statusGrabbed OUTPUT;

	-- dequeue
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID INTO #tmpQueueItems
	FROM dbo.queue_TSApprovalAutomation as qi
	INNER JOIN (
		SELECT top (@batchSize) qi2.itemID 
		FROM dbo.queue_TSApprovalAutomation as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	SELECT qid.itemID, qid.depoDocumentID
	FROM #tmpQueueItems as qi
	INNER JOIN dbo.queue_TSApprovalAutomation as qid on qid.itemID = qi.itemID;
	
	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO

use platformQueue
GO

ALTER PROC dbo.queue_TSApprovalAutomation_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @issueCount INT, @timeToUse DATETIME, @queueTypeID INT, @processingStatusID INT, @readyStatusID INT, 
		@grabProcessingStatusID int, @failedStatusID int, @tier varchar(12), @errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	SELECT @tier = tier from membercentral.dbo.fn_getServerSettings();
	EXEC dbo.queue_getQueueTypeID @queueType='TSApprovalAutomation', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForAttachCheck', @queueStatusID=@grabProcessingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyForAttachCheck', @queueStatusID=@readyStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processAttachCheck', @queueStatusID=@processingStatusID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='failed', @queueStatusID=@failedStatusID OUTPUT;

	-- We dont upload when not in production, so delete the queue when not in prod
	IF @tier = 'Production' BEGIN
		-- TSApprovalAutomation / processAttachCheck autoreset to readyToProcess
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(minute, -10, GETDATE());
		SELECT @issueCount = count(itemID) FROM dbo.queue_TSApprovalAutomation WHERE statusID = @processingStatusID AND dateUpdated < @timeToUse;
		IF @issueCount > 0 BEGIN
			UPDATE dbo.queue_TSApprovalAutomation
			SET statusID = @readyStatusID, 
				dateUpdated = GETDATE()
			WHERE statusID = @processingStatusID 
			AND dateUpdated < @timeToUse;

			SET @errorTitle = 'TSApprovalAutomation Queue Issue';
			SET @errorSubject = 'TSApprovalAutomation queue moved items from processAttachCheck to readyForAttachCheck';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END

		-- TSApprovalAutomation / grabbedForAttachCheck autoreset to readyForAttachCheck
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(minute, -10, GETDATE());
		SELECT @issueCount = count(itemID) FROM dbo.queue_TSApprovalAutomation WHERE statusID = @grabProcessingStatusID AND dateUpdated < @timeToUse;
		IF @issueCount > 0 BEGIN
			UPDATE dbo.queue_TSApprovalAutomation
			SET statusID = @readyStatusID, 
				dateUpdated = GETDATE()
			WHERE statusID = @grabProcessingStatusID 
			AND dateUpdated < @timeToUse;

			SET @errorTitle = 'TSApprovalAutomation Queue Issue';
			SET @errorSubject = 'TSApprovalAutomation queue moved items from grabbedForAttachCheck to readyForAttachCheck';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END

		-- TSApprovalAutomation readyForAttachCheck notify
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(itemID) FROM dbo.queue_TSApprovalAutomation WHERE statusID = @readyStatusID AND dateUpdated < @timeToUse;
		IF @issueCount > 0 BEGIN
			SET @errorTitle = 'TSApprovalAutomation Queue Issue';
			SET @errorSubject = 'TSApprovalAutomation queue has items in readyForAttachCheck with dateUpdated older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END

		-- TSApprovalAutomation failed notify. Use DateAdded since we change dateupdated upon fail but still keep trying to process it.
		SET @issueCount = 0;
		SET @timeToUse = DATEADD(hour, -4, GETDATE());
		SELECT @issueCount = count(itemID) FROM dbo.queue_TSApprovalAutomation WHERE statusID = @failedStatusID AND dateAdded < @timeToUse AND isNotified = 0;
		IF @issueCount > 0 BEGIN
			UPDATE dbo.queue_TSApprovalAutomation
			SET isNotified = 1 
			WHERE statusID = @failedStatusID 
			AND dateAdded < @timeToUse 
			AND isNotified = 0;

			SET @errorTitle = 'TSApprovalAutomation Queue Issue';
			SET @errorSubject = 'TSApprovalAutomation queue has items in failed status with dateAdded older than 4 hours.';
			EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
		END
	END ELSE BEGIN
		SELECT @issueCount = count(itemID) FROM dbo.queue_TSApprovalAutomation;
		IF @issueCount > 0
			DELETE FROM dbo.queue_TSApprovalAutomation;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_TSApprovalAutomation_enqueue
@depoDocumentID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @statusReady int, @nowDate datetime = GETDATE();
	EXEC dbo.queue_getStatusIDbyType @queueType='TSApprovalAutomation', @queueStatus='readyForAttachCheck', @queueStatusID=@statusReady OUTPUT;

	IF NOT EXISTS (SELECT 1 FROM dbo.queue_TSApprovalAutomation WHERE depoDocumentID = @depoDocumentID) BEGIN
		INSERT INTO dbo.queue_TSApprovalAutomation (depoDocumentID, dateAdded, dateUpdated, statusID)
		VALUES (@depoDocumentID, @nowDate, @nowDate, @statusReady);

		EXEC membercentral.dbo.sched_resumeTask @name='Process TS Approvals Automation Queue', @engine='BERLinux';
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.queue_TSApprovalAutomation_grabbedForAttachmentCheck

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems(itemID int);

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @batchSize tinyint = 100;
	EXEC dbo.queue_getQueueTypeID @queueType='TSApprovalAutomation', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyForAttachCheck', @queueStatusID=@statusReady OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='grabbedForAttachCheck', @queueStatusID=@statusGrabbed OUTPUT;

	-- dequeue
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID INTO #tmpQueueItems
	FROM dbo.queue_TSApprovalAutomation as qi
	INNER JOIN (
		SELECT top (@batchSize) qi2.itemID 
		FROM dbo.queue_TSApprovalAutomation as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	SELECT qid.itemID, qid.depoDocumentID
	FROM #tmpQueueItems as qi
	INNER JOIN dbo.queue_TSApprovalAutomation as qid on qid.itemID = qi.itemID;
	
	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO

DECLARE @queueTypeID int;
EXEC dbo.queue_getQueueTypeID @queueType='TSApprovalAutomation', @queueTypeID=@queueTypeID OUTPUT;

UPDATE dbo.tblQueueStatuses SET queueStatus = 'readyForAttachCheck' WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
UPDATE dbo.tblQueueStatuses SET queueStatus = 'grabbedForAttachCheck' WHERE queueTypeID = @queueTypeID AND queueStatus = 'grabbedForProcessing';
UPDATE dbo.tblQueueStatuses SET queueStatus = 'processAttachCheck' WHERE queueTypeID = @queueTypeID AND queueStatus = 'processingItem';
GO

