<cfsavecontent variable="local.js">
	<cfoutput>
	<script language="javascript">
		let subscriptionListTable, checkAllSubscrips = 0, arrUnchkedSubs = [], arrChkedSubs = [], totalSubscripsCount=0;
		var #ToScript(local.subscribersListLink,'subscribersListLink')#
		var #ToScript(this.link.grpSelectGotoLink,"link_grpSelectGotoLink")#
		var #ToScript(this.link.memSelectGotoLink,"link_memSelectGotoLink")#
		var #ToScript(arguments.event.getValue('mc_adminNav.adminHomeResource'),"link_adminHomeResource")#
		
		function initSubscriptionListTable(){
			let domString = "<'row'<'col-sm-6 col-md-6'<'float-left mt-2'l><'float-left p-1 m-2 selConCountDisp'>><'col-sm-6 col-md-6'p>>" 
							+ "<'row'<'col-sm-12'tr>>" 
							+ "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";

			subscriptionListTable = $('##subscriptionListTable').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 100,
				"lengthMenu": [ 100, 250, 500 ],
				"dom": domString,
				"ajax": { 
					"url": subscribersListLink,
					"type": "post",
					"data": function(d) {
						$.each($('##frmFilter').serializeArray(),function() {
							d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
						});
					}
				},
				"autoWidth": false,
				"columns": [
					{				
						"orderable": false,
						"className": 'dt-body-center text-center align-top',				
						"width": "2%",
						"data": null,
						"render": function (data, type, row, meta){
							let renderData = '';
							if (type === 'display') {
								var isChecked = "";
								if($("##masterCheckBox:checkbox:checked").length){
									isChecked = "checked";
								}
								renderData += '<input type="checkbox" name="subscriptionCheckbox" class="subscriptionCheckbox" '+ isChecked +' value="' + data.subscriberID + '" onclick="onCheckSubscriptionEntry(this);">';										
							}	
							return type === 'display' ? renderData : data;
						}
					},
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') { 						
								renderData += '<div><a href="javascript:editMember('+data.memberID+')" title="View Member Record for '+data.memberName+'">'+data.memberName+'</a></div>';
								renderData += '<div class="text-dim small">'+data.company+'</div>';						
							}
							return type === 'display' ? renderData : data;
						},
						"width": "33%", 
						'className': 'align-top'
					},
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') { 						
								renderData += '<div class="float-right">';
								if (data.paymentStatusCode == 'N' && ['A','P'].includes(data.statusCode)) renderData += '<span class="badge badge-warning">Activation Not Met</span>';
								renderData += '<span class="badge badge-neutral-second text-second">' + data.statusName + '</span></div>';
								renderData += '<div><a href="javascript:showSubTree('+data.memberID+','+data.subscriberID+')" title="View Subscription Tree">'+data.subscriptionName+'</a></div>';
								renderData += '<div class="text-dim small">'+data.typeName+'</div>';	
							}
							return type === 'display' ? renderData : data;
						},
						'className': 'align-top',
						"width": "35%", 
						"orderable": false 
					},
					{ "data": "subStartDate", "width": "10%", "className": "align-top" },
					{ "data": "subEndDate", "width": "10%", "className": "align-top" },
					{ "data": "graceEndDate", "width": "10%", "className": "align-top" }
				],
				"order": [[1, 'asc']],
				"searching": false,
				"pagingType": "simple",
				"drawCallback": function(settings) {
					totalSubscripsCount = subscriptionListTable.page.info().recordsTotal;
					$(".subscriptionCheckbox:checkbox").prop('checked', checkAllSubscrips);

					if(checkAllSubscrips == 1) {
						$.each(arrUnchkedSubs, function( index, value ) {
							$(".subscriptionCheckbox:checkbox[value="+value+"]").prop('checked', false);
						});
					}
					else {
						$.each(arrChkedSubs, function( index, value ) {
							$(".subscriptionCheckbox:checkbox[value="+value+"]").prop('checked', true);
						});
					}

					checkSubscriptionEntry();
					$('##subscriptionListTable_wrapper ul.pagination:last').prepend('<li class="paginate_button page-item "><a href="##" class="page-link" onclick="mca_scrollTo(\'divMCMainContainer\');return false;" title="Back to Top"><i class="fa-regular fa-chevrons-up"></i> Back to Top</a></li>');
				}
			});

			 mca_generateVerboseFilterMessage('frmFilter');
		}
		function checkSubscriptionEntry(){
			cleanupSelectionArrays();

			$.each(arrUnchkedSubs, function( index, value ) {
				$(".subscriptionCheckbox:checkbox[value="+value+"]").prop('checked', false);
			});
			var displayCount = $('##masterCheckBox').is(':checked') ? totalSubscripsCount - arrUnchkedSubs.length : arrChkedSubs.length;
			setSelectedSubCountDisplay(displayCount);
		}
		function cleanupSelectionArrays(){
			if (arrUnchkedSubs.indexOf("") > -1) arrUnchkedSubs.splice(arrUnchkedSubs.indexOf(""), 1);
			if (arrChkedSubs.indexOf("") > -1) arrChkedSubs.splice(arrChkedSubs.indexOf(""), 1);
		}
		function setSelectedSubCountDisplay(c){
			if(totalSubscripsCount != 0){
				$('.selConCountDisp').html('<b>' + totalSubscripsCount + '</b> ' + (totalSubscripsCount == 1 ? "Subscription" : "Subscriptions") + ' found / <b>' + c + '</b> ' + (c == 1 ? "Subscription" : "Subscriptions") + ' selected').show();
			}else{
				$('.selConCountDisp').html('');
			}
		}
		function doCheckAllSubscrips(chk) {
			arrUnchkedSubs = [];
			arrChkedSubs = [];
			checkAllSubscrips = chk ? 1 : 0;
			$(".subscriptionCheckbox:checkbox").prop('checked', chk);
			checkSubscriptionEntry();
		}
		function onCheckSubscriptionEntry(thisObj) {
			var subscriptionID = $(thisObj).val();
			if ($(thisObj).is(':checked')) {
				if(arrUnchkedSubs.includes(subscriptionID)){
					arrUnchkedSubs = $.grep(arrUnchkedSubs, function(value) {
						return value != subscriptionID;
					});
				}		
				if(!arrChkedSubs.includes(subscriptionID)){
					arrChkedSubs.push(subscriptionID);
				}
			}else{
				if(arrChkedSubs.includes(subscriptionID)){
					arrChkedSubs = $.grep(arrChkedSubs, function(value) {
						return value != subscriptionID;
					});
				}
				if(!arrUnchkedSubs.includes(subscriptionID)){
					arrUnchkedSubs.push(subscriptionID);
				}
			}
			checkSubscriptionEntry();
		}
		function reloadRenewals() {
			subscriptionListTable.draw();
		}
		function generateCustomSubRenewRadioFilterVerbose(filterField) {
			let label = "";
			let value = "";
			const fieldId = filterField.attr('id');

			if (filterField.is(':checked')) {
				switch (fieldId) {
					case 'assocTypeMember':
						label = 'Associated With Member';
						value = $('##associatedMemberName').val() + ' (' + $('##associatedMemberNum').val() + ')';
						let incLinkedRecordFldID = $('input[name="linkedRecords"]:checked').attr('id');
						if (incLinkedRecordFldID) value += ' [' + $(`label[for='${incLinkedRecordFldID}']`).text().trim() + ']';
					break;

					case 'assocTypeGroup':
						label = 'Associated With Group';
						value = $('##associatedGroupName').val();
					break;

					default:
						label = $(`label[for='${fieldId}']`).text().trim();
						value = filterField.val();
				}
			}

			return { label, value };
		}
		async function filterSubGrid() {
			if (($('##fSubStatus').val() || '').length == 0){
				alert("Please select a status.");
				return false;
			}
			
			await mca_generateVerboseFilterMessage('frmFilter');
			reloadRenewals();
		}
		function quickSelectSubRates(isRenewalRate){
			let arrRateIDs = $('select##fRate').find('option[data-isrenewalrate="'+isRenewalRate+'"]').map(function(index,element){ 
					return $(element).attr("value");
				}).toArray();
			$('select##fRate').val(arrRateIDs).trigger('change');
		}
		function closeBox() { MCModalUtils.hideModal(); }	
		
		function checkRenewal() {
			if (isEmptyRecordSelection()){
				alert('You have not selected any subscriptions to renew.');
				return false;
			}
			
			let objSelect = getSubSelections();
			let confirmLink =  '#this.link.confirmGenerateRenewals#&chkAll=' + objSelect.chkAll + '&fSubscribers='+ objSelect.fSubscribers +'&fNotSubscribers=' + objSelect.fNotSubscribers;
			MCModalUtils.showModal({
				isslideout: true,
				size: 'md',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'Confirm Renewal Generation',
				iframe: true,
				contenturl: confirmLink,
				strmodalfooter : {
					classlist: 'd-flex',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##btnSubmit").click',
					extrabuttonlabel: 'Generate Renewals',
				}
			});
			
		}
		function getSubSelections(){
			return {
				chkAll: checkAllSubscrips,
				fSubscribers: checkAllSubscrips ? '' : arrChkedSubs.join(','),
				fNotSubscribers: checkAllSubscrips ? arrUnchkedSubs.join(',') : ''
			};
		}
		function isEmptyRecordSelection() {
			return (totalSubscripsCount == 0 || (!checkAllSubscrips && arrChkedSubs.length == 0) || (checkAllSubscrips && arrUnchkedSubs.length == totalSubscripsCount)) ? true: false;
		}		
		function editMember(mID) {
			window.open('#local.editMemberLink#&memberID=' + mID + '&tab=subscriptions','_blank');
		}
		function showSubTree(mid, sid) {
			MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'Subscription Tree for',
				iframe: true,
				contenturl: '#this.link.showSubTree#&mid=' + mid + '&sid=' + sid
			});
		}
		
		function loadSubRenewalsTab() {
			mca_setupDatePickerRangeFields('fTermStartFrom','fTermStartTo');
			mca_setupDatePickerRangeFields('fTermEndFrom','fTermEndTo');
			mca_setupCalendarIcons('frmFilter');
			mca_setupSelect2();

			$('body').on('change', '##fSubType', function(e) {
				mca_callChainedSelect('fSubType', 'fSubscription', link_adminHomeResource, 'subs', 'getSubscriptionsForSubType', 'typeid', 0, false, false);
				$('##fRate').empty().trigger('change');
			});
			$('body').on('change', '##fSubscription', function(e) {
				mca_callChainedSelect('fSubscription', 'fRate', link_adminHomeResource, 'subs', 'getSubRatesForSub', 'subid', 0, true, false, [{name:'data-isrenewalrate', datafield:'isrenewalrate'}]);
			});

			if ($('##associatedMemberID').val()==0 && $('##associatedGroupID').val()==0) $("##aClearAssocType").hide();

			$(".assocType").on("click",function(){	
				var assocType = $('input:radio[name=assocType]:checked').val();
				if (assocType != undefined) {
					if (assocType == "group") selectGroupInvFilter();
					else selectMemberInvFilter();
				}
				$("##aClearAssocType").show();
			});

			$("##aClearAssocType").on("click",function() {
				$(".assocType").each(function(){
					$(this).attr("checked",false);
				});
				$('##associatedVal').html("");
				$('##associatedMemberID').val(0);
				$('##associatedGroupID').val(0);
				$('##associatedMemberName').val('');
				$('##associatedMemberNum').val('');
				$('##associatedGroupName').val('');
				$("##aClearAssocType").hide();
				$("##expandSearch").hide();
			});
		}
		function selectGroupInvFilter() {
			var selhref = link_grpSelectGotoLink+'&mode=direct&fldName=associatedGroupID&retFunction=top.updateGroupField&dispTitle=' + escape('Filter Subscribers by Group');
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Filter Subscribers by Group',
				iframe: true,
				contenturl: selhref,
				strmodalfooter: {
					classlist: 'd-none'
				}
			});
		}
		function selectMemberInvFilter() {
			var selhref = link_memSelectGotoLink+'&mode=direct&fldName=associatedMemberID&retFunction=top.updateField&dispTitle=';
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Filter Subscribers by Member',
				iframe: true,
				contenturl: selhref,
				strmodalfooter : {
					classlist: 'd-none',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '',
					extrabuttonlabel: 'Submit',
				}
			});
		}
		function updateField(fldID, mID, mNum, mName) {
			var fld = $('##'+fldID);
			var fldName = $('##associatedVal');
			fld.val(mID);
			if ((mName.length > 0) && (mNum.length > 0)) {
				$('##associatedMemberName').val(mName);
				$('##associatedMemberNum').val(mNum);
				fldName.html('<div style="padding:8px 0;"><b>' + mName + ' (' + mNum + ')</b></div>');
				$('##expandSearch').show();
				$('##associatedGroupID').val(0);
			} else {
				fldName.html('');
				$('##expandSearch').hide();
			}
		}
		function updateGroupField(fldID,gID,gPath) {
			var fld = $('##'+fldID);
			var fldName = $('##associatedVal');
			fld.val(gID);
			if (gPath.length > 0) {
				var newgPath = gPath.split("\\");
					newgPath.shift();
					newgPath = newgPath.join(" \\ ");	
				$('##associatedGroupName').val(newgPath);
				fldName.html('<div style="padding:8px 0;"><b>' + newgPath + '</b></div>');
				$('##associatedMemberID').val(0);
				$('##expandSearch').hide();
			} else {
				fldName.html('');
				$('##expandSearch').hide();
			}
		}
		
		<!--- history --->
		function toggleATRow(rowID) {
			var st = $('##atChanges_' + rowID).css("display");
			if (st == 'none') {
				$('##atChanges_' + rowID).show();
				$('##atTreeImg_' + rowID).attr("src","/assets/common/images/tree-open.jpg");
			} else {
				$('##atChanges_' + rowID).hide();
				$('##atTreeImg_' + rowID).attr("src","/assets/common/images/tree-closed.jpg");
			}
		}
		function toggleATGrid() {
			var st = $('##mcg_gridboxAT').css("display");
			if (st == 'none') {
				$('##mcg_gridboxAT').show();
				$('##auditResultsShow').hide();
				$('##auditResultsHide').show();
			} else {
				$('##mcg_gridboxAT').hide();
				$('##auditResultsShow').show();
				$('##auditResultsHide').hide();
			}
		}
		function clearFilterSubGrid() {
			/* since reset() won't clear fields with default values */
			$('##frmFilter input[type="hidden"], ##frmFilter input[type="text"], ##fHasCardOnFile').val('');
			$('##fSubPaymentStatus, ##fFreq, ##fSubType, ##fSubscription').val(0);
			$('##fRate').empty().trigger('change');
			$('##aClearAssocType').click();
			$('##fSubStatus').val('A').trigger('change');
			filterSubGrid();
		}
		async function initSubRenewals() {
			<cfif local.SubRenewalsFilter.fSubType gt 0>
				await mca_callChainedSelect('fSubType', 'fSubscription', link_adminHomeResource, 'subs', 'getSubscriptionsForSubType', 'typeid', #local.SubRenewalsFilter.fSubscription#, false, false);
				<cfif local.SubRenewalsFilter.fSubscription gt 0>
					await mca_callChainedSelect('fSubscription', 'fRate', link_adminHomeResource, 'subs', 'getSubRatesForSub', 'subid', '#local.SubRenewalsFilter.fRate#', true, false, [{name:'data-isrenewalrate', datafield:'isrenewalrate'}]);
				</cfif>
			</cfif>

			loadSubRenewalsTab();
			initSubscriptionListTable();
		}
		
		$(function(){
			initSubRenewals();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.js)#">

<cfoutput>
<h4>Generate Renewals</h4>
<div class="toolButtonBar pb-1">
	<div><a href="##" onclick="checkRenewal();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to Generate Renewals for the Selected Subscribers."><i class="fa-solid fa-rotate-right"></i> Generate Renewals for Selected Subscribers</a></div>
</div>

<div id="divFilterForm" class="mb-2" style="display:none;">
	<form name="frmFilter" id="frmFilter" onsubmit="filterSubGrid();return false;" data-filterwrapper="divFilterForm" data-verbosemsgwrapper="divFilterVerbose" data-customverbose-radio="generateCustomSubRenewRadioFilterVerbose" data-filterkey="#local.filterKeyName#">
		<div class="card card-box mb-1">
			<div class="card-header py-1 bg-light">
				<div class="card-header--title font-weight-bold font-size-lg">
					Filter Subscriptions
				</div>
			</div>
			<div class="card-body">
				<div class="row">
					<div class="col-xl-6 col-lg-12">
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fSubType" id="fSubType" class="form-control">
									<option value="0">All Subscription Types</option>
									<cfloop query="local.qrySubTypes">
										<cfset local.allowGenerateMassRenewals = XMLSearch(local.qrySubTypes.subTypePerms,"string(/rights/right[@functionName='GenerateMassRenewals']/@allowed)")>
										<cfif local.allowGenerateMassRenewals>
											<option value="#local.qrySubTypes.typeID#"<cfif local.qrySubTypes.typeID eq local.SubRenewalsFilter.fSubType> selected="selected"</cfif>>#local.qrySubTypes.typeName#</option>
										</cfif>
									</cfloop>
								</select>
								<label for="fSubType">Subscription Type</label>
							</div>
						</div>
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fSubscription" id="fSubscription" class="form-control">
									<option value="0">All Subscriptions</option>
								</select>
								<label for="fSubscription">Subscription</label>
							</div>
						</div>
						<div class="form-group">
							<div class="d-flex align-items-center mb-1">
								<span class="text-grey small mx-1">Quickly Select: </span>
								<a href="javascript:quickSelectSubRates(0);" class="badge badge-neutral-second text-second mr-1">Join Rates</a>
								<a href="javascript:quickSelectSubRates(1);" class="badge badge-neutral-second text-second">Renewal Rates</a>
							</div>
							<div class="form-label-group mb-2">
								<select name="fRate" id="fRate" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2"></select>
								<label for="fRate">Rate</label>
							</div>
						</div>
						<div class="form-row">
							<div class="col">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="fTermStartFrom" id="fTermStartFrom" value="#local.SubRenewalsFilter.fTermStartFrom#" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fTermStartFrom"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermStartFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="fTermStartFrom">Start Date From</label>
										</div>
									</div>
								</div>
							</div>
							<div class="col">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="fTermStartTo" id="fTermStartTo" value="#local.SubRenewalsFilter.fTermStartTo#" size="16" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fTermStartTo"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermStartTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="fTermStartTo">Start Date To</label>
										</div>
									</div>
								</div>
							</div>
						</div>
						
						<div class="form-row">
							<div class="col">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="fTermEndFrom" id="fTermEndFrom" value="#local.SubRenewalsFilter.fTermEndFrom#" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fTermEndFrom"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermEndFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="fTermEndFrom">End Date From</label>
										</div>
									</div>
								</div>
							</div>
							<div class="col">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<div class="input-group dateFieldHolder">
											<input type="text" name="fTermEndTo" id="fTermEndTo" value="#local.SubRenewalsFilter.fTermEndTo#" size="16" class="form-control dateControl">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fTermEndTo"><i class="fa-solid fa-calendar"></i></span>
												<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fTermEndTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
											</div>
											<label for="fTermEndTo">End Date To</label>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="col-xl-6 col-lg-12">
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fSubStatus" id="fSubStatus" multiple="true" class="form-control form-control-sm" data-toggle="custom-select2">
									<cfloop query="local.qrySubStatuses">
										<option value="#local.qrySubStatuses.statusCode#"<cfif (listFind(local.SubRenewalsFilter.fSubStatus, local.qrySubStatuses.statusCode) neq 0) OR (local.SubRenewalsFilter.fSubStatus eq "0" and local.qrySubStatuses.statusCode eq "A")> selected</cfif>>#local.qrySubStatuses.statusName#</option>
									</cfloop>
								</select>
								<label for="fSubStatus">Status</label>
							</div>
						</div>
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fSubPaymentStatus" id="fSubPaymentStatus" class="form-control">
									<option value="0">All Activation Options</option>
									<cfloop query="local.qryPaymentStatuses">
										<option value="#local.qryPaymentStatuses.statusCode#" <cfif local.SubRenewalsFilter.fSubPaymentStatus eq local.qryPaymentStatuses.statusCode>selected</cfif>>#local.qryPaymentStatuses.statusName#</option>
									</cfloop>
								</select>
								<label for="fSubPaymentStatus">Activation Option</label>
							</div>
						</div>
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fFreq" id="fFreq" class="form-control">
									<option value="0">All Frequencies</option>
									<cfloop query="local.qryFrequencies">
										<option value="#local.qryFrequencies.frequencyID#"<cfif local.SubRenewalsFilter.fFreq eq local.qryFrequencies.frequencyID> selected</cfif>>#local.qryFrequencies.frequencyName#</option>
									</cfloop>
								</select>
								<label for="fFreq">Frequency</label>
							</div>
						</div>
						<div class="form-group">
							<div class="form-label-group mb-2">
								<select name="fHasCardOnFile" id="fHasCardOnFile" class="form-control">
									<option value="">With or Without Pay Method Associated</option>
									<option value="Y"<cfif local.SubRenewalsFilter.fHasCardOnFile eq "Y"> selected</cfif>>With Pay Method Associated</option>
									<option value="N"<cfif local.SubRenewalsFilter.fHasCardOnFile eq "N"> selected</cfif>>With no Pay Method Associated</option>
								</select>
								<label for="fHasCardOnFile">Pay Method</label>
							</div>
						</div>
					</div>
				</div>
			
				<div class="form-group row">
					<div class="col-md-12">
						<div class="row">
							<div class="col-auto">
								Associated With:
							</div>
							<div class="col">
								<div class="form-check form-check-inline">
									<input type="radio" name="assocType" id="assocTypeMember" class="assocType form-check-input"<cfif local.SubRenewalsFilter.associatedMemberID gt 0> checked</cfif> value="member">
									<label class="form-check-label" for="assocTypeMember">A Specific Member</label>
								</div>
								<div class="form-check form-check-inline">
									<input type="radio" name="assocType" id="assocTypeGroup" class="assocType form-check-input"<cfif local.SubRenewalsFilter.associatedGroupID gt 0 >checked</cfif> value="group">
									<label class="form-check-label" for="assocTypeGroup">A Specific Group</label>
								</div>
								<a href="##" id="aClearAssocType" class="ml-2">clear</a>
								<div id="associatedVal">
									<cfset local.showExpandSearch = false>
									<cfif local.SubRenewalsFilter.associatedMemberID gt 0>
										<cfset local.showExpandSearch = true>
										<div class="py-3"><b>#local.SubRenewalsFilter.associatedMemberName# (#local.SubRenewalsFilter.associatedMemberNum#)</b></div>
									<cfelseif local.SubRenewalsFilter.associatedGroupID gt 0>
										<div class="py-3"><b>#local.SubRenewalsFilter.associatedGroupName#</b></div>
									</cfif>
								</div>
								<input type="hidden" name="associatedMemberID" id="associatedMemberID" value="#local.SubRenewalsFilter.associatedMemberID#">
								<input type="hidden" name="associatedMemberName" id="associatedMemberName" value="#local.SubRenewalsFilter.associatedMemberName#">
								<input type="hidden" name="associatedMemberNum" id="associatedMemberNum" value="#local.SubRenewalsFilter.associatedMemberNum#">
								<input type="hidden" name="associatedGroupID" id="associatedGroupID" value="#local.SubRenewalsFilter.associatedGroupID#">
								<input type="hidden" name="associatedGroupName" id="associatedGroupName" value="#local.SubRenewalsFilter.associatedGroupName#">
							</div>
							<div class="row col-md-12" id="expandSearch"<cfif NOT local.showExpandSearch> style="display:none"</cfif>>
								<div class="col-md-12">
									<div class="row">
										<div class="col-sm-12 pr-md-0 font-weight-bold my-2">
											Expand search to consider linked records
										</div>
										<div class="col-md-10 col-sm-12">
											<div class="form-check form-check-inline">
												<input type="radio" name="linkedRecords" id="linkedRecordsAll" value="all" class="form-check-input filter-exc-verbose" <cfif local.SubRenewalsFilter.linkedRecords EQ "all">checked="true"</cfif>>
												<label class="form-check-label" for="linkedRecordsAll">Include children of the selected records in search</label>
											</div>
										</div>
										<div class="col-md-10 col-sm-12">
											<div class="form-check form-check-inline">
												<input type="radio" name="linkedRecords" id="linkedRecordSelected" value="selected" class="form-check-input filter-exc-verbose" <cfif local.SubRenewalsFilter.linkedRecords EQ "selected">checked="true"</cfif>>
												<label class="form-check-label" for="linkedRecordSelected">Only include the specific selected records</label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="card-footer p-2 text-right">
				<button type="button" name="btnResetFilterSubs" class="btn btn-sm btn-secondary" onclick="clearFilterSubGrid();">Clear Filters</button>
				<button type="submit" name="btnFilterSubs" class="btn btn-sm btn-primary">
					<i class="fa-light fa-filter"></i> Filter Subscriptions
				</button>
				<button type="button" class="btnReApplyFilter d-none" onclick="reloadRenewals();"></button>
			</div>
		</div>
	</form>
</div>

<div id="divFilterVerbose" class="mb-2" style="display:none;"></div>
<table id="subscriptionListTable" class="table table-sm table-striped table-bordered" style="width:100%">
	<thead>
		<tr>
			<th><input type="checkbox" name="masterCheckBox" id="masterCheckBox" onclick="doCheckAllSubscrips(this.checked);" value="1"></th>
			<th>Name</th>
			<th>Subscription</th>
			<th>Start</th>
			<th>End</th>
			<th>Grace</th>
		</tr>
	</thead>
</table>

<!--- audit trail --->
<a id="auditT" name="auditT"></a>

<div class="row mt-5 mb-3">
	<div class="col-xl-12">
		<div class="card card-box mb-1">
			<div class="card-header py-1 bg-light">
				<div class="card-header--title font-weight-bold font-size-lg">
					Renewal History <cfif ArrayLen(local.subRenewalAuditTrail.arrValue) gt 0>(#local.subRenewalAuditTrail.totalCount#)</cfif>
				</div>
			</div>
			<div class="card-body p-1">
				<cfif ArrayLen(local.subRenewalAuditTrail.arrValue) gt 0>
					<div id="auditResultsShow"<cfif local.auditTrailFlag eq 'all'> style="display:none;"</cfif>>
						<input type="button" id="btnShowHistory" class="btn btn-sm btn-primary" onClick="toggleATGrid()" value="Show History" />
					</div>
					<div id="auditResultsHide" class="my-1 text-right"<cfif local.auditTrailFlag neq 'all'> style="display:none;"</cfif>>
						<input type="button" id="btnHideHistory" class="btn btn-sm btn-secondary" onClick="toggleATGrid()" value="Hide History" /> <cfif local.subRenewalAuditTrail.totalCount neq local.subRenewalAuditTrail.foundCount><input type="button" id="btnShowAllHistory" class="btn btn-sm btn-primary" onClick="document.location.href='#this.link.listRenewals#&at=all##auditT'" value="Show All History" /></cfif>
					</div>
					<div id="mcg_gridboxAT"<cfif local.auditTrailFlag neq 'all'> style="display: none;"</cfif>>
						<table class="table table-sm mt-3">
							<thead>
								<tr>
									<th></th>
									<th>Date</th>
									<th>Description</th>
									<th>Updated By</th>
								</tr>
							</thead>
							<tbody>
							<cfloop from="1" to="#ArrayLen(local.subRenewalAuditTrail.arrValue)#" index="local.atNdx">
								<cfset local.currATItem = local.subRenewalAuditTrail.arrValue[local.atNdx]>
								<cfset local.atTS = parseDateTime(local.currATItem["_id__timestamp"])>
								<cfset local.actMemberInfo = application.objMember.getMemberInfo(memberID=local.currATItem.ACTORMEMBERID)>
								
								<tr onclick="toggleATRow('#local.currATItem["_id"].ToString()#')">
									<td class="align-top"><cfif arrayLen(local.currATItem.MESSAGES)><img id="atTreeImg_#local.currATItem["_id"].ToString()#" src="/assets/common/images/tree-closed.jpg" /></cfif></td>
									<td class="align-top">#DateFormat(local.atTS, "m/d/yyyy")# #TimeFormat(local.atTS, "HH:mm:ss")#</td>
									<td class="align-top">#local.currATItem.MAINMESSAGE#</td>
									<td class="align-top">#RTrim(local.actMemberInfo.firstname & ' ' &local.actMemberInfo.middleName)# #RTrim(local.actMemberInfo.lastName & ' ' & local.actMemberInfo.suffix)#</td>
								</tr>
								<cfif arrayLen(local.currATItem.MESSAGES)>
									<tbody id="atChanges_#local.currATItem["_id"].ToString()#" style="display:none;">
										<tr class="<cfif local.atNdx mod 2 is 0>ev_modern<cfelse>odd_modern</cfif>" onclick="toggleATRow('#local.currATItem["_id"].ToString()#')">
											<td colspan="2"></td>
											<td colspan="2">
												<cfset local.loopMemberID = 0>
												<cfloop array="#local.currATItem.MESSAGES#" index="local.thisMessage">
													<cfif local.loopMemberID neq local.thisMessage.memberID>
														<cfif local.loopMemberID neq 0><br><br></cfif>
														<a href="#local.editMemberLink#&memberID=#local.thisMessage.memberID#&tab=subscriptions">#local.thisMessage.memberName#</a><br>
														<cfset local.loopMemberID = local.thisMessage.memberID>
													</cfif>
													<cfset local.atMsgLen = Len(local.thisMessage.errType) + Len(local.thisMessage.errMessage) + 5>
													<cfif local.atMsgLen gt 100>
														<cfset local.firstTimeInLoop = true>
														<cfset local.msgCutoff = 100>
														<cfset local.workingString = local.thisMessage.errType & ": " & local.thisMessage.errMessage>
														<cfloop condition="(Len(local.workingString) gt 0)">
															<cfif local.firstTimeInLoop>
																<cfset local.firstTimeInLoop = false>
																&nbsp;&nbsp; 
															<cfelse>
																&nbsp;&nbsp;&nbsp;
															</cfif>
															<cfif (Find(" ", local.workingString) neq 0) AND (Len(local.workingString) gt local.msgCutoff)>
																<cfset local.currSpaceIndex = Left(local.workingString, local.msgCutoff).lastIndexOf(" ")>
															<cfelse>
																<cfset local.currSpaceIndex = Len(local.workingString)>
															</cfif>
															#Left(local.workingString, local.currSpaceIndex)#<br>
															<cfif (Len(local.workingString)-local.currSpaceIndex) gt 0>
																<cfset local.workingString = Right(local.workingString, Len(local.workingString)-local.currSpaceIndex)>
															<cfelse>
																<cfset local.workingString = "">
															</cfif>
														</cfloop>
													<cfelse>
														&nbsp;&nbsp;#local.thisMessage.errType#: #local.thisMessage.errMessage#<br>
													</cfif>
												</cfloop>
											</td>
										</tr>
									</tbody>
								</cfif>
							</cfloop>
							</tbody>
						</table>
					</div>
				<cfelse>
					<div id="auditResultsDesc">
						No renewals recorded.
					</div>
				</cfif>
			</div>
		</div>
	</div>
</div>
</cfoutput>