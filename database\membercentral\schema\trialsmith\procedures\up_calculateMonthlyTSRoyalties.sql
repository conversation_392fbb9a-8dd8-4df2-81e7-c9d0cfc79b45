ALTER PROC dbo.up_calculateMonthlyTSRoyalties
@startDate datetime,
@endDate datetime

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @startDateSDT smalldatetime = SMALLDATETIMEFROMPARTS(year(@startDate),MONTH(@startDate),DAY(@startDate),0,0);
	declare @endDateSDT smalldatetime = SMALLDATETIMEFROMPARTS(year(@endDate),MONTH(@endDate),DAY(@endDate),23,59);
	declare @numMonthsInRange int = dateDiff(m,@startDate,dateadd(d,1,@endDate));

	declare @approvedStatusID int 
	select @approvedStatusID=statusID from trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

	IF OBJECT_ID('tempdb..#tmpRoyalties') IS NOT NULL 
		DROP TABLE #tmpRoyalties;
	IF OBJECT_ID('tempdb..#tmpDocsApproved') IS NOT NULL 
		DROP TABLE #tmpDocsApproved;
	CREATE TABLE #tmpRoyalties (orgcode varchar(10), tsDocSalePct decimal(18,2), tsDocContribAmt decimal(18,2), 
		tsSubSalePct decimal(18,2), eclipsRoyalty decimal(18,2), DocRoyaltyAmtBilled decimal(18,2), 
		DocRoyaltyAmt decimal(18,2), DocRoyaltyAmtSpecialBilled decimal(18,2), DocRoyaltySpecialAmt decimal(18,2), 
		DocsContributed int, DocsContributedRoyaltyAmt decimal(18,2), subAmtBilled decimal(18,2), 
		SubRoyaltyAmt decimal(18,2), eclipsRoyaltyAmt decimal(18,2), 
		totalRoyalty AS DocRoyaltyAmt+SubRoyaltyAmt+DocsContributedRoyaltyAmt+eclipsRoyaltyAmt+DocRoyaltySpecialAmt);
	CREATE TABLE #tmpDocsApproved (documentID int, orgcode varchar(10));

	INSERT INTO #tmpRoyalties (orgcode, tsDocSalePct, tsDocContribAmt, tsSubSalePct, eclipsRoyalty, 
		DocRoyaltyAmtBilled, DocRoyaltyAmt, DocRoyaltyAmtSpecialBilled, DocRoyaltySpecialAmt, DocsContributed, 
		DocsContributedRoyaltyAmt, subAmtBilled, SubRoyaltyAmt, eclipsRoyaltyAmt)
	select tla.state, mcb.tsDocSalePct, mcb.tsDocContribAmt, mcb.tsSubSalePct, mcb.eclipsRoyalty, 
		0, 0, 0, 0, 0, 0, 0, 0, mcb.eclipsRoyalty * @numMonthsInRange
	from dbo.depoTlA as tla
	inner join dbo.memberCentralBilling as mcb on mcb.orgCode = tla.state;

	-- Document Royalty Amount Billed from Document Royalty Report
	update r
	set r.DocRoyaltyAmtBilled = tmp.amountBilled,
		r.DocRoyaltyAmt = (tmp.amountBilled*(r.tsDocSalePct/100))
	from #tmpRoyalties as r
	inner join (
		select t.SourceState, sum(AmountBilled) as AmountBilled
		from dbo.depoTransactions t
		where (accountCode between 3000 and 3999 or accountCode = 8000)
		and t.DatePurchased between @startDateSDT and @endDateSDT
		and (t.royaltyArrangementid is null or t.royaltyArrangementid = 0)
		group by t.SourceState
		having sum(AmountBilled) <> 0
	) as tmp on tmp.SourceState = r.orgcode;

	-- Special Document Royalty Arrangements from the Document Royalty Report with Special Pricing selected
	update r
	set r.DocRoyaltyAmtSpecialBilled = tmp.amountBilled,
		r.DocRoyaltySpecialAmt = tmp.royaltyAmount
	from #tmpRoyalties as r
	inner join (
		select SourceState, sum(AmountBilled) as amountBilled, sum(royaltyAmount) as royaltyAmount
		from (
			select t.royaltyArrangementid, ra.tsDocRASalePct, t.SourceState, sum(AmountBilled) as AmountBilled, 
				cast(SUM(AmountBilled)*(CAST(ra.tsDocRASalePct as decimal(18,2))/100) as decimal(18,2)) as royaltyAmount
			from dbo.depoTransactions t
			inner join dbo.depoDocumentsRoyaltyArrangements as ra on ra.royaltyArrangementid = t.royaltyArrangementid
			where (accountCode between 3000 and 3999 or accountCode = 8000)
			and t.DatePurchased between @startDateSDT and @endDateSDT
			group by t.royaltyArrangementid, ra.tsDocRASalePct, t.SourceState
			having sum(AmountBilled) <> 0
		) as innerTmp
		group by SourceState
	) as tmp on tmp.SourceState = r.orgcode;

	-- Documents Approved in the date range not already paid out (changed in Aug 2025 from just contributed in date range)
	INSERT INTO #tmpDocsApproved (documentID, orgcode)
	SELECT distinct doc.documentID, doc.state
	from dbo.depoDocuments as doc
	INNER JOIN dbo.depoDocumentStatusHistory as sh ON sh.DocumentID = doc.DocumentID
		and sh.dateEntered between @startDate and @endDate
		and sh.statusID = 4
	inner join trialsmith.dbo.depoDocumentStatusHistory as current_dsh on current_dsh.depoDocumentHistoryID = doc.currentStatusHistoryID
		and current_dsh.statusID = @approvedStatusID
	where doc.DocumentTypeID = 1
	and doc.paidOrgRoyalty = 0;

	update r
	set r.DocsContributed = tmp.NumberApproved,
		r.DocsContributedRoyaltyAmt = tmp.NumberApproved*r.tsDocContribAmt
	from #tmpRoyalties as r
	inner join (
		SELECT orgcode as contributingAssociation, COUNT(documentID) AS NumberApproved
		from #tmpDocsApproved
		group by orgcode
	) as tmp on tmp.contributingAssociation = r.orgcode;

	UPDATE doc
	SET doc.paidOrgRoyalty = 1
	FROM dbo.depoDocuments as doc
	INNER JOIN #tmpDocsApproved as tmp on tmp.documentID = doc.documentID;


	-- Subscription Royalty Amount Billed from Subscription Royalty Report
	update r
	set r.subAmtBilled = tmp.amountBilled,
		r.SubRoyaltyAmt = (tmp.amountBilled*(r.tsSubSalePct/100))
	from #tmpRoyalties as r
	inner join (
		select SourceState, sum(AmountBilled) as AmountBilled
		from dbo.depoTransactions
		where accountCode in (select acctcode from dbo.membertype where acctcode <> '1201')
		and DatePurchased between @startDateSDT and @endDateSDT
		group by SourceState
		having sum(AmountBilled) <> 0
	) as tmp on tmp.SourceState = r.orgcode;

	-- only include where there is a net royalty
	DELETE FROM #tmpRoyalties
	where totalRoyalty = 0;


	-- log the run
	DECLARE @runID int;

	INSERT INTO platformStatsMC.dbo.ts_MonthlyRoyaltyRun (runDate, reportStartDate, reportEndDate)
	VALUES (getdate(), @startDate, @endDate);
		SELECT @runID = SCOPE_IDENTITY();

	INSERT INTO platformStatsMC.dbo.ts_MonthlyRoyaltyDetail (runID, orgcode, DepoSalePCT, DepoContribAMT, 
		SubSalePCT, eclipsMonthAMT, DepoSales, DepoSpecialSales, DepoContributions, SubscriptionSales, 
		DepoSalesRoyalty, DepoSpecialSalesRoyalty, DepoContributionsRoyalty, SubscriptionSalesRoyalty,
		eclipsRoyalty, TotalRoyalty)
	SELECT @runID, orgcode, tsDocSalePct/100 as DepoSalePCT, tsDocContribAmt as DepoContribAMT, 
		tsSubSalePct/100 as SubSalePCT, eclipsRoyalty as eclipsMonthAMT, 
		DocRoyaltyAmtBilled as DepoSales, DocRoyaltyAmtSpecialBilled as DepoSpecialSales,
		DocsContributed as DepoContributions, subAmtBilled as SubscriptionSales,
		DocRoyaltyAmt as DepoSalesRoyalty, DocRoyaltySpecialAmt as DepoSpecialSalesRoyalty, 
		DocsContributedRoyaltyAmt as DepoContributionsRoyalty, 
		SubRoyaltyAmt as SubscriptionSalesRoyalty, eclipsRoyaltyAmt as eclipsRoyalty, 
		TotalRoyalty
	FROM #tmpRoyalties;


	IF OBJECT_ID('tempdb..#tmpRoyalties') IS NOT NULL 
		DROP TABLE #tmpRoyalties;
	IF OBJECT_ID('tempdb..#tmpDocsApproved') IS NOT NULL 
		DROP TABLE #tmpDocsApproved;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
