<cfscript>
	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
		formName='frmJoinASection',
		formNameDisplay="SDCBA - Join a Section",
		orgEmailTo="<EMAIL>",
		memberEmailFrom="<EMAIL>"
	));

	local._paymentProfileCode = "HGCGMPNK";
	local._profileID = application.objCustomPageUtils.acct_getProfileID(siteid=local.siteID,profileCode=local._paymentProfileCode);
	local.currSubscriberID = event.getValue('selSub',0);
	local.errorCode = event.getValue('eCode',0);
	local.forumQualifyNumOfDays = 1826;
	local.membershipSubscriptionTypeUID = "D46DFDEA-6E46-4043-BA8F-F4AF38DE1B21";
	local.newLawyerForumSubscriptionTypeUID = "AB44CED5-140C-4EAF-94D0-C8A393EAF7F0";
	local.newLawyerForumSubscriptionUID = "DB27C2C3-B906-4E49-A16F-537746F5DC09";
	local.newLawyerForumSateScheduleTypeUID = "8388DC98-D041-4F26-AAD4-25EDF67F0718";
	local.deiSubscriptionTypeUID = "18C865AD-4049-4BED-A107-767568855FE7";
	local.deiSubscriptionUID = "5250FE7E-2AB2-4C3B-B8EA-357CC4014BBA";
	local.deiSubscriptionRateUID = "B52585B8-ACE3-4BCD-8C56-D5820A8A781D";

	local.strPaymentForm = 	application.objPayments.showGatewayInputForm(
																		siteid=local.siteID,
																		profilecode=local._paymentProfileCode,
																		pmid = local.memberid,
																		showCOF = local.memberid EQ local.useMID,
																		usePopupDIVName='paymentinfo'
																	);
	
	local.memberData 			= application.objMember.getMemberInfo(local.useMID);
	local.member_type 			= application.objMember.getMemberViewData(local.useMID,'member_type',local.orgID);
	local.memberEmail.to 		= application.objMember.getMainEmail(local.useMID).email;	
</cfscript>

<!--- Get member's Top Subscriptions --->
<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.memberSubs">
	select s.subscriberID, s.subscriptionID, s.subEndDate, s.subStartDate, s.graceEndDate, st.statusCode as status, s.GLAccountID, subs.uid,subs.subscriptionName
	from dbo.sub_subscribers s
	inner join ams_members mm
	inner join ams_members m on m.memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.useMID#"> on mm.activeMemberID = m.activeMemberID and mm.memberID = s.memberID
	inner join dbo.sub_statuses st on st.statusID = s.statusID AND (st.statusCode = 'A' OR st.statusCode = 'P' or st.statusCode = 'O')
	inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
	inner join dbo.sub_types t on t.typeID = subs.typeID  and (t.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.membershipSubscriptionTypeUID#"> OR subs.uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" list="yes" value="#local.deiSubscriptionUID#">)
	AND t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#"> 
	where s.parentSubscriberID is null
	ORDER BY s.subEndDate desc
</cfquery>

<cfquery dbtype="query" name="local.deiMemberSubs">
	select * from [local].memberSubs where uid = '#local.deiSubscriptionUID#'
</cfquery>

<cfif (local.memberSubs.recordCount gt 0) and (local.errorCode eq 0)>
	<cfif local.currSubscriberID eq 0>
		<cfset local.currSubscriberID = val(local.memberSubs.subscriberID)>
	</cfif>
	
	<!--- get the info for the top subscription --->
	<cfquery dbtype="query" name="local.qryTopSubInfo">
		select subscriberID, subscriptionID, subEndDate, subStartDate, graceEndDate, status, GLAccountID, uid
		from [local].memberSubs
		where subscriberID = #local.currSubscriberID#
	</cfquery>

	<!--- Get member's Sections --->
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.memberSubscriptions">
		select 
			distinct
			grs.subscriberID, subs.subscriptionID, subs.UID, t.typeID, t.typeName 
		from 
			dbo.fn_getRecursiveSubscriptionsByID(<cfqueryparam cfsqltype="cf_sql_integer" value="#local.siteID#">, <cfqueryparam cfsqltype="cf_sql_integer" value="#local.currSubscriberID#">) grs
		inner join dbo.sub_subscriptions subs on subs.subscriptionID = grs.subscriptionID
		inner join dbo.sub_types t on t.typeID = subs.typeID
		where grs.status <> 'D'
		<cfif (local.deiMemberSubs.recordCount gt 0)>
		union all
		select 
			distinct
			grs.subscriberID, subs.subscriptionID, subs.UID, t.typeID, t.typeName 
		from 
			dbo.fn_getRecursiveSubscriptionsByID(<cfqueryparam cfsqltype="cf_sql_integer" value="#local.siteID#">, <cfqueryparam cfsqltype="cf_sql_integer" value="#local.deiMemberSubs.subscriberID#">) grs
		inner join dbo.sub_subscriptions subs on subs.subscriptionID = grs.subscriptionID
		inner join dbo.sub_types t on t.typeID = subs.typeID
		where grs.status <> 'D'
		</cfif>
	</cfquery>
	
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPaymentsScheduled">
		SET NOCOUNT ON;

		declare @orgID int = <cfqueryparam value="#local.orgID#" cfsqltype="cf_sql_integer">;

		IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
			DROP TABLE ##mcSubscribersForAcct;
		IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
			DROP TABLE ##mcSubscriberTransactions;

		CREATE TABLE ##mcSubscribersForAcct (subscriberID int PRIMARY KEY);
		CREATE TABLE ##mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
			invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
			amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
			assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int,
			creditGLAccountID int, INDEX IDX_mcSubscriberTransactions_subscriberID_amountToConsider (subscriberID, amountToConsider));

		INSERT INTO ##mcSubscribersForAcct (subscriberID)
		select li.listItem
		from dbo.fn_IntListToTable(<cfqueryparam value="#ValueList(local.memberSubscriptions.subscriberID)#" cfsqltype="cf_sql_varchar">,',') as li;

		EXEC dbo.sub_getSubscriberTransactions @orgID=@orgID;

		select IsNull(sum(st.amountToConsider), 0) as amountScheduled
		from dbo.sub_subscribers as s
		inner join ##mcSubscriberTransactions as st on st.subscriberID = s.subscriberID
		where s.orgID = @orgID;

		IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
			DROP TABLE ##mcSubscribersForAcct;
		IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
			DROP TABLE ##mcSubscriberTransactions;
	</cfquery>
	
	<cfif local.qryPaymentsScheduled.amountScheduled gt 0>
		<cfset local.paymentsScheduled = true>
	<cfelse>
		<cfset local.paymentsScheduled = false>
	</cfif>

	
	<cfquery dbtype="query" name="local.memberSectionsAndNewLawyerForum">
		select subscriptionID
		from [local].memberSubscriptions
		where typeName = 'Sections' 
		OR uid = lower(<cfqueryparam cfsqltype="cf_sql_varchar" value="#local.newLawyerForumSubscriptionUID#">)
		OR uid = lower(<cfqueryparam cfsqltype="cf_sql_varchar" value="#local.deiSubscriptionUID#">)		
	</cfquery>

			
	<cfset local.memberSectionsAndNewLawyerForumList	= valueList(local.memberSectionsAndNewLawyerForum.subscriptionID)>
	<cfset local.memberSectionsAndNewLawyerForumCount = listLen(local.memberSectionsAndNewLawyerForumList)>

	<!--- Going to assume that all sections are in the same set for the subscription --->
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySetInfo">
		select ao.childSetID, ao.useAcctCodeInSet, ao.PCNum, ao.PCPctOffEach
		from dbo.sub_addons ao
		inner join dbo.sub_subscriptionSets ss on ss.setID = ao.childSetID
		inner join dbo.sub_sets s on s.setID = ss.setID
		inner join dbo.sub_subscriptions subs on subs.subscriptionID = ss.subscriptionID and subs.status <> 'D'
		inner join dbo.sub_types t on t.typeID = subs.typeID and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#"> and t.status <> 'D'
		where ao.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryTopSubInfo.subscriptionID#">
		AND (t.typeName = 'Sections' OR subs.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.newLawyerForumSubscriptionUID#">)
		group by ao.childSetID, ao.useAcctCodeInSet, ao.PCNum, ao.PCPctOffEach
	</cfquery>
	<cfset local.currSetID = local.qrySetInfo.childSetID>

	<!--- get current number of free subscriptions --->
	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCurrFree">
		select count(subscriberID) as freeCount
		from dbo.fn_getRecursiveMemberSubscriptions(<cfqueryparam cfsqltype="cf_sql_integer" value="#local.useMID#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.currSubscriberID#">) grs
		inner join dbo.sub_subscriptionSets ss on ss.subscriptionID = grs.subscriptionID and ss.setID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.currSetID#">
		where grs.status <> 'D'
		and PCFree = 1
	</cfquery>

	<cfset local.currFreeCount = val(local.qryCurrFree.freeCount)>
	
	<cfset local.statusDaysDiff = DateCompare(local.qryTopSubInfo.subStartDate, Now(),"d")>
	<cfif (local.statusDaysDiff gt 0)>
		<cfset local.startDateToUse = DateFormat(local.qryTopSubInfo.subStartDate, "yyyy-mm-dd") & " " & TimeFormat(local.qryTopSubInfo.subStartDate, "HH:mm:ss.l")>
	<cfelse>
		<!--- preserves the hour, thus the TZ --->
		<cfset local.daysDiff = DateDiff("d", local.qryTopSubInfo.subStartDate, Now())>
		<cfset local.startDateToUse = DateAdd("d", local.daysDiff, local.qryTopSubInfo.subStartDate)>
		<cfset local.startDateToUse = DateFormat(local.startDateToUse, "yyyy-mm-dd") & " " & TimeFormat(local.startDateToUse, "HH:mm:ss.l")>
	</cfif>

	<cfset local.endDateToUse = DateFormat(local.qryTopSubInfo.subEndDate, "yyyy-mm-dd") & " " & TimeFormat(local.qryTopSubInfo.subEndDate, "HH:mm:ss.l")>

	<cfset local.endGraceDateToUse = DateFormat(local.qryTopSubInfo.graceEndDate, "yyyy-mm-dd") & " " & TimeFormat(local.qryTopSubInfo.graceEndDate, "HH:mm:ss.l")>
	
	<!--- add a custom hidden field to help prevent bot submission. the value must be blank and the name is generated by a hash of today's date. --->
	<cfset local.nameOfHiddenField = "memid" & hash(dateformat(now(),'yyyymmdd'))>

	<cfif event.getValue('isSubmitted',0) is 1 and cgi.REQUEST_METHOD eq "POST">
	
		<!--- Handle the processing of the form post --->
		<cfif NOT event.valueExists(local.nameOfHiddenField) OR len(event.getValue(local.nameOfHiddenField)) gt 0>
			<cfoutput><div class="bodytext" style="color:##f00;"><b>The form was not completed in its entirety. Click <a href="javascript:history.go(-1)">here</a> to go back.</b></div></cfoutput>
		<cfelse>
			
			<cfscript>
				// param form values
				arguments.event.paramValue('sections','');		
				arguments.event.paramValue('newLawyerForumSubscription','');	
				local.sTotal = 0;
				local.subActualList = listAppend(arguments.event.getValue('sections'),arguments.event.getValue('newLawyerForumSubscription'));			
				local.subList = ListDeleteValue(local.subActualList,local.memberSectionsAndNewLawyerForumList,",") ;
				local.sCount = ListLen(local.subList);
				local.subDeletedList = ListDeleteValue(local.memberSectionsAndNewLawyerForumList,local.subActualList,",") ;	
				local.sDeleteCount = ListLen(local.subDeletedList);		
			</cfscript>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubDeleted">
				select 
					subs.subscriptionID, subs.subscriptionName, subs.GLAccountID ,sets.setName, subs.uid
				from 
					dbo.sub_subscriptions subs
					inner join dbo.sub_subscriptionSets ss on
						ss.subscriptionID = subs.subscriptionID
					inner join dbo.sub_sets sets on 
						sets.setID = ss.setID 
						and sets.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
												
				where 
					subs.status <> 'D'
					and (sets.setName = 'sections' 
					OR subs.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.newLawyerForumSubscriptionUID#">
					OR subs.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.deiSubscriptionUID#">)
								
					<cfif local.sDeleteCount GT 0>					
						and subs.subscriptionID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#local.subDeletedList#">)		
					<cfelse>
						and subs.subscriptionID = 0
					</cfif>	
					order by 
					subs.subscriptionName								
			</cfquery>
	
			<!--- get new section info --->
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubSelected">
				select 
					subs.subscriptionID, subs.subscriptionName, subs.GLAccountID ,sets.setName, subs.uid
				from 
					dbo.sub_subscriptions subs
					inner join dbo.sub_subscriptionSets ss on
						ss.subscriptionID = subs.subscriptionID
					inner join dbo.sub_sets sets on 
						sets.setID = ss.setID 
						and sets.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
												
				where 
					subs.status <> 'D'
					and (
					sets.setName = 'sections' 
					OR subs.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.newLawyerForumSubscriptionUID#">
					OR subs.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.deiSubscriptionUID#">)
					<cfif local.sCount GT 0>					
						and subs.subscriptionID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#local.subList#">)		
					<cfelse>
						and subs.subscriptionID = 0
					</cfif>		
					order by 
					subs.subscriptionName	
						
			</cfquery>
			
			<cfif ListFind(valueList(local.qrySubSelected.uid),local.deiSubscriptionUID )>
				<cfquery dbtype="query" name="local.qryDeiSubSelected">
					select * from [local].qrySubSelected where uid = '#local.deiSubscriptionUID#'						
				</cfquery>
				<!--- CREATE SUBSCRIPTION ----------------------------------------------------------------------------- --->
				<cfset local.subStruct = structNew()>
				
				<cfset local.subStruct.uid = local.qryDeiSubSelected.uid>

				<!--- Get Subscription Rate --->
				<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.rootSubRate">
					set nocount on;

					declare @subscriptionID int, @memberID int, @isRenewalRate bit, @FID int;
					set @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.useMID#">;
					set @isRenewalRate = 0;
					set @subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryDeiSubSelected.subscriptionID#">;
					set @FID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;

					select uid
					from dbo.fn_sub_getMostExclusiveRateInfo(@memberID,@subscriptionID,@isRenewalRate,@FID);
				</cfquery>

				<cfset local.subStruct.rateUID = local.rootSubRate.uid>

				<cfset local.subStruct.overridePerms = true />

				<cfset local.objSubReg = CreateObject("component","model.admin.subscriptions.SubscriptionReg")>
				<cfset local.subReturn = local.objSubReg.autoSubscribe(event=event, memberID=local.useMID, subStruct=local.subStruct, newAsBilled=false)>
				
				<cfset local.activateSubscription = CreateObject("component","model.admin.subscriptions.subscriptions").overrideActivationMemberSubscription(mcproxy_siteID=local.siteID, subscriberID=local.subReturn.rootSubscriberID, actorMemberID=local.useMID)>
				<cfquery dbtype="query" name="local.qrySubSelected">
					select * from [local].qrySubSelected where uid != '#local.deiSubscriptionUID#'						
				</cfquery>
			</cfif>

			<cfset local.subDeletedList = valueList(local.qrySubDeleted.subscriptionID)/>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.memberDeletedSubscriptions">
				select 
					grs.subscriberID, grs.subscriptionID
				from 
					dbo.fn_getRecursiveSubscriptionsByID(<cfqueryparam cfsqltype="cf_sql_integer" value="#local.siteID#">, <cfqueryparam cfsqltype="cf_sql_integer" value="#local.currSubscriberID#">) grs
				where 
				<cfif local.sDeleteCount GT 0>					
					grs.subscriptionID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#local.subDeletedList#">)		
				<cfelse>
					grs.subscriptionID = 0
				</cfif>
				union all
				select 
					grs.subscriberID, grs.subscriptionID
				from 
					dbo.fn_getRecursiveSubscriptionsByID(<cfqueryparam cfsqltype="cf_sql_integer" value="#local.siteID#">, <cfqueryparam cfsqltype="cf_sql_integer" value="#local.deiMemberSubs.subscriberID#">) grs
				where 
				<cfif local.sDeleteCount GT 0>					
					grs.subscriptionID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#local.subDeletedList#">)		
				<cfelse>
					grs.subscriptionID = 0
				</cfif>	
			</cfquery>
			

			<cfset local.qrySubSelectedForSubscriptions = QueryNew("subscriptionid,subscriptionName,amount,rfid,pcfree,GLAccountID,setName","integer,varchar,integer,integer,integer,integer,varchar")>
			<!--- determine price per section and total price for new sections --->
		
			<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
			<cfloop query="local.qrySubSelected">
				<!--- find the RFID for this subscription --->
				<cfquery name="local.qryRate" datasource="#application.dsn.membercentral.dsn#">
					set nocount on;
					
					declare @FID int, @memberid int, @siteID int, @subscriptionid int;
					select @FID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;
					select @siteid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">;
					select @memberid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.useMID#">;
					select @subscriptionid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySubSelected.subscriptionID#">;

					select rfid, rateAmt, numInstallments, frequencyName, frequency, frequencyID, rateName
					from (
						select rf.rfid, rf.rateAmt, rf.numInstallments, f.frequencyName, f.frequency, f.frequencyID, r.rateName, count(rfmp.rfmpid) as rfmpidCount
						from dbo.sub_subscriptions as s
						inner join dbo.sub_rateSchedules as rs on rs.scheduleID = s.scheduleID and rs.status = 'A'
						inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID and r.status = 'A' and r.isRenewalRate = 0 and getdate() between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
						INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteResourceID = r.siteResourceID AND srfrp.functionID = @FID
							and srfrp.siteID =  @siteID
						INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on srfrp.rightPrintID = gprp.rightPrintID
							and gprp.siteID =  @siteID
						inner join ams_members m on m.groupPrintID = gprp.groupPrintID  and m.memberID = @memberID						
						inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID and rf.rateAmt >= 0 and rf.status = 'A'
						inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
						left outer join dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfid = rf.rfid and rfmp.status = 'A' 
						where s.subscriptionID = @subscriptionid
						group by rf.rfid, rf.rateAmt, rf.numInstallments, f.frequencyName, f.frequency, f.frequencyID, r.rateName
					) x 
					where x.rfmpidCount > 0;
				</cfquery>
			
				<cfif local.qryRate.recordcount is not 1>
					<cflocation url="/?#cgi.QUERY_STRING#&eCode=13" addtoken="no">			
				</cfif>
				
				<cfset QueryAddRow(local.qrySubSelectedForSubscriptions)>
				<cfset QuerySetCell(local.qrySubSelectedForSubscriptions,"subscriptionid",local.qrySubSelected.subscriptionid)>
				<cfset QuerySetCell(local.qrySubSelectedForSubscriptions,"subscriptionName",local.qrySubSelected.subscriptionName)>
				<cfset QuerySetCell(local.qrySubSelectedForSubscriptions,"rfid",local.qryRate.rfid)>
				<cfset QuerySetCell(local.qrySubSelectedForSubscriptions,"GLAccountID",local.qrySubSelected.GLAccountID)>
				<cfset QuerySetCell(local.qrySubSelectedForSubscriptions,"setName",local.qrySubSelected.setName)>
				<cfif (local.qryTopSubInfo.uid eq "5179E656-B391-4EB3-9405-BD88FFF87FA0") OR (local.qryTopSubInfo.uid eq "331D5A68-CE43-48C4-927D-3C91C9FF649C") >
					<!--- Law Student --->
					<!--- law students: 3 free, others 15 ea --->
					<!--- if already has 3 or more, they already have their free sections. All checked sections are 15 --->
					<cfset QuerySetCell(local.qrySubSelectedForSubscriptions,"amount", local.qryRate.rateAmt)>
					<cfif local.currFreeCount lt local.qrySetInfo.PCNum>
						<cfset QuerySetCell(local.qrySubSelectedForSubscriptions,"pcfree",1)>
						<cfset local.currFreeCount = local.currFreeCount + 1>
					<cfelse>
						<cfset QuerySetCell(local.qrySubSelectedForSubscriptions,"pcfree",0)>
					</cfif>
				<cfelseif local.qryTopSubInfo.uid eq "23061A03-F2C8-45A4-842D-C370C8B0E3B1">
					<!--- Honorary Member --->
					<!--- HM: all free --->
					<cfset QuerySetCell(local.qrySubSelectedForSubscriptions,"amount",0)>
					<cfset QuerySetCell(local.qrySubSelectedForSubscriptions,"pcfree",0)>
				<cfelse>
					<!--- All Other Memberships --->
					<!--- else: all 15 ea --->
					<cfset QuerySetCell(local.qrySubSelectedForSubscriptions,"amount", local.qryRate.rateAmt)>
					<cfset QuerySetCell(local.qrySubSelectedForSubscriptions,"pcfree",0)>
				</cfif>
			</cfloop>	

			<cfloop query="local.memberDeletedSubscriptions">
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeleteSubscription">
					set nocount on
					declare @subscriberID int, @rc int,  @siteID int
							
					select @siteID = #local.siteID#
					select @subscriberID = #local.memberDeletedSubscriptions.subscriberID#

					EXEC dbo.sub_updateSubscriberStatus @subscriberID=@subscriberID,@newStatusCode="D",@siteID=@siteID,@enteredByMemberID=#local.useMID#,@bypassQueue=0,@result=@rc OUTPUT
					set nocount off
				</cfquery>
			</cfloop>
		

			<cfquery name="local.qrySTotal" dbtype="query">
				select sum(amount) as amt
				from [local].qrySubSelectedForSubscriptions
				where pcfree <> 1
			</cfquery>					
			<cfset local.sTotal = val(local.qrySTotal.amt)>

			<!--- If adding to a billed sub, do not add sales. They will be added by the system later. --->
			<cfset local.addSale = true />
			<cfif local.qryTopSubInfo.status eq 'O'>
				<cfset local.addSale = false />
			</cfif>


			
			<!--- ---------------------- --->
			<!--- Payment and accounting --->
			<!--- ---------------------- --->
			<cfset local.strAccTemp = { totalPaymentAmount=local.sTotal, assignedToMemberID=local.useMID, recordedByMemberID=local.useMID, rc=arguments.event.getCollection() } >
			<cfif local.strAccTemp.totalPaymentAmount gt 0>
				<cfset local.strAccTemp.payment = { detail='SDCBA Join a Section', amount=local.strAccTemp.totalPaymentAmount, profileID=local._profileID, profileCode=local._paymentProfileCode, stopOnError=1 }>
			</cfif>	

			<cfif local.addSale eq true>	
				<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>
				<cfset local.strACCResponse = local.objAccounting.doCustomFormPaymentAndAccounting(argumentcollection=local.strAccTemp)>
			</cfif>


			<!--- if payment not successful --->
			<cfif local.strAccTemp.totalPaymentAmount gt 0 and NOT (local.strACCResponse.paymentResponse.responseCode is 1 and local.strACCResponse.paymentResponse.mc_transactionID gt 0) and local.addSale eq true>
				<cfoutput>
				<style>
					.formTitle{
						background: unset;
						color: ##000;
					}
				</style>
				<script type="text/javascript">
					function _FB_hasValue(obj, obj_type){
						if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){
							tmp = obj.value;
							tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
							if (tmp.length == 0) return false;
							else return true;
						} 
						else if (obj_type == 'SELECT'){
							for (var i=0; i < obj.length; i++) {
								if (obj.options[i].selected){
									tmp = obj.options[i].value;
									tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
									if (tmp.length > 0) return true;
								}
							}
							return false;	
						} 
						else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){
							if (obj.checked) return true;
							else return false;	
						} 
						else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){
							if (obj.length == undefined && obj.checked) return true;
							else{
								for (var i=0; i < obj.length; i++){
									if (obj[i].checked) return true;
								}
							}
							return false;
						}
						else{
							return true;
						}
					}
				
					function _FB_validateForm() {
						var thisForm = document.forms["frmDonate"];
						var arrReq = new Array();
						var lastMtrxErr = '';				
						
						#local.strPaymentForm.jsvalidation#
						
						if (arrReq.length > 0) {
							var msg = 'The following questions are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						document.getElementById('formSubmitButton').disabled = 1;
						return true;
					}
				</script>
		
				<div class="form">
				<div class="tsAppHeading formTitle">#local.formNameDisplay#</div>
				<br/>
				<cfform name="frmDonate"  id="frmDonate" method="POST" action="/?pg=joinASection" onSubmit="return _FB_validateForm();">
					<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="1">
					<cfinput type="hidden" name="#local.nameOfHiddenField#"  id="#local.nameOfHiddenField#" value="">
					<cfloop collection="#arguments.event.getCollection()#" item="local.key">
						<cfif listFindNoCase(arguments.event.getValue('fieldnames',''),local.key) 
							and NOT listFindNoCase("isSubmitted,#local.nameOfHiddenField#,btnSubmit",local.key) 
							and NOT findNoCase("p_#local._profileID#_fld_",local.key)>
							<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
						</cfif>
					</cfloop>
					<div class="page">
						<br/>
						<div class="tsAppBodyText tsAppBodyTextImportant" style="padding:6px;border:1px solid ##f00;">Payment failed. #local.strACCResponse.paymentResponse.publicResponseReasontext#</div>
						<div id="paymentinfo">#local.strPaymentForm.inputForm#</div>
					</div>
					<div align="center"><button id="formSubmitButton" type="submit" class="tsAppBodyButton" name="btnSubmit">Submit Changes</button></div>
				</cfform>
				</cfoutput>

			<cfelse>

				<!--- save info to local database --->
				<cftry>
					<cfset local.subscriberIDs = StructNew()>

					<cfloop query="local.qrySubSelectedForSubscriptions">

						<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAddNewSubscription">
							set nocount on
							
							declare @subscriberID int, @rc int, @activationOptionCode char(1), @siteID int
							
							select @siteID = #local.siteID#
							
							select @activationOptionCode=a.subActivationCode
							from dbo.sub_subscriptions subs
							inner join dbo.sub_activationOptions a 
							<cfif local.paymentsScheduled>
								on a.subActivationID = subs.subActivationID
							<cfelse>
								on a.subActivationID = subs.subAlternateActivationID
							</cfif>
							where subs.subscriptionID = #local.qrySubSelectedForSubscriptions.subscriptionID#
							
							
							EXEC dbo.sub_addSubscriber @orgID=#local.orgid#,
									@memberID=#local.useMID#,
									@subscriptionID=#local.qrySubSelectedForSubscriptions.subscriptionID#,
									@parentSubscriberID=#local.currSubscriberID#, @RFID=#local.qrySubSelectedForSubscriptions.rfid#, 

									<cfif local.qrySetInfo.useAcctCodeInSet eq 1>
										@GLAccountID=#local.qrySubSelectedForSubscriptions.GLAccountID#,
									<cfelse>
										@GLAccountID=#local.qryTopSubInfo.GLAccountID#,
									</cfif>

									@status='O', @subStartDate='#local.startDateToUse#', @subEndDate='#local.endDateToUse#', 

									<cfif len(local.endGraceDateToUse) gt 0>
										@graceEndDate='#local.endGraceDateToUse#', 
									<cfelse>
										@graceEndDate=NULL,
									</cfif>

										@recogStartDate='#local.startDateToUse#', @recogEndDate='#local.endDateToUse#',

									<cfif local.qrySubSelectedForSubscriptions.pcfree eq 1>
										@pcfree=1,
									<cfelse>
										@pcfree=0,
									</cfif>

									@activationOptionCode=@activationOptionCode,
									@recordedByMemberID=#local.useMID#, 
									@bypassQueue=0, 
									@subscriberID=@subscriberID OUTPUT

							update dbo.sub_subscribers
							set lastPrice = convert(decimal(18,2), '#local.qrySubSelectedForSubscriptions.amount#')
							where subscriberID = @subscriberID
							
							<cfif (local.qryTopSubInfo.status eq "A")>
								EXEC dbo.sub_updateSubscriberStatus	@subscriberID=@subscriberID,@newStatusCode="P",@siteID=@siteID,@enteredByMemberID=#local.useMID#,@bypassQueue=0,@result=@rc OUTPUT
								IF @rc = 1
								begin
									EXEC dbo.sub_updateSubscriberStatus	@subscriberID=@subscriberID,@newStatusCode="A",@siteID=@siteID,@enteredByMemberID=#local.useMID#,@bypassQueue=0,@result=@rc OUTPUT
								end
							<cfelseif (local.qryTopSubInfo.status eq "P")>
								EXEC dbo.sub_updateSubscriberStatus @subscriberID=@subscriberID,@newStatusCode="P",@siteID=@siteID,@enteredByMemberID=#local.useMID#,@bypassQueue=0,@result=@rc OUTPUT
							</cfif>

							select @subscriberID as subscriberID
							
							set nocount off
						</cfquery>
						
						<cfset local.subscriberIDs[local.qrySubSelectedForSubscriptions.subscriptionID] = local.qryAddNewSubscription.subscriberID>
					</cfloop>
					
					<cfset local.savedLocal = true>
					<cfcatch type="any">
						<cfset application.objError.SendError(cfcatch=cfcatch)>
						<cfset local.savedLocal = false>
					</cfcatch>
				</cftry>				
	
				<!--- accounting for revenue --->
				<cfif local.savedLocal eq true AND local.addSale eq true>
					<cftransaction>
						<cftry>
						<cfloop query="local.qrySubSelectedForSubscriptions">
							<cfif local.qrySubSelectedForSubscriptions.pcfree eq 1>
								<cfset local.loopPriceToUse = 0>
							<cfelse>
								<cfset local.loopPriceToUse = local.qrySubSelectedForSubscriptions.amount>
							</cfif>

							<cfset local.strACCTemp = { assignedToMemberID	= local.useMID,
														recordedByMemberID	= local.useMID,
														detail = "Section Dues: #local.qrySubSelectedForSubscriptions.SubscriptionName#",
														amount = local.loopPriceToUse,
														transactionDate = now() }>
							<cfif local.qrySetInfo.useAcctCodeInSet eq 1>
								<cfset local.strACCTemp.revenueGLAccountID = local.qrySubSelectedForSubscriptions.GLAccountID>
							<cfelse>
								<cfset local.strACCTemp.revenueGLAccountID = local.qryTopSubInfo.GLAccountID>
							</cfif>
							<cfset local.strACCSale = local.objAccounting.recordSale(argumentcollection=local.strACCTemp)>
							<cfif local.strACCSale.rc IS NOT 0 OR local.strACCSale.transactionID IS 0>
								<cfthrow message="Failed to create sale for #local.qrySubSelectedForSubscriptions.SubscriptionName#">
							<cfelse>
								<cfstoredproc procedure="tr_createApplication" datasource="#application.dsn.membercentral.dsn#">
									<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
									<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
									<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="Admin">
									<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.strACCSale.transactionID#">
									<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="Dues">
									<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.subscriberIDs[local.qrySubSelectedForSubscriptions.subscriptionID]#">
									<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
								</cfstoredproc>
							</cfif>
						</cfloop>	
						
						<cfif StructKeyExists(local.strACCResponse,"paymentResponse") and local.strACCResponse.paymentResponse.responseCode is 1 and local.strACCResponse.paymentResponse.mc_transactionID gt 0>									
							<cfset local.objAccounting.allocateToInvoice(paymentTransactionID=local.strACCResponse.paymentResponse.mc_transactionID, recordedByMemberID=local.useMID, transactionDate=now())>
						</cfif>		

						<!--- Close all Zero Dollar invoices --->
						<cfset local.objAccounting.closeInvoice(closeOnlyZeroDollarInvoices=true)>

						<cfset local.savedAccounting = true>
						<cfcatch type="any">
							<cftransaction action="rollback" />
							<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
							<cfset local.savedAccounting = false>
						</cfcatch>
						</cftry>			
					</cftransaction>
				</cfif>

				<!--- construct email --->
				<cfset local.txtstyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:10pt">
				<cfsavecontent variable="local.pdftext">
					<cfoutput>
						<div style="#local.txtstyle#">
							<p>SDCBA Join a Section form submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
							<p>
								<cfif local.addSale eq true>
									#local.strACCResponse.accResponseMessage#
								</cfif>
								<cfif local.savedLocal>
									This information has already been updated on the website.<br/>
								<cfelse>
									<span style="color:##f00;"><b>This information was not immediately updated on the website.</b></span><br/>
								</cfif>
								<cfif StructKeyExists(local,'savedAccounting') AND local.savedAccounting eq true>
									This revenue has been recorded in the member's transactions on the website.<br/>
								<cfelseif local.addSale eq false>
									<span style="color:##f00;"><b>This revenue was not recorded in the member's transactions on the website. This will be recorded with the main subscription.</b></span><br/>
								<cfelse>
									<span style="color:##f00;"><b>This revenue was not recorded in the member's transactions on the website.</b></span><br/>
								</cfif>		
							</p>			
							<table cellpadding="2" cellspacing="0" border="0">
								<tr style="#local.txtstyle#"><td colspan="2" nowrap><b>Member Information</b></td></tr>
								<tr style="#local.txtstyle#"><td valign="top">MemberNumber:</td><td valign="top">#local.memberData.memberNumber#</td></tr>
								<tr style="#local.txtstyle#"><td valign="top">Name:</td><td valign="top">#session.cfcUser.memberData.firstName# #session.cfcUser.memberData.lastName#</td></tr>
							</table>
							<br/>					
							<table cellpadding="2" cellspacing="0" border="0">
								<tr style="#local.txtstyle#"><td><b>New Sections</b></td></tr>
								<tr style="#local.txtstyle#">
									<td>
										<cfset local.isSectionExits=false>
																							
										<cfloop query="local.qrySubSelected">
											<cfif lcase(local.qrySubSelected.setName) EQ "sections">
												<cfset local.isSectionExits=true>
												#local.qrySubSelected.subscriptionName#<br />
											</cfif>
										</cfloop>	
									
										<cfif local.isSectionExits EQ false>
											No sections were added
										</cfif>
									</td>
								</tr>
							</table>
							<br/>
							<table cellpadding="2" cellspacing="0" border="0">
								<tr style="#local.txtstyle#"><td><b>Deleted Sections</b></td></tr>
								<tr style="#local.txtstyle#">
									<td>
										<cfset local.isDeletedSectionExits=false>
																																	
										<cfloop query="local.qrySubDeleted">
											<cfif lcase(local.qrySubDeleted.setName) EQ "sections">	
												<cfset local.isDeletedSectionExits=true>
												#local.qrySubDeleted.subscriptionName#<br />
											</cfif>
										</cfloop>	
										
										<cfif local.isDeletedSectionExits EQ false>
											No sections were deleted
										</cfif>
									</td>
								</tr>
							</table>
							<br/>
							<table cellpadding="2" cellspacing="0" border="0">
								<tr style="#local.txtstyle#"><td><b>New Forums</b></td></tr>
								<tr style="#local.txtstyle#">
									<td>
										<cfset local.isForumsExits=false>
																						
										<cfloop query="local.qrySubSelected">
											<cfif lcase(local.qrySubSelected.setName) NEQ "sections">	
												<cfset local.isForumsExits=true>
												#local.qrySubSelected.subscriptionName#<br />
											</cfif>
										</cfloop>	
										
										<cfif local.isForumsExits EQ false>
											No Forums were added
										</cfif>
									</td>
								</tr>
							</table>
							<br/>
							<table cellpadding="2" cellspacing="0" border="0">
								<tr style="#local.txtstyle#"><td><b>Deleted Forums</b></td></tr>
								<tr style="#local.txtstyle#">
									<td>
										<cfset local.isDeletedForumsExits=false>													
																				
										<cfloop query="local.qrySubDeleted">
											<cfif lcase(local.qrySubDeleted.setName) NEQ "sections">
												<cfset local.isDeletedForumsExits=true>
												#local.qrySubDeleted.subscriptionName#<br />
											</cfif>
										</cfloop>	
										
										<cfif local.isDeletedForumsExits EQ false>
											No Forums were deleted
										</cfif>
									</td>
								</tr>
							</table>
							<br/>
							<table cellpadding="2" cellspacing="0" border="0">
								<tr style="#local.txtstyle#"><td colspan="2" nowrap><b>Purchase Information</b></td></tr>
								<tr style="#local.txtstyle#"><td valign="top">Section Total:</td><td valign="top">#dollarFormat(local.stotal)#</td></tr>
							</table>
							<br/>
						</div>
					</cfoutput>
				</cfsavecontent>				

				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<p style="font-family:Verdana, Arial, Helvetica, sans-serif;font-size:10pt">Thank you for submitting your SDCBA - Join A Section form! Please print this page - it is your receipt.</p>	
						<hr />
						#local.pdftext#
					</cfoutput>
				</cfsavecontent>

				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email=local.memberEmail.from },
					emailto=[{ name="", email=local.memberEmail.to }],
					emailreplyto=local.ORGEmail.to,
					emailsubject=local.memberEmail.SUBJECT,
					emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
					emailhtmlcontent=local.mailContent,
					siteID=local.siteID,
					memberID=val(local.useMID),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
				)>
				<cfset local.emailSentToUser = local.responseStruct.success>
			
				<!--- SEND EMAIL --->
				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<cfif NOT local.emailSentToUser>
							#session.cfcUser.memberData.firstName# #session.cfcUser.memberData.lastName# was not sent email confirmation due to bad Data.<br />
							Please contact, and let them know.
							<hr />
						</cfif>
						#local.pdftext#	
					</cfoutput>
				</cfsavecontent>

				<cfscript>
					local.arrEmailTo = [];
					local.toEmailArr = listToArray(replace(local.ORGEmail.to,",",";","all"),';');
					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
					}

					if (arrayLen(local.arrEmailTo)) {
						local.responseStruct = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="", email=local.ORGEmail.from },
							emailto=local.arrEmailTo,
							emailreplyto=local.ORGEmail.from,
							emailsubject=local.ORGEmail.SUBJECT,
							emailtitle=local.formNameDisplay,
							emailhtmlcontent=local.mailContent,
							siteID=arguments.event.getValue('mc_siteinfo.siteID'),
							memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
							messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
							sendingSiteResourceID=this.siteResourceID
						);
					}
				</cfscript>

				<!--- locate to landing page --->
				<cflocation url="/?pg=formlanding" addtoken="no">
	
			</cfif>
		</cfif>
	
	<cfelse>
		<!--- DISPLAY THE FORM --->

		<!--- Get New Lawyer Division Subscription --->
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.sectionsAndNewLawyerForumSubscription">
			set nocount on;

			declare @FID int, @memberid int, @siteID int, @subscriptionid int;
			select @FID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;
			select @siteid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">;
			select @memberid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.useMID#">;

			select subscriptionID, subscriptionName, subscriptionUID, rfid, rateAmt, numInstallments, frequencyName, frequency, 
				frequencyID, rateName, rateScheduleTypeUID, subscriptionTypeUID, typeName, setName
			from ( 
					select 
						s.subscriptionID, 
						s.subscriptionName, 
						s.uid as subscriptionUID,
						rf.rfid, 
						rf.rateAmt, 
						rf.numInstallments, 
						f.frequencyName, 
						f.frequency, 
						f.frequencyID, 
						r.rateName, 
						rs.uid as rateScheduleTypeUID,
						count(rfmp.rfmpid) as rfmpidCount,
						t.uid as subscriptionTypeUID,
						t.typeName,
						sets.setName
					from dbo.sub_subscriptions as s 
						inner join dbo.sub_types t on t.typeID = s.typeID  
						inner join dbo.sub_subscriptionSets ss WITH(NOLOCK) on ss.subscriptionID = s.subscriptionID 
						inner join dbo.sub_sets sets WITH(NOLOCK) on sets.setID = ss.setID and sets.siteID = @siteid 
						inner join dbo.sub_rateSchedules as rs on rs.scheduleID = s.scheduleID and rs.status = 'A' 
						inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID and r.status = 'A' and r.isRenewalRate = 0 and getdate() between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
						INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteResourceID = r.siteResourceID AND srfrp.functionID = @FID
							and srfrp.siteID =  @siteID
						INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on srfrp.rightPrintID = gprp.rightPrintID
							and gprp.siteID =  @siteID
						inner join ams_members m on m.groupPrintID = gprp.groupPrintID and m.memberID = @memberID	
						inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID and rf.rateAmt >= 0 and rf.status = 'A' 
						inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID 
						left outer join dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfid = rf.rfid and rfmp.status = 'A' 
					where 
						sets.setName = 'sections' OR			
						(
							s.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.newLawyerForumSubscriptionUID#">
							AND t.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.newLawyerForumSubscriptionTypeUID#">
							AND rs.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.newLawyerForumSateScheduleTypeUID#">
						)
						OR
						(
							s.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.deiSubscriptionUID#"> 
							AND t.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.deiSubscriptionTypeUID#">
							AND r.uid = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.deiSubscriptionRateUID#">
						)
					group by 
						s.subscriptionID, 
						s.subscriptionName, 
						s.uid,
						rf.rfid, 
						rf.rateAmt, 
						rf.numInstallments, 
						f.frequencyName, 
						f.frequency, 
						f.frequencyID, 
						r.rateName,
						rs.uid,
						t.uid,
						t.typeName,
						sets.setName
			) x 
			where x.rfmpidCount > 0 
			order by x.subscriptionName;
		</cfquery>

		<cfquery dbtype="query" name="local.sections">
			select * from [local].sectionsAndNewLawyerForumSubscription where lower(setName) = 'sections'
		</cfquery>

		<cfquery dbtype="query" name="local.newLawyerForumSubscription">
			select * from [local].sectionsAndNewLawyerForumSubscription where lower(setName) != 'sections'
		</cfquery>
		<cfset local.numFreeToUse = local.qrySetInfo.PCNum - local.currFreeCount>
		<cfif local.numFreeToUse lt 0>
			<cfset local.numFreeToUse = 0>
		</cfif>
		
		<!--- Local variables from above queries populated towards bottom of page --->
		<cfscript>
		local.column1 = round((local.sections.recordCount + .49) / 2);
		</cfscript>
	
		<!--- JavaScript functions to calculate totals --->
		<cfsavecontent variable="local.js">
			<cfoutput>
			<style>
				.formTitle{
					background: unset;
					color: ##000;
				}
			</style>
			<script type="text/javascript">
				function _FB_hasValue(obj, obj_type) {
					if (obj_type == 'TEXT' || obj_type == 'TEXTAREA') {
						tmp = obj.value;
						tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
						if (tmp.length == 0) return false;
						else return true;
					} else if (obj_type == 'SELECT') {
						for (var i=0; i < obj.length; i++) {
							if (obj.options[i].selected) {
								tmp = obj.options[i].value;
								tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
								if (tmp.length > 0) return true;
							}
						}
						return false;	
					} else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX') {
						if (obj.checked) return true;
						else return false;	
					} else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX') {
						if (obj.length == undefined && obj.checked) return true;
						else {
							for (var i=0; i < obj.length; i++) {
								if (obj[i].checked) return true;
							}
						}
						return false;
					} else {
						return true;
					}
				}
				function _FB_validate() {
					var obj = getTotalDue();
					if (obj.sCount > 0) {
						if (obj.sTotal > 0) {
							var thisForm = document.forms['frmSection'];
							var arrReq = new Array();
							#local.strPaymentForm.jsvalidation#
							if (arrReq.length > 0) {
								var msg = 'The following questions are required:\n\n';
								for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
								alert(msg);
								return false;
							}
						}
						
					} 
					document.getElementById('formSubmitButton').disabled = 1;
					return true;
				}
			
				
				function getTotalDue() {
					var thisForm = document.forms["frmSection"];
					var mLevel = '#local.member_type#';
					var sTotal = 0;
					var sCount = 0;	
								
					var sBilledCount = 0;					
				
					$(".sectionsOptions").each(function( index ) {
  						if ($(this).attr("checked") && $(this).attr("isDisabled") == undefined) {							
							sCount = sCount + 1;
							if ($(this).attr("rateamt") != "0.0000")
								sBilledCount = sBilledCount + 1;
								sTotal = sTotal + parseFloat($(this).attr("rateamt"));
						}
					});	
							

					$(".newLawyerForumSubscriptionOptions").each(function( index ) {
  						if ($(this).attr("checked")  && $(this).attr("isDisabled") == undefined) {
							sCount = sCount + 1;
							if ($(this).attr("rateamt") != "0.0000")
								sBilledCount = sBilledCount + 1;
								sTotal = sTotal + parseFloat($(this).attr("rateamt"));
						}
					});	
								
					switch(mLevel){					
						<!--- law students: local.numFreeToUse free, others 15 ea --->
						case 'LS': 
							if (sCount > #local.numFreeToUse#){
								sTotal = 0;
								<!--- Calculate total based on the order of checkbox selections --->
								for (var i=#local.numFreeToUse#; i < checkList.length; i++) {
									var item = checkList[i];
									<!---I will leave this for later use --->
									<!---
									if (item.rateamt != "0.0000")
										sTotal += 15;
									--->
								}
							}
							else 
								sTotal = 0;
							break;
					
						<!--- HM: all free --->
						case 'HM':
							sTotal = 0; 
							break;
						
						<!--- else: all 15 ea --->
						default:
							sCountCalc = sBilledCount;
							<!---I will leave this for later use --->
							<!---sTotal = sCountCalc * 15;--->
							<!---sTotal = 0;--->
							break;							
					}
						
					var obj = new Object();
						obj.sTotal = sTotal;
						obj.sCount = sCount;
					return obj;
				}
	
				function checkSections() {
					var obj = getTotalDue();
					if (obj.sTotal <= 0 ||  isNaN(obj.sTotal)) document.getElementById('paymentinfo').style.display = 'none';
					else document.getElementById('paymentinfo').style.display = '';
					if (obj.sTotal <= 0 ||  isNaN(obj.sTotal)){
						document.getElementById('totalDue').innerHTML = 0;
					}
				}
				
				function subChange()
				{
					var thisForm = document.forms["frmSection"];
					thisForm.isSubmitted.value = 0;
					thisForm.submit();
				}


				function removeByAttr(arr, attr, value) {
				    var i = arr.length;
				    while(i--){
				       if(arr[i] && arr[i].hasOwnProperty(attr) && (arguments.length > 2 && arr[i][attr] === value )){
				           arr.splice(i,1);
				       }
				    }
				    return arr;
				}
				
				var checkList = [];
				$(document).ready(function(){
				    $(":checkbox").change(function(){
				        if($(this).attr("checked"))
				        {
				        	checkList.push( { key: $(this).attr("value"), rateamt: $(this).attr("rateamt")} );
				        	checkSections();
				        }
				        else
				        {
				        	removeByAttr(checkList, 'key', $(this).attr("value"));
				        	checkSections();
				        }
				    });
				});				
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#application.objCommon.minText(local.js)#">
			
		<cfoutput>
		<div class="form">
			<div class="tsAppHeading formTitle">#local.formNameDisplay#</div>
			<br/>	
			<cfform id="frmSection" method="post" action="/?#cgi.QUERY_STRING#" onsubmit="return _FB_validate();">
				<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="1">
				<cfinput type="hidden" name="#local.nameOfHiddenField#"  id="#local.nameOfHiddenField#" value="">
				<div class="page">
					<div class="section">
						<div class="tsAppLegendTitle sectionTitle">
							Membership ending 
							<cfif local.memberSubs.recordCount gt 1>
								<cfselect name="selSub" id="selSub" onChange="subChange()">
									<cfloop query="local.memberSubs">
										<option value="#local.memberSubs.subscriberID#" <cfif local.currSubscriberID eq local.memberSubs.subscriberID>selected</cfif>>#dateFormat(local.memberSubs.subEndDate, "mm/dd/yyyy")#</option>
									</cfloop>
								</cfselect>
							<cfelse>
								#dateFormat(local.memberSubs.subEndDate, "mm/dd/yyyy")#
								<cfinput type="hidden" name="selSub"  id="selSub" value="#local.memberSubs.subscriberID#">
							</cfif>
							<br><br>
						</div>
						<cfif local.newLawyerForumSubscription.recordCount GT 0>
							<div class="tsAppLegendTitle sectionTitle">SDCBA Divisions</div>
							<table cellspacing="0" cellpadding="2" border="0">
								<tbody>
									<tr>
										<td class="tsAppBodyText">&nbsp;&nbsp;</td>
										<td class="tsAppBodyText" valign="top" nowrap>

											<cfset local.thisX = 0>
											<cfloop query="local.newLawyerForumSubscription">
												<cfset local.thisX = local.thisX + 1>
		
												<cfset local.strInput = { class="tsAppBodyText newLawyerForumSubscriptionOptions", name="newLawyerForumSubscription", type="checkbox", value="#local.newLawyerForumSubscription.subscriptionID#", rateAmt = "#local.newLawyerForumSubscription.rateAmt#" }>
												<cfif listFind(local.memberSectionsAndNewLawyerForumList,local.newLawyerForumSubscription.subscriptionID)>
													<cfset local.strInput['checked'] = "true">
													<cfset local.strInput['isDisabled'] = "true">
												</cfif>
												<cfinput attributeCollection="#local.strInput#">#local.newLawyerForumSubscription.subscriptionName# <cfif local.newLawyerForumSubscription.rateAmt EQ 0>(Free)</cfif><br />
												
												<cfif local.thisX is local.column1>
													</td>
													<td class="tsAppBodyText" valign="top" nowrap>
												</cfif>
											</cfloop>
										</td>
									</tr>
								</tbody>
							</table>
							<br/>
						</cfif>
						<div class="tsAppLegendTitle sectionTitle">SDCBA Sections</div>
						<table cellspacing="0" cellpadding="2" border="0">
							<tr valign="top">
								<td class="tsAppBodyText questionText" colspan="3" id="sectionsCheckboxText">Sections are formed by the SDCBA for the presentation, discussion and study of subjects by an interested group of members.  Now with your basic membership, dues for section membership is free. </td>
							</tr>						
							<tr>
								<td class="tsAppBodyText questionNumber"></td>
								<td class="tsAppBodyText optionsVertical">
									<table border="0" cellpadding="6" cellspacing="0">
									<tr>
										<cfif local.member_type EQ "SECT">
											<td class="tsAppBodyText" valign="top" nowrap>
												<cfset local.thisX = 0>
												<cfloop query="local.sections">
													<cfset local.thisX = local.thisX + 1>
			
													<cfset local.strInput = { class="tsAppBodyText sectionsOptions", name="sections", type="radio", onClick="checkSections()", value="#local.sections.subscriptionID#", rateAmt = "#local.sections.rateAmt#" }>
													<cfif listFind(local.memberSectionsAndNewLawyerForumList,local.sections.subscriptionID)>
														<cfset local.strInput['checked'] = "true">
														<cfset local.strInput['isDisabled'] = "true">
													</cfif>
													<cfinput attributeCollection="#local.strInput#">#local.sections.subscriptionName# <cfif local.sections.rateAmt EQ 0>(Free)</cfif><br />
													
													<cfif local.thisX is local.column1>
														</td>
														<td class="tsAppBodyText" valign="top" nowrap>
													</td></cfif>
												</cfloop>
											
										<cfelse>
											<td class="tsAppBodyText" valign="top" nowrap>
												<cfset local.thisX = 0>
												<cfloop query="local.sections">
													<cfset local.thisX = local.thisX + 1>
			
													<cfset local.strInput = { class="tsAppBodyText sectionsOptions", name="sections", type="checkbox", value="#local.sections.subscriptionID#", rateAmt = "#local.sections.rateAmt#" }>
													<cfif listFind(local.memberSectionsAndNewLawyerForumList,local.sections.subscriptionID)>
														<cfset local.strInput['checked'] = "true">
														<cfset local.strInput['isDisabled'] = "true">
													</cfif>
													<cfinput attributeCollection="#local.strInput#">#local.sections.subscriptionName# <cfif local.sections.rateAmt EQ 0>(Free)</cfif><br />
													
													<cfif local.thisX is local.column1>
														</td>
														<td class="tsAppBodyText" valign="top" nowrap>
													</cfif>
												</cfloop>
											
										</cfif>
										</tr>
									</table>
								</td>
							</tr>
						</table>
						<br/>				
					</div>
										
					<br />
					<div class="section">
						<!---I will leave this for later use. --->
						<!---<div class="tsAppLegendTitle sectionTitle">Payment Information</div>--->
						<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
						<!---I will leave this for later use. --->
						<!---
						<tr valign="top">
							<td class="tsAppBodyText">
								<cfif (local.qryTopSubInfo.uid eq "5179E656-B391-4EB3-9405-BD88FFF87FA0") OR (local.qryTopSubInfo.uid eq "331D5A68-CE43-48C4-927D-3C91C9FF649C") >
									The first #local.qrySetInfo.PCNum# sections are free. Subsequent sections are $15 each.
								<cfelseif local.qryTopSubInfo.uid eq "23061A03-F2C8-45A4-842D-C370C8B0E3B1">
									Sections are free.
								<cfelse>
									Sections are $15 each.
								</cfif>
							</td>
						</tr>
						--->
						<tr>
							<td class="tsAppBodyText">
								<b>Total Amount Due:</b> &nbsp; <b>$<span id="totalDue">0</span></b>
							</td>
						</tr>
						</table>
					</div>
					
					<cfif len(local.strPaymentForm.headCode)>
						<cfhtmlhead text="#application.objCommon.minText(local.strPaymentForm.headCode)#">
					</cfif>
					
					<!--- Credit Card Info--->
					<div id="paymentinfo">
						<br />
						<div class="section">
							<div class="tsAppLegendTitle sectionTitle">Payment Information</div><br/>
							#local.strPaymentForm.inputForm#
						</div>
					</div>
					<br/>
					<!--- Join Sections Button--->
					<div>
						<input name="FBAction" type="hidden" value="postForm">
						<input class="tsAppBodyButton" id="formSubmitButton" name="btnSubmit" type="Submit" value="Submit Changes">
					</div>
				</div>
			</cfform>
		</div>
	
		<!--- Total payment based on choices via javascript --->
		<script>
			$(document).ready(function(){
				checkSections();
			});
		</script>
	
		</cfoutput>				
	</cfif>
<cfelse>
	<cfoutput>
	<div id="divRenewalEx" class="tsAppBodyText formIntro">
		We've found an issue that is preventing us from allowing you to add Sections at this time. We apologize, but it is not available online. Please contact the Member Services Department by calling 619-231-0781 and select option 4 or via <NAME_EMAIL>.
		<br><br>
		We apologize for the inconvenience.
	</div>
	</cfoutput>
</cfif>	


<cffunction	name="ListDeleteValue"	access="public"	returntype="string"	output="false"	hint="Deletes a given value (or list of values) from a list. This is not case sensitive.">

	<cfargument	name="List"	type="string" required="true" hint="The list from which we want to delete values."/>
	<cfargument	name="Value" type="string" required="true" hint="The value or list of values that we want to delete from the first list."/>
	<cfargument	name="Delimiters" type="string"	required="false" default="," hint="The delimiting characters used in the given lists."/>

	<cfset var LOCAL = StructNew() />
	<cfset LOCAL.Result = ArrayNew( 1 ) />
	<cfset LOCAL.ListArray = ListToArray(ARGUMENTS.List,ARGUMENTS.Delimiters) />
	<cfset LOCAL.ValueLookup = StructNew() />
	<cfloop	index="LOCAL.ValueItem"	list="#ARGUMENTS.Value#" delimiters="#ARGUMENTS.Delimiters#">	
		<cfset LOCAL.ValueLookup[ LOCAL.ValueItem ] = true />
	</cfloop>
	
	<cfloop	index="LOCAL.ValueIndex" from="1" to="#ArrayLen( LOCAL.ListArray )#" step="1">

		<cfset LOCAL.Value = LOCAL.ListArray[ LOCAL.ValueIndex ] />
		<cfif NOT StructKeyExists(LOCAL.ValueLookup,LOCAL.Value	)>
			<cfset ArrayAppend(	LOCAL.Result,LOCAL.Value) />
		</cfif>

	</cfloop>

	<cfreturn ArrayToList(LOCAL.Result,	Left( ARGUMENTS.Delimiters, 1 )	) />
</cffunction>
