<cfset layoutmode = event.getValue('mc_pageDefinition.layoutMode','normal')>
<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
<cfset local.robotCommands = "nofollow, noindex, noarchive">
<cfheader name="X-Robots-Tag" value="#local.robotCommands#">


<cfif layoutmode neq "direct">
	<cfset local.allowChangeCPUserPassword = arguments.event.getValue('mc_siteInfo.useRemoteLogin') is 0 and session.cfcuser.memberdata.orgcode EQ arguments.event.getValue('mc_siteInfo.orgcode')>
	<cfset this.objAdmin = CreateObject('component', 'model.admin.admin')>
	<cfset this.objMemberAdmin = CreateObject('component', 'model.admin.members.memberAdmin')>
	<cfset this.objBatchAdmin = CreateObject('component', 'model.admin.transactions.batchAdmin')>
	<cfset local.memSelectGotoLink = this.objAdmin.buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list') & "&mode=direct&autoClose=1&dispTitle=Go%20to%20Member&retFunction=top.gotoMember">
	<cfset local.memGotoLink = this.objMemberAdmin.buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>
	<cfset local.batchGotoLink = this.objBatchAdmin.buildLinkToTool(toolType='BatchAdmin',mca_ta='viewBatch')>
	<cfset local.editVGCLink = this.objAdmin.buildLinkToTool(toolType='VirtualGroup',mca_ta='editCondition') & "&mode=direct">
	<cfset local.redirectToSiteToolLink = this.objAdmin.buildLinkToTool(toolType='MemberDesktopAdmin',mca_ta='redirectToSiteTool') & "&mode=stream">
	<cfset local.manageGroupLink = this.objAdmin.buildLinkToTool(toolType='GroupAdmin',mca_ta='manageGroup') & "&mode=direct">
	<cfset local.setDefaultNavMenuLink = this.objAdmin.buildLinkToTool(toolType='MemberAdmin',mca_ta='setDefaultNavMenu') & "&mode=direct">
	<cfif local.allowChangeCPUserPassword>
		<cfset local.changePasswordLink = this.objMemberAdmin.buildLinkToTool(toolType='MemberAdmin',mca_ta='changeCPUserPW') & "&mode=direct">
	</cfif>

	<!--- top tabs --->
	<cfsavecontent variable="topnav">
		<cfif isDefined('session.mcastruct.strNavKeys') and NOT structIsEmpty(session.mcastruct.strNavKeys)>
			<nav class="navbar navbar-expand-sm navBarMenu">
				<div id="navbarNavDropdown">
					<cfoutput>
					<ul class="nav nav-line nav-line-alt ml-2" id="cpLevel1Nav">
						<cfloop array="#xmlsearch(application.adminNavigationXML,"/navitems/navitem[@navAreaID='1' and @showInNav='1']")#" index="local.tab">
							<cfif structKeyExists(session.mcastruct.strNavKeys, local.tab.xmlAttributes.navKey)>
								<li class="nav-item">
									<a class="nav-link #(arguments.event.getValue('mca_s') eq local.tab.xmlAttributes.navigationID) ? 'active' : ''#" id="cpLevel1Navitem#local.tab.xmlAttributes.navigationID#" href="#arguments.event.getValue('mc_adminNav.adminHome')#&mca_s=#local.tab.xmlAttributes.navigationID#" aria-controls="#local.tab.xmlAttributes.navName#" aria-selected="false">
										<span class="topNavLink1">#local.tab.xmlAttributes.navName#</span>
										<cfif len(local.tab.xmlAttributes.iconClasses)>
											<i class="topNavLink1Icon fa-lg px-2 #local.tab.xmlAttributes.iconClasses#" aria-hidden="true"></i>
										</cfif>										
										<div class="divider"></div>
									</a>
								</li>
							</cfif>
						</cfloop>
					</ul>
					</cfoutput>
				</div>
			</nav>
			<cfset local.helpSuggestion = arguments.event.getValue("mc_adminNav.currentNavigationItem.navname")>
		</cfif>
	</cfsavecontent>
	
	<!--- left items --->
	<cfsavecontent variable="leftnav">
		<cfif isDefined('session.mcastruct.strNavKeys') and NOT structIsEmpty(session.mcastruct.strNavKeys)>
			<cfoutput>
				<cfset local.cpCount = 0>
				<div class="app-sidebar app-sidebar--dark">
					<div class="app-sidebar--content scrollbar-container ps ps--active-y mb-1 mb-auto">
						<div class="sidebar-navigation">
							<ul id="sidebar-nav" class="metismenu">
								<cfloop array="#xmlsearch(application.adminNavigationXML,"/navitems/navitem[@navAreaID='1' and @navigationID='#event.getValue('mca_s')#' and @showInNav='1']/navitem")#" index="local.nav">
									<cfif structKeyExists(session.mcastruct.strNavKeys, local.nav.xmlAttributes.navKey)>
										<cfset local.cpCount = local.cpCount + 1>
										<li>
											<a href="##">
												<span>
													<i class="#((len(local.nav.xmlAttributes.iconClasses)) ? #local.nav.xmlAttributes.iconClasses# : "fa-solid fa-circle-notch")# fa-lg"></i> &nbsp;&nbsp;
													<span>#local.nav.xmlAttributes.navName#</span>
												</span>
												<i class="fa-solid fa-angle-right"></i>
											</a>
											<ul id="cp#local.cpCount#" class="mc-cp3 animated fade mm-collapse">
												<cfloop array="#local.nav.xmlChildren#" index="local.navChild">
													<cfif structKeyExists(session.mcastruct.strNavKeys, local.navChild.xmlAttributes.navKey)>
														<cfif arraylen(local.navChild.xmlChildren) gte 1>
															<li>
																<a href="#arguments.event.getValue('mc_adminNav.adminHome')#&mca_s=#event.getValue('mca_s')#&mca_a=#local.navChild.xmlAttributes.navigationID#&mca_tt=#local.navChild.xmlAttributes.toolTYpeID#&mca_ta=#local.navChild.xmlAttributes.cfcMethod#&mca_hk=1" class="mc-cp-navitem" data-navitemparent="cp#local.cpCount#">
																	#local.navChild.xmlAttributes.navName#  <cfif len(local.navChild.xmlAttributes.helplink)>&nbsp;<i class="fa-solid fa-question-circle" onclick="window.open('#local.navChild.xmlAttributes.helplink#','mcahelp');event.preventDefault();"></i></cfif>
																</a>
															</li>
														<cfelse>
															<cfif local.navChild.xmlAttributes.navDesc eq "SeminarWeb Live">
																<cfset local.navName = event.getValue('mc_siteinfo.swlBrand')>
															<cfelseif local.navChild.xmlAttributes.navDesc eq "SeminarWeb OnDemand">
																<cfset local.navName = event.getValue('mc_siteinfo.swodBrand')>
															<cfelseif local.navChild.xmlAttributes.navDesc eq "Program Bundles">
																<cfset local.navName = event.getValue('mc_siteinfo.swbBrand')>
															<cfelse>
																<cfset local.navName = local.navChild.xmlAttributes.navName>
															</cfif>
															<li>
																<a href="#arguments.event.getValue('mc_adminNav.adminHome')#&mca_s=#event.getValue('mca_s')#&mca_a=#local.navChild.xmlAttributes.navigationID#&mca_tt=#local.navChild.xmlAttributes.toolTYpeID#&mca_ta=#local.navChild.xmlAttributes.cfcMethod#&mca_hk=" class="mc-cp-navitem" data-navitemparent="cp#local.cpCount#">
																	#local.navName# <cfif len(local.navChild.xmlAttributes.helplink)>&nbsp;<i class="fa-solid fa-question-circle" onclick="window.open('#local.navChild.xmlAttributes.helplink#','mcahelp');event.preventDefault();"></i></cfif>
																</a>
															</li>
														</cfif>
													</cfif>
												</cfloop>
											</ul>
										</li>
									</cfif>
								</cfloop>
							</ul>
						</div>
					</div>
					<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and NOT structKeyExists(session,"removeCPWatermark")>
						<div id="divSuperKey" style="display:none;">
							<div class="text-light text-center my-2 mx-1 border border-dark rounded">
								<div class="superuser"> SuperUsers only</div>
								<div class="superdev"> Developers only</div>
							</div>
						</div>
					</cfif>
					<div class="app-sidebar--footer">
						<div class="px-3 py-2 text-center">
							<img src="/assets/admin/images/mclogowhite.png" alt="Powered by MemberCentral.com" class="img-fluid">
						</div>
					</div>					
				</div>
				<div class="sidebar-mobile-overlay"></div>
			</cfoutput>
		</cfif>
	</cfsavecontent>

	<cfset local.arrDashboardNodes = XMLSearch(application.adminNavigationXML,"/navitems//navitem[@navName='My Dashboards']")>
	<cfif arrayLen(local.arrDashboardNodes)>
		<cfset local.showDashboardsSelector = structKeyExists(session.mcastruct.strNavKeys, local.arrDashboardNodes[1].xmlAttributes.navKey)>
		<cfif local.showDashboardsSelector>
			<cfset local.qryDashboards = createObject("component","model.admin.dashboards.dashboard").getDashboards(siteID=arguments.event.getValue('mc_siteInfo.siteid'), mode="topnav")>
			<cfset local.viewDashboardLink = CreateObject('component','model.admin.dashboards.dashboardAdmin').buildLinkToTool(toolType='DashboardsAdmin',mca_ta='viewDashboard')>
		</cfif>
	<cfelse>
		<cfset local.showDashboardsSelector = false>
	</cfif>

	<cfset local.zoneAHasContent = 0>
	<cfif application.objCMS.getZoneItemCount(zone='A',event=event)>
		<cfloop index="local.ZD" array="#arguments.event.getValue("mc_pageDefinition").pageZones['A']#">
			<cfsavecontent variable="local.zoneAcontent"><cfmodule template="/views/#local.ZD.view#.cfm" data="#local.ZD.data#" event="#arguments.event#"></cfsavecontent>
			<cfif len(trim(local.zoneAcontent))>
				<cfset local.zoneAHasContent = 1>
				<cfbreak>
			</cfif>
		</cfloop>
	</cfif>
</cfif>

<!DOCTYPE HTML>
<html>
<head>

	<cfoutput>
	<meta name="robots" content="#local.robotCommands#">
	<!-- MCSkipMergeStart -->
	<title>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</title>
	<!-- MCSkipMergeEnd -->

	<!--- common library --->
	<link rel="apple-touch-icon" sizes="180x180" href="/assets/common/images/apple-icon-180x180.png">
	<link rel="icon" type="image/png" sizes="32x32" href="/assets/common/images/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="/assets/common/images/favicon-16x16.png">
	<link name="favicon" type="image/x-icon" href="/assets/common/images/favicon.ico" rel="shortcut icon" />
	<script type="text/javascript" src="/assets/admin/javascript/admin.js#local.assetCachingKey#"></script>
	<script type="text/javascript" src="/assets/admin/javascript/responsiveHandler.js#local.assetCachingKey#"></script>
	<script type="text/javascript" src="/assets/common/javascript/common.js#local.assetCachingKey#"></script>
	<script type="text/javascript" src="/assets/common/javascript/pendingImages.js#local.assetCachingKey#"></script>
	
	<!--- grid support --->
	<link rel="stylesheet" type="text/css" href="/assets/common/javascript/dhtmlxgrid/dhtmlxgrid.css#local.assetCachingKey#" />
 	<script type="text/javascript" src="/assets/common/javascript/dhtmlxgrid/memberCentral_grid.js#local.assetCachingKey#"></script>
	<script type="text/javascript">dhtmlxError.catchError("LoadXML", mcg_ErrorHandler);</script>

	<!--- Admin Widgets --->
	<script type="text/javascript" src="/assets/admin/javascript/widgets.js#local.assetCachingKey#"></script>

	<!--- Bamburgh --->
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=no" />
	<link rel="stylesheet" type="text/css" href="/assets/common/javascript/jQueryAddons/bamburgh/1.0.0/css/bamburgh.min.css">
	<link rel="stylesheet" type="text/css" href="/assets/common/javascript/jQueryAddons/datatables/1.10.24/css/dataTables.bootstrap4.min.css">
	<link rel="stylesheet" type="text/css" href="/assets/common/javascript/jQueryAddons/datatables/select/1.3.3/css/select.dataTables.min.css">
	<link rel="stylesheet" type="text/css" href="/assets/common/javascript/jQueryAddons/datatables/rowreorder/1.2.8/css/rowReorder.dataTables.min.css">
	<link rel="stylesheet" type="text/css" href="/assets/common/javascript/jQueryAddons/bootstrap4-toggle/3.6.1/css/bootstrap4-toggle.min.css">
	<link rel="stylesheet" type="text/css" href="/assets/common/javascript/jQueryAddons/bootstrap4-tagsinput/0.8.0/css/tagsinput.css">
	<link rel="stylesheet" type="text/css" href="/assets/common/javascript/jQueryAddons/floating-labels/floating-labels.min.css">
	<link rel="stylesheet" type="text/css" href="/assets/admin/css/custom.css#local.assetCachingKey#">
	<link rel="stylesheet" type="text/css" href="/assets/admin/css/styles.css#local.assetCachingKey#" />
	<link rel="stylesheet" type="text/css" href="/assets/admin/css/reponsiveStyles.css#local.assetCachingKey#" />
	</cfoutput>

	<cfif layoutmode neq "direct">
		<!--- navigation --->
		<cfoutput>
		<script type="text/javascript" src="/assets/admin/javascript/navigation.js#local.assetCachingKey#"></script>
		</cfoutput>

		<!--- supporting --->
		<script type="text/javascript" src="/assets/common/javascript/papaparse/5.4.0/papaparse.min.js"></script>

		<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			<cfoutput>
			<script type="text/javascript">
				#ToScript(event.getValue('mc_siteInfo.orgID'),"mca_orgid")#
				#ToScript(local.memSelectGotoLink,"mca_msgtl_link")#
				#ToScript(local.memGotoLink,"mca_mgtl_link")#
				#ToScript(local.editVGCLink,"mca_vgc_link")#
				const #ToScript(local.redirectToSiteToolLink,"mca_st_link")#
				var #ToScript(local.manageGroupLink,"mca_grp_link")#
				const #ToScript(local.setDefaultNavMenuLink,"mca_df_navmenu")#
				<cfif local.allowChangeCPUserPassword>
					#ToScript(local.changePasswordLink,"changePasswordLink")#
				</cfif>
				
				$(function() {
					mca_announcecheck();
					<cfif application.MCEnvironment eq "production">
						mca_updatecheck(5);
					</cfif>
					mca_alertcheck();	
					$('##topSearchForm').on('submit',function(e){
						e.preventDefault();
						selectMemberToView();
					});

					<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and application.MCEnvironment neq "production" and NOT isDefined("session.removeCPWatermark")>
						$('##mc_userBox').on('shown.bs.popover', function () {
							$('li##remWatermarkLink a').on('click',removeWatermark);
						});
					</cfif>
					<cfif local.allowChangeCPUserPassword>
						$('##mc_userBox').on('shown.bs.popover', function () {
							$('li##changePasswordLink a').on('click',changeCPUserPW);
						});
					</cfif>
					$('##mc_userBox').on('shown.bs.popover', function () {
						$('li##setDefNavItemLink a').on('click', { mca_a:#event.getValue('mca_a',0)# }, setDefaultNavMenu);
					});
				});
			</script>
			</cfoutput>
		</cfif>
	</cfif>
	
	<cfoutput>
		<cfif layoutmode neq "direct" and event.valueExists('mca_a')>
			<script language="javascript">
				$(function() {
					highlightLinkOnload(#event.getValue('mca_a')#,#event.getValue('mca_lt','0')#);
				});
			</script>
		</cfif>
	</cfoutput>
</head>
<cfif layoutmode eq "direct">
	<body style="<cfif NOT isDefined("session.removeCPWatermark") and ListFindNoCase("development,test,newdevelopment,newtest,localDevelopment",application.MCEnvironment)>background:url(/assets/admin/images/development.gif) #fff;<cfelseif NOT isDefined("session.removeCPWatermark") and ListFindNoCase("beta,newbeta",application.MCEnvironment)>background:url(/assets/admin/images/beta.gif) #fff;<cfelse>background-image:none;background-color:#fff;</cfif>">
	<!-- MCSkipMergeStart -->
	<div id="adminwrapper">
		<cfif application.objCMS.getZoneItemCount(zone='Main',event=event)>
			<div class="bodyText" style="margin:6px;">
				<cfoutput>#application.objCMS.renderZone(zone='Main',event=event)#</cfoutput>
			</div>
		</cfif>
	</div>
	<!-- MCSkipMergeEnd -->
<cfelse>
	<body>
		<div class="app-wrapper">
			<cfif len(leftnav)><cfoutput>#leftnav#</cfoutput></cfif>
			<div class="app-main appMainWrap" >
				<div class="mc_customheader w-100 text-light d-flex align-items-center">
					<div class="lead flex-grow-1 siteNameWrap"><span class="siteNameHeader"><cfoutput>#arguments.event.getValue('mc_siteinfo.sitename')#</cfoutput></span> Control Panel</div>
					
					<cfif local.showDashboardsSelector and local.qryDashboards.recordCount>
						<div class="d-flex mr-md-4 dropdown">
							<button class="btn btn-sm btn-secondary dropdown-toggle" type="button" id="topNavDashDropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
								<i class="fa-solid fa-analytics"></i>
							</button>
							<div class="dropdown-menu" aria-labelledby="topNavDashDropdownMenuButton">									
								<cfoutput query="local.qryDashboards">
									<a class="dropdown-item" href="#local.viewDashboardLink#&dashboardID=#local.qryDashboards.dashboardID#">#local.qryDashboards.dashboardName# <cfif local.qryDashboards.maskData><span class="badge badge-warning">DEMO</span></cfif></a>
								</cfoutput>
							</div>
						</div>
					</cfif>

					<div class="d-flex align-items-center mr-2">
						<form method="post" name="topSearchForm" id="topSearchForm" class="form-inline pr-lg-3">
							<div class="input-group input-group-sm input-group-seamless d-none d-md-flex">
								<div class="input-group-prepend">
									<span class="input-group-text"><i class="fa-solid fa-search"></i></span>
								</div>
								<input type="text" name="cp_vwbtn_memnum" id="cp_vwbtn_memnum" class="form-control" style="max-width:150px;" placeholder="MemberNumber" autocomplete="off">
							</div>
							<button type="submit" name="btnMCTopSearchMember" id="btnMCTopSearchMember" class="btn-transition-none btn btn-neutral-dark btn-sm ml-1">Find<span class="d-md-none"> Member</span></button>
							<div id="divMCTopSearchMemberLoading" class="d-none spinner-border ml-1 mr-5 text-dark" role="status"></div>
						</form>
					</div>

					<div class="user-box">
						<a id="mc_userBox" href="#" data-trigger="click" data-popover-class="popover-secondary popover-custom-wrapper popover-custom-lg" data-rel="popover-close-outside" data-tip="account-popover" class="p-0 d-flex align-items-center popover-custom" data-placement="bottom" data-boundary="'viewport'">
							<div class="d-block p-0 avatar-icon-wrapper">
								<span class="badge badge-circle badge-success p-top-a">Online</span>
								<div class="avatar-icon rounded">
									<cfif session.cfcuser.memberData.hasMemberPhotoThumb AND session.cfcuser.superuser is 1>
										<cfoutput><img src="/userassets/MC/memberphotosth/#LCASE(session.cfcuser.memberData.membernumber)#.jpg?cb=#getTickCount()#"></cfoutput>
									<cfelseif session.cfcuser.memberData.hasMemberPhotoThumb>
										<cfoutput><img src="/memberphotosth/#LCASE(session.cfcuser.memberData.membernumber)#.jpg?cb=#getTickCount()#"></cfoutput>
									<cfelse>
										<img src="/assets/common/images/directory/default.jpg" class="align-baseline">
									</cfif>
								</div>
							</div>
							<div class="d-none d-xl-block pl-2">
								<div class="font-weight-bold text-light">
									<cfoutput>#session.cfcuser.memberdata.firstname# #session.cfcuser.memberdata.lastname#</cfoutput>
								</div>
								<div class="text-white-50">
									<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
										Super User
									<cfelseif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
										Site Administrator
									</cfif>
								</div>
							</div>
							<span class="pl-3"><i class="fa-solid fa-angle-down text-light opacity-5"></i></span>
						</a>
					</div>
					<div id="account-popover" class="d-none">
						<ul class="list-group list-group-flush text-left bg-transparent">
							<li class="list-group-item rounded-top">
								<ul class="nav nav-pills nav-pills-hover flex-column">
									<li class="nav-item">
										<a class="nav-link pl-0" href="/?pg=support" target="_blank">
											<i class="fa-solid fa-headset"></i> Support Center
										</a>
									</li>
									<li class="nav-item">
										<a class="nav-link pl-0" href="/?pg=support&sa=zenDeskGuide&zendeskArea=community&link=topics" target="_blank">
											<i class="fa-regular fa-comments"></i> Admin Community
										</a>
									</li>
									<li class="nav-item" id="setDefNavItemLink">
										<a class="nav-link pl-0" href="#">
											<i class="fa-regular fa-bars"></i> Set Default Screen
										</a>
									</li>
									<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and application.MCEnvironment neq "production" and NOT isDefined("session.removeCPWatermark")>
										<li class="nav-item" id="remWatermarkLink">
											<a class="nav-link pl-0" href="#">
												<i class="fa-solid fa-eraser"></i> Remove Watermark
											</a>
										</li>
									</cfif>
									<cfif local.allowChangeCPUserPassword>
										<li class="nav-item" id="changePasswordLink">
											<a class="nav-link pl-0" href="#">
												<i class="fa-solid fa-lock-alt"></i> Change Password
											</a>
										</li>
									<cfelseif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
										<li class="nav-item">
											<cfoutput><a class="nav-link pl-0" href="#application.objSiteInfo.getSiteInfo('MC').scheme#://#application.objSiteInfo.getSiteInfo('MC').mainhostname#/admin" target="_blank"></cfoutput>
												<i class="fa-solid fa-lock-alt"></i> Change Password
											</a>
										</li>
									</cfif>
									<li class="nav-item">
										<a class="nav-link pl-0" href="/?logout">
											<i class="fa-solid fa-sign-out-alt"></i> Logout
										</a>
									</li>
								</ul>
							</li>
						</ul>
					</div>
				</div>
				<div class="app-header">
					<div class="d-flex">
						<button class="navbar-toggler hamburger hamburger--elastic toggle-sidebar-mobile mr-1" type="button">
							<span class="hamburger-box">
								<span class="hamburger-inner"></span>
							</span>
						</button>
						<cfif len(trim(topnav))>
							<cfoutput><!-- MCSkipMergeStart -->#topnav#<!-- MCSkipMergeEnd --></cfoutput>
						</cfif>
					</div>
				</div>
				<!--- end: app header --->
				<div class="app-content">
					<div class="app-content--inner">
						<div id="divMCAnnouncements" class="d-none">
							<div id="divMCAnnouncementsArea" class="d-flex"></div>
						</div>

						<div class="app-breadcrumb d-flex justify-content-between mb-4 p-3 shadow-sm app-breadcrumbTop">
							<ol class="align-self-center breadcrumb text-uppercase font-size-xs p-0 m-0">
								<cfloop array="#arguments.event.getValue('mc_adminNav.currentNavigationItem').arrCrumbs#" index="local.thisEl" item="local.thisCrumb">
									<cfif isStruct(local.thisCrumb)>
										<cfif len(local.thisCrumb.link) and local.thisEl lt arrayLen(arguments.event.getValue('mc_adminNav.currentNavigationItem').arrCrumbs)>
											<cfoutput><li class="breadcrumb-item"><a href="#local.thisCrumb.link#">#local.thisCrumb.text#</a></li></cfoutput>
										<cfelse>
											<cfoutput><li class="breadcrumb-item">#local.thisCrumb.text#</li></cfoutput>
										</cfif>
									<cfelse>
										<cfoutput><li class="breadcrumb-item">#local.thisCrumb#</li></cfoutput>
									</cfif>
								</cfloop>
							</ol>
							
							<div class="align-self-center mt-1 mt-md-0 justify-content-md-end">
								<div id="MCAlertTab" class="d-inline-block text-nowrap mr-2">
									<i class="fa-light fa-circle-notch fa-spin fa-lg"></i>
								</div>
							</div>
						</div>

						<div class="row">
							<div class="col-xl-12">
								<div class="card card-box p-3 mb-1">
									<!-- MCSkipMergeStart -->
									<cfif application.objCMS.getZoneItemCount(zone='Main',event=event)>
										<div id="divMCMainContainer" class="bodyText<cfif NOT structKeyExists(session,"removeCPWatermark")><cfif ListFindNoCase("development,test,newdevelopment,newtest,localDevelopment",application.MCEnvironment)> mc_development_watermark<cfelseif ListFindNoCase("beta,newbeta",application.MCEnvironment)> mc_beta_watermark</cfif></cfif>" style="margin:6px;">
											<cfoutput>#application.objCMS.renderZone(zone='Main',event=event)#</cfoutput>
										</div>
									</cfif>
									<!-- MCSkipMergeEnd -->
								</div>
							</div>
						</div>

						<div id="divRecentMCUpdates" class="row d-none">
							<div class="col-xl-12">
								<div id="divRecentMCUpdatesArea" class="card card-box card-border-top bg-light my-1"></div>
							</div>
						</div>

						<cfif local.zoneAHasContent is 1>
							<div id="divCPZoneA" class="row">
								<div class="col-xl-12 font-size-sm text-black-50">
									<div id="divCPZoneAArea" class="card card-box card-border-top bg-light my-1 p-2">
										<cfoutput>#application.objCMS.renderZone(zone='A',event=event)#</cfoutput>
									</div>
								</div>
							</div>
						</cfif>
					</div>

					<cfif application.objCMS.getZoneItemCount(zone='ToolBar',event=event)>
						<div class="app-footer font-size-sm text-black-50 d-block w-100 py-2">
							<cfoutput>#application.objCMS.renderZone(zone='ToolBar',event=event)#</cfoutput>
						</div>
					</cfif>
				</div>
			</div>
			<!--- end: app-main --->
	</div>
	<!--- admin wrapper ends here --->

	<!--- MC Modal --->
	<div id="MCModalContainer" class="d-none"></div>
	<script id="mca_modal_template" type="text/x-handlebars-template">
		<div id="MCModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="MCModalLabel" aria-hidden="true">
			<div class="modal-dialog{{#if displaymode}} {{#switch displaymode}}{{#case "slideout"}}modal-dialog-slideout{{/case}}{{#case "verticallycentered"}}modal-dialog-centered{{/case}}{{/switch}}{{/if}}{{#switch size}}{{#case "xl"}} modal-xl{{/case}}{{#case "lg"}} modal-lg{{/case}}{{#case "sm"}} modal-sm{{/case}}{{/switch}}" role="document">
				<div class="modal-content">
					<div id="MCModalHeader" class="modal-header">
						<h4 class="modal-title d-inline" id="MCModalLabel">{{{title}}}</h4>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
					<div id="MCModalBody" class="modal-body {{strmodalbody.classlist}}">
						{{#if iframe}}
							<iframe name="MCModalBodyIframe" id="MCModalBodyIframe" class="w-100 h-100 border-0 jvectormap-spinner" src="{{contenturl}}" onload="MCModalUtils.onLoadCompleteModal();"></iframe>
						{{else}}
							{{{strmodalbody.content}}}
						{{/if}}
					</div>
					<div id="MCModalFooter" class="modal-footer {{strmodalfooter.classlist}}">
						{{#if strmodalfooter.showclose}}<button type="button" class="btn btn-sm btn-secondary" data-dismiss="modal">{{strmodalfooter.closebuttonlabel}}</button>{{/if}}
						{{#if strmodalfooter.showextrabutton}}
							<button type="button" class="btn btn-sm {{strmodalfooter.extrabuttonclass}}" name="btnMCModalSave" id="btnMCModalSave"{{#if  strmodalfooter.hasextrabuttononclickhandler}} onclick="{{strmodalfooter.extrabuttononclickhandler}}();"{{/if}}{{#if strmodalfooter.disableextrabutton}} disabled{{/if}}>
								{{#compare strmodalfooter.extrabuttoniconclass.length '>' 0 }}<i class="{{strmodalfooter.extrabuttoniconclass}} mr-1"></i>{{/compare}}{{strmodalfooter.extrabuttonlabel}}
							</button>
						{{/if}}
						{{#if strmodalfooter.buttons}}
							{{#each strmodalfooter.buttons}}
								<button type="button" class="btn {{class}}" name="{{name}}" id="{{id}}"{{#compare clickhandler.length '>' 0}} onclick="{{clickhandler}}();"{{/compare}}{{#if disabled}} disabled{{/if}}>
									{{#compare iconclass.length '>' 0 }}<i class="{{iconclass}} mr-1"></i>{{/compare}}{{label}}
								</button>
							{{/each}}
						{{/if}}
					</div>
				</div>
			</div>
		</div>
	</script>
	<script id="mca_modal_footer_template" type="text/x-handlebars-template">
		{{#if showclose}}<button type="button" class="btn btn-sm btn-secondary" data-dismiss="modal">{{closebuttonlabel}}</button>{{/if}}
		{{#if showextrabutton}}
			<button type="button" class="btn btn-sm {{extrabuttonclass}}" name="btnMCModalSave" id="btnMCModalSave"{{#if  hasextrabuttononclickhandler}} onclick="{{extrabuttononclickhandler}}();"{{/if}}>
				{{#compare extrabuttoniconclass.length '>' 0 }}<i class="{{extrabuttoniconclass}} mr-1"></i>{{/compare}}{{extrabuttonlabel}}
			</button>
		{{/if}}
		{{#if buttons}}
			{{#each buttons}}
				<button type="button" class="btn {{class}}" name="{{name}}" id="{{id}}"{{#compare clickhandler.length '>' 0}} onclick="{{clickhandler}}();"{{/compare}}{{#if disabled}} disabled{{/if}}>
					{{#compare iconclass.length '>' 0 }}<i class="{{iconclass}} mr-1"></i>{{/compare}}{{label}}
				</button>
			{{/each}}
		{{/if}}
	</script>

	<!--- admin alert messages --->
	<div id="divAdminAlertMsg" class="d-none"></div></div>

	<script id="mca_groups_template" type="text/x-handlebars-template">
		<cfoutput>
		<div class="table-responsive-xl">
			<table class="mt-4 table table-sm table-striped">
				<thead><tr><th colspan="3">Group (click to view)</th></tr></thead>
				<tbody>{{##each data}}<tr><td><a href="#this.objAdmin.buildLinkToTool(toolType='GroupAdmin',mca_ta='edit')#&groupID={{groupid}}" target="_blank">{{grouppath}}</a></td></tr>{{/each}}</tbody>
			</table>
		</div>
		</cfoutput>
	</script>
	<script id="mca_dupereg_template" type="text/x-handlebars-template">
		<cfoutput>
		<div class="table-responsive-xl">
			<table class="mt-4 table table-sm table-striped">
				<thead><tr><th colspan="3">Member (click to view)</th></tr></thead>
				<tbody>{{##each data}}<tr><td><a href="#local.memGotoLink#&tab=events&memberID={{memberid}}" target="_blank">{{membername}}</a>{{##if company}}<div class="text-black-50">{{company}}</div>{{/if}}<div class="ind">&bull; {{eventname}}</div></td></tr>{{/each}}</tbody>
			</table>
		</div>
		</cfoutput>
	</script>
	<script id="mca_dupeprolic_template" type="text/x-handlebars-template">
		<cfoutput>
		<div class="table-responsive-xl">
			<table class="mt-4 table table-sm table-striped">
				<thead><tr><th colspan="3">Duplicate Professional Licenses (click to view)</th></tr></thead>
				<tbody>{{##each data}}<tr><td><b>{{plname}} - {{licensenumber}}</b><br/><div class="ind">&bull; <a href="#local.memGotoLink#&memberID={{memberid}}" target="_blank">{{membername}}</a></td></tr>{{/each}}</tbody>
			</table>
		</div>
		</cfoutput>
	</script>
	<script id="mca_invalidLinkedRecords_template" type="text/x-handlebars-template">
		<cfoutput>
		<div class="table-responsive-xl">
			<table class="mt-4 table table-sm table-striped">
				<thead><tr><th colspan="3">Top {{data.length}} Invalid Linked Records (click to view)</th></tr></thead>
				<tbody>{{##each data}}<tr><td><b>{{relationTypeName}}</b> between <b>{{relationMasterRecordType}}</b> and <b>{{relationChildRecordType}}</b>
					<div class="ind">
						&bull; Master <b>{{actualMasterRecordType}}</b>: 
						<a href="#local.memGotoLink#&memberID={{masterMemberID}}" target="_blank">{{masterMemberName}}</a>{{##if masterCompany}} - {{masterCompany}}{{/if}}
					</div>
					<div class="ind">
						&bull; Child <b>{{actualChildRecordType}}</b>: 
						<a href="#local.memGotoLink#&memberID={{childMemberID}}" target="_blank">
						{{childMemberName}}</a>{{##if childCompany}} - {{childCompany}}{{/if}}
					</div>
				</td></tr>{{/each}}</tbody>
			</table>
		</div>
		</cfoutput>
	</script>
	<script id="mca_transaction_template" type="text/x-handlebars-template">
		<cfoutput>
		<div class="table-responsive-xl">
			<table class="mt-4 table table-sm table-striped">
				<thead><tr><th>Member</th><th colspan="2">Message</th></tr></thead>
				<tbody>{{##each data}}<tr id="trTransAlertRow{{alertid}}"><td><a href="#local.memGotoLink#&tab=transactions&memberID={{memberid}}" target="_blank">{{membername}}</a>
					<div class="text-black-50">{{##if company}}{{company}}<br/>{{/if}}{{daterecorded}}</div></td>
					<td>{{message}}</td>
					<td><a href="javascript:deleteTransAlert({{alertid}})" target="_self" title="Remove this alert."><i class="fa-solid fa-trash"></i></a></td>
				</tr>{{/each}}</tbody>
			</table>
		</div>
		</cfoutput>
	</script>
	<script id="mca_openinv_template" type="text/x-handlebars-template">
		<cfoutput>
		<table class="mt-4 table table-sm table-striped">
			<thead><tr><th>Member</th><th colspan="2">Invoice</th></tr></thead>
			<tbody>{{##each data}}<tr><td><a href="#local.memGotoLink#&tab=transactions&memberID={{memberid}}" target="_blank">{{membername}}</a>
				{{##if company}}<div class="text-black-50">{{company}}</div>{{/if}}</td>
			<td>{{invoicenumber}}<br/>{{dueamt}} due on {{duedate}}{{##if hascard}}<br/>There is a pay method on file for this invoice.{{/if}}</td></tr>{{/each}}</tbody>
		</table>
		</cfoutput>
	</script>
	<script id="mca_openbatch_template" type="text/x-handlebars-template">
		<cfoutput>
		<div class="table-responsive-xl">
			<table class="mt-4 table table-sm table-striped">
				<thead><tr><th colspan="2">Batch</th><th></th></tr></thead>
				<tbody>{{##each data}}<tr><td><a href="#local.batchGotoLink#&bid={{batchid}}" target="_blank">{{batchname}}</a>
					<div class="text-black-50">Payment Profile: {{profilename}}</div></td><td>Deposit Date: {{depositdate}}<br/>Batch Status: {{status}}<br/>Batch Amt: {{actualamt}}</td></tr>{{/each}}</tbody>
			</table>
		</div>
		</cfoutput>
	</script>
	<script id="mca_oooinv_template" type="text/x-handlebars-template">
		<cfoutput>
		<table class="mt-4 table table-sm table-striped">
			<thead><tr><th colspan="3">Member</th></tr></thead>
			<tbody>{{##each data}}<tr><td> <a href="#local.memGotoLink#&tab=transactions&memberID={{memberid}}" target="_blank">{{membername}}</a>
				{{##if company}}<div class="text-black-50">{{company}}</div>{{/if}}</td></tr>{{/each}}</tbody>
		</table>
		</cfoutput>
	</script>
	<script id="mca_invprofcon_template" type="text/x-handlebars-template">
		<cfoutput>
		<table class="mt-4 table table-sm table-striped">
			<thead><tr><th colspan="3">Allocation</th></tr></thead>
			<tbody>{{##each data}}<tr><td>On {{daterecorded}}, {{membername}} allocated {{allocamt}} of {{detail}} to invoice {{invnumber}}.<br/>Invoice profile {{invprofile}} cannot be paid by pay profile {{payprofile}}.</td></tr>{{/each}}</tbody>
		</table>
		</cfoutput>
	</script>
	<script id="mca_announcements_template" type="text/x-handlebars-template">
		{{#each arrannouncements}}
			<div class="alert d-flex flex-nowrap flex-grow-1 pl-2 align-content-center alert-info fade show" role="alert">
				<span class="font-size-lg d-block d-40 mr-2 text-center">
					<i class="fa-solid fa-info-circle fa-lg"></i>
				</span>
				<span>
					<strong class="d-block">{{{title}}}</strong> {{{content}}}
				</span>
			</div>
		{{/each}}
	</script>
	<cfif application.MCEnvironment eq "production" and layoutmode neq "direct">
		<script id="mca_update_template" type="text/x-handlebars-template">
			<h5 class="card-header">Recent MemberCentral System Updates &nbsp; <small>(<a target="_blank" href="{{linkall}}">View All Updates</a>)</small></h5>
			<div class="card-body">
				<ul class="card-text">
					{{#each data}}
						<li>
							<a href="/?pg=support&sa=zenDeskGuide&link={{id}}" target="_blank">{{title}}</a> &nbsp; <span class="articleDate">{{date}}</span>
						</li>
					{{/each}}
				</ul>
			</div>
		</script>
	</cfif>
	<cfif application.MCEnvironment eq "production" and layoutmode neq "direct" and structKeyExists(session.mcStruct, "zendeskWebWidgetJWT") and len(session.mcStruct.zendeskWebWidgetJWT.token)>
		<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			<cfset local.zendeskorg = event.getValue('mc_siteInfo.siteCode')>
		<cfelse>
			<cfset local.zendeskorg = event.getValue('mc_siteInfo.siteCode') & "CU">
		</cfif>

		<script id="ze-snippet" onload="setupWidget();" src="//static.zdassets.com/ekr/snippet.js?key=9d931583-b689-4d41-89c9-602c23e3f86e" async defer></script>
		<script>
			<cfoutput>
				function setupWidget() {
					window.zESettings = {
						authenticate: { jwt: '#session.mcStruct.zendeskWebWidgetJWT.token#' },
						webWidget: {
							color: { theme: '##2647a0' },
							launcher: {
								label: { '*': 'Help' }
							},
							helpCenter: {
								title: { '*': 'Support Center' },
								messageButton: { '*': 'Open a Support Ticket' },
								originalArticleButton: false
							},
							contactForm: {
								title: { '*': 'Submit Ticket to #event.getValue('mc_siteInfo.supportProviderName')# Support' }
							},
							chat: { suppress: true }
						}
					};

					zE(function() {
						zE.identify({
							name: '#jsStringFormat("#session.cfcuser.memberdata.firstname# #session.cfcuser.memberdata.lastname#")#',
							email: '#session.cfcuser.memberdata.email#',
							organization: '#local.zendeskorg#'
						});
						<cfif structKeyExists(local, "helpSuggestion")>
							zE.setHelpCenterSuggestions({ search: '#jsStringFormat(local.helpSuggestion)#' });
						</cfif>
					});

					$(document).ready(function(){
						var wwjsloc = window.location.protocol + '//' + window.location.hostname; 
						var wwMaxCounter = 0;
						var timerInterval = setInterval(function(){					
							var wwb = $('##webWidget').contents().find('body');
							if (wwb.length > 0) {
								wwb.find('a.src-component-ZendeskLogo-logo').hide();
								wwb.on('click', 'a', function(e) {
									var l = $(this)[0];
									if ((!l.onclick || l.title=='View original article') && l.href.indexOf('https://support.membercentral.com/hc/en-us/articles/') >= 0) {
										l.href = wwjsloc + '/?pg=support&sa=zenDeskGuide&link=' + l.href.replace('https://support.membercentral.com/hc/en-us/articles/','');
									}
								});
								clearInterval(timerInterval);
							} else {
								wwMaxCounter++;
								if (wwMaxCounter > 10) clearInterval(timerInterval);
							}
						}, 2000);
					
					});
				}
			</cfoutput>
		</script>
	</cfif>

</cfif>

<cfif layoutmode neq 'stream'>
	<cfoutput>
	<script src="/assets/common/javascript/jQueryAddons/bamburgh/1.0.0/vendor/popper/js/popper.min.js"></script>
	<script src="/assets/common/javascript/jQueryAddons/bamburgh/1.0.0/vendor/bootstrap/js/bootstrap.min.js"></script>
	<script src="/assets/admin/javascript/bamburgh/bootstrap.min.js#local.assetCachingKey#"></script>
	<script src="/assets/common/javascript/jQueryAddons/bamburgh/1.0.0/vendor/metismenu/js/metismenu.min.js"></script>
	<script src="/assets/admin/javascript/bamburgh/metismenu.min.js#local.assetCachingKey#"></script>
	<script src="/assets/common/javascript/jQueryAddons/bamburgh/1.0.0/vendor/perfect-scrollbar/js/perfect-scrollbar.min.js"></script>
	<script src="/assets/admin/javascript/bamburgh/perfect-scrollbar.min.js#local.assetCachingKey#"></script>
	<script src="/assets/common/javascript/jQueryAddons/bamburgh/1.0.0/vendor/feather-icons/js/feather-icons.min.js"></script>
	<script src="/assets/admin/javascript/bamburgh/feather-icons.min.js#local.assetCachingKey#"></script>
	<script src="/assets/common/javascript/jQueryAddons/bamburgh/1.0.0/vendor/select2/js/select2.min.js"></script>
	<script src="/assets/common/javascript/jQueryAddons/bamburgh/1.0.0/js/bamburgh.min.js"></script>
	<script src="/assets/common/javascript/jQueryAddons/datatables/1.10.24/js/jquery.dataTables.min.js"></script>
	<script src="/assets/common/javascript/jQueryAddons/datatables/1.10.24/js/dataTables.bootstrap4.min.js"></script>
	<script src="/assets/common/javascript/jQueryAddons/datatables/select/1.3.3/js/dataTables.select.min.js"></script>
	<script src="/assets/common/javascript/jQueryAddons/datatables/rowreorder/1.2.8/js/dataTables.rowReorder.min.js"></script>
	<script src="/assets/common/javascript/jQueryAddons/bootstrap4-toggle/3.6.1/js/bootstrap4-toggle.min.js"></script>
	<script src="/assets/common/javascript/jQueryAddons/bootstrap4-tagsinput/0.8.0/js/tagsinput.js"></script>
	<script src="/assets/common/javascript/jQueryAddons/bamburgh/1.0.0/vendor/form-wizard/js/form-wizard.min.js"></script>
	</cfoutput>
</cfif>
</body>
</html>