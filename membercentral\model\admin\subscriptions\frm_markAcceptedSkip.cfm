<cfsavecontent variable="local.js">
	<cfoutput>
	<script language="javascript">
		function enableConfirmBtn() {
			if(isAcceptCheck()) $("##btnAccept").prop('disabled',false);
			else $("##btnAccept").prop('disabled',true);
		}
		function doMarkAsAccepted() {
			mca_hideAlert('err_frmAccept');
			if(isAcceptCheck()) {
				if (! $('input[name="fSkipEmail"]').is(':checked')) {
					mca_showAlert('err_frmAccept','Select an option.');
					return false;
				}

				$('##loadingWrapper').removeClass('d-none');
				$('##frmAccept').addClass('d-none');
				document.forms['frmAccept'].submit();
			}
		}
		function isAcceptCheck(){
			if($.trim($("##strAccept").val().toUpperCase()) == 'ACCEPT') {
				return true;
			}
			return false;
		}

		$(function(){
			$(".selectedSubCount").html(top.$('##selSubCountDisp').data('selcount'));
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.js#">

<cfoutput>
<form name="frmAccept" id="frmAccept" action="#this.link.processMarkAccepted#" method="POST" class="p-2">
	<cfloop collection="#local.strFilters#" item="local.thisFilterKey">
		<input type="hidden" name="#local.thisFilterKey#" id="#local.thisFilterKey#" value="#local.strFilters[local.thisFilterKey]#">
	</cfloop>

	<div class="card card-box p-3 mb-3">
		<h6 class="font-weight-bold mb-2">Send emails to subscribers?</h6>
		<div class="mb-3">Some subscriptions may have Welcome or Renew Email Templates that are triggered upon Acceptance. Do you want to send these notifications?</div>
		<div class="custom-controls-stacked d-block">
			<div class="custom-control custom-radio mb-1">
				<input type="radio" name="fSkipEmail" id="fSkipEmailNo" class="custom-control-input" value="0">
				<label class="custom-control-label" for="fSkipEmailNo">Yes, send notifications</label>
			</div>
			<div class="custom-control custom-radio">
				<input type="radio" name="fSkipEmail" id="fSkipEmailYes" class="custom-control-input" value="1">
				<label class="custom-control-label" for="fSkipEmailYes">No, skip notifications</label>
			</div>
		</div>
		<div id="err_frmAccept" class="alert alert-danger my-3 p-2 d-none"></div>
	</div>
	<div class="alert p-3 alert-warning fade show" role="alert">
		<div class="d-flex mb-2">
			<h5>Confirmation Needed</h5>
		</div>
		<div class="mb-3">
			You are about to accept <span class="font-weight-bold lead selectedSubCount"></span> subscription trees.
			<div class="mt-2">
				To continue, type <span class="font-weight-bold">ACCEPT</span> in the box and click OK: 
				<input type="text" name="strAccept" id="strAccept" value="" maxlength="6" class="form-control form-control-sm d-inline-block" style="width:100px;" onKeyUp="enableConfirmBtn();" autocomplete="off">
				<button type="button" name="btnAccept" id="btnAccept" class="btn btn-sm btn-danger" onclick="doMarkAsAccepted();" disabled>OK</button>
			</div>
		</div>
	</div>
</form>
<div id="loadingWrapper" class="p-3 d-none">
	<div class="my-2 text-center">
		<i class="fa-light fa-circle-notch fa-spin fa-4x"></i> Please wait while we accept the selected subscriptions.
	</div>
</div>
</cfoutput>