<cfcomponent extends="Bucket">

	<cfset variables.thisBucketTypeID = 12>
	<cfset variables.thisBucketType = "courtdocuments">
	<cfset variables.thisBucketCartItemTypeID = 2>
	<cfset variables.thisBucketMaxPerPage = 10>
	<cfset variables.thisBucketMaxShown = 10000>

	<cffunction name="showHeader" access="private" output="false" returntype="string">
		<cfargument name="bucketName" type="string" required="yes">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var header = "">

		<cfsavecontent variable="header">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/courtDocuments/header.cfm">
		</cfsavecontent>

		<cfreturn header>
	</cffunction>
	
	<cffunction name="showSearchForm" access="public" output="false" returntype="string">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="searchID" required="no" type="numeric" default="0">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var local = StructNew()>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>
		
		<!--- load search if passed in --->
		<cfset local.strSearchForm = prepSearchForSearchForm(searchID=arguments.searchID, bucketID=arguments.bucketID)>

		<!--- get bucket info to get any restrictions --->
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>
		<cfset local.strSettings = prepSettings(local.qryBucketInfo.bucketSettings)>
		
		<cfif not variables.cfcuser_isLoggedIn and StructKeyExists(local.strSettings,"searchoverrideguest")>
			<cfset local.guestbanklist = "">
			<cfif StructKeyExists(local.strSettings.searchoverrideguest,"bankids") and len(local.strSettings.searchoverrideguest.bankids)>
				<cfset local.guestbanklist = listappend(local.guestbanklist,local.strSettings.searchoverrideguest.bankids)>
			</cfif>
			<cfif StructKeyExists(local.strSettings.searchoverrideguest,"includedefaultbanks") and local.strSettings.searchoverrideguest.includedefaultbanks>
				<cfset local.qDefaultBanks = getDefaultBanks(session.mcstruct.sitecode)>
				<cfset local.guestbanklist = listappend(local.guestbanklist,valuelist(local.qDefaultBanks.groupid))>
			</cfif>
		</cfif>
		
		<!--- get banks --->
		<cfquery name="local.qryBanks" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			select g.groupid, g.Description
			from dbo.depoGroups as g
			<cfif isdefined("local.guestbanklist") and len(local.guestbanklist)>
				where g.groupid in (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#local.guestbanklist#">)
			<cfelse>
				inner join depoSourceGroup as sg on sg.groupid = g.groupid and sg.depomemberdataid = <cfqueryparam value="#variables.cfcuser_depomemberdataid#" cfsqltype="CF_SQL_INTEGER">
				where 1=1
			</cfif>
			<cfif StructKeyExists(local.strSettings,"restrictorgcode") and len(local.strSettings.restrictorgcode.value)>
				<cfswitch expression="#local.strSettings.restrictorgcode.action#">
					<cfcase value="include">
						and g.orgCode in (<cfqueryparam cfsqltype="cf_sql_varchar" list="yes" value="#local.strSettings.restrictorgcode.value#">)
					</cfcase>
					<cfcase value="exclude">
						and g.orgCode not in (<cfqueryparam cfsqltype="cf_sql_varchar" list="yes" value="#local.strSettings.restrictorgcode.value#">)
					</cfcase>
				</cfswitch>
			</cfif>
			<cfif StructKeyExists(local.strSettings,"restrictbankid")>
				<cfswitch expression="#local.strSettings.restrictbankid.action#">
					<cfcase value="include">
						and g.groupid in (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#local.strSettings.restrictbankid.value#">)
					</cfcase>
					<cfcase value="exclude">
						and g.groupid not in (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#local.strSettings.restrictbankid.value#">)
					</cfcase>
				</cfswitch>
			</cfif>
			order by g.description
		</cfquery>

		<!--- get case types and document types --->
		<cfif (variables.cfcuser_isLoggedIn and StructKeyExists(local.strSettings,"search") and local.strSettings.search.allownationwide)
			or
			(not variables.cfcuser_isLoggedIn and StructKeyExists(local.strSettings,"search") and local.strSettings.search.allownationwide and StructKeyExists(local.strSettings,"searchoverrideguest") and local.strSettings.searchoverrideguest.allownationwide)>
			<cfset local.bankID = 0>
		<cfelse>
			<cfset local.bankID = val(local.qryBanks.groupid)>
		</cfif>
		<cfset local.strCaseTypesAndDocTypes = getCaseTypesAndDocTypes(arguments.bucketID,local.bankid)>
		<cfset local.qryDocumentFlags = getDocumentBankFlags(local.bankid)>

		<cfset showCommonJS(bucket=variables.thisBucketType, bucketID=arguments.bucketID, viewDirectory=arguments.viewDirectory)>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/courtDocuments/searchForm.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="prepSearchForSearchForm" access="private" returntype="struct" output="no" hint="parses the searchXML and populates search form">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
	
		<cfset var local = structNew()>
		
		<cfscript>
		local.returnStruct = StructNew();

		if (arguments.searchID gt 0) {
			// convert origin searchXML to bucket searchXML
			local.searchXML = application.objSearchTranslate.convertSearchXML(arguments.searchID,variables.thisBucketTypeID);

			// read/clean from xml
			local.returnStruct.s_type = local.searchXML.search["s_type"].xmlText;
			local.returnStruct.s_cause = local.searchXML.search["s_cause"].xmlText;
			local.returnStruct.s_ownership = local.searchXML.search["s_ownership"].xmlText;
			local.returnStruct.s_docflags = local.searchXML.search["s_docflags"].xmlText;
			local.returnStruct.s_key_all = local.searchXML.search["s_key_all"].xmlText;
			local.returnStruct.s_key_one = local.searchXML.search["s_key_one"].xmlText;
			local.returnStruct.s_key_phrase = local.searchXML.search["s_key_phrase"].xmlText;
			local.returnStruct.s_key_x = local.searchXML.search["s_key_x"].xmlText;
		} else {
			local.returnStruct.s_type = '';
			local.returnStruct.s_cause = '';
			local.returnStruct.s_ownership = '';
			local.returnStruct.s_docflags = '';
			local.returnStruct.s_key_all = '';
			local.returnStruct.s_key_one = '';
			local.returnStruct.s_key_phrase = '';
			local.returnStruct.s_key_x = '';
		}

		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="prepSearch" access="private" returntype="struct" output="no" hint="parses the searchXML and prepares search criteria">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">

		<cfset var searchXML = application.objSearchTranslate.convertSearchXML(arguments.searchID,variables.thisBucketTypeID)>
		
		<cfreturn prepSearchFromXML(siteID=arguments.siteID,bucketID=arguments.bucketID,searchXML=searchXML)>
	</cffunction>


	<cffunction name="prepSearchFromXML" access="private" returntype="struct" output="no" hint="parses the searchXML and prepares search criteria">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="searchXML" required="yes" type="xml">
	
		<cfset var local = structNew()>
		
		<cfif not variables.initRun>
			<cfset init()>
		</cfif>
		
		<cfscript>
		// get bucket info to get any restrictions
		local.qryBucketInfo = getBucketInfo(arguments.bucketID);

		// read/clean from xml
		local.bid = trim(arguments.searchXML.search["bid"].xmlText);
		local.s_type = trim(arguments.searchXML.search["s_type"].xmlText);
		local.s_cause = trim(arguments.searchXML.search["s_cause"].xmlText);
		local.s_ownership = val(trim(arguments.searchXML.search["s_ownership"].xmlText));
		local.s_documentname = trim(prepareSearchString(arguments.searchXML.search["s_documentname"].xmlText));
		local.s_docflags = trim(arguments.searchXML.search["s_docflags"].xmlText);
		local.s_key_all = trim(prepareSearchString(arguments.searchXML.search["s_key_all"].xmlText));
		local.s_key_one = trim(prepareSearchString(arguments.searchXML.search["s_key_one"].xmlText,true));
		local.s_key_phrase = trim(preparePhraseString(arguments.searchXML.search["s_key_phrase"].xmlText));
		local.s_key_x = trim(prepareSearchString(arguments.searchXML.search["s_key_x"].xmlText));
		
		// prepare keywords
		local.keywordsInclude = "";
		if (Len(local.s_documentname))
			local.keywordsInclude = listAppend(local.keywordsInclude,local.s_documentname,chr(7));
		if (Len(local.s_key_all))
			local.keywordsInclude = listAppend(local.keywordsInclude,local.s_key_all,chr(7));
		if (Len(local.s_key_one))
			local.keywordsInclude = listAppend(local.keywordsInclude,local.s_key_one,chr(7));
		if (Len(local.s_key_phrase))
			local.keywordsInclude = listAppend(local.keywordsInclude,local.s_key_phrase,chr(7));
		local.keywordsInclude = Replace(local.keywordsInclude,chr(7)," and ","ALL");

		local.keywordsExclude = "";
		if (Len(local.s_key_x))
			local.keywordsExclude = replaceNoCase(local.s_key_x," and "," or ","all");

		if (len(local.keywordsExclude) and len(local.keywordsInclude)) 
			local.finalKeywords = local.keywordsInclude & " and not (" & local.keywordsExclude & ")";
		else if (len(local.keywordsInclude))
			local.finalKeywords = local.keywordsInclude;
		else if (len(local.keywordsExclude))
			local.finalKeywords = "a and not (" & local.keywordsExclude & ")";
		else 
			local.finalKeywords = "";

		if (len(local.finalKeywords)) {
			local.finalFullTextKeywords = local.finalKeywords & " AND (tscourtdocxxx)";
		} else {
			local.finalFullTextKeywords = "";
		} 

		//filter docflags, if present
		if (len(local.s_docflags) and len(local.s_ownership) and isnumeric(local.s_ownership) and local.s_ownership gt 0) {
			local.qryDocFlags = getDocumentBankFlags(local.s_ownership,local.s_docflags);
			local.s_docflags = valuelist(local.qryDocFlags.docflagid);
		}
		else
			local.s_docflags = "";

		//ignore doctype and casetype if they are 0
		if (local.s_cause eq 0)
			local.s_cause = "";
		if (local.s_type eq 0)
			local.s_type = "";

		//is this bucket the orgin bucket of the search
		if (local.qryBucketInfo.bucketID eq local.bid)
			local.isOriginBucket = true;
		else
			local.isOriginBucket = false;

		// if this is not source bucket, then ignore ownership (could be improved by only ignoring this field if orgin bucket is of same buckettype as this bucket --- or making this driven by a bucketSetting)
		// if not source bucket add the search all nationwide bucket group id.
		if (not local.isOriginBucket) 
			local.s_ownership = listprepend(variables.cfcuser_TSgroups,0);

		local.settingsStruct = prepSettings(local.qryBucketInfo.bucketSettings);

		// return search struct
		local.returnStruct = structNew();
		structInsert(local.returnStruct,"keywords",local.finalKeywords);
		structInsert(local.returnStruct,"fulltextkeywords",local.finalFullTextKeywords);
		structInsert(local.returnStruct,"type",local.s_type);
		structInsert(local.returnStruct,"cause",local.s_cause);
		structInsert(local.returnStruct,"ownership",local.s_ownership);
		structInsert(local.returnStruct,"documentflags",trim(local.s_docflags));
		structInsert(local.returnStruct,"strSettings",local.settingsStruct);
		structInsert(local.returnStruct,"isoriginbucket",local.isOriginBucket);

		// do i have enough criteria to run a search?
		if (not len(local.finalKeywords) and not len(local.s_type) and not len(local.s_cause) and not len(local.s_ownership) and not len(local.s_docflags))
			structInsert(local.returnStruct,"searchAccepted",false);
		else
			structInsert(local.returnStruct,"searchAccepted",true);

		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="prepSettings" access="private" returntype="struct" output="no" hint="parses the settingsXML into a standardized struct">
		<cfargument name="bucketSettings" required="yes" type="xml">
		
		<cfset var local = StructNew()>
		
		<cfscript>
			// standardize settings
			local.settingsStruct = StructNew();
			local.bucketSettingsXML = XMLParse(arguments.bucketSettings);
			if (StructKeyExists(local.bucketSettingsXML.settings,"restrict_orgcode") and StructKeyExists(local.bucketSettingsXML.settings["restrict_orgcode"].XmlAttributes,"action")) {
				local.settingsStruct.restrictorgcode = structnew();
				
				if (StructKeyExists(local.bucketSettingsXML.settings["restrict_orgcode"].XmlAttributes,"usesiteorgcode") and local.bucketSettingsXML.settings["restrict_orgcode"].XmlAttributes.usesiteorgcode eq "true")
					local.settingsStruct.restrictorgcode.value = session.mcstruct.sitecode;
				else
					local.settingsStruct.restrictorgcode.value = "";

				if (StructKeyExists(local.bucketSettingsXML.settings["restrict_orgcode"].XmlAttributes,"additionalorgcodes") and len(local.bucketSettingsXML.settings["restrict_orgcode"].XmlAttributes.additionalorgcodes))
					local.settingsStruct.restrictorgcode.value = listappend(local.settingsStruct.restrictorgcode.value,local.bucketSettingsXML.settings["restrict_orgcode"].XmlAttributes.additionalorgcodes);

				if (StructKeyExists(local.bucketSettingsXML.settings["restrict_orgcode"].XmlAttributes,"override") and local.bucketSettingsXML.settings["restrict_orgcode"].XmlAttributes.override eq "false")
					local.settingsStruct.restrictorgcode.override = false;
				else
					local.settingsStruct.restrictorgcode.override = true;
				
				if (local.bucketSettingsXML.settings["restrict_orgcode"].XmlAttributes.action eq "include")
					local.settingsStruct.restrictorgcode.action = "include";
				else if (local.bucketSettingsXML.settings["restrict_orgcode"].XmlAttributes.action eq "exclude")
					local.settingsStruct.restrictorgcode.action = "exclude";
				else // no action defined and there is no default so cancel restriction
					structdelete(local.settingsStruct,"orgcode");
			}
			if (StructKeyExists(local.bucketSettingsXML.settings,"restrict_bankid") and StructKeyExists(local.bucketSettingsXML.settings["restrict_bankid"].XmlAttributes,"action")) {
				local.settingsStruct.restrictbankid = structnew();
				local.settingsStruct.restrictbankid.value = local.bucketSettingsXML.settings["restrict_bankid"].xmlText;
				
				//override parameter not currently supported
				if (StructKeyExists(local.bucketSettingsXML.settings["restrict_bankid"].XmlAttributes,"override") and local.bucketSettingsXML.settings["restrict_bankid"].XmlAttributes.override eq "false")
					local.settingsStruct.restrictbankid.override = false;
				else
					local.settingsStruct.restrictbankid.override = true;
	
				if (local.bucketSettingsXML.settings["restrict_bankid"].XmlAttributes.action eq "include")
					local.settingsStruct.restrictbankid.action = "include";
				else if (local.bucketSettingsXML.settings["restrict_bankid"].XmlAttributes.action eq "exclude")
					local.settingsStruct.restrictbankid.action = "exclude";
				else // no action defined and there is no default so cancel restriction
					structdelete(local.settingsStruct,"bankid");
			}

			if (StructKeyExists(local.bucketSettingsXML.settings,"search")) {
				local.settingsStruct.search = structnew();

				if (StructKeyExists(local.bucketSettingsXML.settings["search"].XmlAttributes,"allownationwide") and local.bucketSettingsXML.settings["search"].XmlAttributes.allownationwide eq "true")
					local.settingsStruct.search.allownationwide = true;
				else
					local.settingsStruct.search.allownationwide = false;

				if (StructKeyExists(local.bucketSettingsXML.settings["search"].XmlAttributes,"showcategorycounts") and local.bucketSettingsXML.settings["search"].XmlAttributes.showcategorycounts eq "true")
					local.settingsStruct.search.showcategorycounts = true;
				else
					local.settingsStruct.search.showcategorycounts = false;
			}
			if (StructKeyExists(local.bucketSettingsXML.settings,"searchoverride_guest")) {
				local.settingsStruct.searchoverrideguest = structnew();

				if (StructKeyExists(local.bucketSettingsXML.settings["searchoverride_guest"].XmlAttributes,"bankids") and len(trim(local.bucketSettingsXML.settings["searchoverride_guest"].XmlAttributes.bankids)))
					local.settingsStruct.searchoverrideguest.bankids = local.bucketSettingsXML.settings["searchoverride_guest"].XmlAttributes.bankids;
	
				if (StructKeyExists(local.bucketSettingsXML.settings["searchoverride_guest"].XmlAttributes,"includedefaultbanks") and local.bucketSettingsXML.settings["searchoverride_guest"].XmlAttributes.includedefaultbanks eq "true")
					local.settingsStruct.searchoverrideguest.includedefaultbanks = true;
				else
					local.settingsStruct.searchoverrideguest.includedefaultbanks = false;

				if (StructKeyExists(local.bucketSettingsXML.settings["searchoverride_guest"].XmlAttributes,"allownationwide") and local.bucketSettingsXML.settings["searchoverride_guest"].XmlAttributes.allownationwide eq "true")
					local.settingsStruct.searchoverrideguest.allownationwide = true;
				else
					local.settingsStruct.searchoverrideguest.allownationwide = false;

				if (StructKeyExists(local.bucketSettingsXML.settings["searchoverride_guest"].XmlAttributes,"showcategorycounts") and local.bucketSettingsXML.settings["searchoverride_guest"].XmlAttributes.showcategorycounts eq "true")
					local.settingsStruct.searchoverrideguest.showcategorycounts = true;
				else
					local.settingsStruct.searchoverrideguest.showcategorycounts = false;
			}

			local.settingsStruct.accountstatuschecks = structnew();
			if (StructKeyExists(local.bucketSettingsXML.settings,"accountstatuschecks")) {

				if (StructKeyExists(local.bucketSettingsXML.settings["accountstatuschecks"].XmlAttributes,"bypasschecktrialsmithallowed") and local.bucketSettingsXML.settings["accountstatuschecks"].XmlAttributes.bypasschecktrialsmithallowed eq "true")
					local.settingsStruct.accountstatuschecks.bypasschecktrialsmithallowed = true;
				else
					local.settingsStruct.accountstatuschecks.bypasschecktrialsmithallowed = false;

				if (StructKeyExists(local.bucketSettingsXML.settings["accountstatuschecks"].XmlAttributes,"bypasschecktrialsmithdisabled") and local.bucketSettingsXML.settings["accountstatuschecks"].XmlAttributes.bypasschecktrialsmithdisabled eq "true")
					local.settingsStruct.accountstatuschecks.bypasschecktrialsmithdisabled = true;
				else
					local.settingsStruct.accountstatuschecks.bypasschecktrialsmithdisabled = false;

				if (StructKeyExists(local.bucketSettingsXML.settings["accountstatuschecks"].XmlAttributes,"bypasschecktrialsmithpending") and local.bucketSettingsXML.settings["accountstatuschecks"].XmlAttributes.bypasschecktrialsmithpending eq "true")
					local.settingsStruct.accountstatuschecks.bypasschecktrialsmithpending = true;
				else
					local.settingsStruct.accountstatuschecks.bypasschecktrialsmithpending = false;

				if (StructKeyExists(local.bucketSettingsXML.settings["accountstatuschecks"].XmlAttributes,"bypasschecktrialsmithexpired") and local.bucketSettingsXML.settings["accountstatuschecks"].XmlAttributes.bypasschecktrialsmithexpired eq "true")
					local.settingsStruct.accountstatuschecks.bypasschecktrialsmithexpired = true;
				else
					local.settingsStruct.accountstatuschecks.bypasschecktrialsmithexpired = false;

				if (StructKeyExists(local.bucketSettingsXML.settings["accountstatuschecks"].XmlAttributes,"bypasschecktrialsmithnoplan") and local.bucketSettingsXML.settings["accountstatuschecks"].XmlAttributes.bypasschecktrialsmithnoplan eq "true")
					local.settingsStruct.accountstatuschecks.bypasschecktrialsmithnoplan = true;
				else
					local.settingsStruct.accountstatuschecks.bypasschecktrialsmithnoplan = false;
			} else {

				local.settingsStruct.accountstatuschecks.bypasschecktrialsmithallowed = false;
				local.settingsStruct.accountstatuschecks.bypasschecktrialsmithdisabled = false;
				local.settingsStruct.accountstatuschecks.bypasschecktrialsmithpending = false;
				local.settingsStruct.accountstatuschecks.bypasschecktrialsmithexpired = false;
				local.settingsStruct.accountstatuschecks.bypasschecktrialsmithnoplan = false;
			}
			
		</cfscript>
		<cfreturn local.settingsStruct>
	</cffunction>

	<cffunction name="getResultsCount" access="public" returntype="struct" output="no" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="mcproxy_siteID" required="no">
		<cfargument name="searchID" required="no">
		<cfargument name="bucketID" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["itemcount"] = 'N/A'>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>
		
		<cfif 
			(NOT StructKeyExists(arguments, "mcproxy_siteID") OR NOT IsNumeric(arguments.mcproxy_siteID)) OR 
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID)) OR 
			(NOT StructKeyExists(arguments, "bucketID") OR NOT IsNumeric(arguments.bucketID))>
				<cfset local.isValidArguments = false>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = doGetResultsCount(siteID=arguments.mcproxy_siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doGetResultsCount" access="private" returntype="struct" output="no" hint="searches and returns a count">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">

		<cfset var local = StructNew()>

		<cfset local.strSearch = prepSearch(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>
		<cfset local.returnStruct = StructNew()>

		<cfif NOT local.strSearch.searchAccepted or (not variables.cfcuser_isSiteAdmin and val(local.qryBucketInfo.restrictToGroupID) gt 0 and local.qryBucketInfo.isMemberInRestrictedGroup is not 1)>
			<cfset StructInsert(local.returnStruct,"itemcount",'N/A')>
			<cfset StructInsert(local.returnStruct,"success",true)>
			<cfset saveBucketCount(arguments.searchID,arguments.bucketID,-1)>
		<cfelse>
			<cfset local.cachedItemCount = getCachedBucketCount(arguments.searchID,arguments.bucketID)>
			<cfif local.cachedItemCount gte 0>
				<cfset StructInsert(local.returnStruct,"itemcount",local.cachedItemCount)>
				<cfset StructInsert(local.returnStruct,"success",true)>
			<cfelse>
				<cfset local.returnStruct = runResultsCount(siteID=arguments.siteID,bucketID=arguments.bucketID,strSearch=local.strSearch)>
				<cfset saveStats(arguments.searchID,'#variables.thisBucketType#.getResultsCount',local.returnStruct.ExecutionTime,local.returnStruct.itemCount)>
				<cfset saveBucketCount(arguments.searchID,arguments.bucketID,local.returnStruct.itemCount)>
				<!--- Remove structkeys not expected by caller --->
				<cfset structDelete(local.returnStruct, "ExecutionTime")/>
			</cfif>
		</cfif>
		<cfreturn local.returnStruct/>
	</cffunction>

	<cffunction name="getResultsCountForSearchIndex" access="public" output="no" returntype="struct">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="firstname" required="true" type="string">
		<cfargument name="lastname" required="true" type="string">

		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.searchXML">
			<cfoutput>
			<search xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2">
				<bid>#val(arguments.bucketID)#</bid>
				<s_cause></s_cause>
				<s_docflags></s_docflags>
				<s_documentname>#xmlFormat(trim(replace(arguments.firstname,chr(34),'','ALL')))# #xmlFormat(trim(replace(arguments.lastname,chr(34),'','ALL')))#</s_documentname>
				<s_key_all></s_key_all>
				<s_key_one></s_key_one>
				<s_key_phrase></s_key_phrase>
				<s_key_x></s_key_x>
				<s_ownership xsi:nil="true"/>
				<s_state></s_state>
				<s_type></s_type>
			</search>
			</cfoutput>
		</cfsavecontent>
		<cfset local.searchXML = XMLParse(local.searchXML)>
		
		<cfset local.strSearch = prepSearchFromXML(siteID=arguments.siteID,bucketID=arguments.bucketID,searchXML=local.searchXML)>
		<cfset local.returnStruct = StructNew()>
		<cfif NOT local.strSearch.searchAccepted>
			<cfset StructInsert(local.returnStruct,"itemcount",'N/A')>
			<cfset StructInsert(local.returnStruct,"success",true)>
		<cfelse>
			<cfset local.returnStruct = runResultsCount(siteID=arguments.siteID,bucketID=arguments.bucketID,strSearch=local.strSearch)>
		</cfif>
		<cfreturn local.returnStruct/>
	</cffunction>
	<cffunction name="runResultsCount" access="private" returntype="struct" output="no" hint="searches and returns a count">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="strSearch" required="yes" type="struct">

		<cfset var local = StructNew()>
		
		<cfif not variables.initRun>
			<cfset init()>
		</cfif>
		
		<cfset local.memberGroups = variables.cfcuser_TSgroups>
		<cfif StructKeyExists(arguments.strSearch.strSettings,"search") and arguments.strSearch.strSettings.search.allownationwide>
			<cfset local.qJoinedAndPublicGroups = cleanGroups(arguments.strSearch.ownership,true)>
		<cfelse>
			<cfset local.qJoinedAndPublicGroups = cleanGroups(arguments.strSearch.ownership,false)>
		</cfif>
		<cfset local.stringJoinedAndPublicGroups = valuelist(local.qJoinedAndPublicGroups.groupid)>
		<cfset local.stringJoinedGroupsOnly = local.memberGroups>

		<cfif not variables.cfcuser_isLoggedIn and StructKeyExists(arguments.strSearch.strSettings,"searchoverrideguest") and StructKeyExists(arguments.strSearch.strSettings.searchoverrideguest,"bankids")>
			<cfset local.stringJoinedAndPublicGroups = listappend(valuelist(local.qJoinedAndPublicGroups.groupid),arguments.strSearch.strSettings.searchoverrideguest.bankids)>
			<cfset local.stringJoinedGroupsOnly = listappend(local.memberGroups,arguments.strSearch.strSettings.searchoverrideguest.bankids)>
		</cfif>

		<cfif not variables.cfcuser_isLoggedIn and StructKeyExists(arguments.strSearch.strSettings,"searchoverrideguest") and StructKeyExists(arguments.strSearch.strSettings.searchoverrideguest,"includedefaultbanks") and arguments.strSearch.strSettings.searchoverrideguest.includedefaultbanks>
			<cfset local.qDefaultBanks = getDefaultBanks(session.mcstruct.sitecode)>
			<cfset local.stringJoinedAndPublicGroups = listappend(valuelist(local.qJoinedAndPublicGroups.groupid),valuelist(local.qDefaultBanks.groupid))>
			<cfset local.stringJoinedGroupsOnly = listappend(local.memberGroups,valuelist(local.qDefaultBanks.groupid))>
		</cfif>

		<cfif not len(local.stringJoinedAndPublicGroups)>
			<cfset local.stringJoinedAndPublicGroups = "-1">
		</cfif>

		<cfif not len(local.stringJoinedGroupsOnly)>
			<cfset local.stringJoinedGroupsOnly = "-1">
		</cfif>

		<cfset local.returnStruct = StructNew()>

		<cfquery name="local.qryResults" datasource="#application.dsn.tlasites_trialsmith.dsn#" result="local.qryStat">
			set nocount on;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			declare @itemCount int, @s varchar(8000), @fulltext_s VARCHAR(8000);
			declare @approvedStatusID int 
			select @approvedStatusID=statusID from  depoDocumentStatuses where statusName = 'Approved'

			<cfif len(arguments.strSearch.keywords)>
				set @s = <cfqueryparam value="#arguments.strSearch.keywords#" cfsqltype="CF_SQL_LONGVARCHAR">;
				set @fulltext_s = <cfqueryparam value="#arguments.strSearch.fulltextkeywords#" cfsqltype="CF_SQL_LONGVARCHAR">;
	
				DECLARE @tmpSH TABLE (documentID int PRIMARY KEY, rank int);

				INSERT INTO @tmpSH (rank, documentID)
				SELECT max(rank) as rank, documentID
				FROM (
					SELECT sdsearch.rank, sd.documentID
					FROM containstable(search.dbo.depodocuments,searchtext,@fulltext_s) AS sdsearch
					INNER JOIN search.dbo.depodocuments sd on sdsearch.[key] = sd.id
					UNION
					SELECT dsearch.rank, d.documentID
					FROM containstable(dbo.depodocuments,*,@s) AS dsearch
					INNER JOIN dbo.depodocuments d on dsearch.[key] = d.documentid
				) sh
				GROUP BY documentID;
			</cfif>

			select @itemCount = count(*)
			from dbo.depoDocuments as d 
			inner join dbo.depoDocumentStatusHistory as dsh 
				on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
				and dsh.statusID = @approvedStatusID
			inner join dbo.depoCaseTypes as ct on d.caseTypeId = ct.caseTypeID
				<cfif arguments.strSearch.cause gt 0>
					and ct.casetypeid in (<cfqueryparam value="#arguments.strSearch.cause#" cfsqltype="CF_SQL_INTEGER" list="yes">)
				</cfif>
			inner join dbo.depogroups as g on d.groupid = g.groupid
				and g.groupid in (<cfqueryparam value="#local.stringJoinedAndPublicGroups#" cfsqltype="CF_SQL_INTEGER" list="yes">)
				<cfif arguments.strSearch.ownership gt 0>
					and g.groupid in (<cfqueryparam value="#arguments.strSearch.ownership#" cfsqltype="CF_SQL_INTEGER" list="yes">)
				</cfif>
			inner join depoDocumentTypes t on d.documentTypeId = t.TypeId
				<cfif arguments.strSearch.type gt 0>
					and t.TypeId in (<cfqueryparam value="#arguments.strSearch.type#" cfsqltype="CF_SQL_INTEGER" list="yes">)
				</cfif>
			<cfif len(arguments.strSearch.keywords)>
				inner join @tmpSH as searchhits on searchhits.documentid = d.documentid
			</cfif>
			<cfif len(arguments.strSearch.documentflags)>
				inner join docflaglinks fl on fl.documentid = d.documentid and fl.docflagid in (<cfqueryparam value="#arguments.strSearch.documentflags#" cfsqltype="CF_SQL_INTEGER" list="yes">)
			</cfif>
			<cfif len(local.stringJoinedGroupsOnly)>
				left outer join dbo.depoGroupDocumentTypeOverrides as ot on ot.groupid = g.groupid 
					and ot.typeid = t.typeid
					and ot.groupid in (#local.stringJoinedGroupsOnly#)
				where (t.dcourtdoc = 1 or ot.typeid is not null)
			<cfelse>
				where t.dcourtdoc = 1
			</cfif>

			<cfif StructKeyExists(arguments.strSearch.strSettings,"restrictorgcode") and len(arguments.strSearch.strSettings.restrictorgcode.value)>
				<cfswitch expression="#arguments.strSearch.strSettings.restrictorgcode.action#">
					<cfcase value="include">
						and g.orgCode in (<cfqueryparam cfsqltype="cf_sql_varchar" list="yes" value="#arguments.strSearch.strSettings.restrictorgcode.value#">)
					</cfcase>
					<cfcase value="exclude">
						and g.orgCode not in (<cfqueryparam cfsqltype="cf_sql_varchar" list="yes" value="#arguments.strSearch.strSettings.restrictorgcode.value#">)
					</cfcase>
				</cfswitch>
			</cfif>
			<cfif StructKeyExists(arguments.strSearch.strSettings,"restrictbankid") and len(arguments.strSearch.strSettings.restrictbankid.value)>
				<cfswitch expression="#arguments.strSearch.strSettings.restrictbankid.action#">
					<cfcase value="include">
						and g.groupid in (#arguments.strSearch.strSettings.restrictbankid.value#)
					</cfcase>
					<cfcase value="exclude">
						and g.groupid not in (#arguments.strSearch.strSettings.restrictbankid.value#)
					</cfcase>
				</cfswitch>
			</cfif>;

			select @itemCount as itemCount;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset StructInsert(local.returnStruct,"executionTime",local.qryStat.ExecutionTime)>
		<cfset StructInsert(local.returnStruct,"itemcount",local.qryResults.itemCount)>
		<cfset StructInsert(local.returnStruct,"success",true)>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getNotLoggedInResults" access="private" output="false" returntype="struct" hint="searches and returns not logged in text">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="searchID" type="numeric" required="yes">
		<cfargument name="bucketID" type="numeric" required="yes">
		<cfargument name="viewDirectory" type="string" required="no" default="default">
		
		<cfset var local = StructNew()>
		
		<cfif not variables.initRun>
			<cfset init()>
		</cfif>
		
		<cfset local.strResultsCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>
		<cfset local.hasDepositionBucket = CreateObject("component","model.search.search").getBucketIDByType(siteID=arguments.siteID, bucketType='Depositions')>

		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/courtDocuments/notLoggedInResults.cfm">
		</cfsavecontent>
		<cfset local.stResults = ReReplace(local.stResults,'\s{2,}',' ','ALL')>		

		<cfset local.returnStruct = StructNew()>
		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfset StructInsert(local.returnStruct,"itemcount",local.strResultsCount.itemCount)>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getResults" access="public" returntype="struct" output="no" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="mcproxy_siteID" required="no">
		<cfargument name="searchID" required="no">
		<cfargument name="bucketID" required="no">
		<cfargument name="startRow" required="no">
		<cfargument name="sortType" required="no">
		<cfargument name="filter" required="no" default="">
		<cfargument name="queryOnly" required="no" default="0">
		<cfargument name="viewDirectory" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["errmsg"] = 'Invalid Request'>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>
		
		<cfif 
			(NOT StructKeyExists(arguments, "mcproxy_siteID") OR NOT IsNumeric(arguments.mcproxy_siteID)) OR
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID)) OR
			(NOT StructKeyExists(arguments, "bucketID") OR NOT IsNumeric(arguments.bucketID)) OR
			(NOT StructKeyExists(arguments, "startRow") OR NOT IsNumeric(arguments.startRow)) OR
			NOT StructKeyExists(arguments, "sortType") OR
			(StructKeyExists(arguments, "queryOnly") AND NOT IsBoolean(arguments.queryOnly)) OR
			(NOT StructKeyExists(arguments, "viewDirectory") OR NOT ListFind(variables.viewDirectories, arguments.viewDirectory))>
				<cfset local.isValidArguments = false>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = doGetResults(siteID=arguments.mcproxy_siteID, searchID=arguments.searchID,
				bucketID=arguments.bucketID, startrow=arguments.startrow, sortType=arguments.sortType,
				filter=arguments.filter, queryOnly=arguments.queryOnly, viewDirectory=arguments.viewDirectory)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doGetResults" access="private" returntype="struct" output="no" hint="searches and returns a struct result">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="startRow" required="yes" type="numeric">
		<cfargument name="sortType" required="yes" type="string">
		<cfargument name="filter" required="yes" type="string">
		<cfargument name="queryOnly" required="yes" type="boolean">
		<cfargument name="viewDirectory" required="yes" type="string">

		<cfset var local = StructNew()>
		
		<cfif not variables.initRun>
			<cfset init()>
		</cfif>
		
		<cfset local.strSearch = prepSearch(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>
		<cfset local.returnStruct = StructNew()>

		<!--- checks.
		1. logged in?
		2. RestrictToGroup?
		3. search accepted?
		4. TrialSmithAllowed?
		5. TrialSmithDisabled?
		6. TrialSmithPending?
		7. TrialSmithExpired?
		8. TrialSmithNoPlan?
		--->
		<cfif NOT variables.cfcuser_isLoggedIn>
			<cfreturn getNotLoggedInResults(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID,viewDirectory=arguments.viewDirectory)>
		<cfelseif NOT variables.cfcuser_isSiteAdmin AND val(local.qryBucketInfo.restrictToGroupID) GT 0 AND local.qryBucketInfo.isMemberInRestrictedGroup NEQ 1>
			<cfreturn showNotAllowed(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, bucketName=local.qryBucketInfo.bucketName, 
				accessDeniedMessage=local.qryBucketInfo.accessDeniedMessage, viewDirectory=arguments.viewDirectory)>
		<cfelseif NOT local.strSearch.searchAccepted>
			<cfreturn showSearchNotAccepted(searchID=arguments.searchID,bucketID=arguments.bucketID,bucketName=local.qryBucketInfo.bucketName,viewDirectory=arguments.viewDirectory)>
		<cfelseif not local.strSearch.strSettings.accountstatuschecks.bypasschecktrialsmithallowed and variables.cfcuser_TrialSmithAllowed is not 1>
			<cfreturn showTrialsmithNotAllowed(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfelseif not local.strSearch.strSettings.accountstatuschecks.bypasschecktrialsmithdisabled and variables.cfcuser_TrialSmithDisabled is 1>
			<cfreturn showTrialsmithDisabled(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfelseif not local.strSearch.strSettings.accountstatuschecks.bypasschecktrialsmithpending and variables.cfcuser_TrialSmithPending is 1>
			<cfreturn showTrialsmithPending(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfelseif not local.strSearch.strSettings.accountstatuschecks.bypasschecktrialsmithexpired and variables.cfcuser_TrialSmithExpired is 1>
			<cfreturn showTrialsmithExpired(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfelseif not local.strSearch.strSettings.accountstatuschecks.bypasschecktrialsmithnoplan and variables.cfcuser_TrialSmithNoPlan is not 0>
			<cfreturn showTrialsmithNoPlan(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfelse>
			<cfset local.strCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>

			<!--- is filter is present, parse it. filter is in format: groupid|casetypeid|documenttypeid --->
			<cfset local.strSearch.filter_group = "-1">
			<cfset local.strSearch.filter_casetype = "0">
			<cfset local.strSearch.filter_doctype = "0">
			<cfif listlen(arguments.filter,"|") is 3>
				<cfif getToken(arguments.filter,1,"|") gte 0>
					<cfset local.strSearch.filter_group = getToken(arguments.filter,1,"|")>
					<cfset local.strSearch.ownership = local.strSearch.filter_group>
				</cfif>
				<cfif getToken(arguments.filter,2,"|") gt 0>
					<cfset local.strSearch.filter_casetype = getToken(arguments.filter,2,"|")>
					<cfset local.strSearch.cause = local.strSearch.filter_casetype>
				</cfif>
				<cfif getToken(arguments.filter,3,"|") gt 0>
					<cfset local.strSearch.filter_doctype = getToken(arguments.filter,3,"|")>
					<cfset local.strSearch.type = local.strSearch.filter_doctype>
				<cfelse>	
					<!--- override max per page to show more drilldown options --->
					<cfset variables.thisBucketMaxPerPage = 100>
				</cfif>
			<cfelse>	
				<!--- override max per page to show more drilldown options --->
				<cfset variables.thisBucketMaxPerPage = 100>
			</cfif>

			<cfset local.memberGroups = variables.cfcuser_TSgroups>

			<!--- handle available groups --->
			<cfif StructKeyExists(local.strSearch.strSettings,"search") and local.strSearch.strSettings.search.allownationwide>
				<cfset local.qJoinedAndPublicGroups = cleanGroups(local.strSearch.ownership,true)>
			<cfelse>
				<cfset local.qJoinedAndPublicGroups = cleanGroups(local.strSearch.ownership,false)>
			</cfif>
			<cfif not local.qJoinedAndPublicGroups.recordcount>
				<cfset local.stringJoinedAndPublicGroups = "-1">
			<cfelse>
				<cfset local.stringJoinedAndPublicGroups = valuelist(local.qJoinedAndPublicGroups.groupid)>
			</cfif>

			<cfquery name="local.qryResults" datasource="#application.dsn.tlasites_trialsmith.dsn#" result="local.qryStat">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @itemCount int;
				DECLARE @depomemberdataid int = <cfqueryparam value="#variables.cfcuser_depomemberdataid#" cfsqltype="CF_SQL_INTEGER">;

				declare @approvedStatusID int 
				select @approvedStatusID=statusID from  depoDocumentStatuses where statusName = 'Approved'

				<cfif len(local.strSearch.keywords)>
					DECLARE @s varchar(8000) = <cfqueryparam value="#local.strSearch.keywords#" cfsqltype="CF_SQL_LONGVARCHAR">;		
					DECLARE @fulltext_s VARCHAR(8000) = <cfqueryparam value="#local.strSearch.fulltextkeywords#" cfsqltype="CF_SQL_LONGVARCHAR">;
 
					DECLARE @tmpSH TABLE (documentID int PRIMARY KEY, rank int);

					INSERT INTO @tmpSH (rank, documentID)
					SELECT max(rank) as rank, documentid
					FROM (
						SELECT sdsearch.rank, sd.documentid
						FROM containstable(search.dbo.depodocuments,searchtext,@fulltext_s) AS sdsearch
						INNER JOIN search.dbo.depodocuments sd on sdsearch.[key] = sd.id
						UNION
						SELECT dsearch.rank, d.documentid
						FROM containstable(dbo.depodocuments,*,@s) AS dsearch
						INNER JOIN dbo.depodocuments d on dsearch.[key] = d.documentid
					) sh
					GROUP BY documentID;
				</cfif>

				<cfif arguments.queryOnly eq 1 or local.strSearch.filter_doctype gt 0>
					DECLARE @tmpResults TABLE (autoid int IDENTITY(1,1), documentid int, rank int);

					INSERT INTO @tmpResults (documentid, rank)	
					Select d.documentid, <cfif len(local.strSearch.keywords)>searchhits.rank<cfelse>1000</cfif> as rank
				<cfelseif local.strSearch.filter_casetype gt 0>
					DECLARE @tmpResults TABLE (autoid int IDENTITY(1,1), docCount int, doctypeid int, doctypedesc varchar(255), doctypelabel varchar(50));

					INSERT INTO @tmpResults (doctypeid, doctypedesc, doctypelabel, docCount)	
					select t.typeID, t.description, isnull(g.doctypelabel,'Type of Document') as doctypelabel, count(d.documentid) as docCount
				<cfelseif local.strSearch.filter_group gte 0>
					DECLARE @tmpResults TABLE (autoid int IDENTITY(1,1), docCount int, casetypeid int, casetypedesc varchar(100), casetypelabel varchar(50));

					INSERT INTO @tmpResults (casetypeid, casetypedesc, casetypelabel, docCount)	
					select ct.casetypeid, ct.description, isNull(g.casetypelabel,'Cause of Action') as casetypelabel, count(d.documentid) as docCount
				<cfelse>
					DECLARE @tmpResults TABLE (autoid int IDENTITY(1,1), groupID int, docCount int, description varchar(400));

					INSERT INTO @tmpResults (groupid, description, docCount)
					select d.groupid, g.description, count(d.documentid) as docCount
				</cfif>
				from dbo.depoDocuments as d 
				inner join dbo.depoDocumentStatusHistory as dsh 
					on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
					and dsh.statusID = @approvedStatusID
				inner join dbo.depoCaseTypes as ct on d.caseTypeId = ct.caseTypeID
					<cfif len(local.strSearch.cause)>
						and ct.casetypeid in (<cfqueryparam value="#local.strSearch.cause#" cfsqltype="CF_SQL_INTEGER" list="yes">)
					</cfif>
				inner join dbo.depogroups as g on d.groupid = g.groupid
					<cfif listlen(local.stringJoinedAndPublicGroups)>
						and g.groupid in (<cfqueryparam value="#local.stringJoinedAndPublicGroups#" cfsqltype="CF_SQL_INTEGER" list="yes">)
					</cfif>
				inner join depoDocumentTypes t on d.documentTypeId = t.TypeId
					<cfif len(local.strSearch.type)>
						and t.TypeId in (<cfqueryparam value="#local.strSearch.type#" cfsqltype="CF_SQL_INTEGER" list="yes">)
					</cfif>
				<cfif len(local.strSearch.keywords)>
					inner join @tmpSH as searchhits on searchhits.documentid = d.documentid
				</cfif>
				<cfif len(local.strSearch.documentflags)>
					inner join dbo.docflaglinks fl on fl.documentid = d.documentid and fl.docflagid in (<cfqueryparam value="#local.strSearch.documentflags#" cfsqltype="CF_SQL_INTEGER" list="yes">)
				</cfif>

				<!--- only consider groups user is a member of --->
				<cfif len(local.memberGroups)>
					left outer join dbo.depoGroupDocumentTypeOverrides as ot on ot.groupid = g.groupid 
						and ot.typeid = t.typeid
						and ot.groupid in (<cfqueryparam value="#local.memberGroups#" cfsqltype="CF_SQL_INTEGER" list="yes">)

					where (t.dcourtdoc = 1 or ot.typeid is not null)
				<cfelse>
					where t.dcourtdoc = 1
				</cfif>
				<cfif StructKeyExists(local.strSearch.strSettings,"restrictorgcode") and len(local.strSearch.strSettings.restrictorgcode.value)>
					<cfswitch expression="#local.strSearch.strSettings.restrictorgcode.action#">
						<cfcase value="include">
							and g.orgCode in (<cfqueryparam cfsqltype="cf_sql_varchar" list="yes" value="#local.strSearch.strSettings.restrictorgcode.value#">)
						</cfcase>
						<cfcase value="exclude">
							and g.orgCode not in (<cfqueryparam cfsqltype="cf_sql_varchar" list="yes" value="#local.strSearch.strSettings.restrictorgcode.value#">)
						</cfcase>
					</cfswitch>
				</cfif>
				<cfif StructKeyExists(local.strSearch.strSettings,"restrictbankid") and len(local.strSearch.strSettings.restrictbankid.value)>
					<cfswitch expression="#local.strSearch.strSettings.restrictbankid.action#">
						<cfcase value="include">
							and g.groupid in (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#local.strSearch.strSettings.restrictbankid.value#">)
						</cfcase>
						<cfcase value="exclude">
							and g.groupid not in (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#local.strSearch.strSettings.restrictbankid.value#">)
						</cfcase>
					</cfswitch>
				</cfif>
				<cfif arguments.queryOnly eq 1 or local.strSearch.filter_doctype gt 0>
					order by rank desc, d.DocumentDate desc
				<cfelseif local.strSearch.filter_casetype gt 0>
					group by t.typeID, t.description, isnull(g.doctypelabel,'Type of Document')
					order by t.description
				<cfelseif local.strSearch.filter_group gte 0>
					group by ct.casetypeid, ct.description, isNull(g.casetypelabel,'Cause of Action')
					order by ct.description
				<cfelse>
					group by d.groupid, g.description
					order by g.description
				</cfif>;
				
				SELECT @itemCount = @@ROWCOUNT;

				<cfif local.strSearch.filter_doctype gt 0>
					select TOP (<cfqueryparam value="#variables.thisBucketMaxPerPage#" cfsqltype="CF_SQL_INTEGER">) @itemCount as itemCount,
						d.documentid, d.pages, d.expertname, d.style, d.documentdate, d.state, ct.description as causedesc, d.notes,
						dfo.dateLastModified as uploadpdfdate, m.email, m.firstname, m.lastname, m.billingfirm, m.phone, d.groupid,
						(select count(*) from dbo.docflaglinks where documentid = d.documentid) as numdocflags,
						owned = CASE
							WHEN d.depomemberdataid = @depomemberdataid then 1
							WHEN EXISTS (select documentID from dbo.depoPermissions where depomemberdataid = @depomemberdataid and documentid = d.documentid) then 1
							ELSE 0
							END,
						inCart = CASE
							WHEN EXISTS (select cartID from dbo.documentcart where documentid = d.documentid and depomemberdataid = @depomemberdataid and itemTypeID = #variables.thisBucketCartItemTypeID#) then 1
							ELSE 0
							END,
							<cfif local.strSearch.filter_group gt 0>
								(select description from dbo.depoGroups where groupID = #local.strSearch.filter_group#)
							<cfelse>
								null
							</cfif> as grpLabel,
							<cfif local.strSearch.filter_casetype gt 0>
								(select isNull(description,'Cause of Action') from dbo.depoCaseTypes where casetypeid = #local.strSearch.filter_casetype#)
							<cfelse>
								null
							</cfif> as caseLabel,
							<cfif local.strSearch.filter_doctype gt 0>
								(select isNull(description,'Type of Document') from dbo.depoDocumentTypes where typeid = #local.strSearch.filter_doctype#)
							<cfelse>
								null
							</cfif> as docLabel
					from @tmpResults as tmp
					inner join dbo.depoDocuments as d on d.documentid = tmp.documentid
					inner join dbo.depogroups as g on d.groupid = g.groupid
					inner join dbo.depoDocumentTypes as t on d.documentTypeId = t.TypeId
					inner join dbo.depoCaseTypes as ct on d.caseTypeId = ct.caseTypeID
					inner join dbo.depomemberdata as m on m.depomemberdataid = d.depomemberdataid
					left outer join dbo.depoDocumentsFilesOnline as dfo on dfo.documentID = d.documentID and dfo.fileType = 'pdf'
					where tmp.autoid >= <cfqueryparam value="#arguments.startRow#" cfsqltype="CF_SQL_INTEGER">
					ORDER BY tmp.autoid;
				<cfelseif arguments.queryOnly eq 1>
					select TOP (<cfqueryparam value="#variables.thisBucketMaxPerPage#" cfsqltype="CF_SQL_INTEGER">) @itemCount as itemCount,
						d.documentid, d.pages, d.expertname, d.style, d.documentdate, d.state, ct.description as causedesc, t.description as docType, d.notes,
						dfo.dateLastModified as uploadpdfdate, m.email, m.firstname, m.lastname, m.billingfirm, m.phone, d.groupid,
						(select count(*) from dbo.docflaglinks where documentid = d.documentid) as numdocflags,
						owned = CASE
							WHEN d.depomemberdataid = @depomemberdataid then 1
							WHEN EXISTS (select documentID from dbo.depoPermissions where depomemberdataid = @depomemberdataid and documentid = d.documentid) then 1
							ELSE 0
							END,
						inCart = CASE
							WHEN EXISTS (select cartID from dbo.documentcart where documentid = d.documentid and depomemberdataid = @depomemberdataid and itemTypeID = #variables.thisBucketCartItemTypeID#) then 1
							ELSE 0
							END,
						g.description as grpLabel,
						bankCount = count(*) over (partition by d.groupid),
						isNull(g.casetypelabel,'Cause of Action') as caseLabel,
						isNull(g.doctypelabel,'Type of Document') as docLabel,
						tla.description as orgname
					from @tmpResults as tmp
					inner join dbo.depoDocuments as d on d.documentid = tmp.documentid
					inner join dbo.depogroups as g on d.groupid = g.groupid
					inner join dbo.depotla as tla on g.orgcode = tla.state
					inner join dbo.depoDocumentTypes as t on d.documentTypeId = t.TypeId
					inner join dbo.depoCaseTypes as ct on d.caseTypeId = ct.caseTypeID
					inner join dbo.depomemberdata as m on m.depomemberdataid = d.depomemberdataid
					left outer join dbo.depoDocumentsFilesOnline as dfo on dfo.documentID = d.documentID and dfo.fileType = 'pdf'
					ORDER BY tla.description, g.description, tmp.autoID;
				<cfelse>
					DECLARE @docsInFolders int;
					SELECT @docsInFolders = sum(doccount) from @tmpResults;

					select TOP (<cfqueryparam value="#variables.thisBucketMaxPerPage#" cfsqltype="CF_SQL_INTEGER">) *, @itemcount as itemcount, @docsInFolders as docsInFolders,
						<cfif local.strSearch.filter_group gt 0>
							(select description from dbo.depoGroups where groupID = #local.strSearch.filter_group#)
						<cfelse>
							null
						</cfif> as grpLabel,
						<cfif local.strSearch.filter_casetype gt 0>
							(select isNull(description,'Cause of Action') from dbo.depoCaseTypes where casetypeid = #local.strSearch.filter_casetype#)
						<cfelse>
							null
						</cfif> as caseLabel,
						<cfif local.strSearch.filter_doctype gt 0>
							(select isNull(description,'Type of Document') from dbo.depoDocumentTypes where typeid = #local.strSearch.filter_doctype#)
						<cfelse>
							null
						</cfif> as docLabel
					from @tmpResults as tmp
					where tmp.autoid >= <cfqueryparam value="#arguments.startRow#" cfsqltype="CF_SQL_INTEGER">
					ORDER BY tmp.autoid;
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfset saveStats(arguments.searchID,'#variables.thisBucketType#.getResults',local.qryStat.ExecutionTime)>

			<cfif arguments.queryOnly eq 0>
				<!--- auto drill down if only one subfolder --->
				<cfif local.strSearch.filter_doctype is 0 and local.qryResults.itemCount is 1>
					<cfif local.strSearch.filter_casetype gt 0>
						<cfset local.tmpfilter = "#local.strSearch.filter_group#|#local.strSearch.filter_casetype#|#local.qryResults.doctypeid#">
					<cfelseif local.strSearch.filter_group gt 0>
						<cfset local.tmpfilter = "#local.strSearch.filter_group#|#local.qryResults.casetypeid#|0">
					<cfelse>
						<cfset local.tmpfilter = "#local.qryResults.groupid#|0|0">
					</cfif>
					<cfreturn getResults(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID,startRow=arguments.startRow,sortType=arguments.sortType,filter=local.tmpFilter,viewDirectory=arguments.viewDirectory)>
				</cfif>

				<!--- adjust maxperpage based on actual data if necessary and get page variables --->
				<cfscript>
				if (local.strSearch.filter_doctype gt 0) {
					local.documentsFoundForPaging = local.qryResults.itemcount;
					local.documentsFound = local.qryResults.itemcount;
				} else {
					local.documentsFoundForPaging = local.qryResults.recordcount;
					local.documentsFound = local.qryResults.docsInFolders;
				}
				local.MaxPerPage = iif(local.documentsFoundForPaging gt variables.thisBucketMaxPerPage,variables.thisBucketMaxPerPage,local.documentsFoundForPaging);
				if (local.MaxPerPage gt 0) {
					local.NumTotalPages = Ceiling(local.documentsFoundForPaging / variables.thisBucketMaxPerPage);
					local.NumCurrentPage = int((int(arguments.startRow) + variables.thisBucketMaxPerPage - 1) / variables.thisBucketMaxPerPage);
				} else {
					local.NumTotalPages = 0;
					local.NumCurrentPage = 0;
				}
				</cfscript>

				<cfset local.dropboxAppKey = "">
				<cfif structKeyExists(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode), "dropboxappkey") and len(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).dropboxappkey)>
					<cfset local.dropboxAppKey = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).dropboxappkey>
				</cfif>
			
				<!--- return content --->
				<cfsavecontent variable="local.stResults">
					<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/courtDocuments/results.cfm">
				</cfsavecontent>
			<cfelse>
				<cfset local.stResults = "">
			</cfif>

			<cfset local.stResults = ReReplace(local.stResults,'\s{2,}',' ','ALL')>		
			<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
			<cfif arguments.queryOnly eq 1>
				<cfset StructInsert(local.returnStruct,"resultqry",local.qryResults)>
			<cfelse>
				<cfset StructInsert(local.returnStruct,"numTotalPages",val(local.NumTotalPages))>
				<cfset StructInsert(local.returnStruct,"numCurrentPage",val(local.NumCurrentPage))>
			</cfif>
			<cfset StructInsert(local.returnStruct,"thisBucketCartItemTypeID",variables.thisBucketCartItemTypeID)>
			<cfset StructInsert(local.returnStruct,"itemcount",val(local.strCount.itemCount))>
			<cfset StructInsert(local.returnStruct,"success",true)>
	
			<cfreturn local.returnStruct>
		</cfif>
	</cffunction>

	<cffunction name="saveSearchForm" access="public" output="no" returntype="numeric" hint="saves the form vars to a search and returns the searchid">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="formvars" required="yes" type="struct">

		<cfset var local = structNew()>
		
		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfsavecontent variable="local.xmlSearch">
			<cfoutput>
			<search xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2">
				<bid><cfif StructKeyExists(arguments.formvars,"bid")>#val(arguments.formvars.bid)#</cfif></bid>
				<s_cause><cfif StructKeyExists(arguments.formvars,"s_cause")>#xmlFormat(trim(arguments.formvars.s_cause))#</cfif></s_cause>
				<s_docflags><cfif StructKeyExists(arguments.formvars,"s_docflags")>#xmlFormat(trim(arguments.formvars.s_docflags))#</cfif></s_docflags>
				<s_key_all><cfif StructKeyExists(arguments.formvars,"s_key_all")>#xmlFormat(trim(arguments.formvars.s_key_all))#</cfif></s_key_all>
				<s_key_one><cfif StructKeyExists(arguments.formvars,"s_key_one")>#xmlFormat(trim(arguments.formvars.s_key_one))#</cfif></s_key_one>
				<s_key_phrase><cfif StructKeyExists(arguments.formvars,"s_key_phrase")>#xmlFormat(trim(arguments.formvars.s_key_phrase))#</cfif></s_key_phrase>
				<s_key_x><cfif StructKeyExists(arguments.formvars,"s_key_x")>#xmlFormat(trim(arguments.formvars.s_key_x))#</cfif></s_key_x>
				<cfif StructKeyExists(arguments.formvars,"s_ownership") and len(arguments.formvars.s_ownership)>
					<s_ownership>#xmlFormat(arguments.formvars.s_ownership)#</s_ownership>
				<cfelse>
					<s_ownership xsi:nil="true"/>
				</cfif>
				<s_state><cfif StructKeyExists(arguments.formvars,"s_state")>#xmlFormat(trim(arguments.formvars.s_state))#</cfif></s_state>
				<s_type><cfif StructKeyExists(arguments.formvars,"s_type")>#xmlFormat(trim(arguments.formvars.s_type))#</cfif></s_type>
			</search>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.searchID = saveSearchXML(val(arguments.formvars.bid),local.xmlSearch)>
		
		<cfreturn local.searchID>
	</cffunction>

	<cffunction name="cleanGroups" access="private" returntype="query" output="no" hint="returns query of TS groups user is authorized to search">
		<cfargument name="chosenGroups" type="string" required="No" default="" hint="list of groupids to filter against. Should not be quoted">
		<cfargument name="includePublicGroups" type="boolean" required="no" default="false" hint="Determines if public groups that the user is not a member of should be included">
		<cfargument name="restrictCategoryID" type="numeric" required="no" default="0" hint="Used to limit to a specific categoryID. Ignored if lte 0.">
		
		<cfset var local = StructNew()>
		
		<cfif not variables.initRun>
			<cfset init()>
		</cfif>
		
		<cfset local.memberGroups = variables.cfcuser_TSgroups>

		<!--- remove "0" from chosengroups --->
		<cfset local.zerogroup = listfind(arguments.chosenGroups,0)>
		<cfif local.zerogroup>
			<cfset arguments.chosenGroups = listDeleteAt(arguments.chosenGroups,local.zerogroup)>
		</cfif>
		
		<cfif listlen(local.memberGroups) or arguments.includePublicGroups>
			<cfquery name="local.qryGroups" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				select groupid, description
				from dbo.depogroups
				where 1=1
				<cfif arguments.restrictCategoryID gt 0>
					and categoryid = #arguments.restrictCategoryID#
				</cfif>
				<cfif listlen(local.memberGroups) and includePublicGroups>
					and (
						groupid in (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#local.memberGroups#">)
						or
						(privateflag = 0 and categoryid = 1)
					)
				<cfelseif listlen(local.memberGroups)>
					and groupid in (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#local.memberGroups#">)
				<cfelse>
					and privateflag = 0 
					and categoryid = 1
				</cfif>
				<cfif listlen(arguments.chosenGroups)>
					and groupid in (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#arguments.chosenGroups#">)
				</cfif>
			</cfquery>
		<cfelse>
			<cfset local.qryGroups = QueryNew("groupid,description","Integer,Varchar")>
		</cfif>

		<cfreturn local.qryGroups>
	</cffunction>
	
	<cffunction name="getCaseTypesAndDocTypes" access="public" output="yes" returntype="struct">	
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="bankid" required="yes" type="numeric">
		<cfargument name="documentTypeId" required="no" type="numeric" default="0">
		<cfargument name="caseTypeId" required="no" type="numeric" default="0">
	
		<cfset var local = StructNew()>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>
		
		<cfset local.returnstruct = structNew()>
		<cfset local.returnstruct.qryCaseTypes = getCaseTypes(arguments.bankid,arguments.documentTypeID)>
		<cfset local.returnstruct.qryDocumentTypes = getDocumentTypes(arguments.bankid,arguments.caseTypeId)>
		<cfset local.returnstruct.documentTypeId = arguments.documentTypeId>
		<cfset local.returnstruct.caseTypeId = arguments.caseTypeId>

		<!--- get label names --->
		<cfquery name="local.returnstruct.qryLabels" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			select top 1 casetypelabel, doctypelabel
			from dbo.depoGroups
			where groupid = <cfqueryparam value="#arguments.bankid#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<!--- get bucket info to get any restrictions --->
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>
		<cfset local.strSettings = prepSettings(local.qryBucketInfo.bucketSettings)>

		<cfif variables.cfcuser_isLoggedIn and StructKeyExists(local.strSettings,"search") and local.strSettings.search.showcategorycounts>
			<cfset local.returnstruct.showcategorycounts = true>
		<cfelseif not variables.cfcuser_isLoggedIn and StructKeyExists(local.strSettings,"search") and local.strSettings.search.showcategorycounts and StructKeyExists(local.strSettings,"searchoverrideguest") and local.strSettings.searchoverrideguest.showcategorycounts>
			<cfset local.returnstruct.showcategorycounts = true>
		<cfelse>
			<cfset local.returnstruct.showcategorycounts = false>
		</cfif>

		<cfset local.returnstruct.success = true>

		<cfreturn local.returnstruct>
	</cffunction>
	
	<cffunction name="getCaseTypes" access="private" output="yes" returntype="query">	
		<cfargument name="bankid" required="yes" type="numeric">
		<cfargument name="documentTypeId" required="no" type="numeric" default="0">
		<cfargument name="cachedWithin" type="any" required="no" default="#CreateTimeSpan(0,1,0,0)#">
	
		<cfset var local = StructNew()>

		<cfif arguments.bankid is 0>
			<cfset local.qryGroups = cleanGroups('',true,1)>
			<cfset local.grplist = valuelist(local.qryGroups.groupid)>
		<cfelse>
			<cfset local.grplist = arguments.bankid>
		</cfif>

		<cfquery name="local.qryCaseTypes" datasource="#application.dsn.tlasites_trialsmith.dsn#" cachedwithin="#arguments.cachedWithin#">
			set nocount on
			
			declare @documenttypeid int = #arguments.documentTypeId#
			declare @approvedStatusID int 
			select @approvedStatusID=statusID from trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

			-- create and populate temp table
			DECLARE @tmpAllowedDocumentTypes TABLE (autoid int IDENTITY(1,1), typeid int)
			
			insert into @tmpAllowedDocumentTypes (typeid)
			select distinct typeid
			from depogroups g
			inner join depodocumenttypes t on t.categoryid = g.categoryid
				and g.groupid in (#local.grplist#)
				and t.dcourtdoc = 1
			union
			select distinct typeid 
			from dbo.depoGroupDocumentTypeOverrides
			where groupid in (#local.grplist#)
			
			-- main query
			Select d.caseTypeid, ct.description, count(d.caseTypeId) as DocCount
			from dbo.depoCaseTypes as ct 
			inner join dbo.depoDocuments as d
				on d.caseTypeId = ct.caseTypeID
				and d.groupid in (#local.grplist#)
				<cfif arguments.documentTypeId gt 0>
					and d.documentTypeId = @documenttypeid
				</cfif>
			inner join dbo.depoDocumentStatusHistory as dsh on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
				and dsh.statusID = @approvedStatusID
			inner join @tmpAllowedDocumentTypes as at on d.documenttypeid = at.typeID
			group by d.caseTypeId, ct.description
			order by ct.description
			
			set nocount off
		</cfquery>

		<cfreturn local.qryCaseTypes>
	</cffunction>

	<cffunction name="getDocumentTypes" access="private" output="yes" returntype="query">	
		<cfargument name="bankid" required="yes" type="numeric">
		<cfargument name="caseTypeId" required="no" type="numeric" default="0">
		<cfargument name="cachedWithin" type="any" required="no" default="#CreateTimeSpan(0,1,0,0)#">
	
		<cfset var local = StructNew()>

		<cfif arguments.bankid is 0>
			<cfset local.qryGroups = cleanGroups('',true,1)>
			<cfset local.grplist = valuelist(local.qryGroups.groupid)>
		<cfelse>
			<cfset local.grplist = arguments.bankid>
		</cfif>
		
		<cfquery name="local.qryDocTypes" datasource="#application.dsn.tlasites_trialsmith.dsn#" cachedwithin="#arguments.cachedWithin#">
			set nocount on
			
			declare @caseTypeId int = #arguments.caseTypeId#

			-- create and populate temp table
			DECLARE @tmpAllowedDocumentTypes TABLE (autoid int IDENTITY(1,1), typeid int)

			declare @approvedStatusID int 
			select @approvedStatusID=statusID from trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

			insert into @tmpAllowedDocumentTypes (typeid)
			select distinct typeid
			from depogroups g
			inner join depodocumenttypes t on t.categoryid = g.categoryid
				and g.groupid in (#local.grplist#)
				and t.dcourtdoc = 1
			union
			select distinct typeid 
			from dbo.depoGroupDocumentTypeOverrides
			where groupid in (#local.grplist#)
			
			-- main query
			Select t.TypeId as documenttypeid, t.description, count(*) as DocCount
			from depodocumenttypes t
			inner join depoDocuments d on t.typeid = d.documenttypeid
				and d.groupid in (#local.grplist#)
				<cfif arguments.casetypeid gt 0>
					and d.casetypeid = @caseTypeId
				</cfif>
			inner join dbo.depoDocumentStatusHistory as dsh on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
				and dsh.statusID = @approvedStatusID
			inner join @tmpAllowedDocumentTypes at on t.TypeId = at.TypeId 
			group by t.TypeId, t.description
			order by t.description
			
			set nocount off
		</cfquery>

		<cfreturn local.qryDocTypes>
	</cffunction>

	<cffunction name="getDocumentBankFlags" access="private" output="yes" returntype="query">	
		<cfargument name="bankid" required="yes" type="numeric">
		<cfargument name="docflagidfilterlist" required="no" type="string" default="">

		<cfset var local = StructNew()>

		<cfquery name="local.getDocFlags" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			select docflagid, name 
			from dbo.docflags
			where groupid = <cfqueryparam value="#arguments.bankid#" cfsqltype="CF_SQL_INTEGER">
			<cfif len(trim(arguments.docflagidfilterlist))>
				and docflagid in (<cfqueryparam value="#arguments.docflagidfilterlist#" list="yes" cfsqltype="CF_SQL_INTEGER">)
			</cfif>
		</cfquery>

		<cfreturn local.getDocFlags>
	</cffunction>

	<cffunction name="getDefaultBanks" access="private" output="yes" returntype="query">	
		<cfargument name="orgcode" required="yes" type="string">
		<cfargument name="cachedWithin" type="any" required="no" default="#CreateTimeSpan(0,1,0,0)#">

		<cfset var local = StructNew()>

		<cfquery name="local.getbanks" datasource="#application.dsn.tlasites_trialsmith.dsn#" cachedwithin="#arguments.cachedWithin#">
			select groupid, description
			from dbo.depogroups
			where orgcode = '#arguments.orgcode#'
			and defaultbank = 1
		</cfquery>

		<cfreturn local.getbanks>
	</cffunction>

	<cffunction name="showNotAllowed" access="private" output="false" returntype="struct">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="bucketName" required="yes" type="string">
		<cfargument name="accessDeniedMessage" required="yes" type="string">
		<cfargument name="includeBucketCount" required="no" type="boolean" default="true">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>

		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/courtDocuments/notAllowed.cfm">
		</cfsavecontent>

		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfif arguments.includeBucketCount>
			<cfset local.strCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
			<cfset StructInsert(local.returnStruct,"itemcount",local.strCount.itemCount)>
		<cfelse>
			<cfset StructInsert(local.returnStruct,"itemcount",-1)>
		</cfif>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getBucketSettingsHelpText" access="public" output="false" returntype="struct">
		
		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>

		<cfsavecontent variable="local.returnStruct.helpText">
			<cfoutput>
				<div class="alert align-items-center pl-2 align-content-center alert-primary show mb-2 alertSection" role="alert">
					<ul>
						<li>usesiteorgcode = true or false</li>
						<li>additionalorgcodes = true or false</li>
						<li>override = true or false -- if true then override restriction, DEFAULT True</li>
						<li>action = include or exclude </li>
						<li>allownationwide = true or false, DEFAULT false</li>
						<li>showcategorycounts = true or false, DEFAULT false</li>
						<li>includedefaultbanks = true or false, DEFAULT false</li>
						<li>bypasschecktrialsmithallowed = true or false, DEFAULT false</li>
						<li>bypasschecktrialsmithdisabled = true or false, DEFAULT false</li>
						<li>bypasschecktrialsmithpending = true or false, DEFAULT false</li>
						<li>bypasschecktrialsmithexpired = true or false, DEFAULT false</li>
						<li>bypasschecktrialsmithnoplan = true or false, DEFAULT false</li>
						<li>bankids -- list of bank ids /li>
					</ul>
					<p class="ml-3 mb-0">Notes:</p>
					<ul>
						<li>override parameter not currently supported</li>

						<li>if no action defined and there is no default so cancel restriction</li>
					</ul>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnStruct>
	</cffunction>
</cfcomponent>