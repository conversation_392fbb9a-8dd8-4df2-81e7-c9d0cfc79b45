<cfcomponent extends="model.admin.admin" output="no">
	<cfscript>
		defaultEvent 														= 'controller';
		variables.appResourceType 							= 'FileShare';
		variables.fileShareSettings 						= structNew();
		//variables.applicationReservedURLParams 	= 'fsAction,fsSectionID,fsDocumentID,lh,un';
	</cfscript>
  	
	<cffunction name="init" access="private" returntype="void" output="false">
		<cfset variables.fileShareSettings = getFileShareSettings(this.appInstanceID)/>
	</cffunction>
		
	<cffunction name="controller" access="public" output="true" returntype="struct" hint="controller for this app">
    <cfargument name="Event" type="any">
    <cfscript>
			var local = structNew();
			// Load Objects for the page ---------------------------------------------------------------- ::
	
			this.objFSAdmin		= CreateObject("component","model.admin.fileShare.fileShare");
			// init variables
			if( ListFindNoCase('home,message',arguments.event.getValue('mca_ta')) ){ }
			else{
				this.fsData 				= this.objFSAdmin.getFSData(arguments.event);
				this.appInstanceID 	= this.fsData.applicationInstanceID;
				this.siteResourceID = this.fsData.siteResourceID;
				init();
				buildAppRightsStruct(memberid=session.cfcuser.memberdata.memberid,siteid=arguments.event.getValue('mc_siteInfo.siteid'));
			}
			// Build Quick Links for SectionAdmin ------------------------------------------------------- ::
			this.link.home				= buildCurrentLink(arguments.event,"home");
			this.link.edit				= buildCurrentLink(arguments.event,"editFS");
			this.link.editFile		= buildCurrentLink(arguments.event,"editFile");
			this.link.editVersion	= buildCurrentLink(arguments.event,"editVersion");
			this.link.saveFSMeta	= buildCurrentLink(arguments.event,"saveFSMeta");
			this.link.editCategory	= buildCurrentLink(arguments.event,"editCategory") & "&mode=direct";
			this.link.addCategory	= buildCurrentLink(arguments.event,"addCategory") & "&mode=direct";
			this.link.editCategoryTreeName	= buildCurrentLink(arguments.event,"editCategoryTreeName") & "&mode=direct";
			this.link.addCategoryTreeName	= buildCurrentLink(arguments.event,"addCategoryTreeName") & "&mode=direct";
			
			this.link.newSection	= buildCurrentLink(arguments.event,"newSection");
			this.link.editSection	= buildCurrentLink(arguments.event,"editSection");
			this.link.saveSection	= buildCurrentLink(arguments.event,"saveSection");
			this.link.versions		= buildCurrentLink(arguments.event,"versions");
			this.link.message  		= buildCurrentLink(arguments.event,"message");
			this.link.documentDetails	= buildCurrentLink(arguments.event,"documentDetails");
			this.link.editDocMetaData	= buildCurrentLink(arguments.event,"editDocMetaData");
			this.link.saveDocMetaData	= buildCurrentLink(arguments.event,"saveDocMetaData");
			this.link.restoreVersion	= buildCurrentLink(arguments.event,"restoreVersion");
			this.link.pageMembersReport = buildLinkToTool(toolType='SiteStatsAdmin',mca_ta='pageMembersReport');
			</cfscript>
 			
			<cfscript>
			// Run Assigned Method ---------------------------------------------------------------------- ::			
			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
	
	<cffunction name="home" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.fileShareLink 	= "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=fileShareJSON&meth=getFileShare&mode=stream";
			local.appTypeID = application.objApplications.getApplicationTypeIDFromName('FileShare2');
			local.addpageLink = CreateObject("component","model.admin.admin").buildLinkToTool(toolType='PageAdmin',mca_ta='addPage');
			local.canAddInstance = CreateObject("component","model.admin.pages.appCreationProcess").canAddAppInstance(siteID=arguments.event.getValue('mc_siteInfo.siteID'), applicationTypeID=local.appTypeID);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude  template="dsp_fileShare.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
		
	<cffunction name="editFS" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.paramValue('fsID',0);		

			// PAGE SECURITY ---------------------------------------------------------------------------- ::
			if( (appRightsStruct.fsEditAnyMetaData + appRightsStruct.fsEditOwnMetaData) LT 1 ){
				application.objCommon.redirect('#this.link.message#&message=1');
			}	

			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='', text=encodeForHTML(this.fsData.fileShareName) });

			local.statType = "page";
			local.statsrange = arguments.event.getValue("statsrange",'7');
			local.statsmonth = arguments.event.getValue("statsmonth",'0');
			local.statsyear = arguments.event.getValue("statsyear",'0');
			local.currentTab = arguments.event.getValue("tab",'');

			local.selStatsRange = arguments.event.getValue("statsrange",'blnk');
			local.selStatsMonth = arguments.event.getValue("statsmonth",0);
			local.selStatsYear = arguments.event.getValue("statsyear",0);
			local.statsrangeStartYear = year(now()) -5;
			local.statsrangeEndYear = year(now());

			if (isnumeric(local.statsrange))
			{
				local.startdate = dateadd("d",-local.statsrange,now());
				local.enddate = now();
			}
			else
			{
				local.startdate = createdate(statsyear, statsmonth, 1);
				local.enddate = createdate(statsyear, statsmonth, DaysInMonth(local.startdate));
			}

			local.baseTestLink = getAppBaseLink(applicationInstanceID=this.fsData.applicationInstanceID);
			local.memSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list');
			local.massAddCategoryLink = buildCurrentLink(arguments.event,"massAddCategory") & "&fsID=#arguments.event.getValue('fsID')#&mode=direct";
		</cfscript>

		<cfif variables.fileShareSettings.isFileShare2 eq 1>
			<!--- links for category XMLs --->
			<cfset local.categoryTreeListXML = "/?pg=admin&mca_ajaxlib=dhtmlGrid&com=fileShareXML&meth=getCategoryTrees&mode=stream&srID=#this.siteResourceID#">
			<cfset local.categoryListXML = "/?pg=admin&mca_ajaxlib=dhtmlGrid&com=fileShareXML&meth=getCategories&mode=stream&fsID=#arguments.event.getValue('fsID')#">

			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
				<cfquery name="local.qryDocTypes" datasource="#application.dsn.tlasites_trialsmith.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select TypeID, Description
					from dbo.depoDocumentTypes
					where updateDisplay = 1 
					and allowEndUserUploads=1
					and categoryid = 1
					order by Description;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfset local.importTSDocsToFileShare2Link	= buildCurrentLink(arguments.event,"importTSDocsToFileShare2") & "&fsID=#arguments.event.getValue('fsID')#">
			</cfif>
		</cfif>

		<!--- get page name --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPageInfo">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select pageName 
			from dbo.fn_cms_getApplicationInstancePagePath(<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteid')#">,<cfqueryparam cfsqltype="cf_sql_integer" value="#this.fsData.applicationInstanceID#">);

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfset local.pg = local.qryPageInfo.pageName>

		<cfif  local.currentTab eq 'StatsTab'>
			<cfstoredproc procedure="stats_docDownloads" datasource="#application.dsn.platformstatsMC.dsn#" result="local.tmpResult">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#" null="No">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#this.fsData.applicationInstanceID#" null="No">
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#local.startdate#" null="No">
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#local.enddate#" null="No">
				<cfprocresult name="local.qryCountsInDateRange" resultset="1">
				<cfprocresult name="local.qryTop10InDateRange" resultset="2">
				<cfprocresult name="local.qryTop10AllTime" resultset="3">
			</cfstoredproc>

			<!--- setup start, end dates --->
			<cfif ((isnumeric(local.statsrange)) AND (local.statsrange gt 0)) OR (local.statsrange eq 'cstm')>

				<cfstoredproc procedure="up_pageStats" datasource="#application.dsn.platformstatsMC.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mc_siteInfo.siteid')#" null="No">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mc_siteInfo.orgid')#" null="No">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.pg#" null="No">
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#local.startdate#" null="No">
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#local.enddate#" null="No">
					<cfprocresult name="local.qryStats" resultset="1">
					<cfprocresult name="local.qryGroups" resultset="2">
				</cfstoredproc>
		
				<cfif local.statType eq "page">
					<cfquery name="local.qryGuestGroup" dbtype="query">
						select count(*) as GGCount 
						from [local].qryGroups 
						where groupName = 'Guest'
					</cfquery>
					<cfif local.qryGuestGroup.GGCount gt 0>
						<cfset local.publicAccess = "Yes">
					<cfelse>
						<cfset local.publicAccess = "No">
					</cfif>
				</cfif>
			</cfif>				
		</cfif>				

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="dsp_fileShareForm.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="saveFSMeta" access="public" output="false" returntype="struct" hint="Save demographic info">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>		

		<!--- check to see if any custom fields have been selected --->
		<cfset local.showCustomFields = '' />
		<cfloop list="#arguments.event.getValue('fieldNames')#" index="local.current">
			<cfif listFirst(local.current,"_") EQ 'CUSTOM' AND evaluate(local.current) EQ 1>
				<cfset local.showCustomFields = listAppend(local.showCustomFields,listLast(local.current,"_")) />
			</cfif>
		</cfloop>
		<cfset arguments.event.setValue('showCustomFields',local.showCustomFields) />

		<cfscript>
			local.objAdminFS = CreateObject("component","model.admin.fileShare.fileShare");
			local.saveSuccess 			= true;
			// PAGE SECURITY ---------------------------------------------------------------------------- ::
			if( (appRightsStruct.fsEditAnyMetaData + appRightsStruct.fsEditOwnMetaData) LT 1 ){
				application.objCommon.redirect('#this.link.message#&message=1');
			}	
			if( arguments.event.getValue('newapplicationInstanceName') NEQ arguments.event.getValue('oldapplicationInstanceName') ){
				local.save = local.objAdminFS.saveMeta(arguments.event);
			}
			
			local.saveDisplayOptions = local.objAdminFS.saveFSDisplayOptions(arguments.event);
			
			application.objCommon.redirect("#this.link.edit#&fsID=#arguments.event.getValue('fsID')#&msg=1");
			
		</cfscript>			
		<cfsavecontent variable="local.data">
			<cfoutput>
			
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
		
	<cffunction name="editSection" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();
			arguments.event.paramValue('fsID',0);
			arguments.event.paramValue('sID',0);
			
			// PAGE SECURITY ---------------------------------------------------------------------------- ::
			if( (appRightsStruct.fsEditAnyMetaData + appRightsStruct.fsEditOwnMetaData) LT 1 ){
				application.objCommon.redirect('#this.link.message#&message=1');
			}	
			
			local.sectionData = this.objFSAdmin.getSectionData(arguments.event.getValue('sID'),arguments.event.getValue('mc_siteInfo.siteID'));
			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='#this.link.edit#&fsID=#local.rc.fsID#&tab=SectDocTab', text=encodeForHTML(this.fsData.fileShareName) });
			appendBreadCrumbs(arguments.event,{ link='', text="Edit Section" });
						
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				
				<cfinclude template="frm_section.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="saveSection" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			// PAGE SECURITY ---------------------------------------------------------------------------- ::
			if( (appRightsStruct.fsEditAnyMetaData + appRightsStruct.fsEditOwnMetaData) LT 1 ){
				application.objCommon.redirect('#this.link.message#&message=1');
			}	
			
			this.objFSAdmin.updateSection(
				arguments.event.getValue('mc_siteInfo.siteID'),
				arguments.event.getValue('sID'),
				arguments.event.getValue('newsectionName'));
			
			
			application.objCommon.redirect("#this.link.editSection#&fsID=#arguments.event.getValue('fsID')#&sID=#arguments.event.getValue('sID')#&msg=1");
			
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="newSection" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			// PAGE SECURITY ---------------------------------------------------------------------------- ::
			if( (appRightsStruct.fsEditAnyMetaData + appRightsStruct.fsEditOwnMetaData) LT 1 ){
				application.objCommon.redirect('#this.link.message#&message=1');
			}	
			
			local.sectionID = addSection(arguments.event.getTrimValue('newSectionName'),arguments.event.getValue('fsParentSectionID'),arguments.event.getValue('mc_siteInfo.siteID'));
			
			
			application.objCommon.redirect("#this.link.edit#&fsID=#arguments.event.getValue('fsID')#&tab=SectDocTab&msg=1");
			
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	
	<cffunction name="editFile" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.paramValue('fsAction','add');
			local.rc = arguments.event.getCollection();
			arguments.event.paramValue('fsID',0);
			arguments.event.paramValue('dID',0);
			// PAGE SECURITY ---------------------------------------------------------------------------- ::
			if( (appRightsStruct.fsEditAnyMetaData + appRightsStruct.fsEditOwnMetaData) LT 1 ){
				application.objCommon.redirect('#this.link.message#&message=1');
			}	
			if( arguments.event.getValue('fsAction','add') EQ "add" ){ local.data = actionAddDocument(arguments.event); }
			else{ local.data = actionEditDocument(arguments.event); }
			dataStruct = local.data;
			fileShareSettings = variables.fileShareSettings;
			appRightsStruct = variables.appRightsStruct;
			local.objDocument 												= CreateObject("component","model.system.platform.document");
			local.data.getDocLanguages	= local.objDocument.getDocLanguages(arguments.event.getValue('dID'));

			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='#this.link.edit#&fsID=#local.rc.fsID#&tab=SectDocTab', text=encodeForHTML(this.fsData.fileShareName) });
			if( arguments.event.getValue('dID') EQ 0 ) appendBreadCrumbs(arguments.event,{ link='', text="Add File" });
			else appendBreadCrumbs(arguments.event,{ link='', text="Edit File" });
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_file.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="versions" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			arguments.event.paramValue('fsAction','add');
			local.rc = arguments.event.getCollection();
			arguments.event.paramValue('fsID',0);
			arguments.event.paramValue('dID',0);
			//  SECURITY ---------------------------------------------------------------------------- ::
			if( (appRightsStruct.fsEditAnyMetaData + appRightsStruct.fsEditOwnMetaData) LT 1 ){
				application.objCommon.redirect('#this.link.message#&message=1');
			}	
			local.data = actionEditDocument(arguments.event);
			dataStruct = local.data;
			fileShareSettings = variables.fileShareSettings;
			appRightsStruct = variables.appRightsStruct;
			rc = local.rc;
			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='#this.link.edit#&fsID=#local.rc.fsID#&tab=SectDocTab', text=encodeForHTML(this.fsData.fileShareName) });
			appendBreadCrumbs(arguments.event,{ link='#this.link.editFile#&fsAction=edit&dID=#arguments.event.getValue('dID')#&fsID=#arguments.event.getValue('fsID')#', text="Edit File" });
			appendBreadCrumbs(arguments.event,{ link='', text="Versions" });
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_versions.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="editDocMetaData" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			arguments.event.paramValue('fsID',0);		
			// PAGE SECURITY ---------------------------------------------------------------------------- ::
			if( (appRightsStruct.fsEditAnyMetaData + appRightsStruct.fsEditOwnMetaData) LT 1 ){
				application.objCommon.redirect('#this.link.message#&message=1');
			}	
			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='#this.link.edit#&fsID=#url.fsID#&tab=SectDocTab', text=encodeForHTML(this.fsData.fileShareName) });
			if(arguments.event.getValue('dID') EQ 0) appendBreadCrumbs(arguments.event,{ link='', text="Add File" });
			else { 
				appendBreadCrumbs(arguments.event,{ link='#this.link.editFile#&fsAction=edit&dID=#arguments.event.getValue("dID")#&fsID=#arguments.event.getValue("fsID")#', text="Edit File" });
				appendBreadCrumbs(arguments.event,{ link='', text="Edit Meta Data" });
			}
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_editMetaData.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="saveDocMetaData" access="public" output="false" returntype="struct" hint="Save document meta info">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
		
		<cfif arguments.event.getValue('dlID',0) eq 0>
			<cfset local.newFile = local.objDocument.uploadFile("form.newFile")>
			<cfset local.objDocument.forceFileExtentionIfBlank(local.newFile)>
			
			<!--- insert new document language --->
			<cfquery datasource="#application.dsn.membercentral.dsn#">
				INSERT INTO cms_documentLanguages (documentID, languageID, docTitle, docDesc, dateCreated, dateModified)
				VALUES	(<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('dID')#">,<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('lID')#">,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('docTitle')#">,<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('docDesc')#">,getDate(),getDate())
			</cfquery>
			<!--- upload new document and save it to documentVersion table --->
					<cfset local.loadDocument = local.objDocument.updateDocument(
													documentID=arguments.event.getValue('dID'),
													siteID=arguments.event.getValue('mc_siteInfo.siteID'),
													author='',
													docTitle=arguments.event.getValue('docTitle'),
													docDesc=arguments.event.getValue('docDesc'),
													fileData=local.newFile,
													sectionID=arguments.event.getValue('sectionID'),
													contributorMemberID=application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=event.getvalue('mc_pageDefinition.orgID')),
													recordedByMemberID=application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=event.getvalue('mc_pageDefinition.orgID')),
													newFileUploaded=1,
													languageID=arguments.event.getValue('lID'))>
		<cfelse><!--- otherwise update current documentLanguage record --->
			<cfquery datasource="#application.dsn.membercentral.dsn#">
				UPDATE dbo.cms_documentLanguages
				SET	docTitle = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('docTitle')#">,
					docDesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('docDesc')#">,
					dateCreated = <cfqueryparam cfsqltype="cf_sql_date" value="#now()#">,
					dateModified = <cfqueryparam cfsqltype="cf_sql_date" value="#now()#">
				WHERE documentLanguageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('dlID')#">
			</cfquery>
		</cfif>
		
		<cfscript>
			application.objCommon.redirect("#this.link.editDocMetaData#&dID=#arguments.event.getValue('dID')#&fsID=#arguments.event.getValue('fsID')#");
		</cfscript>	 	
	</cffunction>
	
	<cffunction name="restoreVersion" access="public" output="false" returntype="struct" hint="Activate document">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>		
				
		<cfquery datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
        	BEGIN TRY
				BEGIN TRAN;
					UPDATE dbo.cms_documentVersions
					SET isActive = 0
					WHERE documentLanguageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('dlID')#">
			
					UPDATE dbo.cms_documentVersions
					SET isActive = 1
					WHERE documentVersionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('dvID')#">
				COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cflocation url="#this.link.editDocMetaData#&dID=#arguments.event.getValue('dID')#&fsID=#arguments.event.getValue('fsID')#" addtoken="false">
	</cffunction>
		
	<cffunction name="actionEditDocument" access="private" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="yes"/>
		
		<cfset var local = structNew()>
		<cfset local.rc = arguments.event.getCollection()>
		<cfset local.sectionToShow = arguments.event.getValue('fsSectionID',variables.fileShareSettings.rootSectionID)>
		<cfset local.actionReturnStruct = structNew()>
			
		<cfif listFind(valuelist(variables.fileShareSettings.qrySections.sectionID),local.sectionToShow)>
			<cfset local.actionReturnStruct.sectionToShow = local.sectionToShow>
			
			<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
			<cfset local.actionReturnStruct.getDocData = local.objDocument.getAdminDocumentData(arguments.event.getValue('dID'),arguments.event.getValue('VID',0))>
			<cfset local.actionReturnStruct.getDocLanguages = local.objDocument.getDocLanguages(arguments.event.getValue('dID'))>
			
			<cfif variables.appRightsStruct.fsDeleteAny or (variables.appRightsStruct.fsDeleteOwn and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (local.actionReturnStruct.getDocData.memberid eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=event.getvalue('mc_pageDefinition.orgID'))))>
				<cfset local.canDelete = true>
			<cfelse>
				<cfset local.canDelete = false>
			</cfif>
			
			<cfif variables.appRightsStruct.fsReuploadAny or (variables.appRightsStruct.fsReuploadOwn and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (local.actionReturnStruct.getDocData.memberid eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=event.getvalue('mc_pageDefinition.orgID'))))>
				<cfset local.canReupload = true>
			<cfelse>
				<cfset local.canReupload = false>
			</cfif>
			
			<cfif variables.appRightsStruct.fsEditAnyMetadata or (variables.appRightsStruct.fsEditOwnMetadata and application.objUser.isLoggedIn(cfcuser=session.cfcuser) and (local.actionReturnStruct.getDocData.memberid eq application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=event.getvalue('mc_pageDefinition.orgID'))))>
				<cfset local.canEditMetaData = true>
			<cfelse>
				<cfset local.canEditMetaData = false>
			</cfif>

			<cfif isdefined("local.rc.fsDocumentSaved") and local.rc.dID neq 0>
				<cfscript>
				local.DocID = arguments.event.getValue("dID");
				local.newContributorMemberID = 0;
				local.fileUploaded = FALSE;
				local.i=1;

				while (local.i LTE listLen(arguments.event.getValue('languageID'))) {
					if((local.canReupload) and (arguments.event.getTrimValue('newFile#local.i#') NEQ "") ){
						arguments.event.setValue('fileToUpload','newFile');
						local.fileUploaded = TRUE;
						try {
							local.newFile = local.objDocument.uploadFile("form.newFile#local.i#");
						} 
						catch(any excpt) {
							local.fileUploaded = FALSE;
						}					

						if( local.fileUploaded ){
							local.objDocument.forceFileExtentionIfBlank(local.newFile);
							local.strArgs = {	
								documentID=arguments.event.getValue('dID'),
								siteID=arguments.event.getValue('mc_siteInfo.siteID'),
								author=arguments.event.getValue('fsAuthor#local.i#'),
								docTitle=arguments.event.getValue('fsdocTitle#local.i#'),
								docDesc=arguments.event.getValue('fsdocDesc#local.i#'),
								fileData=local.newFile,
								sectionID=arguments.event.getValue('fsSectionID'),
								contributorMemberID=application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=event.getvalue('mc_pageDefinition.orgID')),
								recordedByMemberID=application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=event.getvalue('mc_pageDefinition.orgID')),
								newFileUploaded=1,
								languageID=local.i,
								oldFileExt=local.actionReturnStruct.getDocData.fileExt 
							};

							if (len(arguments.event.getTrimValue('fsPublicationDate#local.i#',''))) {
								local.strArgs.publicationDate =arguments.event.getValue('fsPublicationDate#local.i#');
							}
									
							local.loadDocument = local.objDocument.updateDocument(argumentCollection=local.strArgs);

							arguments.event.setValue('fsFileName',local.newFile.clientFile);
							arguments.event.setValue('fsfileExt',local.newFile.clientFileExt);
							local.newContributorMemberID = application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=event.getvalue('mc_pageDefinition.orgID'));
						} else {
							//application.objCommon.redirect('#this.link.message#&message=3');
						}
					}

					if (local.canEditMetaData) {
						if (variables.appRightsStruct.fsAddSubFolder and len(arguments.event.getTrimValue('newSectionName'))) {
							local.sectionID = addSection(arguments.event.getTrimValue('newSectionName'),arguments.event.getValue('fsSectionID'),arguments.event.getValue('mc_siteInfo.siteID'));
							arguments.event.setValue('fsSectionID',local.sectionID);
						}
						if( !local.fileUploaded ){
							local.fileData = { clientFile=arguments.event.getValue('fsfileName'), serverFileExt=arguments.event.getValue('fsfileExt') };
							local.strArgs = {	
								documentID=arguments.event.getValue('dID'),
								siteID=arguments.event.getValue('mc_siteInfo.siteID'),
								author=arguments.event.getValue('fsAuthor#local.i#'),
								docTitle=arguments.event.getValue('fsdocTitle#local.i#'),
								docDesc=arguments.event.getValue('fsdocDesc#local.i#'),
								fileData=local.fileData,
								sectionID=arguments.event.getValue('fsSectionID'),
								contributorMemberID=application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=event.getvalue('mc_pageDefinition.orgID')),
								recordedByMemberID=application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=event.getvalue('mc_pageDefinition.orgID')),
								newFileUploaded=0,
								languageID=local.i 
							};
							if (len(arguments.event.getTrimValue('fsPublicationDate#local.i#',''))) {
								local.strArgs.publicationDate =arguments.event.getValue('fsPublicationDate#local.i#');
							}
							local.loadDocument = local.objDocument.updateDocument(argumentCollection=local.strArgs);
						}
					}
					
					local.i=local.i+1;
				}

				application.objCommon.redirect("#this.link.editFile#&fsAction=#arguments.event.getValue('fsAction')#&fsID=#arguments.event.getValue('fsID')#&dID=#arguments.event.getValue('dID')#&msg=2");
				</cfscript>
			</cfif>
		<cfelse>
			<cfset local.actionReturnStruct.view = "fileshare/notfound">
		</cfif>

		<cfreturn local.actionReturnStruct>
	</cffunction>
	
	<cffunction name="actionAddDocument" access="private" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="yes"/>
		<cfset var local 									= structNew()>
		<cfset local.rc 									= arguments.event.getCollection()>
		<cfset local.sectionToShow 				= arguments.event.getValue('fsSectionID',variables.fileShareSettings.rootSectionID)/>
		<cfset local.actionReturnStruct 	= structNew()>
		<cfparam name="local.rc.languageID" default="#session.mcstruct.languageID#">
		
		<cfset local.i = local.rc.languageID>
		
		<cfif listFind(valuelist(variables.fileShareSettings.qrySections.sectioniD),local.sectionToShow)>
			<cfset local.actionReturnStruct.sectionToShow = local.sectionToShow/>
			<cfset local.actionReturnStruct.getDocData = structNew()>
			<cfset local.actionReturnStruct.getDocData.documentID = 0>
			<cfset local.actionReturnStruct.getDocData.sectionID = local.actionReturnStruct.sectionToShow>
			<cfset local.actionReturnStruct.getDocData.docTitle = "">
			<cfset local.actionReturnStruct.getDocData.docDesc = "">
			<cfset local.actionReturnStruct.getDocData.fileName = "">
			<cfset local.actionReturnStruct.getDocData.dateCreated = "">
			<cfset local.actionReturnStruct.getDocData.dateModified = "">
			<cfset local.actionReturnStruct.getDocData.fileExt = "">
			<cfif variables.appRightsStruct.fsAddDocuments and isdefined("local.rc.fsDocumentSaved")>
				<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
				<cfscript>
					// check to see if there is a new file to upload --------------------------------------------
					if(arguments.event.getTrimValue('newFile#local.i#') NEQ "" ){
						// if yes then set fileToUpload to the form variable newFile ------------------------------
						arguments.event.setValue('fileToUpload','newFile#local.i#');
						// pre set the fileUploaded variable to TRUE ----------------------------------------------
						local.fileUploaded = TRUE;
						// try to upload the file to the proper destination ---------------------------------------
						try {
							local.newFile = local.objDocument.uploadFile("form.newFile#local.i#");
							if (local.newFile.uploadComplete){
								local.objDocument.forceFileExtentionIfBlank(local.newFile);
								arguments.event.setValue('fsFileName',local.newFile.clientFile);
								arguments.event.setValue('fsfileExt',local.newFile.clientFileExt);
							}
							else{ local.fileUploaded = FALSE; }
						}
						catch(any excpt) {
							// if if fails to upload the set the fileUploaded flag to FALSE -------------------------
							local.fileUploaded = FALSE;
						}
					}
					if (variables.appRightsStruct.fsAddSubFolder and len(arguments.event.getTrimValue('newSectionName'))) {
						local.sectionID = addSection(arguments.event.getTrimValue('newSectionName'),arguments.event.getValue('fsSectionID'),arguments.event.getValue('mc_siteInfo.siteID'));
						arguments.event.setValue('fsSectionID',local.sectionID);
					}
					if( local.fileUploaded ){
						if (len(arguments.event.getTrimValue('fsPublicationDate#local.i#'))) {
							local.insertDoc = local.objDocument.insertDocument(
								siteID=arguments.event.getValue('mc_siteInfo.siteID'),
								resourceType='ApplicationCreatedDocument',
								parentSiteResourceID=variables.fileShareSettings.siteResourceID,
								sectionID=arguments.event.getValue('fsSectionID'),
								docTitle=arguments.event.getValue('fsdocTitle#local.i#'),
								docDesc=arguments.event.getValue('fsdocDesc#local.i#'),
								author=arguments.event.getValue('fsAuthor#local.i#'),
								fileData=local.newFile,
								isVisible=1,
								contributorMemberID=session.cfcuser.memberdata.memberID,
								recordedByMemberID=session.cfcuser.memberdata.memberID,
								publicationDate=arguments.event.getValue('fsPublicationDate#local.i#'));
						}
						else {
							local.insertDoc = local.objDocument.insertDocument(
								siteID=arguments.event.getValue('mc_siteInfo.siteID'),
								sectionID=arguments.event.getValue('fsSectionID'),
								docTitle=arguments.event.getValue('fsdocTitle#local.i#'),
								docDesc=arguments.event.getValue('fsdocDesc#local.i#'),
								author=arguments.event.getValue('fsAuthor#local.i#'),
								fileData=local.newFile,
								isVisible=1,
								contributorMemberID=session.cfcuser.memberdata.memberID,
								recordedByMemberID=session.cfcuser.memberdata.memberID);
						}
						
						arguments.event.setValue('dID',local.insertDoc.documentID);
						linkDocViewRightsToFileShare(local.insertDoc.documentID);
					}	
					else{
						// error in upload - locate to message page and apply message ---------------------------
						//application.objCommon.redirect('#this.link.message#&message=3');
						local.actionReturnStruct.displayMessage = local.newFile.ReasonText;
						local.actionReturnStruct.view = "fileshare/fileEcho";
					}
					application.objCommon.redirect("#this.link.editFile#&fsAction=edit&fsID=#arguments.event.getValue('fsID')#&dID=#arguments.event.getValue('dID')#&msg=1");
				</cfscript>
			</cfif>
		<cfelse>
			<cfset local.actionReturnStruct.view = "fileshare/notfound"/>
		</cfif>
		<cfreturn local.actionReturnStruct/>
	</cffunction>
	
	<cffunction name="linkDocViewRightsToFileShare" access="private" returntype="void" output="no">
		<cfargument name="documentID" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<cfquery name="local.qryGetInfo" datasource="#application.dsn.membercentral.dsn#">
			select siteid, siteResourceID
			from dbo.cms_documents
			where documentid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.documentID#">
		</cfquery>
		<cfstoredproc procedure="cms_createSiteResourceRight" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam cfsqltype="cf_sql_integer" value="#local.qryGetInfo.siteid#">
			<cfprocparam cfsqltype="cf_sql_integer" value="#local.qryGetInfo.siteResourceID#">
			<cfprocparam cfsqltype="cf_sql_bit" value="1">
			<cfprocparam cfsqltype="cf_sql_varchar" value="4">
			<cfprocparam cfsqltype="cf_sql_integer" null="yes">
			<cfprocparam cfsqltype="cf_sql_integer" null="yes">
			<cfprocparam cfsqltype="cf_sql_integer" value="#variables.fileShareSettings.siteresourceID#">
			<cfprocparam cfsqltype="cf_sql_integer" value="4">
		</cfstoredproc>
	</cffunction>

	<cffunction name="addCategory" access="public" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.actionStruct = structNew()>
		
		<cfset local.returnStruct.actionStruct.view = "category/editCategoryBS4">
		<cfset local.returnStruct.actionStruct.controllingSRID = this.siteResourceID>
		<cfset local.returnStruct.actionStruct.showAll = false>
		<cfset local.returnStruct.actionStruct.catID = 0>
		<cfset local.returnStruct.actionStruct.defaultTreeID = arguments.event.getValue('treeID', 0)>
		<cfset local.returnStruct.actionStruct.retFunction = 'finishUpdate'>

		<cfset local.returnStruct.fileShareSettings = variables.fileShareSettings>
		<cfset local.returnStruct.appRightsStruct 	= StructNew()>

		<cfsavecontent variable="local.data">
			<cfoutput><cfmodule template="/views/#local.returnStruct.actionStruct.view#.cfm" data="#local.returnStruct#" event="#arguments.event#"></cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>			
	</cffunction>

	<cffunction name="editCategory" access="public" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.actionStruct = structNew()>
		
		<cfset local.returnStruct.actionStruct.view = "category/editCategoryBS4">
		<cfset local.returnStruct.actionStruct.controllingSRID = this.siteResourceID>
		<cfset local.returnStruct.actionStruct.showAll = false>
		<cfset local.returnStruct.actionStruct.catID = arguments.event.getValue('categoryID', 0)>
		<cfset local.returnStruct.actionStruct.defaultTreeID = arguments.event.getValue('treeID', 0)>
		<cfset local.returnStruct.actionStruct.retFunction = 'finishUpdate'>

		<cfset local.returnStruct.fileShareSettings = variables.fileShareSettings>
		<cfset local.returnStruct.appRightsStruct 	= StructNew()>

		<cfsavecontent variable="local.data">
			<cfoutput><cfmodule template="/views/#local.returnStruct.actionStruct.view#.cfm" data="#local.returnStruct#" event="#arguments.event#"></cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>			
	</cffunction>

	<cffunction name="massAddCategory" access="public" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>

		<cfset local.categoryTreeID = val(arguments.event.getValue('treeID',0))>
		<cfset local.qryCategoryTree = this.objFSAdmin.getFileShareCategoryTree(categoryTreeID=local.categoryTreeID, siteResourceID=variables.fileShareSettings.siteResourceID)>

		<cfset arguments.event.setValue('categoryTreeID',val(local.qryCategoryTree.categoryTreeID))>
		<cfset arguments.event.setValue('_addTitle',"Mass Add Categories for #local.qryCategoryTree.categoryTreeName#")>
		<cfset arguments.event.setValue('_retFunc',"finishUpdate")>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="/model/admin/categories/frm_massAddCategory.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="editCategoryTreeName" access="public" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>

		<cfset local.categoryTreeID = arguments.event.getValue('treeID',0)>
		<cfset local.categoryTreeAction = arguments.event.getValue('action','')>
		<cfset local.errMessage = ''>	
		
		<!--- validate that category tree belongs to the fs2 passed in --->
		<cfset local.checkCategoryTree = this.objFSAdmin.getFileShareCategoryTree(categoryTreeID=local.categoryTreeID, siteResourceID=variables.fileShareSettings.siteResourceID)>

		<cfif local.categoryTreeAction eq 'add'>
			<cfset local.categoryTreeName = "">
		<cfelse>
			<cfset local.categoryTreeName = local.checkCategoryTree.categoryTreeName>
			<cfif (local.categoryTreeID eq 0) OR (local.checkCategoryTree.recordcount neq 1)>
				<cfset local.errMessage = 'Invalid Category Tree.'>		
			</cfif>	
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				 <cfinclude template="frm_categoryTree.cfm" />					
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>			
	</cffunction>
	
	<cffunction name="addCategoryTreeName" access="public" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>

		<cfset local.categoryTreeID = arguments.event.getValue('treeID',0)>
		<cfset local.errMessage = ''>		
		
		<cfsavecontent variable="local.treeNameJS">
			<script language="javascript" src="/assets/common/javascript/common.js"></script>
			<cfoutput>
			<script language="javascript">
				function checkCategoryTree()
				{
					var arrReq = new Array();
					mca_hideAlert('frm_err');
					
					var updateCategoryTreeResult = function(r) {
				
						if (r.success && r.success == 'true') {
							addCategoryTree();
						}
						else if (r.success && r.success == 'false') {
							arrReq[arrReq.length] = 'That category tree name is already in use.\r\nPlease enter a different category tree name.';
						}
						else {
							arrReq[arrReq.length] = 'Unable to update category tree name.';
						}

						showErrorMessage(arrReq);
					};
					
					if($.trim($("##catTreeName").val()) != ''){
						var objParams = { controllingSiteResourceID:#variables.fileShareSettings.siteResourceID#,
										categoryTreeID:0,
										categoryTreeName:$("##catTreeName").val() };
						TS_AJX('ADMINCATEGORIES','testCategoryTreeNameExists',objParams,updateCategoryTreeResult);
					}else{
						arrReq[arrReq.length] = 'Category Tree Name is required.';
						showErrorMessage(arrReq);
					}
				}
				
				function addCategoryTree()
				{
					var arrReq = new Array();
					mca_hideAlert('frm_err');
					
					var updateCategoryTreeResult = function(r) {
				
						if (r.success && r.success == 'true') {
							top.refreshCategoryTrees(r.categoryTreeID);
							top.closeBox();
						}
						else {
							arrReq[arrReq.length] = 'Unable to update category tree name.';
							showErrorMessage(arrReq);
						}
						
					};
					
					var objParams = { 
										siteID: #arguments.event.getValue('mc_siteInfo.siteid',0)#,
										controllingSiteResourceID:#variables.fileShareSettings.siteResourceID#,
										categoryTreeName:$("##catTreeName").val() };
					TS_AJX('ADMINCATEGORIES','addCategoryTree',objParams,updateCategoryTreeResult);
				}
				function showErrorMessage(arrReq){
					if(arrReq.length){
						mca_showAlert('frm_err', arrReq.join('<br/>'), true);
					}			
				}
			</script>
			
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#application.objCommon.minText(local.treeNameJS)#">
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>New Category Tree</h4>				
				<cfform name="frmEditCategoryTree"  id="frmEditCategoryTree" method="POST" action="">
					<cfif len(local.errMessage) GT 0>
						<div class="alert alert-danger mb-2">#local.errMessage#</div>
					<cfelse>
						<cfinput type="hidden" name="categoryTreeID"  id="categoryTreeID" value="0">
						<div id="frm_err" class="alert alert-danger mb-2 d-none"></div>
						<div class="form-group row mt-2">
							<label for="catTreeName" class="col-sm-3 col-form-label-sm font-size-md">Category Tree *</label>
							<div class="col-sm-9">
								<cfinput type="text" name="catTreeName" id="catTreeName" class="form-control form-control-sm" required="yes" message="Category Tree Name is required" value="">
							</div>
						</div>
						<div class="form-group row mt-2">
							<div class="col-sm-3"></div>
							<div class="col-sm-9">
								<button type="button" class="btn btn-sm btn-primary" onClick="checkCategoryTree();">Add</button>
							</div>
						</div>
					</cfif>
				</cfform>				
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>			
	</cffunction>	
	
	<cffunction name="getFileShareSettings" access="private" returntype="struct">
		<cfargument name="applicationInstanceID" type="numeric" required="yes"/>
		
		<cfset var local = structNew()>
		
		<cfset local.fileShareSettings = structNew()>
		
		<cfquery name="local.getFileShareSettings" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @FSRTID int, @CommRTID int;
			set @FSRTID = dbo.fn_getResourceTypeID('FileShare2');
			set @CommRTID = dbo.fn_getResourceTypeID('Community');

			SELECT ai.siteresourceID, ai.applicationInstanceName, ai.siteID, fs.applicationInstanceID, fs.fileShareID, fs.rootSectionID, fs.recordsperpage,
				fs.showPublicationDate, fs.requirePublicationDate, fs.showAuthor, fs.requireAuthor, fs.showVersioning, fs.showContributedBy, fs.showFirm, fs.showTags, fs.showTagsAsLinks,
				fs.showAddress, fs.showCustomFields, fs.authorLabel, fs.alwaysShowFolders, fs.showBrowseTitle, fs.showInteriorText,
				fs.showDocDownloadCountToMembers, fs.isMultipleSelectSearch, fs.columnWidth, fs.notifyEmails, fs.notifyOnAdd, fs.notifyOnUpdate,
				fs.columnToShow, fs.ignoreRightsCheck, fs.showResultSorting, fs.defaultResultSorting, fs.showShareButton,
				fileShareName = ai.applicationInstanceName + case WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')' ELSE '' END,
				isnull(communityInstances.applicationInstanceName,'') as communityName, sr.siteResourceStatusID as srStatusID,
				sr.resourceTypeID, srs.siteResourceStatusDesc as srsStatus,
				case when sr.resourceTypeID = @FSRTID then 1 else 0 end as isFileShare2
			FROM dbo.fs_fileShare fs
			INNER JOIN dbo.cms_applicationInstances ai ON ai.applicationInstanceID = fs.applicationInstanceID 
				AND fs.applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.applicationInstanceID#">
			INNER JOIN dbo.cms_siteResources sr ON ai.siteResourceID = sr.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses srs ON sr.siteResourceStatusID = srs.siteResourceStatusID
			INNER JOIN dbo.cms_siteResources AS parentResource ON parentResource.siteResourceID = sr.parentSiteResourceID
			left outer join dbo.cms_siteResources AS grandparentResource
				inner join dbo.cms_applicationInstances AS CommunityInstances on communityInstances.siteResourceID = grandParentResource.siteResourceID
				on grandparentResource.siteResourceID = parentResource.parentSiteResourceID
				and grandparentResource.resourceTypeID = @CommRTID;
		</cfquery>

		<cfset variables.appResourceTypeID = local.getFileShareSettings.resourceTypeID/>
		<cfloop index="local.thisField" list="#local.getFileShareSettings.columnList#">
			<cfset local.fileShareSettings[local.thisField] = local.getFileShareSettings[local.thisField]/>
		</cfloop>

		<cfquery name="local.fileShareSettings.qrySections" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int, @startSectionID int, @startSectionPath varchar(max), @startSectionPathExpanded varchar(max);
			SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.fileShareSettings.siteID#">;
			SET @startSectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.fileShareSettings.rootSectionID#">;

			SELECT @startSectionPath=thePath, @startSectionPathExpanded=thePathExpanded
			FROM dbo.cms_pageSections
			WHERE siteID = @siteID
			AND sectionID = @startSectionID;

			SELECT ps.sectionID, ps.sectionName,
				thePath = replace(ps.thePath,@startSectionPath,'0001'), 
				thePathExpanded = case
					when ps.sectionID = @startSectionID then ''
					when len(@startSectionPathExpanded) > 0 then replace(ps.thePathExpanded, @startSectionPathExpanded + ' \ ','')
					else ps.thePathExpanded
				end
			FROM dbo.cache_cms_recursivePageSections rps
			INNER JOIN dbo.cms_pageSections AS ps ON ps.sectionID = rps.startSectionID
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = ps.siteResourceID
				AND sr.siteResourceStatusID = 1
				AND sr.siteID = @siteID
			WHERE rps.sectionID = @startSectionID
			AND rps.siteID = @siteID
			ORDER BY ps.thePath;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.fileShareSettings />
	</cffunction>
		
	<cffunction name="addSection" access="private" returntype="numeric">
		<cfargument name="sectionName" type="String" required="true">
		<cfargument name="parentSectionID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.insertSection">
			
			SET NOCOUNT ON 
			
			DECLARE  	@sectionResourceTypeID int, 
								@sectionID int,
								@sectionName varchar(50),
								@siteID int,
								@parentSectionID int,
								@inheritPlacements bit
			
			SELECT @sectionResourceTypeID  	= dbo.fn_getResourceTypeID('UserCreatedSection') 
			SELECT @sectionName 						= <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.sectionName#">
			SELECT @siteID 									= <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			SELECT @parentSectionID 				= <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.parentSectionID#">
			SELECT @inheritPlacements				= 1
			
			EXEC 
				dbo.cms_createPageSection 
					@siteID 								= @siteID, 
					@sectionResourceTypeID 	= @sectionResourceTypeID, 
					@ovTemplateID 					= null, 
					@ovTemplateIDMobile 					= null,
					@ovModeID 							= null, 
					@parentSectionID 				= @parentSectionID, 
					@sectionName 						= @sectionName, 
					@sectionCode 						= null,
					@sectionBreadcrumb 					= null,
					@inheritPlacements			= @inheritPlacements,
					@sectionID 							= @sectionID OUTPUT

			select @sectionID as sectionID
			SET NOCOUNT OFF
				
		</cfquery>
		<cfset local.returnInt = local.insertSection.sectionID>
		<cfreturn local.returnInt>
	</cffunction>

	<cffunction name="importTSDocsToFileShare2" access="public" output="false" returntype="struct" hint="copies ts docs to fileshare2">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.categoryList = "">

		<cfquery name="local.qryFileShareCategoryTrees" datasource="#application.dsn.membercentral.dsn#">
			select categoryTreeID
			from dbo.cms_categoryTrees
			where controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#this.siteResourceID#">
		</cfquery>
		<cfloop query="local.qryFileShareCategoryTrees">
			<cfif len(arguments.event.getValue('frmCategory_#local.qryFileShareCategoryTrees.categoryTreeID#',''))>
				<cfset local.categoryList = listAppend(local.categoryList,arguments.event.getValue('frmCategory_#local.qryFileShareCategoryTrees.categoryTreeID#'))>
			</cfif>
		</cfloop>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryQueueDocuments">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				IF OBJECT_ID('tempdb..##tmpDocuments') IS NOT NULL
					DROP TABLE ##tmpDocuments;
				CREATE TABLE ##tmpDocuments (documentID int, documentDate datetime, Description varchar(255), expertName varchar(500), notes varchar(max));

				declare @depomemberdataid int, @fileShareID int, @siteID int, @contributorMemberID int, @recordedByMemberID int, 
					@categoryList varchar(max), @itemGroupUID uniqueidentifier, @readyToProcessStatusID int, @rootSectionID int,
					@xmlMessage xml;
				set @depomemberdataid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('depomemberdataid',0)#">;
				set @fileShareID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fsID')#">;
				set @siteID = #arguments.event.getValue('mc_siteInfo.siteID')#;
				set @contributorMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('contributingMemberID',0)#">;
				set @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;
				set @categoryList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.categoryList#">;

				declare @approvedStatusID int 
				select @approvedStatusID=statusID from trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

				INSERT INTO ##tmpDocuments (documentID, Description, ExpertName, DocumentDate, Notes)
				select d.documentID, dt.Description, d.ExpertName, d.DocumentDate, d.Notes
				from trialsmith.dbo.depoDocuments as d
				inner join trialsmith.dbo.depoDocumentStatusHistory as dsh on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
					and dsh.statusID = @approvedStatusID
				inner join trialsmith.dbo.depoDocumentTypes as dt on dt.TypeID = d.documentTypeID
				inner join trialsmith.dbo.depoDocumentsFilesOnline as dfo on dfo.documentID = d.documentID and dfo.fileType = 'pdf'
				where d.depomemberdataid = @depomemberdataid
				<cfif len(arguments.event.getValue('docTypeIDList',''))>
					and d.documentTypeID in (0#arguments.event.getValue('docTypeIDList')#)
				<cfelse>
					and d.documentTypeID in (
						select TypeID 
						from trialsmith.dbo.depoDocumentTypes 
						where updateDisplay = 1 
						and allowEndUserUploads=1 
						and categoryid = 1
					)
				</cfif>
				and not exists (select documentID from dbo.cms_documents where importedDocumentID = d.documentID);

				IF @@ROWCOUNT > 0 BEGIN
					set @itemGroupUID = NEWID();

					select @readyToProcessStatusID = qs.queueStatusID
					from platformQueue.dbo.tblQueueStatuses as qs
					inner join platformQueue.dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
					where qt.queueType = 'depoDocsToFileShare2'
					and qs.queueStatus = 'readyToProcess';

					select @rootSectionID = rootSectionID from dbo.fs_fileshare where fileShareID = @fileShareID;

					INSERT INTO platformQueue.dbo.queue_depoDocsToFileShare2 (itemGroupUID, fileShareID, depoDocumentID, 
						depoDocumentDate, depoDocumentDesc, depoDocumentTitle, depoObjectKey, siteID, rootSectionID, 
						memberID, recordedByMemberID, categoryIDList, fsObjectKeyPrefix, statusID, dateAdded, dateUpdated)
					SELECT @itemGroupUID, @fileShareID, documentID, DocumentDate, Notes, 
						LEFT(isnull([Description] + ': ','') + isnull(ExpertName,'Expert'), 200),
						'depos/pdfs/' + right('0000' + cast(documentID % 1000 as varchar(4)),4) + '/' + cast(documentID as varchar(10)) + '.pdf',
						@siteID, @rootSectionID, @contributorMemberID, @recordedByMemberID, @categoryList, 
						'sitedocuments/#lcase(arguments.event.getValue('mc_siteInfo.orgcode'))#/#lcase(arguments.event.getValue('mc_siteInfo.sitecode'))#/',
						@readyToProcessStatusID, getdate(), getdate()
					FROM ##tmpDocuments;

					-- send message to service broker to create all the individual messages
					select @xmlMessage = isnull((
						select 'depoDocsToFileShare2Load' as t, cast(@itemGroupUID as varchar(60)) as u
						FOR XML RAW('mc'), TYPE
					),'<mc/>');
					EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
				END

				IF OBJECT_ID('tempdb..##tmpDocuments') IS NOT NULL
					DROP TABLE ##tmpDocuments;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cflocation url="#this.link.edit#&fsID=#arguments.event.getValue('fsID')#&tab=impDocTab&impmsg=1">
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();			
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry...</h4>
				<cfif arguments.event.valueExists('message')>
					<p>
						<cfswitch expression="#arguments.event.getValue('message',1)#">
							<cfcase value="1"><b>You do not have rights to this section.</b></cfcase>
						</cfswitch>
					</p>				
				</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
</cfcomponent>

