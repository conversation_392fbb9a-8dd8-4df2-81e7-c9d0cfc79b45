<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			
			// set rights into event -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			// build quick links ------------------------------------------------------------------------ ::
			this.link.list = buildCurrentLink(arguments.event,"list");
			this.link.listGroups = buildCurrentLink(arguments.event,"listGroups");
			this.link.message = buildCurrentLink(arguments.event,"message");

			// Run Assigned Method ---------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];

			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			initMemberSearch(event=arguments.event);
			local.memberAdminSRID = arguments.event.getValue('mc_siteinfo.memberAdminSiteResourceID');
			local.tmpRights = buildRightAssignments(siteResourceID=local.memberAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			local.disableAddMemberAction = val(arguments.event.getValue('disableAddMemberAction',0));
			local.addMemNewTab = val(arguments.event.getValue('addMemNewTab',0));
			local.hasAddMemberRights = (local.tmpRights.AddAll OR local.tmpRights.AddOrg) AND NOT local.disableAddMemberAction;
			local.runMemNumContains = arguments.event.getValue('runMemNumContains','');

			local.qryGetFS = CreateObject("component","model.admin.members.memberAdmin").getLocatorFieldsetID(siteresourceID=local.memberAdminSRID, area='search');

			if (len(arguments.event.getValue('addbtnName')))
				local.linkToMembers = buildLinkToTool(toolType='MemberAdmin',mca_ta='search');
		</cfscript>

		<cfif len(arguments.event.getTrimValue('inGrp'))>
			<cfquery name="local.qryGroup" datasource="#application.dsn.membercentral.dsn#">
				select groupPathExpanded
				from dbo.ams_groups
				where groupID = <cfqueryparam value="#arguments.event.getTrimValue('inGrp')#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
		</cfif>
		<cfif len(arguments.event.getTrimValue('NotInGrp'))>
			<cfquery name="local.qryGroup" datasource="#application.dsn.membercentral.dsn#">
				select groupPathExpanded
				from dbo.ams_groups
				where groupID = <cfqueryparam value="#arguments.event.getTrimValue('NotInGrp')#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
		</cfif>
		<cfif len(arguments.event.getTrimValue('inEvReg'))>
			<cfquery name="local.qryEvent" datasource="#application.dsn.membercentral.dsn#">
				select cl.contentTitle
				from dbo.ev_events as e
				inner join dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID and cl.languageID = 1
				where e.eventID = <cfqueryparam value="#arguments.event.getTrimValue('inEvReg')#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
		</cfif>
		<cfif len(arguments.event.getTrimValue('NotInEvReg'))>
			<cfquery name="local.qryEvent" datasource="#application.dsn.membercentral.dsn#">
				select cl.contentTitle
				from dbo.ev_events as e
				inner join dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID and cl.languageID = 1
				where e.eventID = <cfqueryparam value="#arguments.event.getTrimValue('NotInEvReg')#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_search.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="listGroups" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		
		<cfscript>
		arguments.event.paramValue('fGroupName','');
		
		arguments.event.paramValue('fldName','');
		arguments.event.paramValue('dispTitle','Select Group');
		arguments.event.paramValue('autoClose','1');
		arguments.event.paramValue('retFunction','top.updateField');
		arguments.event.paramValue('closeFunction','top.MCModalUtils.hideModal');
		arguments.event.paramValue('gridMode','listing');

		local.grpsListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=groupsJSON&meth=getGroups&gridMode=#arguments.event.getTrimValue('gridMode')#&mode=stream";
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_groupSelector.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="initMemberSearch" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			// Setup Default Form Params
			arguments.event.paramValue('notInGrp','');
			arguments.event.paramValue('inGrp','');
			arguments.event.paramValue('notInEvReg','');
			arguments.event.paramValue('inEvReg','');
			arguments.event.paramValue('fldName','');
			arguments.event.paramValue('autoClose','1');
			arguments.event.paramValue('retFunction','top.updateField');
			arguments.event.paramValue('closeFunction','top.MCModalUtils.hideModal');
			arguments.event.paramValue('dispTitle','Select Member');
			arguments.event.paramValue('addbtnName','');
			
			local.formLink = buildCurrentLink(arguments.event,"memberSearchResults") & "&notInGrp=#arguments.event.getValue('notInGrp')#&inGrp=#arguments.event.getValue('inGrp')#&InEvReg=#arguments.event.getValue('inEvReg')#&notInEvReg=#arguments.event.getValue('notInEvReg')#&mode=stream";

			arguments.event.setValue('formLink',local.formLink);
		</cfscript>
	</cffunction>

	<cffunction name="memberSearchResults" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			initMemberSearch(event=arguments.event);

			local.appInstanceSettings = super.getInstanceSettings(this.appInstanceID);
			local.numPerPageInSearchResults = xmlSearch(local.appInstanceSettings.settingsXML,'string(/settings/setting[@name="numPerPageInSearchResults"]/@value)');
			local.showMemberPhotosInSearchResults = xmlSearch(local.appInstanceSettings.settingsXML,'string(/settings/setting[@name="showMemberPhotosInSearchResults"]/@value)');
			if (local.showMemberPhotosInSearchResults neq "false") local.showMemberPhotosInSearchResults = true;

			local.memberAdminSRID = arguments.event.getValue('mc_siteinfo.memberAdminSiteResourceID');
		</cfscript>

		<!--- put form vars into url vars. --->
		<cfif cgi.request_method eq "post">
			<cfset local.searchRedirectLink = arguments.event.getValue('formLink')>
			<cfset local.strMemSearchForm = duplicate(form)>
			<cfset structDelete(local.strMemSearchForm,"btn_st_s")>
			<cfset structDelete(local.strMemSearchForm,"fieldnames")>
			<cfloop collection="#local.strMemSearchForm#" item="local.currentitem">
				<cfif ReFindNoCase('mat?_[0-9]+_postalcode_radius',local.currentitem)>
					<cfif len(local.strMemSearchForm[replaceNoCase(local.currentitem,'_radius','')])>
						<cfset local.searchRedirectLink = local.searchRedirectLink & "&" & lcase(local.currentitem) & "=" & urlEncodedFormat(local.strMemSearchForm[local.currentitem])>
					</cfif>
				<cfelseif len(local.strMemSearchForm[local.currentitem])>
					<cfset local.searchRedirectLink = local.searchRedirectLink & "&" & lcase(local.currentitem) & "=" & urlEncodedFormat(local.strMemSearchForm[local.currentitem])>
				</cfif>
			</cfloop>
		<cfelse>
			<cfset local.searchRedirectLink = "/?#cgi.query_string#">
		</cfif>

		<!--- support for paging --->
		<cfset arguments.event.setValue('memPageNum',int(val(arguments.event.getValue('memPageNum',1))))>
		<cfset local.paging = { 
				link=ReReplaceNoCase(local.searchRedirectLink,'&memPageNum=[0-9]+','','ALL'),
				rowsize=local.numPerPageInSearchResults,
				currPage=arguments.event.getValue('memPageNum'),
				nextPage=arguments.event.getValue('memPageNum') + 1,
				prevPage=arguments.event.getValue('memPageNum') - 1
			}>

		<!--- get search and result fields --->
		<cfset local.objMFS = CreateObject("component","model.system.platform.memberFieldsets")>
		<cfset local.objMemberAdmin = CreateObject("component","model.admin.members.memberAdmin")>
		<cfset local.qrySearchFieldsetID = local.objMemberAdmin.getLocatorFieldsetID(siteResourceID=local.memberAdminSRID, area='search')>
		<cfset local.qryResultsFieldsetID = local.objMemberAdmin.getLocatorFieldsetID(siteResourceID=local.memberAdminSRID, area='results')>

		<!--- determine search conditions --->
		<cfset local.strSearchConditions = local.objMFS.getSQLSearchConditionsFromFieldSet(fieldsetID=local.qrySearchFieldsetID.fieldsetID, fieldsetUsage="memberAdminSearch", event=arguments.event)>
		<cfset structInsert(local.strSearchConditions.sqlParams,'siteid',{ value=arguments.event.getValue('mc_siteinfo.siteID'), cfsqltype="CF_SQL_INTEGER" })>
		<cfset structInsert(local.strSearchConditions.sqlParams,'orgid',{ value=arguments.event.getValue('mc_siteinfo.orgID'), cfsqltype="CF_SQL_INTEGER" })>
		<cfset structInsert(local.strSearchConditions.sqlParams,'resultsFSID',{ value=local.qryResultsFieldsetID.fieldsetID, cfsqltype="CF_SQL_INTEGER" })>
		<cfset structInsert(local.strSearchConditions.sqlParams,'startrow',{ value="#(local.paging.prevPage*local.paging.rowsize)+1#", cfsqltype="CF_SQL_INTEGER" })>
		<cfset structInsert(local.strSearchConditions.sqlParams,'endrow',{ value="#(local.paging.prevPage*local.paging.rowsize)+local.paging.rowsize#", cfsqltype="CF_SQL_INTEGER" })>

		<cfset local.groupJoinSQL = ''>
		<cfset local.groupWhereSQL = ''>
		<cfset local.eventJoinSQL = ''>
		<cfset local.eventWhereSQL = ''>

		<cfif len(arguments.event.getTrimValue('inGrp'))>
			<cfquery name="local.qryGroup" datasource="#application.dsn.membercentral.dsn#">
				select groupID, groupPathExpanded
				from dbo.ams_groups
				where groupID = <cfqueryparam value="#arguments.event.getTrimValue('inGrp')#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>

			<cfsavecontent variable="local.groupJoinSQL">
				<cfoutput>
					inner join dbo.cache_members_groups as mgInc on mgInc.orgID = @orgID and mgInc.memberID = m.memberID and mgInc.groupID = #int(val(local.qryGroup.groupID))#
				</cfoutput>
			</cfsavecontent>
		</cfif>
		<cfif len(arguments.event.getTrimValue('NotInGrp'))>
			<cfquery name="local.qryGroup" datasource="#application.dsn.membercentral.dsn#">
				select groupID, groupPathExpanded 
				from dbo.ams_groups
				where groupID = <cfqueryparam value="#arguments.event.getTrimValue('NotInGrp')#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>

			<cfsavecontent variable="local.groupJoinSQL">
				<cfoutput>
					left outer join dbo.cache_members_groups as mgExc on mgExc.orgID = @orgID and mgExc.memberID = m.memberID and mgExc.groupID = #int(val(local.qryGroup.groupID))#
				</cfoutput>
			</cfsavecontent>
			<cfset local.groupWhereSQL = 'and mgExc.memberID is null'>
		</cfif>
		<cfif len(arguments.event.getTrimValue('inEvReg'))>
			<cfquery name="local.qryEvent" datasource="#application.dsn.membercentral.dsn#">
				select e.eventID, cl.contentTitle
				from dbo.ev_events as e
				inner join dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID and cl.languageID = 1
				where e.eventID = <cfqueryparam value="#arguments.event.getTrimValue('inEvReg')#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>

			<cfsavecontent variable="local.eventJoinSQL">
				<cfoutput>
					inner join dbo.ams_members as mm on mm.memberID = m.activeMemberID
					inner join dbo.ev_registrants as evr on evr.recordedOnSiteID = @siteID and evr.memberID = mm.memberID
					inner join dbo.ev_registration as evreg on evreg.registrationID = evr.registrationID and evreg.eventID = #int(val(local.qryEvent.eventID))#
				</cfoutput>
			</cfsavecontent>
		</cfif>
		<cfif len(arguments.event.getTrimValue('NotInEvReg'))>
			<cfquery name="local.qryEvent" datasource="#application.dsn.membercentral.dsn#">
				select e.eventID, cl.contentTitle
				from dbo.ev_events as e
				inner join dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID and cl.languageID = 1
				where e.eventID = <cfqueryparam value="#arguments.event.getTrimValue('inEvReg')#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>

			<cfsavecontent variable="local.eventJoinSQL">
				<cfoutput>
					inner join dbo.ams_members as mm on mm.memberID = m.activeMemberID
					left outer join dbo.ev_registrants as evr 
						inner join dbo.ev_registration as evreg on evreg.registrationID = evr.registrationID and evreg.eventID = #int(val(local.qryEvent.eventID))#
						on evr.recordedOnSiteID = @siteID and evr.memberID = mm.memberID
				</cfoutput>
			</cfsavecontent>
			<cfset local.eventWhereSQL = 'and evr.registrantID is null'>
		</cfif>

		<cfset local.qryMembers = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @totalMatches int, @orgID int, @siteID int, @resultsFSID int, @outputFieldsXML xml, @startrow int, @endrow int;
			set @siteID = :siteid;
			set @orgID = :orgid;
			set @resultsFSID = :resultsFSID;
			set @startrow = :startrow;
			set @endrow = :endrow;

			IF OBJECT_ID('tempdb..##tmpMAMemberIDs') IS NOT NULL
				DROP TABLE ##tmpMAMemberIDs;
			IF OBJECT_ID('tempdb..##tmpMAMembers') IS NOT NULL
				DROP TABLE ##tmpMAMembers;
			IF OBJECT_ID('tempdb..##tmpMAResults') IS NOT NULL
				DROP TABLE ##tmpMAResults;
			CREATE TABLE ##tmpMAMemberIDs (memberID int PRIMARY KEY);
			CREATE TABLE ##tmpMAMembers (memberID int PRIMARY KEY, lastName varchar(75), firstName varchar(75), membernumber varchar(50),
				MCAccountStatus char(1), hasMemberPhotoThumb bit, company varchar(200), mc_row int);
			CREATE TABLE ##tmpMAResults (MFSAutoID int IDENTITY(1,1) not null);

			#local.strSearchConditions.stFilterPrep#

			insert into ##tmpMAMemberIDs (memberID)
			select distinct m.memberID
			from dbo.ams_members as m
			#local.groupJoinSQL#
			#local.eventJoinSQL#
			where m.orgID = @orgID
			and m.status in ('A','I')
			and m.isProtected = 0
			#local.groupWhereSQL#
			#local.eventWhereSQL#
			#local.strSearchConditions.stFilter#;

			#local.strSearchConditions.stFilterPost#

			select @totalMatches = count(*) from ##tmpMAMemberIDs;

			insert into ##tmpMAMembers (memberID, lastname, firstname, membernumber, MCAccountStatus, hasMemberPhotoThumb, company, mc_row)
			select m.memberID, m.lastname, m.firstname, m.membernumber, m.status, m.hasMemberPhotoThumb, m.company,
				ROW_NUMBER() OVER (order by m.lastname, m.firstname, m.membernumber) as mc_row
			from dbo.ams_members as m
			inner join ##tmpMAMemberIDs as tmpM on tmpM.memberID = m.memberID
			where m.orgID = @orgID
			and m.status in ('A','I');

			delete from ##tmpMAMembers
			where mc_row not between @startrow and @endrow;

			EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@resultsFSID, @existingFields='m_lastname,m_firstname,m_membernumber,m_company',
				@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmpMAMembers', @membersResultTableName='##tmpMAResults',
				@linkedMembers=0, @mode='view', @outputFieldsXML=@outputFieldsXML OUTPUT;

			-- return @outputFieldsXML in only the first row to reduce the query payload
			SELECT *, CASE WHEN mc_row = @startrow THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
			FROM (
				SELECT m.lastname, m.firstname, m.membernumber, m.MCAccountStatus, m.hasMemberPhotoThumb, m.company, 
					m.mc_row, tmpM.*, @totalMatches as mc_totalMatches
				FROM ##tmpMAMembers as m 
				INNER JOIN ##tmpMAResults as tmpM on tmpM.memberID = m.memberID
			) tmp
			ORDER BY mc_row;

			IF OBJECT_ID('tempdb..##tmpMAMemberIDs') IS NOT NULL
				DROP TABLE ##tmpMAMemberIDs;
			IF OBJECT_ID('tempdb..##tmpMAMembers') IS NOT NULL
				DROP TABLE ##tmpMAMembers;
			IF OBJECT_ID('tempdb..##tmpMAResults') IS NOT NULL
				DROP TABLE ##tmpMAResults;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			local.strSearchConditions.sqlParams, { datasource=application.dsn.memberCentral.dsn, result="local.qryMembersResult" }
		)>

		<cfif local.qryMembers.recordCount gt 0>
			<cfset local.xmlResultFields = local.qryMembers.mc_outputFieldsXML[1]>
			<cfset local.qryOutputFields = local.objMFS.getOutputFieldsFromXML(outputFieldsXML=local.xmlResultFields)>
			<cfset local.LastLoginDateInFS = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,17)='ml_datelastlogin_']")>
			<cfset local.RecordTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_recordtypeid']")>
			<cfset local.memberTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_membertypeid']")>
			<cfset local.memberStatusInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_status']")>

			<!--- remove fields from qryOutputFields that are handled manually --->
			<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
				SELECT *
				FROM [local].qryOutputFields
				WHERE fieldcodeSect NOT IN ('mc','m','ma','mat','mp','mpt')
			</cfquery>

			<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.event.getValue('mc_siteinfo.orgid'), includeTags=1)>
			<cfset local.strOrgAddressTypes = structNew()>
			<cfloop query="local.orgAddressTypes">
				<cfif local.orgAddressTypes.isTag is 1>
					<cfset local.strOrgAddressTypes["t#local.orgAddressTypes.addressTypeID#"] = local.orgAddressTypes.addressType>
				<cfelse>
					<cfset local.strOrgAddressTypes[local.orgAddressTypes.addressTypeID] = local.orgAddressTypes.addressType>
				</cfif>
			</cfloop>
			<cfset local.mc_combinedAddresses = structNew()>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,3)='mp_' or substring(@fieldCode,1,4)='mat_' or substring(@fieldCode,1,4)='mpt_']")>
			<cfloop array="#local.tmp#" index="local.thisField">
				<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
				<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
					<cfset local.strKey = "t#local.thisATID#">
				<cfelse>
					<cfset local.strKey = local.thisATID>
				</cfif>
				<cfif NOT StructKeyExists(local.mc_combinedAddresses,local.strKey)>
					<cfset local.mc_combinedAddresses[local.strKey] = { addr='', type=local.strOrgAddressTypes[local.strKey] } >
				</cfif>
			</cfloop>

			<cfif local.paging.currpage eq 1>
				<cfset local.currCountStart = 1>
				<cfset local.currCountStop = local.paging.rowsize>
			<cfelse>
				<cfset local.currCountStart = local.paging.rowSize * (local.paging.currpage - 1) + 1>
				<cfset local.currCountStop 	= local.paging.rowsize * (local.paging.currpage - 1) + local.qryMembers.recordCount>
			</cfif>

			<cfset local.arrAllClassifications = local.objMemberAdmin.getClassificationsForMemberIDList(siteResourceID=local.memberAdminSRID, memberIDList=valueList(local.qryMembers.memberid))>

			<cfsavecontent variable="local.data">
				<cfinclude template="frm_search_results.cfm">
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<script type="text/javascript">
						cancelSearchResults();
						showALAlert('There are no results based on your search criteria.');
					</script>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry...</h4>
				<cfif arguments.event.valueExists('message')>
					<p>
						<cfswitch expression="#arguments.event.getValue('message')#">
							<cfcase value="1"><b>You do not have rights to this section.</b></cfcase>
						</cfswitch>
					</p>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

</cfcomponent>