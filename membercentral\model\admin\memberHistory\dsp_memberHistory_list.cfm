<cfsavecontent variable="local.js">
	<cfoutput>
	<script language="javascript">
		let historyListTable, arrUnchkedHis = [], arrChkedHis = [];
		var #ToScript(lcase(local.historyTitleSingular),'MHItemSingleLabel')#
		var #ToScript(lcase(local.historyTitle),'MHItemPluralLabel')#
		var #ToScript(local.memSelectLink,'link_selectmember')#
		var #ToScript(local.grpSelectLink,'link_selectgroup')#
		var #ToScript(local.historyListLink,'historyListLink')#
		
		function initHistoryTable(){
			let domString = "<'row'<'col-sm-5 col-md-5'<'float-left mt-2'l><'float-left p-1 m-2 selHisCountDisp'>><'col-sm-7 col-md-7'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";
			
			historyListTable = $('##historyList').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 10,
				"lengthMenu": [ 10, 25, 50, 100 ],
				"dom": domString,
				"language": {
					"lengthMenu": "_MENU_"
				},
				"ajax": { 
					"url": historyListLink,
					"type": "post",
					"data": function(d) {
						$.each($('##frmFilter').serializeArray(),function() {
							d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
						});
					}
				},
				"autoWidth": false,
				"columns": [
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<input type="checkbox" name="historyCheckbox" class="historyCheckbox" value="' + data.historyID + '" onclick="onCheckHistoryEntry(this);" '+ ($("##masterCheckBox").is(':checked') ? 'checked' : '') +'>';
							}
							return type === 'display' ? renderData : data;
						},
						'searchable': false,
						'orderable': false,
						'className': 'text-center align-top',
						"width": "5%",
					},
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += "<a href='javascript:editMember("+data.memberID+")'>"+data.memberName+"</a>";
								if(data.memberCompany.length) renderData +="<div class='small text-dim'>"+data.memberCompany+"</div>";
								if(data.linkMemberID) {
									renderData += '<div class="mt-1"><i class="fa-solid fa-link fa-sm"></i> <a href="javascript:editMember('+data.linkMemberID+')">'+data.linkMemberName+'</a></div>';
									if(data.linkMemberCompany.length) renderData +="<div class='small text-dim pl-4'>"+data.linkMemberCompany+"</div>";
								}
							}
							return type === 'display' ? renderData : data;
						},
						"width": "30%",
						'className': 'align-top',
						'orderable': true
					},
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += "<div>" + data.typeName + "</div>";
								if(data.categoryName.length) renderData +="<div>"+data.categoryName+"</div>";
								if(data.userDate.length && data.userEndDate.length && data.userDate != data.userEndDate) {
									renderData += data.userDate + " - " + data.userEndDate;
								} else if(data.userDate.length) {
									renderData += data.userDate;
								}
							}
							return type === 'display' ? renderData : data;
						},
						'className': 'align-top',
						"width": "15%",
						'orderable': true
					},
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += data.dateEntered + " by " + data.enteredByMemberName;
								if(data.quantity) renderData += "<div>Qty: "+data.quantity+"</div>";
								if(data.dollarAmt.length) renderData += "<div>Amt: "+data.dollarAmt+"</div>";
								if($.trim(data.description).length) renderData += "<div>"+data.description+"</div>";
							}
							return type === 'display' ? renderData : data;
						},
						'className': 'align-top',
						"width": "40%",
						'orderable': true
					},			
					{ 
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								if(data.hasEditRights)
									renderData += '<a href="##" onclick="editMH('+data.historyID+');return false;" title="View Item" class="btn btn-sm text-primary p-1 mr-1"><i class="fa-solid fa-pen-to-square font-size-md"></i></a>';
								else 
									renderData += '<a href="##" class="btn btn-sm text-primary p-1 mr-1 invisible"><i class="fa-solid fa-pen-to-square font-size-md"></i></a>';
								if(data.hasDeleteRights) 
									renderData += '<a href="##" onclick="removeItem('+data.historyID+');return false;" title="Remove Item" class="btn btn-sm text-primary p-1 text-danger"><i class="fa-solid fa-trash-alt"></i></a>';
								else 
									renderData += '<a href="##" class="btn btn-sm text-primary p-1"><i class="fa-solid fa-trash-alt"></i></a>';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "10%",
						"className": "text-center align-top",
						"orderable": false
					}
				],
				"order": [[3, 'desc']],
				"searching": false,
				"pagingType": "simple",
				"drawCallback": function(settings) {
					checkHistoryEntry();
				}
			});
			mca_generateVerboseFilterMessage('frmFilter');
		}
		function changeFilterLinkedType(atype) {
			if (atype != undefined) {
				if (atype == "group") selectLinkedGroupFilter();
				else selectLinkedMemberFilter();
			}
		}
		function selectLinkedMemberFilter() {
			var selhref = link_selectmember + '&mode=direct&fldName=fLinkedMemberID&retFunction=top.updateFilterLinkedMemberField&disableAddMemberAction=1&dispTitle=' + escape('Filter #local.historyTitle# by Linked Member');
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Filter #local.historyTitle# by Linked Member',
				iframe: true,
				contenturl: selhref,
				strmodalfooter: {
					classlist: 'd-none'
				}
			});
		}
		function selectLinkedGroupFilter() {
			var selhref = link_selectgroup + '&mode=direct&fldName=fLinkedGroupID&retFunction=top.updateFilterLinkedGroupField&dispTitle=' + escape('Filter #local.historyTitle# by Group');
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Filter #local.historyTitle# by Group',
				iframe: true,
				contenturl: selhref,
				strmodalfooter: {
					classlist: 'd-none'
				}
			});
		}
		function updateFilterLinkedMemberField(fldID,mID,mNum,mName) {
			$('##fLinkedMemberID').val(mID);
			$('##fLinkedMemberName').val(mName);
			$('##fLinkedMemberNum').val(mNum);
			$('##fLinkedGroupID').val("");
			$('##fLinkedGroupName').val("");
			$('##fLinkedValInfo').find('span:first').html(mName + ' (' + mNum + ')');
			$('##fLinkedValInfo').removeClass('d-none');
		}
		function updateFilterLinkedGroupField(fldID,gID,gPath) {
			$('##fLinkedGroupID').val(gID);
			$('##fLinkedMemberID').val("");
			$('##fLinkedMemberName').val("");
			$('##fLinkedMemberNum').val("");
			$('##fLinkedValInfo').find('span:first').html('');
			$('##fLinkedValInfo').addClass('d-none');

			if (gPath.length > 0) {
				var newgPath = gPath.split("\\");
					newgPath.shift();
					newgPath = newgPath.join(" \\ ");
				$('##fLinkedGroupName').val(newgPath);
				$('##fLinkedValInfo').find('span:first').html(newgPath);
				$('##fLinkedValInfo').removeClass('d-none');
			}
		}
		function clearLinkedType() {
			$(".fLinkedType").each(function(){
				$(this).attr("checked",false);
			});
			$('##fLinkedValInfo').find('span:first').html('');
			$('##fLinkedValInfo').addClass('d-none');
			$('##fLinkedMemberID').val("");
			$('##fLinkedMemberName').val("");
			$('##fLinkedMemberNum').val("");
			$('##fLinkedGroupID').val("");
			$('##fLinkedGroupName').val("");
		}
		function changeFilterAssignType(atype) {
			if (atype != undefined) {
				if (atype == "group") selectAssignGroupFilter();
				else selectAssignMemberFilter();
			}
		}
		function selectAssignMemberFilter() {
			var selhref = link_selectmember + '&mode=direct&fldName=fAssignedMemberID&retFunction=top.updateFilterAssignMemberField&disableAddMemberAction=1&dispTitle=';
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Filter #local.historyTitle# by Assigned Member',
				iframe: true,
				contenturl: selhref,
				strmodalfooter: {
					classlist: 'd-none'
				}
			});
		}
		function selectAssignGroupFilter() {
			var selhref = link_selectgroup + '&mode=direct&fldName=fAssignedGroupID&retFunction=top.updateFilterAssignGroupField&dispTitle=' + escape('Filter #local.historyTitle# by Group');
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Filter #local.historyTitle# by Group',
				iframe: true,
				contenturl: selhref,
				strmodalfooter: {
					classlist: 'd-none'
				}
			});
		}
		function updateFilterAssignMemberField(fldID,mID,mNum,mName) {
			$('##fAssignedMemberID').val(mID);
			$('##fAssignedMemberName').val(mName);
			$('##fAssignedMemberNum').val(mNum);
			$('##fAssignedGroupID').val("");
			$('##fAssignedGroupName').val("");
			$('##fAssignedValInfo').find('span:first').html(mName + ' (' + mNum + ')');
			$('##fAssignedValInfo').removeClass('d-none');
		}
		function updateFilterAssignGroupField(fldID,gID,gPath) {
			$('##fAssignedGroupID').val(gID);
			$('##fAssignedMemberID').val("");
			$('##fAssignedMemberName').val("");
			$('##fAssignedMemberNum').val("");
			$('##fAssignedValInfo').find('span:first').html('');
			$('##fAssignedValInfo').addClass('d-none');

			if (gPath.length > 0) {
				var newgPath = gPath.split("\\");
					newgPath.shift();
					newgPath = newgPath.join(" \\ ");
				$('##fAssignedGroupName').val(newgPath);
				$('##fAssignedValInfo').find('span:first').html(newgPath);
				$('##fAssignedValInfo').removeClass('d-none');
			}
		}
		function clearAssignType() {
			$(".fAssignType").each(function(){
				$(this).attr("checked",false);
			});
			$('##fAssignedValInfo').find('span:first').html('');
			$('##fAssignedValInfo').addClass('d-none');
			$('##fAssignedMemberID').val("");
			$('##fAssignedMemberName').val("");
			$('##fAssignedMemberNum').val("");
			$('##fAssignedGroupID').val("");
			$('##fAssignedGroupName').val("");
		}

		function clearfilterMH() {
			$('##frmFilter').find('input[type="text"]').val('');
			$('##frmFilter').find('select').val(0);
			clearEnteredMember();
			clearAssignType();
			clearLinkedType();			
			dofilterMH();
		}
		function dofilterMH() {
			mca_generateVerboseFilterMessage('frmFilter');
			reloadHistoryListTable(true);
		}

		function generateCustomRadioFilterVerbose(filterField) {
			let label = "";
			let value = "";
			const fieldId = filterField.attr('id');
			if (filterField.is(':checked')) {
				switch (fieldId) {
					case 'fSelType':
							label = 'Entered By Member';
							value = $('##fSelectMemberName').val() + ' (' + $('##fSelectMemberNum').val() + ')';
						break;
					case 'fAssignTypeMember':
							label = 'Assigned To Member';
							value = $('##fAssignedMemberName').val() + ' (' + $('##fAssignedMemberNum').val() + ')';
						break;
					case 'fAssignTypeGroup':
							label = 'Assigned To Group';
							value = $('##fAssignedGroupName').val();
						break;
					case 'fLinkedTypeMember':
							label = 'Linked To Member';
							value = $('##fLinkedMemberName').val() + ' (' + $('##fLinkedMemberNum').val() + ')';
						break;
					case 'fLinkedTypeGroup':
							label = 'Linked To Group';
							value = $('##fLinkedGroupName').val();
						break;
					default:
						value = filterField.html();
						break;
				}
			}	
			return { label, value };
		}
		
		function reloadHistoryListTable(resetSettings) {
			if (resetSettings) {
				$('##masterCheckBox').prop('checked',true);
				arrUnchkedHis = []; arrChkedHis = [];
			}
			historyListTable.draw();
		}
		function exportMH() {
			if (!existsMHOnFilteredGrid()) return false;
		
			var opts = getFilters();
			var exportURL = '#local.mhExportLink#&' + $('##frmFilter').serialize();
				exportURL += '&chkAll=' + opts.chkAll;
				exportURL += '&historyIDList=' + opts.historyIDList;
				exportURL += '&notHistoryIDList=' + opts.notHistoryIDList;
			
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Export #local.historyTitle#',
				iframe: false, 
				contenturl: '', 
				strmodalfooter: {
					classlist: 'text-right',
					showclose: true,
					showextrabutton: false
				}
			});
			setTimeout(function () {
				$('##MCModalBody').html('<div><img src="/assets/common/images/progress.gif" width="16" height="16" alt="Please wait..."> Please Wait...</div><br/><br/>');		
				$('##MCModalBody').load(exportURL);
			}, 300); 
		}
		function doExportMH(u) {
			self.location.href = '/tsdd/' + u;
			MCModalUtils.hideModal();
		}
		function editMH(id) { 
			if(id) {
				var title = 'Edit #local.historyTitleSingular#';
				var contenturl = '#local.mhEditLink#&hID='+id;
			} else {
				var title = 'Add #local.historyTitleSingular#';
				var contenturl = '#local.mhAddLink#&typeID=#local.typeID#';
			} 

			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: title,
				iframe: true,
				contenturl: contenturl,
				strmodalfooter: {
					classlist: 'text-right',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: "$('##MCModalBodyIframe')[0].contentWindow.onSubmitSaveHistoryForm",
					extrabuttonlabel: 'Save #local.historyTitleSingular#',
				}
			});
		}
		function editMember(mID) {
			window.open('#local.editMemberLink#&memberID=' + mID + '&tab=#local.editMemberTab#','_blank');
		}
		function removeItem(hID) { 
			var msg = '<div class="alert alert-warning" id="divDeleteConfirmationArea"><b>Confirmation Needed</b><br/><p>Are you sure you want to delete this entry?<br/><br/>This action cannot be undone and the entry will be deleted.</p></div><div id="divDeleteFormArea"></div>';

			MCModalUtils.showModal({
				verticallycentered: true,
				size: 'md',
				title: 'Delete #local.historyTitleSingular#',
				strmodalbody: { 
					content: msg
				},
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-danger ml-auto',
					extrabuttonlabel: 'Remove'
				}
			});
			$('##btnMCModalSave').on('click', function(){
				$(this).html('Removing...').prop('disabled',true);
				doRemoveItem(hID);
			});
		}
		function doRemoveItem(hID) {
			$('##divDeleteConfirmationArea').hide();
			$('##divDeleteFormArea').html(mca_getLoadingHTML()).load('#local.mhDeleteItemLink#&hID=' + hID);
			
		}
		function removeItems() { 
			if (!existsMHOnFilteredGrid()) return false;

			let entryCount = Number($('.selHisCountDisp').attr('data-selcount'));
			let entryCountMsg = entryCount > 1 ? ('all <b>' + entryCount + '</b> entries') : 'this entry item';
			let msg = '<div class="alert alert-warning" id="divDeleteConfirmationArea"><b>Confirmation Needed</b><br/><p>Are you sure you want to delete '+entryCountMsg+' identified by the current filter?<br/><br/>This action cannot be undone and the entries will be deleted.</p></div><div id="divDeleteFormArea"></div>';

			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'md',
				title: 'Delete #local.historyTitle#',
				strmodalbody: { 
					content: msg
				},
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-danger ml-auto',
					extrabuttononclickhandler: 'doRemoveItems',
					extrabuttonlabel: 'Remove'
				}
			});
		}
		function doRemoveItems() {			
			$('##divDeleteConfirmationArea').hide();				
			var opts = getFilters();
			var deleteURL = '#local.mhDeleteItemsLink#&' + $('##frmFilter').serialize();
				deleteURL += '&chkAll=' + opts.chkAll;
				deleteURL += '&historyIDList=' + opts.historyIDList;
				deleteURL += '&notHistoryIDList=' + opts.notHistoryIDList;
			$('##divDeleteFormArea').html(mca_getLoadingHTML()).load(deleteURL);
			$('##btnMCModalSave').html('Please wait...').prop('disabled',true);
			
		}
		function doRemoveItemDone() {
			top.reloadHistoryListTable(true);
			MCModalUtils.hideModal();
		}
		function selMember(fldID) {
			var selhref = link_selectmember + '&mode=direct&autoClose=1&retFunction=top.doSelectMember&disableAddMemberAction=1&dispTitle=&fldName='+ fldID;
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Select Member',
				iframe: true,
				contenturl: selhref,
				strmodalfooter: {
					classlist: 'd-none'
				}
			});
		}
		function doSelectMember(fldID,mID,mNum,mName) {
			$('##fSelectMemberID').val(mID);
			$('##fSelectMemberName').val(mName);
			$('##fSelectMemberNum').val(mNum);
			$('##fSelectedMemberInfo').find('span:first').html(mName + ' (' + mNum + ')');
			$('##fSelectedMemberInfo').removeClass('d-none');
			$('##fSelType').prop('checked',true);
			return false;
		}
		function clearEnteredMember() {
			$('##fSelectMemberID').val(0);
			$('##fSelectMemberName').val('');
			$('##fSelectMemberNum').val('');
			$('##fSelectedMemberInfo').find('span:first').html('');
			$('##fSelectedMemberInfo').addClass('d-none');
			$('##fSelType').prop('checked',false);
		}
		function analyzeMH() {
			if (!existsMHOnFilteredGrid()) return false;

			var opts = getFilters();
			var analyzeMHLink = '#local.mhAnalyzeMemberHistory#&lstType=m&historyTitle=#local.historyTitle##iif(not findNoCase("typeID",local.mhAnalyzeMemberHistory),de("&typeID=#local.typeID#"),de(""))#&mode=direct&' + $('##frmFilter').serialize();
				analyzeMHLink += '&chkAll=' + opts.chkAll;
				analyzeMHLink += '&historyIDList=' + opts.historyIDList;
				analyzeMHLink += '&notHistoryIDList=' + opts.notHistoryIDList;
				MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Member #replaceNoCase(local.historyTitle,"Member","")# Totals',
				iframe: true,
				contenturl: analyzeMHLink,
				strmodalfooter: {
					classlist: 'text-right',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: 'onPrinterFriendlyClick',
					extrabuttonlabel: 'Printer-Friendly',
				}
			});
		}
		function onPrinterFriendlyClick(){
			$('##MCModalBodyIframe')[0].contentWindow.onPrintBtnClick();
		}
		function reCategorizeMH() {
			if (!existsMHOnFilteredGrid()) return false;

			var opts = getFilters();
			var reCategorizeMHLink = '#local.mhReCategorizeLink#&typeID=#local.typeID#&' + $('##frmFilter').serialize();
				reCategorizeMHLink += '&chkAll=' + opts.chkAll;
				reCategorizeMHLink += '&historyIDList=' + opts.historyIDList;
				reCategorizeMHLink += '&notHistoryIDList=' + opts.notHistoryIDList;

			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Recategorize #local.historyTitle#',
				iframe: true,
				contenturl: reCategorizeMHLink,
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: "$('##MCModalBodyIframe')[0].contentWindow.validateAndReCatMHEntries",
					extrabuttonlabel: 'Save',
				}
			});
		}
		function massEmailMH() {
			if (!existsMHOnFilteredGrid()) return false;

			var opts = getFilters();
			var emailLink = '#local.mhMassEmailLink#&' + $('##frmFilter').serialize();
				emailLink += '&chkAll=' + opts.chkAll;
				emailLink += '&historyIDList=' + opts.historyIDList;
				emailLink += '&notHistoryIDList=' + opts.notHistoryIDList;

			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'E-mail Filtered #local.historyTitle#',
				iframe: true,
				contenturl: emailLink,
				strmodalfooter: {
					classlist: 'd-none'
				}
			});
		}
		function setSelectedMHCountDisplay(c){
			$('##selMHCountDisp').html(' / <b>' + c + '</b> ' + (c == 1 ? MHItemSingleLabel : MHItemPlurallabel) + ' selected').show();
		}
		function existsMHOnFilteredGrid() {
			if((historyListTable.page.info().recordsTotal || 0) == 0) {
				alert('There are no filtered entries in #LCase(local.historyTitle)# to act upon.');
				return false;
			} else if($(".historyCheckbox:checkbox:checked").length == 0){
				alert('There are no selected entries in #LCase(local.historyTitle)# to act upon.');
				return false;
			} else {
				return true;
			}
		}
		function doCheckAllMHEntries(chk) {	
			arrUnchkedHis = [];
			arrChkedHis = [];
			$(".historyCheckbox:checkbox").prop('checked', chk);
			checkHistoryEntry();
		}
		function onCheckHistoryEntry(thisObj) {
			if ($(thisObj).is(':checked')) {
				if(arrUnchkedHis.includes($(thisObj).val())) {
					arrUnchkedHis = $.grep(arrUnchkedHis, function(value) {
						return value != $(thisObj).val();
					});
				}		
				if(!arrChkedHis.includes($(thisObj).val())) {
					arrChkedHis.push($(thisObj).val());
				}
			} else {
				if(arrChkedHis.includes($(thisObj).val())) {
					arrChkedHis = $.grep(arrChkedHis, function(value) {
						return value != $(thisObj).val();
					});
				}
				if(!arrUnchkedHis.includes($(thisObj).val())) {
					arrUnchkedHis.push($(thisObj).val());
				}
			}
			checkHistoryEntry();
		}
		function setSelectedHisCountDisplay(c){
			if(historyListTable.page.info().recordsTotal != 0){
				$('.selHisCountDisp').html('<b>' + (historyListTable.page.info().recordsTotal || 0) + '</b> ' + ((historyListTable.page.info().recordsTotal || 0) == 1 ? MHItemSingleLabel : MHItemPluralLabel) + ' found / <b>' + c + '</b> ' + (c == 1 ? MHItemSingleLabel : MHItemPluralLabel) + ' selected').show();
			}else{
				$('.selHisCountDisp').html('');
			}
			$('.selHisCountDisp').attr('data-selcount',c);
		}
		function checkHistoryEntry() {
			/* master checkbox unchecked */
			if (!$('##masterCheckBox').is(':checked')) {
				$.each(arrChkedHis, function( index, value ) {
					$(".historyCheckbox:checkbox[value="+value+"]").prop('checked', true);
				});
			}

			$.each(arrUnchkedHis, function( index, value ) {
				$(".historyCheckbox:checkbox[value="+value+"]").prop('checked', false);
			});
			
			var displayCount = 0;
			if ($('##masterCheckBox').is(':checked')) {
				displayCount = (historyListTable.page.info().recordsTotal || 0) - arrUnchkedHis.length;
			} else {
				var selectedIds = arrChkedHis.join(",");
				displayCount = selectedIds.length ? selectedIds.split(',').length : 0;
			}
			setSelectedHisCountDisplay(displayCount);
		}
		function getFilters() {
			var d = [];

			$.each($('##frmFilter').serializeArray(),function() {
				d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
			});

			if ($('##masterCheckBox').is(':checked')) {
				d['chkAll'] = 1;
				d['historyIDList'] = '';
				d['notHistoryIDList'] = arrUnchkedHis.join(',');
			} else {
				d['chkAll'] = 0;
				d['historyIDList'] = arrChkedHis.join(",");
				d['notHistoryIDList'] = '';
			}	
			return d;
		}
		
		$(function() {
			mca_setupDatePickerRangeFields('fDateFrom','fDateTo');
			mca_setupDatePickerRangeFields('fEntDateFrom','fEntDateTo');
			mca_setupDatePickerRangeFields('fEndDateFrom','fEndDateTo');
			mca_setupCalendarIcons('frmFilter');
			initHistoryTable();			
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.js)#">

<cfoutput>
<!--- button bar --->
<div class="toolButtonBar">
	<div><a href="##" onclick="editMH(0);return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to add a new #local.historyTitleSingular#."><i class="fa-regular fa-circle-plus"></i> Add #local.historyTitleSingular#</a></div>
	<div><a href="##" onclick="massEmailMH();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to email filtered #LCase(local.historyTitle)#."><i class="fa-regular fa-envelope"></i> Email #local.historyTitle#</a></div>
	<div><a href="##" onclick="exportMH();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to export #local.historyTitleSingular#."><i class="fa-regular fa-download"></i> Export #local.historyTitle#</a></div>
	<div><a href="##" onclick="removeItems();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to remove #local.historyTitle#."><i class="fa-regular fa-trash-can-list"></i> Delete #local.historyTitle#</a></div>
	<div><a href="##" onclick="analyzeMH();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to analyze #local.historyTitleSingular#."><i class="fa-regular fa-table-cells"></i> Analyze #local.historyTitle#</a></div>
	<div><a href="##" onclick="reCategorizeMH();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to recategorize #local.historyTitle#."><i class="fa-regular fa-layer-group"></i> Re-Categorize #local.historyTitle#</a></div>
</div>

<div id="divFilterForm" style="display:none;">
	<div class="row mb-3">
		<div class="col-xl-12">
			<div class="card card-box mb-1">
				<div class="card-header py-1 bg-light">
					<div class="card-header--title font-weight-bold font-size-lg">
						Filter #local.historyTitle#
					</div>
				</div>
				<div class="card-body pb-3">
					<form name="frmFilter" id="frmFilter" onsubmit="dofilterMH();return false;" data-filterwrapper="divFilterForm" data-verbosemsgwrapper="divMHListFilterVerbose" data-customverbose-radio="generateCustomRadioFilterVerbose" data-filterkey="#local.filterKeyName#">
						<div class="row">
							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="fDateFrom" id="fDateFrom" value="#local.strFilterData.fDateFrom#" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="fDateFrom"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="fDateFrom">Start Date From</label>
												</div>
											</div>
										</div>
									</div>
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="fDateTo" id="fDateTo" value="#local.strFilterData.fDateTo#" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="fDateTo"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="fDateTo">Start Date To</label>
												</div>
											</div>
										</div>
									</div>
								</div>

								<div class="form-row">
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="fEndDateFrom" id="fEndDateFrom" value="#local.strFilterData.fEndDateFrom#" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="fEndDateFrom"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fEndDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="fEndDateFrom">End Date From</label>
												</div>
											</div>
										</div>
									</div>
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="fEndDateTo" id="fEndDateTo" value="#local.strFilterData.fEndDateTo#" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="fEndDateTo"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fEndDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="fEndDateTo">End Date To</label>
												</div>
											</div>
										</div>
									</div>
								</div>

								<div class="form-row">
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="fEntDateFrom" id="fEntDateFrom" value="#local.strFilterData.fEntDateFrom#" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="fEntDateFrom"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fEntDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="fEntDateFrom">Date Entered From</label>
												</div>
											</div>
										</div>
									</div>
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="fEntDateTo" id="fEntDateTo" value="#local.strFilterData.fEntDateTo#" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="fEntDateTo"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fEntDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="fEntDateTo">Date Entered To</label>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<select name="parentChildCategoryID" id="parentChildCategoryID" class="form-control">
													<option value="0">Any category</option>
													<cfloop query="local.qryCategories">
														<option value="#local.qryCategories.parentChildCategoryID#" <cfif local.qryCategories.parentChildCategoryID eq local.strFilterData.parentChildCategoryID>selected </cfif>><cfif ListLen(local.qryCategories.parentChildCategoryID,'_') eq 2>&nbsp;&nbsp;&nbsp;</cfif>#local.qryCategories.categoryName#</option>
													</cfloop>
												</select>
												<label for="parentChildCategoryID">Category</label>
											</div>
										</div>
									</div>
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<input type="text" name="fKeyword" id="fKeyword" value="#local.strFilterData.fKeyword#" class="form-control">
												<label for="fKeyword">Description contains...</label>
											</div>
										</div>
									</div>
								</div>

								<cfif local.typeID is 1>
									<div class="form-row">
										<div class="col-sm-6 col-xs-12 ">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<input type="text" name="fQuantityFrom" id="fQuantityFrom" class="form-control" value="#local.strFilterData.fQuantityFrom#">
													<label for="fQuantityFrom">Qty From</label>
												</div>
											</div>
										</div>
										<div class="col-sm-6 col-xs-12">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<input type="text" name="fQuantityTo" id="fQuantityTo" class="form-control" value="#local.strFilterData.fQuantityTo#">
													<label for="fQuantityTo">Qty To</label>
												</div>
											</div>
										</div>
									</div>
								</cfif>

								<cfif local.typeID is 1>
									<div class="form-row">
										<div class="col-sm-6 col-xs-12">
											<div class="input-group flex-nowrap">
												<div class="input-group-prepend">
													<span class="input-group-text px-3">$</span>
												</div>
												<div class="form-label-group flex-grow-1 mb-0">
													<input type="text" name="fAmtFrom" id="fAmtFrom" value="#local.strFilterData.fAmtFrom#" class="form-control">
													<label for="fAmtFrom">Amt From</label>
												</div>
											</div>
										</div>
										<div class="col-sm-6 col-xs-12">
											<div class="input-group flex-nowrap">
												<div class="input-group-prepend">
													<span class="input-group-text px-3">$</span>
												</div>
												<div class="form-label-group flex-grow-1 mb-0">
													<input type="text" name="fAmtTo" id="fAmtTo" value="#local.strFilterData.fAmtTo#" class="form-control">
													<label for="fAmtTo">Amt To</label>
												</div>
											</div>
										</div>
									</div>
								</cfif>
							</div>
						</div>

						<div class="form-row mb-2">
							<div class="col-auto" style="width:100px;">Entered by:</div>
							<div class="col d-flex">
								<span id="fSelectedMemberInfo" class="badge badge-primary mx-2<cfif NOT val(local.strFilterData.fSelectMemberID)> d-none</cfif>">
									<span>#local.strFilterData.fSelectMemberName# (#local.strFilterData.fSelectMemberNum#)</span>
									<button type="button" class="close ml-2 text-white" aria-label="Remove" onclick="clearEnteredMember();">
										<span aria-hidden="true">&times;</span>
									</button>
								</span>
								<a href="##" onclick="selMember('fSelectMemberID');return false;"><i class="fa-light fa-user"></i> Select Member</a>
							</div>
							<input type="radio" name="fSelType" id="fSelType" value="member" class="d-none"<cfif val(local.strFilterData.fSelectMemberID)> checked</cfif>>
							<input type="hidden" name="fSelectMemberID" id="fSelectMemberID" value="#local.strFilterData.fSelectMemberID#">
							<input type="hidden" name="fSelectMemberName" id="fSelectMemberName" value="#local.strFilterData.fSelectMemberName#">
							<input type="hidden" name="fSelectMemberNum" id="fSelectMemberNum" value="#local.strFilterData.fSelectMemberNum#">
						</div>

						<div class="form-row mb-2">
							<div class="col-auto" style="width:100px;">Assigned To:</div>
							<div class="col">
								<div class="form-group">
									<div class="form-check form-check-inline">
										<input class="form-check-input fAssignType" type="radio" name="fAssignType" id="fAssignTypeMember" <cfif local.strFilterData.fAssignedMemberID gt 0>checked</cfif> value="member" onclick="changeFilterAssignType(this.value);">
										<label class="form-check-label" for="fAssignTypeMember">A Specific Member</label>
									</div>
									<div class="form-check form-check-inline">
										<input class="form-check-input fAssignType" type="radio" name="fAssignType" id="fAssignTypeGroup" <cfif local.strFilterData.fAssignedGroupID gt 0>checked</cfif> value="group" onclick="changeFilterAssignType(this.value);">
										<label class="form-check-label" for="fAssignTypeGroup">A Specific Group</label>
									</div>
									<input type="hidden" name="fAssignedMemberID" id="fAssignedMemberID" value="#local.strFilterData.fAssignedMemberID#">
									<input type="hidden" name="fAssignedMemberName" id="fAssignedMemberName" value="#local.strFilterData.fAssignedMemberName#">
									<input type="hidden" name="fAssignedMemberNum" id="fAssignedMemberNum" value="#local.strFilterData.fAssignedMemberNum#">
									<input type="hidden" name="fAssignedGroupID" id="fAssignedGroupID" value="#local.strFilterData.fAssignedGroupID#">
									<input type="hidden" name="fAssignedGroupName" id="fAssignedGroupName" value="#local.strFilterData.fAssignedGroupName#">
								</div>
								<div>
									<span id="fAssignedValInfo" class="badge badge-primary mx-2<cfif val(local.strFilterData.fAssignedMemberID) EQ 0 AND val(local.strFilterData.fAssignedGroupID) EQ 0> d-none</cfif>">
										<span><cfif val(local.strFilterData.fAssignedMemberID)>#local.strFilterData.fAssignedMemberName# (#local.strFilterData.fAssignedMemberNum#)<cfelseif val(local.strFilterData.fAssignedGroupID)>#local.strFilterData.fAssignedGroupName#</cfif></span>
										<button type="button" class="close ml-2 text-white" aria-label="Remove" onclick="clearAssignType();">
											<span aria-hidden="true">&times;</span>
										</button>
									</span>
								</div>
							</div>
						</div>

						<div class="form-row mb-2">
							<div class="col-auto" style="width:100px;">Linked To:</div>
							<div class="col">
								<div class="form-group">
									<div class="form-check form-check-inline">
										<input class="form-check-input fLinkedType" type="radio" name="fLinkedType" id="fLinkedTypeMember" <cfif local.strFilterData.fLinkedMemberID gt 0>checked</cfif> value="member" onclick="changeFilterLinkedType(this.value);">
										<label class="form-check-label" for="fLinkedTypeMember">A Specific Member</label>
									</div>
									<div class="form-check form-check-inline">
										<input class="form-check-input fLinkedType" type="radio" name="fLinkedType" id="fLinkedTypeGroup" <cfif local.strFilterData.fLinkedGroupID gt 0>checked</cfif> value="group" onclick="changeFilterLinkedType(this.value);">
										<label class="form-check-label" for="fLinkedTypeGroup">A Specific Group</label>
									</div>
									<input type="hidden" name="fLinkedMemberID" id="fLinkedMemberID" value="#local.strFilterData.fLinkedMemberID#">
									<input type="hidden" name="fLinkedMemberName" id="fLinkedMemberName" value="#local.strFilterData.fLinkedMemberName#">
									<input type="hidden" name="fLinkedMemberNum" id="fLinkedMemberNum" value="#local.strFilterData.fLinkedMemberNum#">
									<input type="hidden" name="fLinkedGroupID" id="fLinkedGroupID" value="#local.strFilterData.fLinkedGroupID#">
									<input type="hidden" name="fLinkedGroupName" id="fLinkedGroupName" value="#local.strFilterData.fLinkedGroupName#">
								</div>
								<div>
									<span id="fLinkedValInfo" class="badge badge-primary mx-2<cfif val(local.strFilterData.fLinkedMemberID) EQ 0 AND val(local.strFilterData.fLinkedGroupID) EQ 0> d-none</cfif>">
										<span><cfif val(local.strFilterData.fLinkedMemberID)>#local.strFilterData.fLinkedMemberName# (#local.strFilterData.fLinkedMemberNum#)<cfelseif val(local.strFilterData.fLinkedGroupID)>#local.strFilterData.fLinkedGroupName#</cfif></span>
										<button type="button" class="close ml-2 text-white" aria-label="Remove" onclick="clearLinkedType();">
											<span aria-hidden="true">&times;</span>
										</button>
									</span>
								</div>
							</div>
						</div>
						
						<cfif arguments.event.getValue('ret','all') neq 'all'> 
							<div class="form-group row mb-3">
								<div class="col-md-2 col-sm-12 pr-md-0">
									Linked: 
								</div>
								<div class="col-sm-auto">
									<select name="fMHLink" id="fMHLink" class="form-control form-control-sm">
										<option value="A">Show All Items</option>
										<option value="HO">Limit to items belonging to member</option>
										<option value="LO">Limit to items where member is the linked member</option>
									</select>
								</div>
							</div>
						</cfif>

						<div class="form-group row">
							<div class="col-md-12 text-right">
								<button type="button" name="btnResetFilterGrid" class="btn btn-sm btn-secondary" onclick="clearfilterMH()">Clear Filters</button>
								<button type="submit" name="btnFilterGrid" class="btn btn-sm btn-primary">
									<i class="fa-light fa-filter"></i> Filter #local.historyTitle#
								</button>
								<button type="button" class="btnReApplyFilter d-none" onclick="reloadHistoryListTable(true);"></button>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="mt-2 mr-2 text-right">
	<span id="mcg_rnum"></span><span id="selMHCountDisp"></span>
</div>
<div id="divMHListFilterVerbose" style="display:none;"></div>
<table id="historyList" class="table table-sm table-striped table-bordered" style="width:100%">
	<thead>
		<tr>
			<th><input type="checkbox" name="masterCheckBox" id="masterCheckBox" onclick="doCheckAllMHEntries(this.checked);" value="1" checked="checked"></th>
			<th>Member</th>
			<cfif local.typeID EQ 2>
				<th>Relationship</th>
			<cfelse>
				<th>Category</th>
			</cfif>
			<th>Detail</th>
			<th>Tools</th>
		</tr>
	</thead>
</table>
</cfoutput>