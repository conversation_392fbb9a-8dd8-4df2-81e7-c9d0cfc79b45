ALTER PROC dbo.up_depoDocumentsWithMissingSearchText 
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @errorSubject varchar(300), @errmsg varchar(max),@cutoff datetime = dateadd(day,-1,getdate());
	declare @approvedStatusID int 
	select @approvedStatusID=statusID from trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

	select @itemCount = count(*) from (
		select d.documentID
		from trialsmith.dbo.depodocuments as d
		inner join trialsmith.dbo.depoDocumentStatusHistory as dsh on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
			and dsh.statusID = @approvedStatusID
		inner join trialsmith.dbo.depoDocumentsFilesOnline as dfo on dfo.documentID = d.documentID
			and dfo.fileType = 'txt'
			and dfo.dateLastModified < @cutoff
			except
		select documentID
		from search.dbo.depodocuments
	) as missingSearchText;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	IF @itemCount > 0 BEGIN
		SET @errorSubject = 'Missing Search Text';
		SET @errmsg = 'Depos with uploaded text files are missing search text.';
		EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
