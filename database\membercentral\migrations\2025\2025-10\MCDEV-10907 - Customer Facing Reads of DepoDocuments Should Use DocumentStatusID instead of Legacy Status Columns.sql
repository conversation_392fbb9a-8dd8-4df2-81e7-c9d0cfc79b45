use customapps;
GO

ALTER PROC dbo.ts_processSubscriberDepositionsFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpFilteredDocIDs') IS NOT NULL
		DROP TABLE #tmpFilteredDocIDs;
	IF OBJECT_ID('tempdb..#tmpDepoMemberDataIDs') IS NOT NULL
		DROP TABLE #tmpDepoMemberDataIDs;
	IF OBJECT_ID('tempdb..#tmpSiteAdmins') IS NOT NULL
		DROP TABLE #tmpSiteAdmins;
	CREATE TABLE #tmpFilteredDocIDs (documentID int);
	CREATE TABLE #tmpDepoMemberDataIDs (depomemberDataID int);
	CREATE TABLE #tmpSiteAdmins (depomemberdataID int);

	DECLARE @readyToProcessStatusID int, @processingItemStatusID int, @firstName varchar(100), @lastName varchar(100), @searchText varchar(400);

	declare @approvedStatusID int
	select @approvedStatusID=statusID from  trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='tsSubscriberDepos', @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='tsSubscriberDepos', @queueStatus='processingItem', @queueStatusID=@processingItemStatusID OUTPUT;

	SELECT @firstName = firstName, @lastName = lastName
	FROM platformQueue.dbo.queue_tsSubscriberDepos
	WHERE itemID = @itemID
	AND statusID = @readyToProcessStatusID;

	IF @firstName IS NULL
		GOTO on_done;

	-- update queue item status
	UPDATE platformQueue.dbo.queue_tsSubscriberDepos
	SET statusID = @processingItemStatusID,
		dateUpdated = GETDATE()
	WHERE itemID = @itemID;

	-- site admins
	INSERT INTO #tmpSiteAdmins (depomemberdataID)
	SELECT DISTINCT np.depomemberdataID
	FROM membercentral.dbo.ams_networkProfiles AS np
	INNER JOIN membercentral.dbo.ams_memberNetworkProfiles AS mnp ON mnp.profileID = np.profileID
		AND mnp.status = 'A'
		AND np.status = 'A'
	INNER JOIN membercentral.dbo.ams_members AS m ON m.memberID = mnp.memberID
	INNER JOIN membercentral.dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.organizations AS org ON org.orgID = mActive.orgID
	INNER JOIN membercentral.dbo.ams_groups AS g ON g.orgID = org.orgID
		AND g.groupCode = 'SiteAdmins'
	INNER JOIN membercentral.dbo.cache_members_groups AS mg ON mg.orgID = g.orgID
		AND mg.memberID = mActive.memberID
		AND mg.groupID = g.groupID;

	-- get matching depomembers
	INSERT INTO #tmpDepoMemberDataIDs (depomemberDataID)
	SELECT DISTINCT d.depomemberdataID
	FROM trialsmith.dbo.depomemberdataUniqueNames AS du
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.uniqueNameID = du.uniqueID
	INNER JOIN trialsmith.dbo.depoTransactions as dt on dt.depoMemberDataID = d.depomemberdataID
	INNER JOIN trialsmith.dbo.membertype as mt on mt.AcctCode = dt.AccountCode
		AND mt.AcctCode NOT IN ('1000','1201','1007')
	LEFT OUTER JOIN #tmpSiteAdmins AS tmp ON tmp.depoMemberDataID = d.depomemberdataID
	WHERE du.firstName = @firstName
	AND du.lastName = @lastName
	AND d.TLAMemberState <> 'CP'
	AND d.adminFlag2 <> 'Y'
	AND d.includeInSubscriberDepos = 1
	AND tmp.depomemberdataID IS NULL;

	IF @@ROWCOUNT > 0 BEGIN
		-- clear existing documents tied to these depomemberdataids
		IF EXISTS (
			select 1
			FROM searchMC.dbo.ts_subscriberDepositions as tsd
			INNER JOIN #tmpDepoMemberDataIDs AS dm on dm.depomemberDataID = tsd.depomemberdataID)
		DELETE tsd
		FROM searchMC.dbo.ts_subscriberDepositions as tsd
		INNER JOIN #tmpDepoMemberDataIDs AS dm on dm.depomemberDataID = tsd.depomemberdataID;

		-- escape single quotes and double quotes
		SET @firstName = REPLACE(REPLACE(@firstName,'''',''''''),'"','""');
		SET @lastName = REPLACE(REPLACE(@lastName,'''',''''''),'"','""');

		DECLARE @FirstGroup TABLE (AGroup VARCHAR(50));
		INSERT INTO @FirstGroup VALUES ('Taken by');
		INSERT INTO @FirstGroup VALUES ('For the plaintiff');
		INSERT INTO @FirstGroup VALUES ('For the plaintiffs');
		INSERT INTO @FirstGroup VALUES ('For plaintiffs');
		INSERT INTO @FirstGroup VALUES ('For plaintiff');
		INSERT INTO @FirstGroup VALUES ('Represented by');
		INSERT INTO @FirstGroup VALUES ('On Behalf of the Plaintiff');
		INSERT INTO @FirstGroup VALUES ('On Behalf of the Plaintiffs');

		DECLARE @SearchString VARCHAR(3000);
		SET @SearchString = 'tsdoctypeid1xxx and ( NEAR(("Appearances","'+@firstName+'","'+@lastName+'") , 50, TRUE) OR ';

		SELECT @SearchString = @SearchString + 'NEAR(("'+AGroup+'","'+@firstName+'", "'+@lastName+'"), 20, TRUE) OR '
		FROM @FirstGroup;

		SELECT @SearchString = LEFT(@SearchString, LEN(@SearchString) - 3);
		SELECT @SearchString = @SearchString + ')';

		INSERT INTO #tmpFilteredDocIDs (documentID)
		SELECT d.documentID
		FROM containstable(search.dbo.depoDocuments,limitedsearchtext, @Searchstring) as ft inner join search.dbo.depoDocuments d on d.id = ft.[KEY];

		IF @@ROWCOUNT > 0
			INSERT INTO searchMC.dbo.ts_subscriberDepositions (depomemberdataID, depoDocumentID)
			SELECT dm.depomemberdataID, d.documentID
			FROM #tmpFilteredDocIDs AS tmp
			INNER JOIN trialsmith.dbo.depodocuments AS d ON d.documentID = tmp.documentID
			inner join trialsmith.dbo.depoDocumentStatusHistory as dsh
				on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
				and dsh.statusID = @approvedStatusID
			OUTER APPLY #tmpDepoMemberDataIDs AS dm;
	END

	-- delete queue item
	DELETE FROM platformQueue.dbo.queue_tsSubscriberDepos
	WHERE itemID = @itemID;

	on_done:

	IF OBJECT_ID('tempdb..#tmpFilteredDocIDs') IS NOT NULL
		DROP TABLE #tmpFilteredDocIDs;
	IF OBJECT_ID('tempdb..#tmpDepoMemberDataIDs') IS NOT NULL
		DROP TABLE #tmpDepoMemberDataIDs;
	IF OBJECT_ID('tempdb..#tmpSiteAdmins') IS NOT NULL
		DROP TABLE #tmpSiteAdmins;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

use search;
GO

ALTER PROC dbo.searchEngineIndex_populateQueue

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpNamesHold') IS NOT NULL
		DROP TABLE #tmpNamesHold;
	IF OBJECT_ID('tempdb..#tmpNames') IS NOT NULL
		DROP TABLE #tmpNames;
	IF OBJECT_ID('tempdb..#tmpNamesToDelete') IS NOT NULL
		DROP TABLE #tmpNamesToDelete;
	CREATE TABLE #tmpNamesHold (firstname VARCHAR(150), lastname VARCHAR(150));
	CREATE TABLE #tmpNames (nameID INT IDENTITY(1,1), firstname VARCHAR(150), lastname VARCHAR(150), baseSlug VARCHAR(200) INDEX IX_baseSlug);
	CREATE TABLE #tmpNamesToDelete (nameID INT PRIMARY KEY);

	declare @approvedStatusID int
	select @approvedStatusID=statusID from trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

	INSERT INTO #tmpNamesHold (firstname, lastname)
	SELECT fname, lname
	FROM trialsmith.dbo.depoDocuments d
	inner join trialsmith.dbo.depoDocumentStatusHistory as dsh
		on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
		and dsh.statusID = @approvedStatusID
	WHERE NULLIF(fname, '') IS NOT NULL
	AND NULLIF(lname, '') IS NOT NULL
	AND DocumentTypeID = 1
	GROUP BY lname, fname;

	-- strip non A-Z 0-9 dash and space
	UPDATE #tmpNamesHold
	SET lastname = replace(LTRIM(RTRIM(membercentral.dbo.fn_RegExReplace(lastname,'[^A-Za-z0-9\- ]+',' '))),'  ',' '),
		firstname = replace(LTRIM(RTRIM(membercentral.dbo.fn_RegExReplace(firstname,'[^A-Za-z0-9\- ]+',' '))),'  ',' ');

	DELETE FROM #tmpNamesHold
	WHERE lastname = '' OR firstname = '';

	-- final pool of distinct names
	INSERT INTO #tmpNames (firstname, lastname)
	SELECT firstname, lastname
	FROM #tmpNamesHold
	GROUP BY firstname, lastname;

	-- Create baseSlug
	UPDATE #tmpNames
	SET baseSlug = LOWER(replace(firstname + ' ' + lastname,' ','-'));

	-- if there are still dupe baseSlugs, just keep the first one
	DELETE tmp
	FROM #tmpNames as tmp
	INNER JOIN (
		select nameID, baseSlug, ROW_NUMBER() OVER (PARTITION BY baseSlug ORDER BY nameID) as baseNum
		from #tmpNames
	) as tmp2 on tmp2.nameID = tmp.nameID and tmp2.baseNum > 1;

	-- Insert new names into tblSearchEngineIndex
	DECLARE @dateNextRun datetime = DATEADD(HOUR, 1, GETDATE());

	INSERT INTO dbo.tblSearchEngineIndex (firstname, lastname, baseSlug, slug, dateNextRun)
	SELECT tmp.firstname, tmp.lastname, tmp.baseSlug, tmp.baseSlug, @dateNextRun
	FROM #tmpNames as tmp
	LEFT OUTER JOIN dbo.tblSearchEngineIndex as sei on sei.baseSlug = tmp.baseSlug
	WHERE sei.nameID IS NULL;

	-- names to delete that no longer exist in depoDocuments
	INSERT INTO #tmpNamesToDelete (nameID)
	SELECT sei.nameID
	FROM dbo.tblSearchEngineIndex as sei
	LEFT OUTER JOIN #tmpNames as tmp on tmp.baseSlug = sei.baseSlug
	WHERE tmp.nameID IS NULL;

	DELETE seibc
	FROM dbo.tblSearchEngineIndexBucketCounts as seibc
	INNER JOIN #tmpNamesToDelete as tmp on tmp.nameID = seibc.nameID;

	DELETE sei
	FROM dbo.tblSearchEngineIndex as sei
	INNER JOIN #tmpNamesToDelete as tmp on tmp.nameID = sei.nameID;


	-- populate the queue with all names
	DECLARE @queueStatusID int, @itemCount int = 0;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='depoCrawlerIndex', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;

	INSERT INTO platformQueue.dbo.queue_depoCrawlerIndex (nameID, statusID)
	SELECT nameID, @queueStatusID
	FROM dbo.tblSearchEngineIndex
		EXCEPT
	SELECT nameID, @queueStatusID
	FROM platformQueue.dbo.queue_depoCrawlerIndex;

	SET @itemCount = @@ROWCOUNT;

	IF @itemCount > 0
		EXEC membercentral.dbo.sched_resumeTask @name='Expert Name Crawler Index', @engine='MCLuceeLinux';

	SELECT @itemCount as itemCount;

	IF OBJECT_ID('tempdb..#tmpNamesHold') IS NOT NULL
		DROP TABLE #tmpNamesHold;
	IF OBJECT_ID('tempdb..#tmpNames') IS NOT NULL
		DROP TABLE #tmpNames;
	IF OBJECT_ID('tempdb..#tmpNamesToDelete') IS NOT NULL
		DROP TABLE #tmpNamesToDelete;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO

ALTER PROC dbo.up_appendSearchCodes
@documentID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @appendString varchar(max);

	--- Verify append has not been performed.
	select documentID from search.dbo.depodocuments where documentID = @documentID and searchtext like '%*tsdocbeginheader*%';
	IF @@ROWCOUNT = 0 BEGIN
		SELECT @appendString =
		'  *%*%*%*%*%*%*tsdocbeginheader*%*%*%*%*%*%* ------------------------------------------------------------------------ For TrialSmith Office use Only ------------------------------------------------------------------------ ' +
		convert(varchar(20), d.DocumentID) + ' ' + isnull(d.ExpertName,'') + ' ' + isnull(d.Style, '') + ' ' +  isnull(d.DefenseLawyer, '') + ' ' +
		convert(varchar(20), d.DocumentDate) + ' ' + isnull(d.Notes,'') + ' ' + isnull(d.State, '') + ' ' + isnull(d.Category,'') + ' ' +
		isnull(d.Summary, '') + ' ' + isnull(d.PlaintiffLawyer, '') + ' ' +	isnull(d.jurisdiction, '') + ' - ' +  isnull(s.name, '') + ' ' +
		isnull(d.Disposition, '') + 'tsdoc' + convert(varchar(20), d.DocumentID) + 'xxx tsgroupid' + convert(varchar(20), d.GroupID) +
		'xxx - ' + isnull(g.Description, '') + ' tsdoctypeid' + convert(varchar(20), d.DocumentTypeID) + 'xxx - ' + isnull(t.Description, '') +
		' tscasetypeid' + convert(varchar(20), d.CaseTypeID) + 'xxx - ' + isnull(ct.Description, '') +
		case when t.dspecial = 1 THEN ' tsspecialcollectionxxx ' else '' end +
		case when t.dcourtdoc = 1 THEN ' tscourtdocxxx ' else '' end +
		case when d.hot = 'YES' OR d.hot = 'Y' THEN ' tshotdocxxx' else '' end+
		case when ds.statusName = 'Disabled' THEN ' tsdisableddocxxx ' else '' end
		FROM trialsmith.dbo.depoDocuments d
		INNER JOIN trialsmith.dbo.depoDocumentStatusHistory as dsh on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
		inner join trialsmith.dbo.depoDocumentStatuses as ds on ds.statusID = dsh.statusID
		INNER JOIN trialsmith.dbo.depoGroups g ON d.GroupID = g.GroupID
		INNER JOIN trialsmith.dbo.depoDocumentTypes t ON d.DocumentTypeID = t.TypeID
		LEFT OUTER JOIN trialsmith.dbo.depoCaseTypes ct ON d.CaseTypeID = ct.CaseTypeID
		LEFT OUTER join trialsmith.dbo.states s on d.jurisdiction = s.code
		where d.documentid = @documentID;

		-- Append string
		update search.dbo.depodocuments set searchtext = CONCAT(searchtext,  @appendString) where documentID = @documentID;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO


use trialsmith;
GO

ALTER FUNCTION [dbo].[fn_Documents_hasContributed] (
	@documentID int,
	@depomemberdataid int
)
returns bit
AS
begin

	declare @hasContributed bit

	-- am I in a firm plan? If so, contributed for anyone in plan

	IF EXISTS (
		SELECT d.DocumentID
		FROM dbo.depoDocuments AS d
		inner join dbo.depoDocumentStatusHistory as dsh
			on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
			and d.documentID = @documentID
		inner join dbo.depoDocumentStatuses as ds on ds.statusID = dsh.statusID
			and ds.statusName = 'Approved'
		inner join dbo.depodocumenttypes as dt on d.documenttypeid = dt.typeid and dt.acctcode between 3000 and 3999
		inner join (
			select @depomemberdataid as depomemberdataid
			union
			select fpl.depoMemberDataID
			from dbo.tlaFirmPlanLink as fpl
			where fpl.firmPlanID = (select firmPlanID from dbo.tlaFirmPlanLink where depoMemberDataID = @depomemberdataid)
		) as acct on acct.depomemberdataid = d.depomemberdataid


	)
		SELECT @hasContributed = 1
	else
		SELECT @hasContributed = 0

	return @hasContributed
end
GO



ALTER PROC dbo.report_DocumentContributions
@orgCode varchar(10),
@radioDoc varchar(10),
@startDate datetime,
@endDate datetime,
@isSummary bit,
@exportFileName varchar(200)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @selectsql varchar(max);
	declare @approvedStatusID int
	select @approvedStatusID=statusID from  trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

	IF OBJECT_ID('tempdb..#tmpDocs') IS NOT NULL
		DROP TABLE #tmpDocs;
	IF OBJECT_ID('tempdb..#tmpDocsSum') IS NOT NULL
		DROP TABLE #tmpDocsSum;
	CREATE TABLE #tmpDocs (documentID int, contributingAssociation varchar(10), Jurisdiction varchar(50),
		ExpertName varchar(500), Style varchar(255), documentDate datetime, dateEntered datetime, CreditAmount decimal(18,2),
		Notes varchar(max), DepoAmazonBucks bit, depomemberdataid int, firstname varchar(100), lastname varchar(100),
		email varchar(100), DepoAmazonBucksEmail varchar(100), documentDateYear varchar(4), documentType varchar(100), Status varchar(25));
	CREATE TABLE #tmpDocsSum (contributingAssociation varchar(10), NumberContrib int, NumberAmazonBucks int);

	INSERT INTO #tmpDocs (documentID, contributingAssociation, Jurisdiction, ExpertName, Style, documentDate, dateEntered,
		Notes, DepoAmazonBucks, depomemberdataid, firstname, lastname, email, DepoAmazonBucksEmail, documentDateYear, documentType,
		Status, CreditAmount)
	select doc.documentID, doc.state as contributingAssociation, doc.Jurisdiction, doc.ExpertName, doc.Style,
		doc.documentDate, doc.dateEntered, doc.Notes, doc.DepoAmazonBucks,
		d.depomemberdataid, d.firstname, d.lastname, d.email, doc.DepoAmazonBucksEmail, year(doc.documentDate),
		dt.Description as documentType, ds.statusName,
		case when doc.DepoAmazonBucks = 1
			then isnull((select sum(pc.AmazonBucksCreditAmount) from dbo.PurchaseCredits as pc where pc.DocumentID = doc.DocumentID),0)
			else isnull((select sum(pc.PurchaseCreditAmount) from dbo.PurchaseCredits as pc where pc.DocumentID = doc.DocumentID),0)
			end
	from dbo.depoDocuments as doc
	inner join trialsmith.dbo.depoDocumentStatusHistory as dsh
		on dsh.depoDocumentHistoryID = doc.currentStatusHistoryID
		and dsh.statusID = @approvedStatusID
	inner join dbo.depoDocumentTypes as dt on dt.TypeID = doc.DocumentTypeID
	inner join dbo.depoMemberData as d on d.depomemberdataid = doc.depomemberdataid
	inner join dbo.depoDocumentStatuses as ds on ds.statusID = dsh.statusID
	where doc.DateEntered between @startDate and @endDate
	and 1 = case when @orgCode <> 'All' AND doc.state <> @orgCode then 0 else 1 end
	and 1 = case
		when @radioDoc = 'ALL' and doc.DocumentTypeID NOT IN (2,9) then 1
		when @radioDoc = 'ALLNODEPS' and doc.DocumentTypeID NOT IN (1,2,9) then 1
		when @radioDoc = 'DEPSAMAZON' and doc.DocumentTypeID = 1 and doc.DepoAmazonBucks = 1 then 1
		when @radioDoc = 'DEPS' and doc.DocumentTypeID = 1 then 1
		else 0
		end;

	IF @isSummary = 1 BEGIN
		INSERT INTO #tmpDocsSum
		SELECT contributingAssociation, COUNT(documentID) AS NumberContrib, SUM(case when DepoAmazonBucks = 1 then 1 else 0 end) as NumberAmazonBucks
		FROM #tmpDocs
		GROUP BY contributingAssociation;

		IF @exportFileName is null BEGIN

			select contributingAssociation as State, NumberContrib, NumberAmazonBucks
			from #tmpDocsSum
			order by contributingAssociation;

			select sum(NumberContrib) as DocCount
			from #tmpDocsSum;

			select documentDateYear, count(documentID) as DocCount
			from #tmpDocs
			group by documentDateYear
			order by documentDateYear;

		END ELSE BEGIN

			SET @selectsql = '
				SELECT contributingAssociation, NumberContrib, NumberAmazonBucks, ROW_NUMBER() OVER(order by contributingAssociation) as mcCSVorder
				*FROM* #tmpDocsSum';
			EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@exportFileName, @returnColumns=0;

		END
	END ELSE BEGIN
		IF @exportFileName is null BEGIN
			select documentID, contributingAssociation, Jurisdiction, ExpertName, Style, documentDate, dateEntered,
				Notes, DepoAmazonBucks, CreditAmount, depomemberdataid, firstname, lastname, documentType, Status
			from #tmpDocs
			order by contributingAssociation, lastname, firstname, depomemberdataid;
		END ELSE BEGIN
			SET @selectsql = '
				SELECT *, ROW_NUMBER() OVER(order by contributingAssociation, lastname, firstname, depomemberdataid) as mcCSVorder
				*FROM* #tmpDocs';
			EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@exportFileName, @returnColumns=0;
		END

	END

	IF OBJECT_ID('tempdb..#tmpDocs') IS NOT NULL
		DROP TABLE #tmpDocs;
	IF OBJECT_ID('tempdb..#tmpDocsSum') IS NOT NULL
		DROP TABLE #tmpDocsSum;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO



ALTER PROC dbo.up_calculateMonthlyTSRoyalties
@startDate datetime,
@endDate datetime

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @startDateSDT smalldatetime = SMALLDATETIMEFROMPARTS(year(@startDate),MONTH(@startDate),DAY(@startDate),0,0);
	declare @endDateSDT smalldatetime = SMALLDATETIMEFROMPARTS(year(@endDate),MONTH(@endDate),DAY(@endDate),23,59);
	declare @numMonthsInRange int = dateDiff(m,@startDate,dateadd(d,1,@endDate));

	declare @approvedStatusID int
	select @approvedStatusID=statusID from trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

	IF OBJECT_ID('tempdb..#tmpRoyalties') IS NOT NULL
		DROP TABLE #tmpRoyalties;
	IF OBJECT_ID('tempdb..#tmpDocsApproved') IS NOT NULL
		DROP TABLE #tmpDocsApproved;
	CREATE TABLE #tmpRoyalties (orgcode varchar(10), tsDocSalePct decimal(18,2), tsDocContribAmt decimal(18,2),
		tsSubSalePct decimal(18,2), eclipsRoyalty decimal(18,2), DocRoyaltyAmtBilled decimal(18,2),
		DocRoyaltyAmt decimal(18,2), DocRoyaltyAmtSpecialBilled decimal(18,2), DocRoyaltySpecialAmt decimal(18,2),
		DocsContributed int, DocsContributedRoyaltyAmt decimal(18,2), subAmtBilled decimal(18,2),
		SubRoyaltyAmt decimal(18,2), eclipsRoyaltyAmt decimal(18,2),
		totalRoyalty AS DocRoyaltyAmt+SubRoyaltyAmt+DocsContributedRoyaltyAmt+eclipsRoyaltyAmt+DocRoyaltySpecialAmt);
	CREATE TABLE #tmpDocsApproved (documentID int, orgcode varchar(10));

	INSERT INTO #tmpRoyalties (orgcode, tsDocSalePct, tsDocContribAmt, tsSubSalePct, eclipsRoyalty,
		DocRoyaltyAmtBilled, DocRoyaltyAmt, DocRoyaltyAmtSpecialBilled, DocRoyaltySpecialAmt, DocsContributed,
		DocsContributedRoyaltyAmt, subAmtBilled, SubRoyaltyAmt, eclipsRoyaltyAmt)
	select tla.state, mcb.tsDocSalePct, mcb.tsDocContribAmt, mcb.tsSubSalePct, mcb.eclipsRoyalty,
		0, 0, 0, 0, 0, 0, 0, 0, mcb.eclipsRoyalty * @numMonthsInRange
	from dbo.depoTlA as tla
	inner join dbo.memberCentralBilling as mcb on mcb.orgCode = tla.state;

	-- Document Royalty Amount Billed from Document Royalty Report
	update r
	set r.DocRoyaltyAmtBilled = tmp.amountBilled,
		r.DocRoyaltyAmt = (tmp.amountBilled*(r.tsDocSalePct/100))
	from #tmpRoyalties as r
	inner join (
		select t.SourceState, sum(AmountBilled) as AmountBilled
		from dbo.depoTransactions t
		where (accountCode between 3000 and 3999 or accountCode = 8000)
		and t.DatePurchased between @startDateSDT and @endDateSDT
		and (t.royaltyArrangementid is null or t.royaltyArrangementid = 0)
		group by t.SourceState
		having sum(AmountBilled) <> 0
	) as tmp on tmp.SourceState = r.orgcode;

	-- Special Document Royalty Arrangements from the Document Royalty Report with Special Pricing selected
	update r
	set r.DocRoyaltyAmtSpecialBilled = tmp.amountBilled,
		r.DocRoyaltySpecialAmt = tmp.royaltyAmount
	from #tmpRoyalties as r
	inner join (
		select SourceState, sum(AmountBilled) as amountBilled, sum(royaltyAmount) as royaltyAmount
		from (
			select t.royaltyArrangementid, ra.tsDocRASalePct, t.SourceState, sum(AmountBilled) as AmountBilled,
				cast(SUM(AmountBilled)*(CAST(ra.tsDocRASalePct as decimal(18,2))/100) as decimal(18,2)) as royaltyAmount
			from dbo.depoTransactions t
			inner join dbo.depoDocumentsRoyaltyArrangements as ra on ra.royaltyArrangementid = t.royaltyArrangementid
			where (accountCode between 3000 and 3999 or accountCode = 8000)
			and t.DatePurchased between @startDateSDT and @endDateSDT
			group by t.royaltyArrangementid, ra.tsDocRASalePct, t.SourceState
			having sum(AmountBilled) <> 0
		) as innerTmp
		group by SourceState
	) as tmp on tmp.SourceState = r.orgcode;

	-- Documents Approved in the date range not already paid out (changed in Aug 2025 from just contributed in date range)
	INSERT INTO #tmpDocsApproved (documentID, orgcode)
	SELECT distinct doc.documentID, doc.state
	from dbo.depoDocuments as doc
	INNER JOIN dbo.depoDocumentStatusHistory as sh ON sh.DocumentID = doc.DocumentID
		and sh.dateEntered between @startDate and @endDate
		and sh.statusID = 4
	inner join trialsmith.dbo.depoDocumentStatusHistory as current_dsh on current_dsh.depoDocumentHistoryID = doc.currentStatusHistoryID
		and current_dsh.statusID = @approvedStatusID
	where doc.DocumentTypeID = 1
	and doc.paidOrgRoyalty = 0;

	update r
	set r.DocsContributed = tmp.NumberApproved,
		r.DocsContributedRoyaltyAmt = tmp.NumberApproved*r.tsDocContribAmt
	from #tmpRoyalties as r
	inner join (
		SELECT orgcode as contributingAssociation, COUNT(documentID) AS NumberApproved
		from #tmpDocsApproved
		group by orgcode
	) as tmp on tmp.contributingAssociation = r.orgcode;

	UPDATE doc
	SET doc.paidOrgRoyalty = 1
	FROM dbo.depoDocuments as doc
	INNER JOIN #tmpDocsApproved as tmp on tmp.documentID = doc.documentID;


	-- Subscription Royalty Amount Billed from Subscription Royalty Report
	update r
	set r.subAmtBilled = tmp.amountBilled,
		r.SubRoyaltyAmt = (tmp.amountBilled*(r.tsSubSalePct/100))
	from #tmpRoyalties as r
	inner join (
		select SourceState, sum(AmountBilled) as AmountBilled
		from dbo.depoTransactions
		where accountCode in (select acctcode from dbo.membertype where acctcode <> '1201')
		and DatePurchased between @startDateSDT and @endDateSDT
		group by SourceState
		having sum(AmountBilled) <> 0
	) as tmp on tmp.SourceState = r.orgcode;

	-- only include where there is a net royalty
	DELETE FROM #tmpRoyalties
	where totalRoyalty = 0;


	-- log the run
	DECLARE @runID int;

	INSERT INTO platformStatsMC.dbo.ts_MonthlyRoyaltyRun (runDate, reportStartDate, reportEndDate)
	VALUES (getdate(), @startDate, @endDate);
		SELECT @runID = SCOPE_IDENTITY();

	INSERT INTO platformStatsMC.dbo.ts_MonthlyRoyaltyDetail (runID, orgcode, DepoSalePCT, DepoContribAMT,
		SubSalePCT, eclipsMonthAMT, DepoSales, DepoSpecialSales, DepoContributions, SubscriptionSales,
		DepoSalesRoyalty, DepoSpecialSalesRoyalty, DepoContributionsRoyalty, SubscriptionSalesRoyalty,
		eclipsRoyalty, TotalRoyalty)
	SELECT @runID, orgcode, tsDocSalePct/100 as DepoSalePCT, tsDocContribAmt as DepoContribAMT,
		tsSubSalePct/100 as SubSalePCT, eclipsRoyalty as eclipsMonthAMT,
		DocRoyaltyAmtBilled as DepoSales, DocRoyaltyAmtSpecialBilled as DepoSpecialSales,
		DocsContributed as DepoContributions, subAmtBilled as SubscriptionSales,
		DocRoyaltyAmt as DepoSalesRoyalty, DocRoyaltySpecialAmt as DepoSpecialSalesRoyalty,
		DocsContributedRoyaltyAmt as DepoContributionsRoyalty,
		SubRoyaltyAmt as SubscriptionSalesRoyalty, eclipsRoyaltyAmt as eclipsRoyalty,
		TotalRoyalty
	FROM #tmpRoyalties;


	IF OBJECT_ID('tempdb..#tmpRoyalties') IS NOT NULL
		DROP TABLE #tmpRoyalties;
	IF OBJECT_ID('tempdb..#tmpDocsApproved') IS NOT NULL
		DROP TABLE #tmpDocsApproved;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO


ALTER PROC dbo.up_depoDocumentsWithMissingSearchText
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @errorSubject varchar(300), @errmsg varchar(max),@cutoff datetime = dateadd(day,-1,getdate());
	declare @approvedStatusID int
	select @approvedStatusID=statusID from trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

	select @itemCount = count(*) from (
		select d.documentID
		from trialsmith.dbo.depodocuments as d
		inner join trialsmith.dbo.depoDocumentStatusHistory as dsh on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
			and dsh.statusID = @approvedStatusID
		inner join trialsmith.dbo.depoDocumentsFilesOnline as dfo on dfo.documentID = d.documentID
			and dfo.fileType = 'txt'
			and dfo.dateLastModified < @cutoff
			except
		select documentID
		from search.dbo.depodocuments
	) as missingSearchText;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	IF @itemCount > 0 BEGIN
		SET @errorSubject = 'Missing Search Text';
		SET @errmsg = 'Depos with uploaded text files are missing search text.';
		EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO


-- Backfill uploadSource


use trialsmith;
GO

declare @userUploadedSourceID int, @adminUploadedSourceID int

select @userUploadedSourceID=uploadSourceID
from depoDocumentUploadSources
where sourceName = 'User Uploaded'

select @adminUploadedSourceID=uploadSourceID
from depoDocumentUploadSources
where sourceName = 'Admin Uploaded'

update d set
    uploadSourceID = case
        when d.uploadStatus is null then @adminUploadedSourceID
        when d.uploadStatus = 0 then @adminUploadedSourceID
        when d.uploadStatus >= 1 then @userUploadedSourceID
    end
from depodocuments d
where d.uploadSourceID is null

GO


-- correct document status for 1 doc matching d.disabled = 'Y' and d.reviewFlag is null and d.uploadStatus is null

declare @approvedStatusID int , @disabledStatusID int, @depomemberdataID int = 64960
select @approvedStatusID=statusID from  trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'
select @disabledStatusID=statusID from  trialsmith.dbo.depoDocumentStatuses where statusName = 'disabled'

insert into depoDocumentStatusHistory (documentID, enteredByDepoMemberDataID, dateEntered, statusID, oldstatusID)
select d.documentID, enteredByDepoMemberDataID = @depomemberdataid, dateEntered=getdate(), statusID = @disabledStatusID, oldstatusID=@approvedStatusID
from depodocuments d
inner join depoDocumentStatusHistory dsh
    on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
inner join depoDocumentStatuses ds
    on ds.statusID = dsh.statusID
    and ds.statusName = 'Approved'
where d.disabled = 'Y' and d.reviewFlag is null and d.uploadStatus is null;
GO

-- correct document status for 49 docs matching d.disabled = 'Y' and d.reviewFlag = 0 and d.uploadStatus = 0

declare @approvedStatusID int , @disabledStatusID int, @depomemberdataID int = 64642
select @approvedStatusID=statusID from  trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'
select @disabledStatusID=statusID from  trialsmith.dbo.depoDocumentStatuses where statusName = 'disabled'

insert into depoDocumentStatusHistory (documentID, enteredByDepoMemberDataID, dateEntered, statusID, oldstatusID)
select d.documentID, enteredByDepoMemberDataID = @depomemberdataid, dateEntered=getdate(), statusID = @disabledStatusID, oldstatusID=@approvedStatusID
from depodocuments d
inner join depoDocumentStatusHistory dsh
    on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
inner join depoDocumentStatuses ds
    on ds.statusID = dsh.statusID
    and ds.statusName = 'Approved'
where d.disabled = 'Y' and d.reviewFlag = 0 and d.uploadStatus = 0;
GO

-- correct document status for 245 docs matching d.disabled = 'Y' and d.reviewFlag = 0 and d.uploadStatus = 2

declare @approvedStatusID int , @disabledStatusID int, @depomemberdataID int = 64642
select @approvedStatusID=statusID from  trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'
select @disabledStatusID=statusID from  trialsmith.dbo.depoDocumentStatuses where statusName = 'disabled'

insert into depoDocumentStatusHistory (documentID, enteredByDepoMemberDataID, dateEntered, statusID, oldstatusID)
select d.documentID, enteredByDepoMemberDataID = @depomemberdataid, dateEntered=getdate(), statusID = @disabledStatusID, oldstatusID=@approvedStatusID
from depodocuments d
inner join depoDocumentStatusHistory dsh
    on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
inner join depoDocumentStatuses ds
    on ds.statusID = dsh.statusID
    and ds.statusName = 'Approved'
where d.disabled = 'Y' and d.reviewFlag = 0 and d.uploadStatus = 2;
GO