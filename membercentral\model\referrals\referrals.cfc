﻿<cfcomponent extends="model.AppLoader" output="no">
	<cfset defaultEvent = "controller" />
	<cfset variables.applicationReservedURLParams = "ra,refaction,clientReferralID,refDateRange,applyPayment,saveClientBtn,taskCode,newStatusID,tab,panel" />
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.dataStruct = StructNew();
			this.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
			local.objReferrals	= CreateObject("component","model.referrals.referrals");	
			
			arguments.event.paramValue('ra','');
			local.objLocator = CreateObject("component","model.system.user.accountLocater");
			local.ajaxURL = '/?event=cms.showResource&resID=#this.siteResourceID#&mode=stream';
			local.dataStruct.siteResourceID = this.siteResourceID;
			arguments.event.paramValue('mainurl','/?pg=referrals');
			local.dataStruct.mainurl = arguments.event.getValue('mainurl');
			
			arguments.event.paramValue('referralID',0);
			arguments.event.paramValue('refDateRange',0);
			arguments.event.paramValue('filterReferralID','');
			arguments.event.paramValue('filterClientLastName','');
			arguments.event.paramValue('filterClientFirstName','');
			arguments.event.paramValue('filterStatus',0);
			arguments.event.paramValue('filterAmountDue','');
			arguments.event.paramValue('filterfeesDue','');
			arguments.event.paramValue('start',1);	
			arguments.event.paramValue('count',25);
			arguments.event.paramValue('sort','referralid');
			arguments.event.paramValue('orderBy','desc');
			arguments.event.paramValue('caseDateRange',0);
			if(NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcUser.memberData.IdentifiedAsMemberID gt 0){
				local.useMID = session.cfcUser.memberData.IdentifiedAsMemberID;
			}else{
				local.useMID = session.cfcuser.memberdata.memberid;
			}
			local.dataStruct.memberLoggedInID = local.useMID;
			arguments.event.paramValue('memberID',local.dataStruct.memberLoggedInID);
			local.dataStruct.thisAction = arguments.event.getValue('ra');
			local.siteID = arguments.event.getValue('mc_siteinfo.siteid');
			local.orgID = arguments.event.getValue('mc_siteInfo.orgID');
			arguments.event.paramValue('siteID',local.siteID );
			local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
			local.objReferralXML = CreateObject("component","model.admin.referrals.referralsXML");
			local.objWebsite = CreateObject("component","model.admin.website.website");
			local.dataStruct.qryStates = application.objCommon.getStates();
			// convert times from central (how stored in db) to default timezone of site
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
			local.dataStruct.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));		
			local.referralDate = now();
			if (local.regTimeZone neq "US/Central") {
				local.referralDate = local.objTZ.convertTimeZone(dateToConvert=local.referralDate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone);
			}		
			local.dataStruct.qryReferralSettings = getReferralSettings(local.siteID);
			local.dataStruct.referralID = local.dataStruct.qryReferralSettings.referralID;
							
			local.viewToUse = "echo";
			local.viewData = '';
		</cfscript>
		
		<cfif (application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true") or (isdefined("session.enableMobile") and session.enableMobile)>
			<cfset arguments.event.setValue('viewDirectory', 'responsive')>
			<cfset local.viewDirectory = arguments.event.getValue('viewDirectory', 'responsive')>
		<cfelse>
			<cfset arguments.event.setValue('viewDirectory', 'default')>
			<cfset local.viewDirectory = arguments.event.getValue('viewDirectory', 'default')>
		</cfif>
		
		<cfif local.useMID EQ 0>	
			<cfset local.viewData = "<b>You do not have any permission to view this content. Please <a href='/?pg=login'>log in</a>.</b>"/>
		<cfelse>			
			<cfswitch expression="#local.dataStruct.thisAction#">
				<cfcase value="editReferral">
					<cfscript>
						local.dataStruct.msgText = "";
						local.dataStruct.isThisRefRecord = 1;
						local.dataStruct.isThisCaseRecord = 0;

						local.qryCountries = application.objCommon.getCountries();
						local.countryCode = QueryFilter(local.qryCountries, function(thisRow) { return arguments.thisRow.countryID EQ application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).defaultCountryID; }).countryCode;

						local.dataStruct.clientReferralID = arguments.event.getValue('clientReferralID',0);
						if (NOT isValid("integer",local.dataStruct.clientReferralID) or local.dataStruct.clientReferralID lt 0)
							local.dataStruct.clientReferralID = 0;

						if (arguments.event.valueExists('saveClientBtn')){
							saveClientReferral(event=arguments.event);
							local.objAdminReferrals.reprocessConditions(orgID=local.orgID, clientReferralID=local.dataStruct.clientReferralID);
						}

						local.rc = arguments.event.getCollection();
						local.qryGetReferralSources = local.objAdminReferrals.getReferralSources(referralID=local.dataStruct.referralID);
						local.qryGetMemberInfo = application.objMember.getMemberInfo(memberid=local.useMID);
						local.qryGetReferralTypes = local.objAdminReferrals.getReferralTypes(event=arguments.event);
						local.dataStruct.qryGetLanguages 	= local.objAdminReferrals.getReferralLanguages(local.dataStruct.referralID);
						arguments.event.setValue('referralID',local.dataStruct.referralID);
						local.qryGetClientData = getClient(clientReferralID=local.dataStruct.clientReferralID);
						if (val(local.qryGetClientData.isRetainedCase) AND val(local.qryGetClientData.caseID))
							application.objCommon.redirect("/?pg=Referrals&ra=editCase&clientReferralID=" & local.dataStruct.clientReferralID);
						local.qryGetReferralStatus = this.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID).qryStatus;
						
						local.dataStruct.applyPayment = 0;
						/* Client Data */
						local.dataStruct.clientID = local.qryGetClientData.clientID;
						local.dataStruct.countryCode = local.countryCode;
						local.dataStruct.firstName = local.qryGetClientData.firstName;
						local.dataStruct.middleName = local.qryGetClientData.middleName;
						local.dataStruct.lastName = local.qryGetClientData.lastName;
						local.clientName = local.qryGetClientData.clientName;
						local.dataStruct.businessName = local.qryGetClientData.businessName;
						local.dataStruct.address1 = local.qryGetClientData.address1;
						local.dataStruct.address2 = local.qryGetClientData.address2;
						local.dataStruct.city = local.qryGetClientData.city;
						local.dataStruct.state = local.qryGetClientData.state;
						local.dataStruct.postalCode = local.qryGetClientData.postalCode;
						local.dataStruct.email = local.qryGetClientData.email;
						local.dataStruct.homePhone = local.qryGetClientData.homePhone;
						local.dataStruct.cellPhone = local.qryGetClientData.cellPhone;
						local.dataStruct.alternatePhone = local.qryGetClientData.alternatePhone;
						local.dataStruct.homePhoneE164 = local.qryGetClientData.homePhoneE164;
						local.dataStruct.cellPhoneE164 = local.qryGetClientData.cellPhoneE164;
						local.dataStruct.alternatePhoneE164 = local.qryGetClientData.alternatePhoneE164;
						local.dataStruct.clientParentID = arguments.event.getValue('clientParentID',0);
						if(val(local.qryGetClientData.clientParentID))
							local.dataStruct.clientParentID = local.qryGetClientData.clientParentID;
						/* Rep Data */
						local.dataStruct.repID = arguments.event.getValue('repID',0);
						if(val(local.qryGetClientData.repID))
							local.dataStruct.repID = local.qryGetClientData.repID;
						local.dataStruct.repFirstName = local.qryGetClientData.repFirstName;
						local.dataStruct.repLastName = local.qryGetClientData.repLastName;
						local.dataStruct.relationToClient = local.qryGetClientData.relationToClient;
						local.dataStruct.repAddress1 = local.qryGetClientData.repAddress1;
						local.dataStruct.repAddress2 = local.qryGetClientData.repAddress2;
						local.dataStruct.repCity = local.qryGetClientData.repCity;
						local.dataStruct.repState = local.qryGetClientData.repState;
						local.dataStruct.repPostalCode = local.qryGetClientData.repPostalCode;
						local.dataStruct.repEmail = local.qryGetClientData.repEmail;
						local.dataStruct.repHomePhone = local.qryGetClientData.repHomePhone;
						local.dataStruct.repCellPhone = local.qryGetClientData.repCellPhone;
						local.dataStruct.repAlternatePhone = local.qryGetClientData.repAlternatePhone;
						local.dataStruct.repHomePhoneE164 = local.qryGetClientData.repHomePhoneE164;
						local.dataStruct.repCellPhoneE164 = local.qryGetClientData.repCellPhoneE164;
						local.dataStruct.repAlternatePhoneE164 = local.qryGetClientData.repAlternatePhoneE164;
						local.dataStruct.repParentID = arguments.event.getValue('repParentID',0);
						if(val(local.qryGetClientData.repParentID))
							local.dataStruct.repParentID = local.qryGetClientData.repParentID;
						/* Referral Data */
						local.dataStruct.clientReferralID = local.qryGetClientData.clientReferralID;
						local.dataStruct.sourceID = local.qryGetClientData.sourceID;
						local.sourceName = local.qryGetClientData.sourceName;
						local.otherSource = local.qryGetClientData.otherSource;
						local.counselorName = local.qryGetMemberInfo.firstName & " " & local.qryGetMemberInfo.lastName;
						local.dataStruct.qryGetLawyerNotes = local.objAdminReferrals.getReferralNotes(referralID=local.dataStruct.referralID,clientReferralID=local.dataStruct.clientReferralID,noteType='A');
						local.dataStruct.typeID = local.qryGetClientData.referralTypeID;
						local.dataStruct.agencyID = local.qryGetClientData.agencyID;	
						local.agencyName = local.qryGetClientData.agencyName;
						local.dataStruct.communicateLanguageID = local.qryGetClientData.communicateLanguageID;	
						local.dataStruct.languageName = local.qryGetClientData.languageName;
						local.dataStruct.issueDesc = local.qryGetClientData.issueDesc;	
						local.dataStruct.sendSurvey = arguments.event.getValue('sendSurvey',0);
						if(val(local.qryGetClientData.sendSurvey))
							local.dataStruct.sendSurvey = local.qryGetClientData.sendSurvey;
						local.dataStruct.sendNewsBlog = arguments.event.getValue('sendNewsBlog',0);
						if(val(local.qryGetClientData.sendNewsBlog))
							local.dataStruct.sendNewsBlog = local.qryGetClientData.sendNewsBlog;
						local.dataStruct.primPanelID = local.qryGetClientData.primPanelID;
						local.dataStruct.primPanelName = local.qryGetClientData.primPanelName;
						local.dataStruct.statusID = local.qryGetClientData.statusID;
						local.dataStruct.feeTypeID = local.qryGetClientData.feeTypeID;
						local.dataStruct.statusName = local.qryGetClientData.statusName;
						local.dataStruct.isConsultation = local.qryGetClientData.isConsultation;
						local.dataStruct.collectClientFeeFE = local.dataStruct.qryReferralSettings.collectClientFeeFE;
						local.dataStruct.allowFeeDiscrepancy = local.dataStruct.qryReferralSettings.allowFeeDiscrepancy;
						local.dataStruct.feeDiscrepancyAmt = local.dataStruct.qryReferralSettings.feeDiscrepancyAmt;
						local.dataStruct.collectClientFeeFEOverrideTxt = local.dataStruct.qryReferralSettings.collectClientFeeFEOverrideTxt;
						if(local.dataStruct.collectClientFeeFE){
							local.dataStruct.qryGetConsultationFees = local.objAdminReferrals.getConsultationFees(clientReferralID=local.dataStruct.clientReferralID,orgID=val(arguments.event.getValue('mc_siteinfo.orgid')),memberid=local.useMID);
							local.dataStruct.qryGetConsultationFeesTotals = getConsultationFeesTotals(qryItems=local.dataStruct.qryGetConsultationFees);
						}
						local.dataStruct.referralCanEditClient = local.qryGetClientData.referralCanEditClient;
						local.isReferred = local.qryGetClientData.isReferred;	
						local.isAgency = local.qryGetClientData.isAgency; 
						local.isPending	= local.qryGetClientData.isPending;
						local.dataStruct.isClosed = local.qryGetClientData.isClosed;
						local.dataStruct.cannotReopen = local.qryGetClientData.cannotReopen;
						local.dataStruct.isRetainedCase = local.qryGetClientData.isRetainedCase;
						local.clientReferralType = local.qryGetClientData.clientReferralType;
						local.isLawyerReferral = local.qryGetClientData.isLawyerReferral; 
						local.isAgencyReferral = local.qryGetClientData.isAgencyReferral; 
						local.dataStruct.memberID = local.qryGetClientData.memberID;
						local.dataStruct.memberName = local.qryGetClientData.memberName;
						local.dataStruct.callUID = local.qryGetClientData.callUID;
						local.dataStruct.referralPageTitle = "Edit Client Referral ## " & local.dataStruct.clientReferralID & " | " & local.clientName;	
						local.dataStruct.labelTDwidth = "18%";
						local.dataStruct.referralFeePercent = getReferralFeePercent(panelID=val(local.dataStruct.primPanelID), referralID=local.dataStruct.referralID).cumulativeFeePercent;
						/* Case Data */
						local.dataStruct.caseid = 0;
						local.dataStruct.dateCaseClosed = "";
						local.dataStruct.dspMakePayment = 0;
						
						local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields");
						local.referralAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Referrals', siteID=local.siteid);
						local.thisPostTypeFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=local.siteID, resourceType='ClientReferrals', areaName='ClientReferrals', csrid=local.referralAdminSiteResourceID, detailID=0, hideAdminOnly=0);
						local.arrThisPostTypeFields = xmlParse(local.thisPostTypeFieldsXML.returnXML).xmlRoot.xmlChildren;
						local.dataStruct.arrClientRefCustomFields = getClientReferralCustomFieldWithData(itemID=local.dataStruct.clientReferralID, itemType='ClientRefCustom', arrResourceFields=local.arrThisPostTypeFields, objCustomFields=local.objResourceCustomFields);
						local.dataStruct.strAttorneyFields = local.objResourceCustomFields.renderResourceFields(siteID=local.siteID, viewMode=local.viewDirectory, resourceType='Referrals', areaName='Attorney',
							csrid=local.referralAdminSiteResourceID, detailID=0, hideAdminOnly=1, itemType='AttorneyCustom', itemID=local.dataStruct.clientReferralID, trItemType='', trApplicationType='');
						local.viewToUse = "referrals/#local.viewDirectory#/frm_clientReferral";
					</cfscript>
				</cfcase>
				<cfcase value="editReferrals">
					<cfscript>
						local.isPaymentInitialized = arguments.event.getValue('initPay',0);
						local.dataStruct.view = local.viewDirectory;
						local.checkedReferral = arguments.event.getValue('checkedReferral','');
						local.dataStruct.siteID = arguments.event.getValue('mc_siteinfo.siteid');
						local.dataStruct.siteCode = arguments.event.getValue('mc_siteinfo.sitecode');
						local.dataStruct.useMID = local.useMID;
						local.referralFilterSession = application.mcCacheManager.sessionGetValue(keyname='referralFilterSession', defaultValue={});
						if(local.isPaymentInitialized eq 1 && local.checkedReferral neq ''){
							local.qryGetMemberReferrals = getMemberRFPXMLData(	
								refDateRange=local.referralFilterSession.refDateRange,
								filterReferralID=local.referralFilterSession.filterReferralID,
								filterClientLastName=local.referralFilterSession.filterClientLastName,
								filterClientFirstName=local.referralFilterSession.filterClientFirstName,
								checkedReferral=local.checkedReferral);

							local.dataStruct.amountToChargeInProfile = 0;
							for (local.memberReferralObj in local.qryGetMemberReferrals){
								local.dataStruct.amountToChargeInProfile = local.dataStruct.amountToChargeInProfile + local.memberReferralObj.totalFeesDue;
							}
							local.dataStruct.noPaymentStatement = 'No Due for payment';
							local.dataStruct.checkedReferral = local.checkedReferral;
							<!--- merchant profiles allowed--->
							local.dataStruct.paymentGateways = getAllowedPaymentProfiles(profileID=0, clientReferralIDList=local.checkedReferral);

							local.dataStruct.stateIDForTax = arguments.event.getValue('stateIDForTax',0);
							local.dataStruct.zipForTax = arguments.event.getValue('zipForTax','');
							
							local.dataStruct.qryGetMemberReferrals = local.qryGetMemberReferrals;
							local.dataStruct.payAction = "#arguments.event.getValue('mainurl')#&ra=editReferrals&initPay=2";
							local.viewToUse = "referrals/#local.viewDirectory#/dsp_referralsCart";	
						}else if(local.isPaymentInitialized eq 2 && local.checkedReferral neq ''){
							local.dataStruct.profileid = arguments.event.getValue('profileid','');
							local.dataStruct.err = 1;
							local.qryGetMemberReferrals = getMemberRFPXMLData(	
								refDateRange=local.referralFilterSession.refDateRange,
								filterReferralID=local.referralFilterSession.filterReferralID,
								filterClientLastName=local.referralFilterSession.filterClientLastName,
								filterClientFirstName=local.referralFilterSession.filterClientFirstName,
								checkedReferral=local.checkedReferral);

							local.dataStruct.amountToChargeInProfile = 0;
							for (local.memberReferralObj in local.qryGetMemberReferrals){
								local.dataStruct.amountToChargeInProfile = local.dataStruct.amountToChargeInProfile + local.memberReferralObj.totalFeesDue;
							}
							if(local.dataStruct.profileid neq '' && local.dataStruct.amountToChargeInProfile gt 0){
								local.paymentObj = payOutstandingReferralFees(event=arguments.event, referralID=local.dataStruct.referralID, profileID=local.dataStruct.profileid, totalPrice=local.dataStruct.amountToChargeInProfile, 
													useMID=local.useMID, clientReferralIDList=local.checkedReferral);
								
								local.allocationSuccess = 1;
								
								if(local.paymentObj.success){
									// valid payment
									if (arrayLen(local.paymentObj.arrPaymentTransactions)) {
										// supports client consultation fees
										if (local.dataStruct.qryReferralSettings.collectClientFeeFE) {
											recordClientFeeAllocation(orgID=arguments.event.getValue('mc_siteinfo.orgid'), siteID=arguments.event.getValue('mc_siteinfo.siteID'), referralID=local.dataStruct.referralID, 
												arrPaymentTransactions=local.paymentObj.arrPaymentTransactions, clientReferralIDList=local.checkedReferral, useMID=local.useMID);
										}

										for (local.memberReferralObj in local.qryGetMemberReferrals){
											if (local.memberReferralObj.amtToBePaidTotal gt 0) {
												var allocObj = recordReferralFeeAllocation(event=arguments.event, paymentObj=local.paymentObj, rfpData=local.memberReferralObj, useMID=local.useMID);
												if(!allocObj.success && local.allocationSuccess neq 0){
													local.allocationSuccess = 0;
												}
											}
										}
									}

									if(local.allocationSuccess eq 1){
										sendReferralReceiptEmail(event=arguments.event, paymentObj=local.paymentObj, rfpData=local.qryGetMemberReferrals, useMID=local.useMID, isOfflinePayment=local.paymentObj.isOfflinePayment);
										local.dataStruct.isOfflinePayment = local.paymentObj.isOfflinePayment;
										local.dataStruct.memberReferralObj = local.memberReferralObj;
										local.dataStruct.mainurl = arguments.event.getValue('mainurl');
										local.dataStruct.err = 0;
										local.dataStruct.qryGetMemberReferrals = local.qryGetMemberReferrals;
										local.dataStruct.paymentObj = local.paymentObj;
										application.mcCacheManager.sessionDeleteValue(keyname='referralFilterSession');
										local.viewToUse = "referrals/#local.viewDirectory#/referral_fee_payment_receipt";
									}
								}
							}
							if(local.dataStruct.err eq 1){								
								local.dataStruct.mainurl = arguments.event.getValue('mainurl');
								local.viewToUse = "referrals/#local.viewDirectory#/referral_fee_payment_receipt";
							}
						}else{
							local.dataStruct.mainurl = arguments.event.getValue('mainurl');
							local.dataStruct.err = 1;
							local.viewToUse = "referrals/#local.viewDirectory#/referral_fee_payment_receipt";
						}
						
					</cfscript>
				</cfcase>
				<cfcase value="editCase">
					<cfscript>
						local.dataStruct.msgText = "";
						local.dataStruct.isThisRefRecord = 0;
						local.dataStruct.isThisCaseRecord = 1;						
						local.dataStruct.clientReferralID = arguments.event.getValue('clientReferralID');
						local.qryCountries = application.objCommon.getCountries();
						local.countryCode = QueryFilter(local.qryCountries, function(thisRow) { return arguments.thisRow.countryID EQ application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).defaultCountryID; }).countryCode;
						
						if (arguments.event.valueExists('saveClientBtn')){
							saveClientReferral(event=arguments.event);							
							if (arguments.event.valueExists('collectedFee') and val(arguments.event.getValue('collectedFee'))){
								local.saleStruct = structNew();
								
								local.saleStruct = recordSale(event=arguments.event);
								doEmailCaseFeeCollect(event=arguments.event);
								if(local.dataStruct.qryReferralSettings.allowFeeDiscrepancy)
									feeDiscrepancyStatusUpdate(clientReferralID=local.dataStruct.clientReferralID);									
									local.objAdminReferrals.reprocessConditions(orgID=local.orgID, clientReferralID=local.dataStruct.clientReferralID);
								application.objCommon.redirect('#local.dataStruct.mainurl#&ra=editCase&clientReferralID=#local.dataStruct.clientReferralID#');	
							}
							local.currentStatusID = val(arguments.event.getValue('prevStatusID',0));
							local.qryRefClient = local.objAdminReferrals.getClient(clientReferralID=local.dataStruct.clientReferralID);
							if (not local.currentStatusID)
								local.currentStatusID = local.qryRefClient.statusID;
							local.qryGetCurrentstatus = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, statusID=local.currentStatusID).qryStatus;							
							local.objAdminReferrals.updateClientCase(arguments.event);
							if(val(arguments.event.getValue('statusID')) neq val(local.currentStatusID)){
								doEmailCaseStatusUpdate(arguments.event);								
								local.qryGetNewStatus = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, statusID=val(arguments.event.getValue('statusID'))).qryStatus;
								local.objAdminReferrals.updateClientReferralStatus(clientReferralID=local.dataStruct.clientReferralID, statusID=val(arguments.event.getValue('statusID')));
								
								// charge consultation fee
								if (local.dataStruct.qryReferralSettings.collectClientFeeFE EQ 1
										AND val(local.qryGetNewStatus.isConsultation) 
										AND NOT local.objAdminReferrals.isClientReferralFeeSaleExists(orgID=local.orgID, clientReferralID=local.dataStruct.clientReferralID)
										AND val(local.qryRefClient.panelID)) {
									// panel details
									local.qryRefPanelDetails = local.objAdminReferrals.getPanelByID(panelID=local.qryRefClient.panelID);
									
									if(val(local.qryRefPanelDetails.clientReferralAmount)) {
										arguments.event.setValue('clientReferralAmount',local.qryRefPanelDetails.clientReferralAmount);
										local.consultationsaleStruct = structNew();
										local.consultationsaleStruct = this.objAdminReferrals.recordConsultationSale(
											amount=arguments.event.getValue('clientReferralAmount',0.00),
											panelID=local.qryRefClient.panelID,
											referralID=local.dataStruct.referralID,
											clientReferralID=arguments.event.getValue('clientReferralID'),
											callUID=local.qryRefClient.callUID,
											siteID=local.siteID,
											orgID=local.orgID,
											memberID=int(local.useMID) ,
											clientname=local.qryRefClient.firstName & " " &  local.qryRefClient.lastName
										);
									}
								}
								
								local.thisChange =  [{ITEM="Status",OLDVALUE=local.qryGetCurrentstatus.statusName,NEWVALUE=local.qryGetNewStatus.statusName}];
								createObject('component','model.system.platform.history').addReferralUpdateHistory(orgID=int(arguments.event.getValue('mc_siteinfo.orgid')),
									clientReferralID=int(local.dataStruct.clientReferralID), actorMemberID=int(local.useMID), 
									mainMessage="Referral Status Changed (Member)", changes=local.thisChange);									
								if(local.qryGetNewStatus.isClosed){
									local.objAdminReferrals.reprocessConditions(orgID=local.orgID, clientReferralID=local.dataStruct.clientReferralID);
									application.objCommon.redirect(local.dataStruct.mainurl);
								}
							}
							// reprocess conditions after all updations
							local.objAdminReferrals.reprocessConditions(orgID=local.orgID, clientReferralID=local.dataStruct.clientReferralID);
						}
						
						local.rc = arguments.event.getCollection();
						local.qryGetReferralSources = local.objAdminReferrals.getReferralSources(referralID=local.dataStruct.referralID);
						local.qryGetMemberInfo = application.objMember.getMemberInfo(memberid=local.useMID);
						local.qryGetReferralTypes = local.objAdminReferrals.getReferralTypes(event=arguments.event);
						local.dataStruct.qryGetLanguages 	= local.objAdminReferrals.getReferralLanguages(local.dataStruct.referralID);
						arguments.event.setValue('referralID',local.dataStruct.referralID);
						local.qryGetClientData = getClient(clientReferralID=local.dataStruct.clientReferralID);
						if (not val(local.qryGetClientData.isClosed))
							local.params = {referralID=local.dataStruct.referralID, isRetainedCase=1, dspFrontEnd=1};
						else
							local.params = {referralID=local.dataStruct.referralID, isClosed=1, isRetainedCase=1, dspFrontEnd=1};
						
						local.caseStatusResponseStruct = local.objAdminReferrals.getClientReferralStatus(argumentCollection=local.params);
						if(local.caseStatusResponseStruct.success == true){
							local.dataStruct.qryGetCaseStatuses = local.caseStatusResponseStruct.qryStatus;
						} else {
							savecontent variable="local.data" { writeOutput('					
								<div class="alert alert-info alert-dismissible fade show" role="alert">
									Retained Closed Referral Statuses are not defined. Please contact administrator.
								</div>		
							'); }	
							return returnAppStruct(local.data,"echo");
						}
						
						if (len(local.dataStruct.qryReferralSettings.allowFeeTypeMgmt) and local.dataStruct.qryReferralSettings.allowFeeTypeMgmt) {
							local.dataStruct.qryGetFeeTypes = local.objAdminReferrals.getFeeTypes(referralID=local.dataStruct.referralID, isActive=1 );
						}
						local.dataStruct.qryGetCaseFees = local.objAdminReferrals.getCaseFees(caseID=val(local.qryGetClientData.caseID));
						local.dataStruct.qryGetCaseFeesTotals = getFeesTotals(qryItems=local.dataStruct.qryGetCaseFees);
						local.dataStruct.applyPayment = 0;
						if (arguments.event.valueExists('applyPayment'))
							local.dataStruct.applyPayment = 1;						
						/* Client Data */
						local.dataStruct.countryCode = local.countryCode;
						local.dataStruct.clientID = local.qryGetClientData.clientID;
						local.dataStruct.firstName = local.qryGetClientData.firstName;
						local.dataStruct.middleName = local.qryGetClientData.middleName;
						local.dataStruct.lastName = local.qryGetClientData.lastName;
						local.clientName = local.qryGetClientData.clientName;
						local.dataStruct.businessName = local.qryGetClientData.businessName;
						local.dataStruct.address1 = local.qryGetClientData.address1;
						local.dataStruct.address2 = local.qryGetClientData.address2;
						local.dataStruct.city = local.qryGetClientData.city;
						local.dataStruct.state = local.qryGetClientData.state;
						local.dataStruct.postalCode = local.qryGetClientData.postalCode;
						local.dataStruct.email = local.qryGetClientData.email;
						local.dataStruct.homePhone = local.qryGetClientData.homePhone;
						local.dataStruct.cellPhone = local.qryGetClientData.cellPhone;
						local.dataStruct.alternatePhone = local.qryGetClientData.alternatePhone;
						local.dataStruct.homePhoneE164 = local.qryGetClientData.homePhoneE164;
						local.dataStruct.cellPhoneE164 = local.qryGetClientData.cellPhoneE164;
						local.dataStruct.alternatePhoneE164 = local.qryGetClientData.alternatePhoneE164;
						local.dataStruct.clientParentID = arguments.event.getValue('clientParentID',0);
						if(val(local.qryGetClientData.clientParentID))
							local.dataStruct.clientParentID = local.qryGetClientData.clientParentID;
						/* Rep Data */
						local.dataStruct.repID = arguments.event.getValue('repID',0);
						if(val(local.qryGetClientData.repID))
							local.dataStruct.repID = local.qryGetClientData.repID;
						local.dataStruct.repFirstName = local.qryGetClientData.repFirstName;
						local.dataStruct.repLastName = local.qryGetClientData.repLastName;
						local.dataStruct.relationToClient = local.qryGetClientData.relationToClient;
						local.dataStruct.repAddress1 = local.qryGetClientData.repAddress1;
						local.dataStruct.repAddress2 = local.qryGetClientData.repAddress2;
						local.dataStruct.repCity = local.qryGetClientData.repCity;
						local.dataStruct.repState = local.qryGetClientData.repState;
						local.dataStruct.repPostalCode = local.qryGetClientData.repPostalCode;
						local.dataStruct.repEmail = local.qryGetClientData.repEmail;
						local.dataStruct.repHomePhone = local.qryGetClientData.repHomePhone;
						local.dataStruct.repCellPhone = local.qryGetClientData.repCellPhone;
						local.dataStruct.repAlternatePhone = local.qryGetClientData.repAlternatePhone;
						local.dataStruct.repHomePhoneE164 = local.qryGetClientData.repHomePhoneE164;
						local.dataStruct.repCellPhoneE164 = local.qryGetClientData.repCellPhoneE164;
						local.dataStruct.repAlternatePhoneE164 = local.qryGetClientData.repAlternatePhoneE164;
						local.dataStruct.repParentID = arguments.event.getValue('repParentID',0);
						if(val(local.qryGetClientData.repParentID))
							local.dataStruct.repParentID = local.qryGetClientData.repParentID;
						/* Referral Data */
						local.dataStruct.clientReferralID = local.qryGetClientData.clientReferralID;
						local.dataStruct.sourceID = local.qryGetClientData.sourceID;
						local.sourceName = local.qryGetClientData.sourceName;
						local.otherSource = local.qryGetClientData.otherSource;
						local.counselorName = local.qryGetMemberInfo.firstName & " " & local.qryGetMemberInfo.lastName;
						local.dataStruct.qryGetLawyerNotes = local.objAdminReferrals.getReferralNotes(referralID=local.dataStruct.referralID,clientReferralID=local.dataStruct.clientReferralID,noteType="A");
						local.dataStruct.typeID = local.qryGetClientData.referralTypeID;
						local.dataStruct.agencyID = local.qryGetClientData.agencyID;
						local.agencyName = local.qryGetClientData.agencyName;
						local.dataStruct.communicateLanguageID = local.qryGetClientData.communicateLanguageID;
						local.dataStruct.languageName = local.qryGetClientData.languageName;
						local.dataStruct.issueDesc = local.qryGetClientData.issueDesc;	
						local.dataStruct.sendSurvey = arguments.event.getValue('sendSurvey',0);
						if(val(local.qryGetClientData.sendSurvey))
							local.dataStruct.sendSurvey = local.qryGetClientData.sendSurvey;
						local.dataStruct.sendNewsBlog = arguments.event.getValue('sendNewsBlog',0);
						if(val(local.qryGetClientData.sendNewsBlog))
							local.dataStruct.sendNewsBlog = local.qryGetClientData.sendNewsBlog;
						local.dataStruct.statusID = local.qryGetClientData.statusID;
						local.dataStruct.feeTypeID = local.qryGetClientData.feeTypeID;
						local.dataStruct.statusName = local.qryGetClientData.statusName;
						local.dataStruct.referralCanEditClient = local.qryGetClientData.referralCanEditClient;
						local.isReferred = local.qryGetClientData.isReferred;	
						local.isAgency = local.qryGetClientData.isAgency; 
						local.isPending	= local.qryGetClientData.isPending;
						local.dataStruct.isClosed = local.qryGetClientData.isClosed;
						local.dataStruct.cannotReopen = local.qryGetClientData.cannotReopen;
						local.dataStruct.isRetainedCase = local.qryGetClientData.isRetainedCase;
						local.clientReferralType = local.qryGetClientData.clientReferralType;
						local.isLawyerReferral = local.qryGetClientData.isLawyerReferral; 
						local.isAgencyReferral = local.qryGetClientData.isAgencyReferral; 
						local.dataStruct.memberID = local.qryGetClientData.memberID;
						local.dataStruct.memberName = local.qryGetClientData.memberName;
						local.dataStruct.callUID = local.qryGetClientData.callUID;
						local.dataStruct.referralPageTitle = "Edit Case ## " & local.dataStruct.clientReferralID & " | " & local.clientName;	
						if(local.dataStruct.applyPayment)
							local.dataStruct.referralPageTitle = "Fee Information";
						local.dataStruct.labelTDwidth = "18%";
						local.dataStruct.primPanelID = local.qryGetClientData.primPanelID;
						local.dataStruct.primPanelName = local.qryGetClientData.primPanelName;
						local.dataStruct.referralAmount = local.qryGetClientData.referralAmount;
						local.dataStruct.referralFeePercent = getReferralFeePercent(panelID=val(local.dataStruct.primPanelID), referralID=local.dataStruct.referralID).cumulativeFeePercent;
						local.dataStruct.isConsultation = local.qryGetClientData.isConsultation;
						local.dataStruct.collectClientFeeFE = local.dataStruct.qryReferralSettings.collectClientFeeFE;
						local.dataStruct.allowFeeDiscrepancy = local.dataStruct.qryReferralSettings.allowFeeDiscrepancy;
						local.dataStruct.feeDiscrepancyAmt = local.dataStruct.qryReferralSettings.feeDiscrepancyAmt;
						local.dataStruct.collectClientFeeFEOverrideTxt = local.dataStruct.qryReferralSettings.collectClientFeeFEOverrideTxt;
						/* Case Data */
						local.dataStruct.caseID = local.qryGetClientData.caseID;
						local.dataStruct.dateCaseClosed = local.qryGetClientData.dateCaseClosed;
						local.dataStruct.dspMakePayment = 0;
						local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields");
						local.referralAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Referrals', siteID=local.siteid);
						local.thisPostTypeFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=local.siteID, resourceType='ClientReferrals', areaName='ClientReferrals', csrid=local.referralAdminSiteResourceID, detailID=0, hideAdminOnly=0);
						local.arrThisPostTypeFields = xmlParse(local.thisPostTypeFieldsXML.returnXML).xmlRoot.xmlChildren;
						local.dataStruct.arrClientRefCustomFields = getClientReferralCustomFieldWithData(itemID=local.dataStruct.clientReferralID, itemType='ClientRefCustom', arrResourceFields=local.arrThisPostTypeFields, objCustomFields=local.objResourceCustomFields);
						local.dataStruct.strAttorneyFields = local.objResourceCustomFields.renderResourceFields(siteID=local.siteID, viewMode=local.viewDirectory, resourceType='Referrals', areaName='Attorney',
							csrid=local.referralAdminSiteResourceID, detailID=0, hideAdminOnly=1, itemType='AttorneyCustom', itemID=local.dataStruct.clientReferralID, trItemType='', trApplicationType='');
						if(local.dataStruct.collectClientFeeFE){
							local.dataStruct.qryGetConsultationFees = local.objAdminReferrals.getConsultationFees(clientReferralID=local.dataStruct.clientReferralID,orgID=val(arguments.event.getValue('mc_siteinfo.orgid')),memberid=local.useMID);
							local.dataStruct.qryGetConsultationFeesTotals = getConsultationFeesTotals(qryItems=local.dataStruct.qryGetConsultationFees);
						}
					</cfscript>
					<cfloop query="local.dataStruct.qryGetCaseFees">
						<cfif local.dataStruct.qryGetCaseFees.amtToBePaid>
							<cfset local.dataStruct.dspMakePayment = 1 />
							<cfbreak />
						</cfif>
					</cfloop>
					<cfset local.viewToUse = "referrals/#local.viewDirectory#/frm_clientReferral" />
				</cfcase>
				<cfcase value="referralsGrid">
					<cfreturn returnAppStruct(local.objReferralXML.memberReferralsXML(event=arguments.event),"echo") />
				</cfcase>
				<cfcase value="casesGrid">
					<cfreturn returnAppStruct(local.objReferralXML.memberCasesXML(event=arguments.event),"echo") />
				</cfcase>
				<cfcase value="historyGrid">
					<cfreturn returnAppStruct(local.objReferralXML.memberReferralHistoryXML(event=arguments.event),"echo") />
				</cfcase>	
				<cfcase value="rfpGrid">
					<cfreturn returnAppStruct(local.objReferralXML.memberRFPXML(event=arguments.event),"echo") />
				</cfcase>	
				<cfcase value="downloadMonthlyReport">
					<cfscript>
						local.returnPDFStruct = doGenerateReferralReport(orgID=local.orgID, siteID=local.siteID, memberID=local.dataStruct.memberLoggedInID);						
						application.objDocDownload.doDownloadDocument(sourceFilePath=local.returnPDFStruct.referralFilePath, displayName=ListLast(local.returnPDFStruct.referralFilePath,"/"), deleteSourceFile=1);
					</cfscript>
				</cfcase>
				<cfcase value="printMonthlyReport">
					<cfset local.returnPDFStruct = doGenerateReferralReport(orgID=local.orgID, siteID=local.siteID, memberID=local.dataStruct.memberLoggedInID, type="HTML")>
					<cfoutput>#serializeJSON(local.returnPDFStruct)#</cfoutput><cfabort>
				</cfcase>
				<cfcase value="closeReferral">
					<cfscript>
						if(arguments.event.getValue('updateStatusOnly',0)){
							saveClientReferralStatus(event=arguments.event);
						} else
							saveClientReferral(event=arguments.event);
						
						local.objAdminReferrals.reprocessConditions(orgID=local.orgID, clientReferralID=val(arguments.event.getValue('clientReferralID')));

						doEmailCloseReferral(event=arguments.event);
						local.currentStatusID = val(arguments.event.getValue('prevStatusID',0));
						local.qryGetClient = local.objAdminReferrals.getClient(clientReferralID=val(arguments.event.getValue('clientReferralID')));
						if (not local.currentStatusID)
							local.currentStatusID = local.qryGetClient.statusID;
						local.qryGetCurrentstatus = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, statusID=local.currentStatusID).qryStatus;	
						local.qryGetNewStatus = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, statusID=val(arguments.event.getValue('statusID'))).qryStatus;				
						local.thisChange =  [{ITEM="Status",OLDVALUE=local.qryGetCurrentstatus.statusName,NEWVALUE=local.qryGetNewStatus.statusName}];
						createObject('component','model.system.platform.history').addReferralUpdateHistory(orgID=int(local.orgID),
							clientReferralID=val(arguments.event.getValue('clientReferralID')), actorMemberID=int(local.useMID), 
							mainMessage="Referral Status Changed (Member)", changes=local.thisChange);	
						
						// charge consultation fee
						if (local.dataStruct.qryReferralSettings.collectClientFeeFE EQ 1
								AND val(local.qryGetNewStatus.isConsultation) 
								AND NOT local.objAdminReferrals.isClientReferralFeeSaleExists(orgID=local.orgID,clientReferralID=arguments.event.getValue('clientReferralID'))
								AND val(local.qryGetClient.panelID)) {
							// panel details
							local.qryGetPanelDetails = local.objAdminReferrals.getPanelByID(panelID=local.qryGetClient.panelID);
							
							if(val(local.qryGetPanelDetails.clientReferralAmount) ){
								arguments.event.setValue('clientReferralAmount',local.qryGetPanelDetails.clientReferralAmount);
								local.consultationsaleStruct = structNew();
								local.consultationsaleStruct = this.objAdminReferrals.recordConsultationSale(
									amount=arguments.event.getValue('clientReferralAmount',0.00),
									panelID=local.qryGetClient.panelID,
									referralID=local.dataStruct.referralID,
									clientReferralID=arguments.event.getValue('clientReferralID'),
									callUID=local.qryGetClient.callUID,
									siteID=local.siteID,
									orgID=local.orgID,
									memberID=int(local.useMID) ,
									clientname=local.qryGetClient.firstName & " " &  local.qryGetClient.lastName
								);
							}
						}

						application.objCommon.redirect('#local.dataStruct.mainurl#');
					</cfscript>
				</cfcase>	
				<cfcase value="selectReferralStatus">
					<cfscript>
						local.qryGetReferralStatus = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, isClosed=1, isClosedByLawyer=1, isRetainedCase=0, dspFrontEnd=1,isActive=1).qryStatus;
					</cfscript>
					<cfsavecontent variable="local.selectFormJS">
						<cfoutput>
						<script language="JavaScript">
							$(function() {					
								$('##retainCaseBtn').click(function(){
									var errorMsg = "";
									if($('##thisStatusID').val().length == 0) {
										errorMsg += '- Select Referral Status.\n';
									}
									<cfif arguments.event.getValue('grid',0)>
										if (errorMsg.length > 0) {
											alert("There were errors with your submission.\n\n" + errorMsg);
										} else {
											var _statusID = $('##thisStatusID').val();
											$(".referralStatusForm").html('<img src="/assets/common/images/progress.gif" width="16" height="16" alt="Please wait..."> Please Wait...').load('/?event=cms.showResource&resID=#arguments.event.getValue('resID')#&ra=closeReferral&updateStatusOnly=1&clientreferralid=#arguments.event.getValue('clientReferralID')#&statusID='+_statusID, function(){
												top.closeBox();
												top.location.reload();
											});
										}
									<cfelse>
										errorMsg += top.validateClientReferral();

										if (errorMsg.length > 0) {
											alert("There were errors with your submission.\n\n" + errorMsg);
										} else {
											top.$('##statusID').val($('##thisStatusID').val());
											top.$("##frmClient").attr('action', '#local.dataStruct.mainurl#&ra=closeReferral');
											top.$('##frmClient').submit();
											top.closeBox();
										}
									</cfif>
								});
							});
						</script>
						</cfoutput>
					</cfsavecontent>
					<cfhtmlhead text="#application.objCommon.minText(local.selectFormJS)#" />
					<cfsavecontent variable="local.viewData">
						<cfoutput>
							<div class="referralStatusForm">
								<cfif local.viewDirectory eq 'responsive'>
									<div class="row-fluid">
										<h3>Select Referral Status</h3>							
									</div>
									<div class="form-group row-fluid">									
										<select name="thisStatusID" id="thisStatusID" class="form-control ">
											<option value="">Select Referral Status</option>
											<cfloop query="local.qryGetReferralStatus">
											<option value="#local.qryGetReferralStatus.clientReferralStatusID#">#local.qryGetReferralStatus.statusName#</option>
											</cfloop>
										</select>							
									</div>
									<div class="form-group row-fluid" style="text-align:left;">
										<button type="button" id="retainCaseBtn" class="btn">Submit</button>
										<button type="button" id="closeBtn" class="btn" onClick="top.closeBox();">Cancel</button>							
									</div>							
								<cfelse>
									<div style="padding:5px 0 5px 20px;">
										<h3>Select Referral Status</h3>
										<select name="thisStatusID" id="thisStatusID">
											<option value="">Select Referral Status</option>
											<cfloop query="local.qryGetReferralStatus">
											<option value="#local.qryGetReferralStatus.clientReferralStatusID#">#local.qryGetReferralStatus.statusName#</option>
											</cfloop>
										</select>
										<br /><br />
										<div align="left">
											<button type="button" id="retainCaseBtn" class="tsAppBodyButton">Submit</button>
											<button type="button" id="closeBtn" class="tsAppBodyButton" onClick="top.closeBox();">Cancel</button>
										</div>
									</div>
								</cfif>
							</div>
						</cfoutput>
					</cfsavecontent>
						
				</cfcase>
				<cfcase value="viewCaseStatement">
					<cfset local.clientReferralID = arguments.event.getValue('clientReferralID','0') />
					<cfset local.objAdminReferrals = CreateObject('component', 'model.admin.referrals.referrals') />
					<cfset local.qryGetReferralData = local.objAdminReferrals.getReferralData(orgID=arguments.event.getValue('mc_siteInfo.orgID'),clientReferralID=arguments.event.getValue('clientReferralID')) />
					<cfset local.dataStruct.link.viewCaseStatement = "#local.dataStruct.mainurl#&ra=viewCaseStatement&clientReferralID=#local.clientReferralID#&mode=direct" />
					<cfset local.qryGetCaseFees = local.objAdminReferrals.getCaseFees(caseID=local.qryGetReferralData.caseID) />
					<cfset local.qryGetCaseFeesTotals = getFeesTotals(qryItems=local.qryGetCaseFees) />				

					<cfswitch expression="#arguments.event.getValue('refaction','')#">
						<cfcase value="emailCase">
							<cfif local.qryGetReferralData.recordcount and isValid("regex",trim(arguments.event.getValue('_email','')),application.regEx.email)>
								<cfset doEmailCaseStatement(event=arguments.event) />
							</cfif>
							<cfsavecontent variable="local.confMessage">						
							<cfoutput>
							<script language="javascript">
							top.$.colorbox( {onCleanup:top.closeBox, html:'<div style="margin:10px;"><h4>E-mail sent.</h4></div>', overlayClose:false} );
							</script>					
							</cfoutput>
							</cfsavecontent>
							<cfreturn returnAppStruct(local.confMessage,"echo") />					
						</cfcase>
						<cfcase value="loadStatement">
							<cfset local.returnPDFStruct = doGenerateCaseStatement(event=arguments.event, refStruct=local) />
							<cfset application.objDocDownload.doDownloadDocument(sourceFilePath=local.returnPDFStruct.referralFilePath, displayName=ListLast(local.returnPDFStruct.referralFilePath,"/"), deleteSourceFile=1)>
						</cfcase>							
						<cfdefaultcase>
							<cfif not local.qryGetReferralData.recordcount>
								<cfreturn returnAppStruct("That Case was not found.","echo") />
							</cfif>
							<cfset local.dataStruct.memberEmail = application.objMember.getMainEmail(memberid=local.qryGetReferralData.memberID).email />								
							<cfset local.viewToUse = "referrals/#local.viewDirectory#/frm_emailCaseStatement" /> 
						</cfdefaultcase>
					</cfswitch>
				</cfcase>
				<cfcase value="viewReceipt">
					<cfscript>					
						local.dataStruct.referralDate = local.referralDate;
						local.orderNumber = arguments.event.getValue('orderNumber','');
						local.caseid = arguments.event.getValue('caseid',0);
						local.dataStruct.qryOrderDetails = getCaseData(caseid=local.caseid);
						local.qryItems = local.objAdminReferrals.getCaseFeesForAcct(caseid=local.caseid,ordernumber=local.ordernumber);		
						local.referral = application.mcCacheManager.sessionGetValue(keyname='referral', defaultValue={});

						// invalid receipt
						if (NOT structCount(local.referral)) {
							return returnAppStruct("Invalid Receipt.","echo");
						}

						local.dataStruct.strRefPayment = { 
							"profileID": local.referral.referralMerchantProfile.profileID,
							"gatewayClass": local.referral.referralMerchantProfile.gatewayClass, 
							"paymentInstructions": getPaymentInstructions(profileID=val(local.referral.referralMerchantProfile.profileID))
						}

						local.dataStruct.argumentEvent = arguments.event;
						local.viewToUse = "referrals/#local.viewDirectory#/referral_receipt";
					</cfscript>
			
					<cfquery name="local.qryGetOrderTotal" dbtype="query">
						select sum(referralDues) as totalFees from [local].qryItems
					</cfquery>	
					<cfset local.dataStruct.qryItems= local.qryItems />	
					<cfset local.dataStruct.orderTotal = val(local.qryGetOrderTotal.totalFees) />									
				</cfcase>	
				<cfcase value="checkout">
					<cfscript>
						if(val(arguments.event.getValue('caseID',0))){
							local.orderNumber = createUUID();
							local.caseID = arguments.event.getValue('caseID',0);
							local.payFeeList = arguments.event.getValue('payFee','');
							/*--- update order ---*/
							cfloop(list="#local.payFeeList#",index="local.collectedFeeID") {
								updateOrder(local.caseID,local.orderNumber,local.collectedFeeID);
							}

							// tax info
							application.mcCacheManager.sessionSetValue(
								keyname='refCaseTaxInfo#local.caseID#', 
								value={ "stateIDForTax":arguments.event.getValue('stateIDForTax',0), "zipForTax":arguments.event.getValue('zipForTax','') }
							);

							/*--- redirect to buy now ---*/
							application.objCommon.redirect('/?pg=buyNow&item=referral-#local.caseID#|#local.orderNumber#');
						} else {
							application.objCommon.redirect('/?pg=referrals');
						}
					</cfscript>
				</cfcase>	
				<cfcase value="retainCase">
					<cfscript>
						// current status details
						local.qryClientRefStatusDetails = local.objAdminReferrals.getClientReferralStatusDetails(clientReferralID=val(arguments.event.getValue('clientReferralID')), referralID=local.dataStruct.referralID);

						// invalid
						if (local.qryClientRefStatusDetails.isClosed EQ 1 AND local.qryClientRefStatusDetails.cannotReopen EQ 1) {
							location("#local.dataStruct.mainurl#", "false");
						}
						
						local.msgText = "";
						local.caseID = 0;
						local.qryGetClient = local.objAdminReferrals.getClient(clientReferralID=val(arguments.event.getValue('clientReferralID')));
						local.currentStatusID = local.qryGetClient.statusID;
						
						if(arguments.event.getValue('updateStatusOnly',0)) {
							arguments.event.setValue('panelID',local.qryGetClient.panelID);
							arguments.event.setValue('memberLoggedInID',local.dataStruct.memberLoggedInID);
							arguments.event.setValue('newStatusID',arguments.event.getValue('statusID',0));
							saveClientReferralStatus(event=arguments.event);
						} else
							saveClientReferral(event=arguments.event);


						// log status changes
						if (local.currentStatusID NEQ arguments.event.getValue('statusID',0)) {
							local.qryGetCurrentstatus = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, statusID=local.currentStatusID).qryStatus;
							local.qryGetNewStatus = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, statusID=val(arguments.event.getValue('statusID'))).qryStatus;
							local.thisChange =  [{ITEM="Status",OLDVALUE=local.qryGetCurrentstatus.statusName,NEWVALUE=local.qryGetNewStatus.statusName}];
							createObject('component','model.system.platform.history').addReferralUpdateHistory(orgID=local.orgID, clientReferralID=val(arguments.event.getValue('clientReferralID')), 
								actorMemberID=local.useMID, mainMessage="Referral Status Changed (Member)", changes=local.thisChange);
						}
						
						local.caseExists = local.objAdminReferrals.checkCaseExists(clientReferralID=arguments.event.getValue('clientReferralID'));
						if(not local.caseExists){
							local.feeStructureID = getPanelFeeStructureLevelsByID(panelID=arguments.event.getValue('panelID')).feeStructureID;
							if (not val(local.feeStructureID)){
								local.referralID = local.objAdminReferrals.getPanelByID(panelID=arguments.event.getValue('panelID')).referralID;
								local.feeStructureID = getPanelFeeStructureLevelsByID(referralID=local.referralID).feeStructureID;
							}				
							arguments.event.setValue('feeStructureID',local.feeStructureID);
							local.caseID = addCase(event=arguments.event);
						}

						// reprocess conditions after possible saveClientReferralStatus/saveClientReferral/addCase calls
						local.objAdminReferrals.reprocessConditions(orgID=local.orgID, clientReferralID=val(arguments.event.getValue('clientReferralID')));

						if(val(arguments.event.getValue('newStatusID',0))){
							local.qryGetNewStatus = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, statusID=val(arguments.event.getValue('newStatusID'))).qryStatus;
							
							// charge consultation fee							
							if (local.dataStruct.qryReferralSettings.collectClientFeeFE EQ 1
								AND val(local.qryGetNewStatus.isConsultation) 
								AND NOT this.objAdminReferrals.isClientReferralFeeSaleExists(orgID=arguments.event.getValue('mc_siteInfo.orgID'),clientReferralID=arguments.event.getValue('clientReferralID'))) {
								// client info
								local.qryGetClient = local.objAdminReferrals.getClient(clientReferralID=val(arguments.event.getValue('clientReferralID')));
								
								if(val(local.qryGetClient.panelID)){
									local.qryGetPanelDetails = local.objAdminReferrals.getPanelByID(panelID=local.qryGetClient.panelID);
									if(val(local.qryGetPanelDetails.clientReferralAmount)){
										arguments.event.setValue('clientReferralAmount',local.qryGetPanelDetails.clientReferralAmount);
										local.consultationsaleStruct = structNew();
										local.consultationsaleStruct = this.objAdminReferrals.recordConsultationSale(
											amount=arguments.event.getValue('clientReferralAmount',0.00),
											panelID=local.qryGetClient.panelID,
											referralID=local.dataStruct.referralID,
											clientReferralID=arguments.event.getValue('clientReferralID'),
											callUID=local.qryGetClient.callUID,
											siteID=local.siteID,
											orgID=local.orgID,
											memberID=int(local.useMID) ,
											clientname=local.qryGetClient.firstName & " " & local.qryGetClient.lastName
										);
									}
								}
							}
						}
						doEmailRetainCase(event=arguments.event);					
						if (val(local.caseID))
							application.objCommon.redirect('#local.dataStruct.mainurl#');
						else
							local.msg = 3;
					</cfscript>	
				</cfcase>
				<cfcase value="reopenCase">
					<cfset local.msgText = "" />
					<cfset local.msg = "" />
					<cftry>
						<cfscript>
							// current status details
							local.qryClientRefStatusDetails = local.objAdminReferrals.getClientReferralStatusDetails(clientReferralID=arguments.event.getValue('clientReferralID'), referralID=local.dataStruct.referralID);

							// invalid
							if (local.qryClientRefStatusDetails.isClosed EQ 1 AND local.qryClientRefStatusDetails.cannotReopen EQ 1) {
								location("#local.dataStruct.mainurl#", "false");
							}
							
							// save
							saveClientReferral(event=arguments.event);

							local.qryGetNewStatus = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, statusID=val(arguments.event.getValue('newStatusID'))).qryStatus;
							
							// charge consultation fee							
							if (local.dataStruct.qryReferralSettings.collectClientFeeFE EQ 1 
								AND val(local.qryGetNewStatus.isConsultation) 
								AND NOT this.objAdminReferrals.isClientReferralFeeSaleExists(orgID=arguments.event.getValue('mc_siteInfo.orgID'),clientReferralID=arguments.event.getValue('clientReferralID'))) {
								// client info
								local.qryGetClient = local.objAdminReferrals.getClient(clientReferralID=val(arguments.event.getValue('clientReferralID')));
								
								if(val(local.qryGetClient.panelID)){
									local.qryGetPanelDetails = local.objAdminReferrals.getPanelByID(panelID=local.qryGetClient.panelID);
									if(val(local.qryGetPanelDetails.clientReferralAmount)){
										arguments.event.setValue('clientReferralAmount',local.qryGetPanelDetails.clientReferralAmount);
										local.consultationsaleStruct = structNew();
										local.consultationsaleStruct = this.objAdminReferrals.recordConsultationSale(
											amount=arguments.event.getValue('clientReferralAmount',0.00),
											panelID=local.qryGetClient.panelID,
											referralID=local.dataStruct.referralID,
											clientReferralID=arguments.event.getValue('clientReferralID'),
											callUID=local.qryGetClient.callUID,
											siteID=local.siteID,
											orgID=local.orgID,
											memberID=int(local.useMID) ,
											clientname=local.qryGetClient.firstName & " " &  local.qryGetClient.lastName
										);
									}
								}
							}
						</cfscript>				
						<cfset local.objAdminReferrals.updateClientReferralStatus(clientReferralID=arguments.event.getValue('clientReferralID'), statusID=val(arguments.event.getValue('newStatusID'))) />
						<cfset local.objAdminReferrals.reprocessConditions(orgID=local.orgID, clientReferralID=arguments.event.getValue('clientReferralID'))>
						<cfset doEmailCaseStatusUpdate(arguments.event) />
						<cfcatch type="any">
							<cfset local.msg = 3 />
						</cfcatch>
					</cftry>
					<cfif not len(local.msg)>
						<cflocation url="#local.dataStruct.mainurl#" addtoken="false" />
					</cfif>
				</cfcase>
				<cfcase value="selectCase">
					<cfscript>
						local.qryGetCaseStatus = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, isClosed=0, isRetainedCase=1, dspFrontEnd=1).qryStatus;
						
						local.localTet = structNew();
						local.viewData = "";
					</cfscript>
					<cfsavecontent variable="local.selectFormJS">
						<cfoutput>
						<script language="JavaScript">
							$(function() {					
								$('##retainCaseBtn').click(function(){
									var errorMsg = "";
									if($('##thisStatusID').val().length == 0) {
										errorMsg += '- Select Case Status.\n';
									}

									var newOpenStatusID = $('##thisStatusID').val();

									<cfif arguments.event.getValue('grid',0)>
										if (errorMsg.length > 0) {
											alert("There were errors with your submission.\n\n" + errorMsg);
										} else {
											$(".referralStatusForm").html('<img src="/assets/common/images/progress.gif" width="16" height="16" alt="Please wait..."> Please Wait...').load('/?event=cms.showResource&resID=#arguments.event.getValue('resID')#&ra=#arguments.event.getValue("taskCode","retainCase")#&updateStatusOnly=1&clientreferralid=#arguments.event.getValue('clientReferralID')#&statusID='+newOpenStatusID, function(){
												top.closeBox();
												top.location.reload();
											});
										}
									<cfelse>
										errorMsg += top.validateClientReferral();

										if (errorMsg.length > 0) {
											alert("There were errors with your submission.\n\n" + errorMsg);
										} else {
											top.$('##statusID').val(newOpenStatusID);
											top.$("##frmClient").attr('action', '#local.dataStruct.mainurl#&ra=#arguments.event.getValue("taskCode","retainCase")#&newStatusID='+newOpenStatusID);
											top.$('##frmClient').submit();
											top.closeBox();
										}
									</cfif>
								});
							});
						</script>
						</cfoutput>
					</cfsavecontent>
					<cfhtmlhead text="#application.objCommon.minText(local.selectFormJS)#" />
					
					<cfsavecontent variable="local.viewData">
						<cfoutput>
							<div class="referralStatusForm">
								<cfif local.viewDirectory eq 'responsive'>
									<div class="row-fluid">
										<h3>Select Case Status</h3>							
									</div>
									<div class="form-group row-fluid">
										<select name="thisStatusID" id="thisStatusID"  class="form-control " >
											<option value="">Select Case Status</option>
											<cfloop query="local.qryGetCaseStatus">
											<option value="#local.qryGetCaseStatus.clientReferralStatusID#">#local.qryGetCaseStatus.statusName#</option>
											</cfloop>
										</select>							
									</div>
									<div class="form-group row-fluid" style="text-align:left;">
										<button type="button" id="retainCaseBtn" class="btn">Open Case</button>
										<button type="button" id="closeBtn" class="btn" onClick="top.closeBox();">Cancel</button>							
									</div>				
								<cfelse>
									<div style="padding:5px 0 5px 20px;">
										<h3>Select Case Status</h3>
										<select name="thisStatusID" id="thisStatusID">
											<option value="">Select Case Status</option>
											<cfloop query="local.qryGetCaseStatus">
											<option value="#local.qryGetCaseStatus.clientReferralStatusID#">#local.qryGetCaseStatus.statusName#</option>
											</cfloop>
										</select>
										<br /><br />
										<div align="left">
											<button type="button" id="retainCaseBtn" class="tsAppBodyButton">Open Case</button>
											<button type="button" id="closeBtn" class="tsAppBodyButton" onClick="top.closeBox();">Cancel</button>
										</div>
									</div>							
								</cfif>	
							</div>
						</cfoutput>
					</cfsavecontent>
					
				</cfcase>	
				<cfdefaultcase>
					<cfscript>
						if (arguments.event.valueExists('savePanelList')){
							saveMemberPanelList(event=arguments.event);
						}
						local.dataStruct.refUrlString = "";
						local.dataStruct.referralList = local.ajaxURL & "&ra=referralsGrid";
						local.dataStruct.caseUrlString = "";
						local.dataStruct.caseList = local.ajaxURL & "&ra=casesGrid";
						local.dataStruct.rfpList = local.ajaxURL & "&ra=rfpGrid";
						local.objSMSTemplate = createObject("component","model.admin.common.modules.smsTemplate.smsTemplate");
						
						local.dataStruct.link.viewCaseStatement = "#local.dataStruct.mainurl#&ra=viewCaseStatement&mode=direct";
						local.dataStruct.link.viewProgressReport = "#local.dataStruct.mainurl#&ra=viewProgressReport&mode=direct";
						local.dataStruct.link.savePhoneNumber =  "/?event=proxy.ts_json&c=REF&m=savePhoneNumber";
						local.dataStruct.link.updatePhoneNumber =  "/?event=proxy.ts_json&c=REF&m=updatePhoneNumber";
						local.dataStruct.link.deletePhoneNumber =  "/?event=proxy.ts_json&c=REF&m=deletePhoneNumber";
						local.dataStruct.historyUrlString = "";
						local.dataStruct.historyList = local.ajaxURL & "&ra=historyGrid";
						local.dataStruct.qryMemberPanels = getMemberPanels(local.dataStruct.memberLoggedInID);
						local.dataStruct.qryIncativeMemberPanels = getMemberPanels(memberid=local.dataStruct.memberLoggedInID,isInActive=1);
						local.dataStruct.dspPanelList = local.dataStruct.qryReferralSettings.dspPanelList;
						local.dataStruct.rcPanelInstructionsTxt = local.dataStruct.qryReferralSettings.rcPanelInstructionsTxt;
						local.dataStruct.maxNumberOfPanels = local.dataStruct.qryReferralSettings.maxNumberOfPanels;
						local.dataStruct.allowPanelMgmt = local.dataStruct.qryReferralSettings.allowPanelMgmt;
						local.dataStruct.qryGetPanels = getPanels(referralID=local.dataStruct.referralID, statusName="Active", feDspClientReferral=1);
						local.dataStruct.siteID = arguments.event.getValue('siteID');
						local.dataStruct.tabToShow =  arguments.event.getValue('tab','referral');
						local.dataStruct.objReferrals =  local.objReferrals;						
						local.dataStruct.referralsSMS = local.dataStruct.qryReferralSettings.referralsSMS;
						local.dataStruct.feReferralCenterInstructionForMemberID = local.dataStruct.qryReferralSettings.feReferralCenterInstructionForMemberID;
						local.dataStruct.feReferralCenterInstruction = '';
						local.dataStruct.referralID = local.dataStruct.qryReferralSettings.referralID
						
						if(len(local.dataStruct.feReferralCenterInstructionForMemberID)){
							local.dataStruct.feReferralCenterInstruction = getReferralCenterInstruction(referralID = local.dataStruct.referralID);
						}

						local.dataStruct.qrymessagingService = local.objSMSTemplate.getMessagingService(local.dataStruct.siteID,'REFMEMBERS');
						
						if(local.dataStruct.qrymessagingService.recordcount gt 0){
							local.dataStruct.messagingServiceID = local.dataStruct.qrymessagingService.messagingServiceID;
							local.dataStruct.queryMSPhonenumbers = local.objSMSTemplate.getSMSPhonenumbersByMember(
								local.dataStruct.qrymessagingService.messagingServiceID,
								'REFMEMBERS',
								session.cfcuser.memberdata.memberid,
								local.dataStruct.siteID
							);				
						
						}else{
							local.dataStruct.messagingServiceID = 0
						}


						 
						
						local.dataStruct.viewDirectory = local.viewDirectory;
						local.dataStruct.mainURLPay = '';	
						local.qryTemplateDetails = local.objSMSTemplate.getTemplateTriggersList(local.dataStruct.siteID,'REFCLIENTS');
						local.dataStruct.feEnableTextMessagingMember = (val(local.qryTemplateDetails.recordcount) gt 0 AND local.dataStruct.messagingServiceID gt 0)? 1: 0;
							
						local.qryMember = application.objMember.getMemberInfo(session.cfcuser.memberdata.memberid);
						if(local.viewDirectory eq 'responsive'){
							local.dataStruct.refDateRanges = arguments.event.getValue('refDateRange');
							local.dataStruct.filterReferralID = arguments.event.getValue('filterReferralID');
							local.dataStruct.filterClientLastName = arguments.event.getValue('filterClientLastName');
							local.dataStruct.filterClientFirstName = arguments.event.getValue('filterClientFirstName');
							local.dataStruct.filterStatus = arguments.event.getValue('filterStatus');
							local.dataStruct.start = int(arguments.event.getValue('start',1));
							local.dataStruct.count = int(arguments.event.getValue('count',25));
							local.dataStruct.sort = arguments.event.getValue('sort','desc');
							local.dataStruct.orderBy = arguments.event.getValue('orderBy','referralid');
							
							if(local.dataStruct.tabToShow eq 'referral'	){							
								
								local.referralStatusResponseStruct = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, isReferred = 1, isRetainedCase = 0, isOpen = 1);
								if(local.referralStatusResponseStruct.success == true){
									local.dataStruct.qryGetReferralStatus  = local.referralStatusResponseStruct.qryStatus;
								} else {
									return showRefError();
								}
								
								local.argumentStruct = {
														refDateRange=local.dataStruct.refDateRanges
														,filterReferralID=local.dataStruct.filterReferralID
														,filterClientLastName=local.dataStruct.filterClientLastName
														,filterClientFirstName=local.dataStruct.filterClientFirstName
														,filterStatus=local.dataStruct.filterStatus
														,start= local.dataStruct.start
														,count= local.dataStruct.count
														,orderBy = local.dataStruct.orderBy
														,sort=local.dataStruct.sort
														};							
								
								local.returnData = getMemberReferralsGridData(argumentCollection = local.argumentStruct);
								local.dataStruct.totalCount = local.returnData.result.totalCount;
								local.dataStruct.totalPages = local.returnData.result.totalPages;
								local.dataStruct.currentPage = local.returnData.result.currentPage;
								local.dataStruct.referralData = local.returnData.result.data;							
								
							}else if(local.dataStruct.tabToShow eq 'mrc'){
								local.dataStruct.filterfeesDue = arguments.event.getValue('filterfeesDue');
	
								local.referralStatusResponseStruct = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, isReferred = 1, isRetainedCase = 1, isOpen = 1, dspFrontEnd = 1);
								if(local.referralStatusResponseStruct.success == true){
									local.dataStruct.qryGetReferralCaseStatus  = local.referralStatusResponseStruct.qryStatus;
								} else {
									return showRefError();
								}	

								local.dataStruct.siteCode = arguments.event.getValue('mc_siteinfo.sitecode');	

								local.argumentStruct = {
														refDateRange=local.dataStruct.refDateRanges
														,filterReferralID=local.dataStruct.filterReferralID
														,filterClientLastName=local.dataStruct.filterClientLastName
														,filterClientFirstName=local.dataStruct.filterClientFirstName
														,filterStatus=local.dataStruct.filterStatus
														,filterfeesDue=local.dataStruct.filterfeesDue
														,start= local.dataStruct.start
														,count= local.dataStruct.count
														,orderBy = local.dataStruct.orderBy
														,sort=local.dataStruct.sort
														};							
								
								local.returnData = getMemberCasesGridData(argumentCollection = local.argumentStruct);
								local.dataStruct.totalCount = local.returnData.result.totalCount;
								local.dataStruct.totalPages = local.returnData.result.totalPages;
								local.dataStruct.currentPage = local.returnData.result.currentPage;
								local.dataStruct.mrcData = local.returnData.result.data;	
							
							}
							else if(local.dataStruct.tabToShow eq 'mrch'){
								local.dataStruct.filterAmountDue = arguments.event.getValue('filterAmountDue');
								
								local.referralStatusResponseStruct = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, isReferred = 1, isRetainedCase = 0, isOpen = 1);
								if(local.referralStatusResponseStruct.success == true){
									local.dataStruct.qryGetReferralHistoryStatusSet1  = local.referralStatusResponseStruct.qryStatus;
								} else {
									return showRefError();
								}

								local.referralStatusResponseStruct = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, dspFrontEnd = 1);
								if(local.referralStatusResponseStruct.success == true){
									local.dataStruct.qryGetReferralHistoryStatusSet2  = local.referralStatusResponseStruct.qryStatus;
								} else {
									return showRefError();
								}

								local.argumentStruct = {
														refDateRange=local.dataStruct.refDateRanges
														,filterReferralID=local.dataStruct.filterReferralID
														,filterClientLastName=local.dataStruct.filterClientLastName
														,filterClientFirstName=local.dataStruct.filterClientFirstName
														,filterStatus=local.dataStruct.filterStatus
														,filterAmountDue=local.dataStruct.filterAmountDue
														,start= local.dataStruct.start
														,count= local.dataStruct.count
														,orderBy = local.dataStruct.orderBy
														,sort=local.dataStruct.sort
														};							
								
								local.returnData = getMemberReferralHistoryGridData(argumentCollection = local.argumentStruct);
								local.dataStruct.totalCount = local.returnData.result.totalCount;
								local.dataStruct.totalPages = local.returnData.result.totalPages;
								local.dataStruct.currentPage = local.returnData.result.currentPage;
								local.dataStruct.mrchData = local.returnData.result.data;						
							}else if(local.dataStruct.tabToShow eq 'rfp'){
								local.dataStruct.filterfeesDue = arguments.event.getValue('filterfeesDue');
								local.dataStruct.isPaymentInitialized = arguments.event.getValue('initPay',0);
	
								local.referralStatusResponseStruct = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, isReferred = 1, isRetainedCase = 1, isOpen = 1, dspFrontEnd = 1);
								if(local.referralStatusResponseStruct.success == true){
									local.dataStruct.qryGetReferralCaseStatus  = local.referralStatusResponseStruct.qryStatus;
								} else {
									return showRefError();
								}	
								local.checkedReferral = '';
								if(local.dataStruct.isPaymentInitialized eq 1){
									local.checkedReferral = arguments.event.getValue('checkedReferral','');
								}
								if(local.dataStruct.isPaymentInitialized eq 2){
									local.checkedReferral = arguments.event.getValue('checkedReferral','');
									local.profileid = arguments.event.getValue('profileid','');
								}
								local.dataStruct.mainURLPay = "#arguments.event.getValue('mainurl')#&panel=browse&tab=rfp&initPay=1&mode=stream";
								local.dataStruct.siteID = arguments.event.getValue('mc_siteinfo.siteid');
								local.dataStruct.siteCode = arguments.event.getValue('mc_siteinfo.sitecode');
								
								local.argumentStruct = { 
									mcproxy_siteID=local.dataStruct.siteID,
									refDateRange=local.dataStruct.refDateRanges,
									filterReferralID=local.dataStruct.filterReferralID,
									filterClientLastName=local.dataStruct.filterClientLastName,
									filterClientFirstName=local.dataStruct.filterClientFirstName,
									filterStatus=local.dataStruct.filterStatus,
									filterfeesDue=local.dataStruct.filterfeesDue,
									start= local.dataStruct.start,
									count= local.dataStruct.count,
									orderBy = local.dataStruct.orderBy,
									sort=local.dataStruct.sort,
									checkedReferral=local.checkedReferral
								};
								application.mcCacheManager.sessionSetValue(keyname='referralFilterSession', value=local.argumentStruct);
								
								local.returnData = getMemberRFPGridData(argumentCollection = local.argumentStruct);
								local.dataStruct.totalCount = local.returnData.result.totalCount;
								local.dataStruct.totalPages = local.returnData.result.totalPages;
								local.dataStruct.currentPage = local.returnData.result.currentPage;
								local.dataStruct.rfpData = local.returnData.result.data;
								local.qryBilling = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.dataStruct.memberLoggedInID);
								local.dataStruct.stateIDforTax = val(local.qryBilling.stateID);
								local.dataStruct.zipForTax = local.qryBilling.postalCode;
								if (len(local.dataStruct.zipForTax) AND NOT application.objCommon.checkBillingZIP(billingZip=local.dataStruct.zipForTax, billingStateID=val(local.dataStruct.stateIDforTax)).isvalidzip) {
									local.dataStruct.zipForTax = "";
								}

								if(local.dataStruct.isPaymentInitialized eq 1){
									local.amountToChargeInProfile = 0;
									for (i=1; i <= arrayLen(local.dataStruct.rfpData ); i++) {
										local.amountToChargeInProfile = local.amountToChargeInProfile + local.dataStruct.rfpData[i].totalFeesDue;
									}
									local.noPaymentStatement = 'No Due for payment';
									<!--- merchant profiles allowed--->
									local.paymentGateways = getAllowedPaymentProfiles(profileID=0, clientReferralIDList=local.checkedReferral);
									local.stateIDForTax = arguments.event.getValue('stateIDForTax',0);
									local.zipForTax = arguments.event.getValue('zipForTax','');
									
									local.view = local.viewDirectory;
									local.dataStruct.view = local.viewDirectory;
									local.payAction = "#arguments.event.getValue('mainurl')#&panel=browse&tab=rfp&initPay=2";			
									
									savecontent variable="local.data" { 
										include template="/views/referrals/#local.viewDirectory#/dsp_referralsCart.cfm";
									}	
									return returnAppStruct(local.data,"echo");
								}else if(local.dataStruct.isPaymentInitialized eq 2){
									local.amountToChargeInProfile = 0;
									for (i=1; i <= arrayLen(local.dataStruct.rfpData ); i++) {
										local.amountToChargeInProfile = local.amountToChargeInProfile + local.dataStruct.rfpData [i].totalFeesDue;
									}
									if(local.profileid neq '' && local.amountToChargeInProfile gt 0){
										local.paymentObj = payOutstandingReferralFees(event=arguments.event, referralID=local.dataStruct.referralID, profileID=local.profileid, totalPrice=local.amountToChargeInProfile, 
																useMID=local.useMID, clientReferralIDList=local.checkedReferral);
										local.allocationSuccess = 1;
										if(local.paymentObj.success){
											// valid payment
											if (arrayLen(local.paymentObj.arrPaymentTransactions)) {
												// supports client consultation fees
												if (local.dataStruct.qryReferralSettings.collectClientFeeFE) {
													recordClientFeeAllocation(orgID=arguments.event.getValue('mc_siteinfo.orgid'), siteID=arguments.event.getValue('mc_siteinfo.siteID'), referralID=local.dataStruct.referralID, 
														arrPaymentTransactions=local.paymentObj.arrPaymentTransactions, clientReferralIDList=local.checkedReferral, useMID=local.useMID);
												}
												
												for (i=1; i <= arrayLen(local.dataStruct.rfpData ); i++) {
													if (local.dataStruct.rfpData[i].amtToBePaidTotal gt 0) {
														var allocObj = recordReferralFeeAllocation(event=arguments.event, paymentObj=local.paymentObj, rfpData=local.dataStruct.rfpData[i], useMID=local.useMID);
														if(!allocObj.success && local.allocationSuccess neq 0){
															local.allocationSuccess = 0;
														}
													}
												}
											}

											if(local.allocationSuccess eq 1){
												sendReferralReceiptEmail(event=arguments.event, paymentObj=local.paymentObj, rfpData=local.dataStruct.rfpData, useMID=local.useMID, isOfflinePayment=local.paymentObj.isOfflinePayment);
												local.isOfflinePayment = local.paymentObj.isOfflinePayment;
												local.rfpData = local.dataStruct.rfpData;
												local.mainurl = arguments.event.getValue('mainurl');
												local.err = 0;
												savecontent variable="local.data" { 
													include template="/views/referrals/#local.viewDirectory#/referral_fee_payment_receipt.cfm";
												}	
												return returnAppStruct(local.data,"echo");
											}
										}									
									}
									local.err = 1;
									local.mainurl = arguments.event.getValue('mainurl');
									savecontent variable="local.data" { 
										include template="/views/referrals/#local.viewDirectory#/referral_fee_payment_receipt.cfm";
									 }		
									return returnAppStruct(local.data,"echo");
									
								}
							}
						}
						else{

							local.referralStatusResponseStruct = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, isReferred = 1, isRetainedCase = 0, isOpen = 1);
							if(local.referralStatusResponseStruct.success == true){
								local.dataStruct.qryGetReferralStatus  = local.referralStatusResponseStruct.qryStatus;
							} else {
								return showRefError();
							}
							

							local.referralStatusResponseStruct = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, isReferred = 1, isRetainedCase = 1, isOpen = 1, dspFrontEnd = 1);
							if(local.referralStatusResponseStruct.success == true){
								local.dataStruct.qryGetReferralCaseStatus  = local.referralStatusResponseStruct.qryStatus;
							} else {
								return showRefError();
							}

							local.referralStatusResponseStruct = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, isReferred = 1, isRetainedCase = 0, isOpen = 1);
							if(local.referralStatusResponseStruct.success == true){
								local.dataStruct.qryGetReferralHistoryStatusSet1  = local.referralStatusResponseStruct.qryStatus;
							} else {
								return showRefError();
							}

							local.referralStatusResponseStruct = local.objAdminReferrals.getClientReferralStatus(referralID=local.dataStruct.referralID, dspFrontEnd = 1);
							if(local.referralStatusResponseStruct.success == true){
								local.dataStruct.qryGetReferralHistoryStatusSet2  = local.referralStatusResponseStruct.qryStatus;
							} else {
								return showRefError();
							}

							local.qryBilling = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteinfo.orgid'), memberID=local.dataStruct.memberLoggedInID);
							local.dataStruct.stateIDforTax = val(local.qryBilling.stateID);
							local.dataStruct.zipForTax = local.qryBilling.postalCode;
							if (len(local.dataStruct.zipForTax) AND NOT application.objCommon.checkBillingZIP(billingZip=local.dataStruct.zipForTax, billingStateID=val(local.dataStruct.stateIDforTax)).isvalidzip) {
								local.dataStruct.zipForTax = "";
							}
						}			
						
						local.viewToUse = "referrals/#local.viewDirectory#/dsp_referralCenter";					
						
					</cfscript>
				</cfdefaultcase>
			</cfswitch>
			<!--- record app hit --->
			<cfset application.objPlatformStats.recordAppHit(appname="referrals",appsection="") />
		</cfif>

		<!--- return the app struct --->
		
		<cfif local.viewToUse eq 'echo'>			
			<cfreturn returnAppStruct(local.viewData,local.viewToUse) />
		<cfelse>
			<cfreturn returnAppStruct(local.dataStruct,local.viewToUse) />
		</cfif >
	</cffunction>

	<cffunction name="getMemberReferrals" output="false" returntype="query" hint="returns member referrals">
		<cfargument name="memberID" type="numeric" required="true" />
		<cfargument name="siteID" type="numeric" required="true" />
		<cfargument name="refDateRange" type="numeric" required="false" default="0" />
		<cfargument name="filterReferralID" type="string" required="false" default="" />
		<cfargument name="filterClientLastName" type="string" required="false" default="" />
		<cfargument name="filterClientFirstName" type="string" required="false" default="" />
		<cfargument name="filterStatus" type="numeric" required="false" default="0" />
		<cfargument name="isReport" type="numeric" required="false" />
		
		<cfset var local = structNew()>
		
		<cfset local.referralID = getReferralSettings(arguments.siteID).referralID>
		
		<cfquery name="local.qryReferrals" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @memberID int, @referralID int, @filterReferralID varchar(100), @filterClientLastName varchar(200), 
				@filterClientFirstName varchar(200), @filterStatus int;
			set @memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="cf_sql_integer" />;
			set @filterReferralID = <cfqueryparam value="%#arguments.filterReferralID#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientLastName = <cfqueryparam value="%#arguments.filterClientLastName#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientFirstName = <cfqueryparam value="%#arguments.filterClientFirstName#%" cfsqltype="cf_sql_varchar" />;
			set @filterStatus = <cfqueryparam value="#arguments.filterStatus#" cfsqltype="cf_sql_integer" />;
			set @referralID = <cfqueryparam value="#local.referralID#" cfsqltype="cf_sql_integer" />;

			<cfif isDefined("arguments.refDateRange") and arguments.refDateRange>
				declare @dateCutoff datetime, @daysToLookBack int;
				set @daysToLookBack = <cfqueryparam value="#arguments.refDateRange#" cfsqltype="cf_sql_integer" />;
				set @dateCutoff = dateadd(dd,-@daysToLookBack,getdate());
			</cfif>

			declare @clientReferrals TABLE (clientReferralID int PRIMARY KEY);

			insert into @clientReferrals (clientReferralID)
			select cr.clientReferralID
			from dbo.ref_clientReferrals cr
			INNER JOIN dbo.ref_clients c on c.clientID = cr.clientID
				and c.referralID = @referralID
				<cfif len(arguments.filterClientLastName)>
					and c.lastName like @filterClientLastName
				</cfif>
				<cfif len(arguments.filterClientFirstName)>
					and c.firstName like @filterClientFirstName
				</cfif>
			INNER JOIN dbo.ams_members m on m.memberID = cr.memberID
			INNER JOIN dbo.ams_members activeM on activeM.memberID = m.activeMemberID
				and activeM.memberID = @memberID
			INNER JOIN dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID
				and crs.isDeleted = 0
				and crs.isOpen = 1
				<cfif arguments.filterStatus>
					and crs.clientReferralStatusID = @filterStatus
				</cfif>
			INNER JOIN dbo.ref_clientTypes ct on ct.clientTypeID = c.typeID
				and ct.clientType = 'Client'
			WHERE cr.referralID = @referralID
			<cfif isDefined("arguments.refDateRange") and arguments.refDateRange>
				and cr.clientReferralDate > @dateCutoff
			</cfif>
			<cfif len(arguments.filterReferralID)>
				and cr.clientReferralID like @filterReferralID
			</cfif>
			<cfif not isDefined("arguments.isReport")>
				except
			select clientReferralID
			from dbo.ref_cases
			where referralID = @referralID
			</cfif>;

			select c.clientID, c.referralID, c.firstName, c.middleName, c.lastName, c.lastName + ', ' + c.firstName as clientName,
				c.businessName, c.address1, c.address2, c.address3, c.city,	c.state, c.postalCode, c.countryID,
				c.email, c.homePhone, c.cellPhone, c.alternatePhone, c.typeID, cr.statusID,
				cr.clientReferralID, cr.typeID as referralTypeID, cr.memberID, cr.callUID, cr.issueDesc, cr.clientReferralDate,
				crt.clientReferralType, crt.isReferral as isLawyerReferral, crt.isAgency as isAgencyReferral,
				crs.statusName, crs.canEditClient as referralCanEditClient, crs.canEditFilter as referralCanEditFilter, 
				crs.canEditLawyer as referralCanEditLawyer, crs.canRefer, crs.isReferred, crs.isAgency, crs.isPending,
				crs.isOpen, crs.isClosed, crs.isDeleted, c.dateCreated, c.createdBy, c.clientParentID,
				c.dateLastUpdated, c.relationToClient
			from dbo.ref_clients c
			INNER JOIN dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
			INNER JOIN @clientReferrals temp on temp.clientReferralID = cr.clientReferralID
			INNER JOIN dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID
			INNER JOIN dbo.ref_clientReferralTypes crt on crt.clientReferralTypeID = cr.typeID
			WHERE c.referralID = @referralID
			order by cr.clientReferralID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>		

		<cfreturn local.qryReferrals>
	</cffunction>

	<cffunction name="getLinkToClientReferral" access="public" output="false" returntype="string">
		<cfargument name="referralCode" type="string" required="true" />
		<cfargument name="siteID" type="numeric" required="true" />

		<cfquery name="local.qrygetReferralData" datasource="#application.dsn.membercentral.dsn#">
			select cr.clientReferralID, ai.applicationInstanceID
			from dbo.ref_referrals as r
			INNER JOIN dbo.cms_applicationInstances as ai 
			on ai.applicationInstanceID = r.applicationInstanceID		
			and ai.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			INNER JOIN dbo.ref_clientReferrals cr 
			on cr.referralID = r.referralID
			and cr.referralCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.referralCode#">
		</cfquery>

		<cfif local.qrygetReferralData.recordcount is 0> 
			<cfset local.pageLink = "">
		<cfelse>
			<cfset local.pageLink = "/?#application.objApplications.getAppBaseLink(applicationInstanceID=local.qrygetReferralData.applicationInstanceID, siteID=arguments.siteID)#&ra=editReferral&clientReferralID=#local.qrygetReferralData.clientReferralID#">
		</cfif>

		<cfreturn local.pageLink>
	</cffunction>

	<cffunction name="getMemberReferralsGridDataCount" output="false" returntype="query" hint="returns member referrals">
		<cfargument name="memberID" type="numeric" />
		<cfargument name="siteID" type="numeric" />
		<cfargument name="refDateRange" type="numeric" required="false" default="0" />
		<cfargument name="filterReferralID" type="string" required="false" default="" />	
		<cfargument name="filterClientLastName" type="string" required="false" default="" />
		<cfargument name="filterClientFirstName" type="string" required="false" default="" />
		<cfargument name="filterStatus" type="numeric" required="false" default="0" />
		<cfargument name="pageStart" type="numeric" default="1" required="false" />
		<cfargument name="pageEnd" type="numeric" default="3" required="false" />
		
		<cfset var local = structNew()>
		
		<cfset local.referralID = getReferralSettings(arguments.siteID).referralID>
		
		<cfquery name="local.qryReferrals" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @memberID int, @referralID int, @filterReferralID varchar(100), @filterClientLastName varchar(200), 
				@filterClientFirstName varchar(200), @filterStatus int;
			set @memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="cf_sql_integer" />;
			set @referralID = <cfqueryparam value="#local.referralID#" cfsqltype="cf_sql_integer" />;
			set @filterReferralID = <cfqueryparam value="%#arguments.filterReferralID#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientLastName = <cfqueryparam value="%#arguments.filterClientLastName#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientFirstName = <cfqueryparam value="%#arguments.filterClientFirstName#%" cfsqltype="cf_sql_varchar" />;
			set @filterStatus = <cfqueryparam value="#arguments.filterStatus#" cfsqltype="cf_sql_integer" />;
			
			<cfif isDefined("arguments.refDateRange") and arguments.refDateRange>
				declare @dateCutoff datetime, @daysToLookBack int;
				set @daysToLookBack = <cfqueryparam value="#arguments.refDateRange#" cfsqltype="cf_sql_integer" />;
				set @dateCutoff = dateadd(dd,-@daysToLookBack,getdate());
			</cfif>

			declare @clientReferrals TABLE (clientReferralID int PRIMARY KEY);

			insert into @clientReferrals (clientReferralID)
			select cr.clientReferralID
			from dbo.ref_clientReferrals cr
			INNER JOIN dbo.ref_clients c on c.clientID = cr.clientID
				and c.referralID = @referralID
				<cfif len(arguments.filterClientLastName)>
					and c.lastName like @filterClientLastName
				</cfif>
				<cfif len(arguments.filterClientFirstName)>
					and c.firstName like @filterClientFirstName
				</cfif>
			INNER JOIN dbo.ams_members m on m.memberID = cr.memberID
			INNER JOIN dbo.ams_members activeM on activeM.memberID = m.activeMemberID
				and activeM.memberID = @memberID
			INNER JOIN dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID
				and crs.isDeleted = 0
				and crs.isOpen = 1
				and crs.isCoordination = 0
				<cfif arguments.filterStatus>
					and crs.clientReferralStatusID = @filterStatus
				</cfif>
			INNER JOIN dbo.ref_clientTypes ct on ct.clientTypeID = c.typeID
				and ct.clientType = 'Client'
			WHERE cr.referralID = @referralID
			<cfif isDefined("arguments.refDateRange") and arguments.refDateRange>
				and cr.clientReferralDate > @dateCutoff
			</cfif>
			<cfif len(arguments.filterReferralID)>
				and cr.clientReferralID like @filterReferralID
			</cfif>
				except
			select clientReferralID
			from dbo.ref_cases
			where referralID = @referralID;

			select count(cr.clientReferralID) as totalCount 
			from dbo.ref_clients c
			INNER JOIN dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
			INNER JOIN @clientReferrals temp on temp.clientReferralID = cr.clientReferralID
			INNER JOIN dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID
			INNER JOIN dbo.ref_clientReferralTypes crt on crt.clientReferralTypeID = cr.typeID
			WHERE c.referralID = @referralID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryReferrals>
	</cffunction>
	
	<cffunction name="getMemberReferralsGridData" access="public" output="false" returntype="struct">
		<cfargument name="refDateRange" type="numeric" required="false" default="0" />
		<cfargument name="filterReferralID" type="string" required="false" default="" />
		<cfargument name="filterClientLastName" type="string" required="false" default="" />
		<cfargument name="filterClientFirstName" type="string" required="false" default="" />
		<cfargument name="filterStatus" type="numeric" required="false" default="0" />	
		<cfargument name="start" type="numeric" required="false" default="1" />
		<cfargument name="count" type="numeric" required="false" default="1" />
		<cfargument name="sort" type="string" required="true" default="referralid"/>
		<cfargument name="orderBy" type="string" required="true" default="desc"/>		
		
		<cfset var local = structNew() />

		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcUser.memberData.IdentifiedAsMemberID gt 0>
			<cfset local.useMID = session.cfcUser.memberData.IdentifiedAsMemberID>
		<cfelse>
			<cfset local.useMID = session.cfcuser.memberdata.memberid>
		</cfif>
		
		<cfquery name="local.qryGetSiteID" datasource="#application.dsn.membercentral.dsn#">
			select dbo.fn_getSiteIDFromSiteCode('#session.mcstruct.siteCode#') as siteID
		</cfquery>

		<cfset local.referralID = getReferralSettings(local.qryGetSiteID.siteID).referralID />
		<cfscript>
			local.count = arguments.count;
			if(arguments.start != ''){
				local.currentPage = arguments.start;
			}else{
				local.currentPage = 1;
			}
			local.startLimit = (local.currentPage - 1) * local.count + 1;
			local.endLimit = local.startLimit + (local.count - 1);
			
			local.totalCountResult = getMemberReferralsGridDataCount(siteID=local.qryGetSiteID.siteID, memberID=local.useMID,refDateRange=arguments.refDateRange, filterReferralID=arguments.filterReferralID, filterClientLastName=arguments.filterClientLastName, filterClientFirstName=arguments.filterClientFirstName, filterStatus=arguments.filterStatus);	
			local.totalCount = local.totalCountResult.TOTALCOUNT;
			if(local.totalCount % local.count == 0){
				local.totalPages =  int(local.totalCount/local.count);
			}else{
				local.totalPages =  int(local.totalCount/local.count) + 1;
			}
			
			if(local.totalPages == 0){
				local.totalPages = 1;
			}
		</cfscript>
		
		<cfquery name="local.qryResults" datasource="#application.dsn.membercentral.dsn#" returntype="array">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @memberID int, @referralID int, @filterReferralID varchar(100), @filterClientLastName varchar(200), 
				@filterClientFirstName varchar(200), @filterStatus int, @startLimit int, @endLimit int;
			set @memberID = <cfqueryparam value="#local.useMID#" cfsqltype="cf_sql_integer" />;
			set @referralID = <cfqueryparam value="#local.referralID#" cfsqltype="cf_sql_integer" />;
			set @filterReferralID = <cfqueryparam value="%#arguments.filterReferralID#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientLastName = <cfqueryparam value="%#arguments.filterClientLastName#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientFirstName = <cfqueryparam value="%#arguments.filterClientFirstName#%" cfsqltype="cf_sql_varchar" />;
			set @filterStatus = <cfqueryparam value="#arguments.filterStatus#" cfsqltype="cf_sql_integer" />;
			set @startLimit = <cfqueryparam value="#local.startLimit#" cfsqltype="cf_sql_integer" />;
			set @endLimit = <cfqueryparam value="#local.endLimit#" cfsqltype="cf_sql_integer" />;

			<cfif isDefined("arguments.refDateRange") and arguments.refDateRange>
				declare @dateCutoff datetime, @daysToLookBack int;
				set @daysToLookBack = <cfqueryparam value="#arguments.refDateRange#" cfsqltype="cf_sql_integer" />;
				set @dateCutoff = dateadd(dd,-@daysToLookBack,getdate());
			</cfif>

			declare @clientReferrals TABLE (clientReferralID int PRIMARY KEY);

			insert into @clientReferrals (clientReferralID)
			select cr.clientReferralID
			from dbo.ref_clientReferrals cr
			INNER JOIN dbo.ref_clients c on c.clientID = cr.clientID
				and c.referralID = @referralID
				<cfif len(arguments.filterClientLastName)>
					and c.lastName like @filterClientLastName
				</cfif>
				<cfif len(arguments.filterClientFirstName)>
					and c.firstName like @filterClientFirstName
				</cfif>
			INNER JOIN dbo.ams_members m on m.memberID = cr.memberID
			INNER JOIN dbo.ams_members activeM on activeM.memberID = m.activeMemberID
				and activeM.memberID = @memberID
			INNER JOIN dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID
				and crs.isDeleted = 0
				and crs.isOpen = 1
				and crs.isCoordination = 0
				<cfif arguments.filterStatus>
					and crs.clientReferralStatusID = @filterStatus
				</cfif>				
			INNER JOIN dbo.ref_clientTypes ct on ct.clientTypeID = c.typeID
				and ct.clientType = 'Client'
			WHERE cr.referralID = @referralID
			<cfif arguments.refDateRange>
				and cr.clientReferralDate > @dateCutoff
			</cfif>
			<cfif len(arguments.filterReferralID)>
				and cr.clientReferralID like @filterReferralID
			</cfif>
				except
			select clientReferralID
			from dbo.ref_cases
			where referralID = @referralID;

			WITH ResultsCTE as (
				select ROW_NUMBER() OVER ( ORDER BY 
					<cfif len(arguments.sort)>
						<cfif arguments.sort EQ "referralid">
							cr.clientReferralID 
						<cfelseif arguments.sort EQ "clientname">
							c.lastName + c.firstName 
						<cfelseif arguments.sort EQ "description">
							cr.issueDesc 
						<cfelseif arguments.sort EQ "status">
							crs.statusName 
						<cfelseif arguments.sort EQ "referralDate">
							cr.clientReferralDate 
						</cfif>
						<cfif len(arguments.orderBy)>
							 #arguments.orderBy#
						</cfif>
					<cfelse>
						cr.clientReferralID
					</cfif>	) AS RowNum,
					c.clientID,	c.referralID,
					c.firstName, c.middleName, c.lastName,
					c.lastName + ', ' + c.firstName as clientName,
					c.businessName, c.address1, c.address2, c.address3,
					c.city,	c.state, c.postalCode, c.countryID,
					c.email, c.homePhone, c.cellPhone, c.alternatePhone,
					c.typeID, cr.statusID,
					cr.clientReferralID, cr.typeID as referralTypeID, cr.memberID,
					cr.callUID, cr.issueDesc, CONVERT(varchar, cr.clientReferralDate, 101) as clientReferralDate,
					crt.clientReferralType, crt.isReferral as isLawyerReferral, 
					crt.isAgency as isAgencyReferral,
					crs.statusName, 
					crs.canEditClient as referralCanEditClient, 
					crs.canEditFilter as referralCanEditFilter, 
					crs.canEditLawyer as referralCanEditLawyer,
					crs.canRefer, crs.isReferred, crs.isAgency, crs.isPending,
					crs.isOpen, crs.isClosed, crs.isDeleted,
					c.dateCreated, c.createdBy, c.clientParentID,
					c.dateLastUpdated, c.relationToClient
				from dbo.ref_clients c
				INNER JOIN dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
				INNER JOIN @clientReferrals temp on temp.clientReferralID = cr.clientReferralID
				INNER JOIN dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID
				INNER JOIN dbo.ref_clientReferralTypes crt on crt.clientReferralTypeID = cr.typeID
				WHERE c.referralID = @referralID
			) 
			SELECT *
			FROM ResultsCTE
			WHERE RowNum >= @startLimit AND RowNum <= @endLimit
			ORDER BY RowNum;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.gridJSON = { totalCount = int(local.totalCount), totalPages = int(local.totalPages), currentPage = int(local.currentPage),
			sort = arguments.sort, orderby = arguments.orderBy, data = local.qryResults }>
		<cfset local.data.result = local.gridJSON>
		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>	

	<cffunction name="getClientReferralCustomFieldData" access="public" output="false" returntype="query">
		<cfargument name="clientReferralID" type="numeric" required="yes">

		<cfset var qryClientReferralCustomFieldData = "">
		
		<cfquery name="qryClientReferralCustomFieldData" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT fieldText as title, replace(fieldReference,',','') as titleOnInvoice, fieldOrder, 
				STRING_AGG(customValue,', ') as answer
			FROM dbo.fn_cf_getResponses(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.clientReferralID#">,'ClientRefCustom',NULL)
			GROUP BY fieldText, fieldReference, fieldOrder
			ORDER BY fieldOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryClientReferralCustomFieldData>
	</cffunction>

	<cffunction name="getMemberCases" output="false" returntype="query" hint="returns  member cases">
		<cfargument name="memberID" type="numeric" />
		<cfargument name="siteID" type="numeric" />
		<cfargument name="caseDateRange" type="numeric" required="false" default="0"/>		
		<cfargument name="filterReferralID" type="string" required="false" default="" />
		<cfargument name="filterClientLastName" type="string" required="false" default="" />
		<cfargument name="filterClientFirstName" type="string" required="false" default="" />
		<cfargument name="filterStatus" type="numeric" required="false" default="0" />
		<cfargument name="filterfeesDue" type="string" required="false" default=""/>	

		<cfset var local = structNew() />

		<cfset local.qryReferralSettings = getReferralSettings(siteID=arguments.siteID)>
		<cfset local.referralID = local.qryReferralSettings.referralID>

		<cfquery name="local.qryCases" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int, @orgID int, @memberID int, @referralID int, @filterReferralID varchar(100), @filterClientLastName varchar(200), 
				@filterClientFirstName varchar(200), @filterfeesDue varchar(50), @filterStatus int;
			set @siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="cf_sql_integer" />;
			set @memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="cf_sql_integer" />;
			set @referralID = <cfqueryparam value="#local.referralID#" cfsqltype="cf_sql_integer" />;
			set @filterReferralID = <cfqueryparam value="%#arguments.filterReferralID#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientLastName = <cfqueryparam value="%#arguments.filterClientLastName#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientFirstName = <cfqueryparam value="%#arguments.filterClientFirstName#%" cfsqltype="cf_sql_varchar" />;
			set @filterStatus = <cfqueryparam value="#arguments.filterStatus#" cfsqltype="cf_sql_integer" />;
			set @filterfeesDue = <cfqueryparam value="#arguments.filterfeesDue#" cfsqltype="cf_sql_varchar" />;
			select @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

			<cfif isDefined("arguments.caseDateRange") and arguments.caseDateRange>
				declare @dateCutoff datetime, @daysToLookBack int;
				set @daysToLookBack = <cfqueryparam value="#arguments.caseDateRange#" cfsqltype="cf_sql_integer" />;
				set @dateCutoff = dateadd(dd,-@daysToLookBack,getdate());
			</cfif>

			select 
				c.clientID,	c.referralID,
				c.firstName, c.middleName, c.lastName,
				c.lastName + ', ' + c.firstName as clientName,
				c.businessName, c.address1, c.address2, c.address3,
				c.city,	c.state, c.postalCode, c.countryID,
				c.email, c.homePhone, c.cellPhone, c.alternatePhone,
				c.typeID, cr.statusID,
				cr.clientReferralID, cr.typeID as referralTypeID, cr.memberID,
				cr.callUID, cr.issueDesc, cr.clientReferralDate,
				crt.clientReferralType, crt.isReferral as isLawyerReferral, 
				crt.isAgency as isAgencyReferral,
				crs.statusName, 
				crs.canEditClient as referralCanEditClient, 
				crs.canEditFilter as referralCanEditFilter, 
				crs.canEditLawyer as referralCanEditLawyer,
				crs.canRefer, crs.isReferred, crs.isAgency, crs.isPending,
				c.dateCreated, c.createdBy, c.clientParentID,
				c.dateLastUpdated, c.relationToClient,
				rc.caseID, rc.enteredByMemberID, rc.notesTxt as caseNotesTxt,
				rc.caseFees, rc.dateCaseOpened, rc.dateCaseClosed,
				rc.dateCreated as dateCaseCreated, rc.dateLastUpdated as dateCaseLastUpdated,
				(m.firstName  + 
				case
					when m.middlename is not null and len(m.middlename) > 0  then
						' ' + left(m.middleName, 1) + ' '
					else
						' '
				end  + m.lastName) as memberName
				<cfif local.qryReferralSettings.collectClientFeeFE>
					, ISNULL(clientFeesTotal.totalClientFee,0) as totalClientFee, ISNULL(clientFeesTotal.clientFeePaid,0) as clientFeePaid, ISNULL(clientFeesTotal.clientFeeDue,0) as clientFeeDue
				</cfif>
			from dbo.ref_clients c
			INNER JOIN dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
				<cfif len(arguments.filterReferralID)>
					and cr.clientReferralID like @filterReferralID
				</cfif>
			INNER JOIN dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID
				and crs.isDeleted <> 1
				and crs.isClosed <> 1
				<cfif arguments.filterStatus>
					and crs.clientReferralStatusID = @filterStatus
				</cfif>				
			INNER JOIN dbo.ref_clientTypes ct on ct.clientTypeID = c.typeID
					and ct.clientType = 'Client'
			INNER JOIN dbo.ref_clientReferralTypes crt on crt.clientReferralTypeID = cr.typeID
			INNER JOIN dbo.ref_cases rc on rc.referralID = @referralID and rc.clientReferralID = cr.clientReferralID
				<cfif isDefined("arguments.caseDateRange") and arguments.caseDateRange>
					and rc.dateCaseOpened >= @dateCutoff
				</cfif>
			INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberid = cr.memberid
			INNER JOIN dbo.ams_members m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID and m2.memberID = @memberID
			<cfif local.qryReferralSettings.collectClientFeeFE>
				OUTER APPLY dbo.fn_ref_totalClientFeeAndPaid(@orgID,c.clientID) as clientFeesTotal
			</cfif>
			where c.referralID = @referralID
			<cfif len(arguments.filterClientLastName)>
				and c.lastName like @filterClientLastName
			</cfif>
			<cfif len(arguments.filterClientFirstName)>
				and c.firstName like @filterClientFirstName
			</cfif>					
			order by c.lastName, c.firstName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn local.qryCases>
	</cffunction>
	
	<cffunction name="getMemberCasesGridData" access="public" output="false" returntype="struct">
		<cfargument name="refDateRange" type="numeric" required="false" default="0" />
		<cfargument name="filterReferralID" type="string" required="false" default="" />
		<cfargument name="filterClientLastName" type="string" required="false" default="" />
		<cfargument name="filterClientFirstName" type="string" required="false" default="" />
		<cfargument name="filterStatus" type="numeric" required="false" default="0" />
		<cfargument name="filterfeesDue" type="string" required="false" default=""/>		
		<cfargument name="start" type="numeric" required="false" default="1" />
		<cfargument name="count" type="numeric" required="false" default="1" />
		<cfargument name="sort" type="string" required="true" default="referralid"/>
		<cfargument name="orderBy" type="string" required="true" default="desc"/>		
		
		<cfset var local = structNew() />

		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcUser.memberData.IdentifiedAsMemberID gt 0>
			<cfset local.useMID = session.cfcUser.memberData.IdentifiedAsMemberID>
		<cfelse>
			<cfset local.useMID = session.cfcuser.memberdata.memberid>
		</cfif>
		
		<cfquery name="local.qryGetSiteID" datasource="#application.dsn.membercentral.dsn#">
			select dbo.fn_getSiteIDFromSiteCode('#session.mcstruct.siteCode#') as siteID
		</cfquery>

		<cfset local.qryReferralSettings = getReferralSettings(local.qryGetSiteID.siteID)>
		<cfset local.referralID = local.qryReferralSettings.referralID>

		<cfscript>
			local.count = arguments.count;
			if(arguments.start != ''){
				local.currentPage = arguments.start;
			}else{
				local.currentPage = 1;
			}
			local.startLimit = (local.currentPage - 1) * local.count + 1;
			local.endLimit = local.startLimit + (local.count - 1);
		</cfscript>
		
		<cfquery name="local.arrResults" datasource="#application.dsn.membercentral.dsn#" returntype="array">			
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tempReferralData') IS NOT NULL
				DROP TABLE ##tempReferralData;

			declare @memberID int, @referralID int, @filterReferralID varchar(100), @filterClientLastName varchar(200), 
				@filterClientFirstName varchar(200), @filterfeesDue varchar(50), @filterStatus int, @startLimit int, @endLimit int, 
				@siteID int, @orgID int, @totalCount int;
			set @memberID = <cfqueryparam value="#local.useMID#" cfsqltype="cf_sql_integer" />;
			set @referralID = <cfqueryparam value="#local.referralID#" cfsqltype="cf_sql_integer" />;
			set @filterReferralID = <cfqueryparam value="%#arguments.filterReferralID#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientLastName = <cfqueryparam value="%#arguments.filterClientLastName#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientFirstName = <cfqueryparam value="%#arguments.filterClientFirstName#%" cfsqltype="cf_sql_varchar" />;
			set @filterStatus = <cfqueryparam value="#arguments.filterStatus#" cfsqltype="cf_sql_integer" />;
			set @filterfeesDue = <cfqueryparam value="#arguments.filterfeesDue#" cfsqltype="cf_sql_varchar" />;
			set @startLimit = <cfqueryparam value="#local.startLimit#" cfsqltype="cf_sql_integer" />;
			set @endLimit = <cfqueryparam value="#local.endLimit#" cfsqltype="cf_sql_integer" />;
			set @siteID = <cfqueryparam value="#local.qryGetSiteID.siteID#" cfsqltype="cf_sql_integer">;
			select @orgID = orgID from dbo.sites where siteID = @siteID;

			<cfif arguments.refDateRange>
				declare @dateCutoff datetime, @daysToLookBack int;
				set @daysToLookBack = <cfqueryparam value="#arguments.refDateRange#" cfsqltype="cf_sql_integer" />;
				set @dateCutoff = dateadd(dd,-@daysToLookBack,getdate());
			</cfif>
			
			SELECT c.clientID,	c.referralID,
				c.firstName, c.middleName, c.lastName,
				c.lastName + ', ' + c.firstName as clientName,
				c.businessName, c.address1, c.address2, c.address3,
				c.city,	c.state, c.postalCode, c.countryID,
				c.email, c.homePhone, c.cellPhone, c.alternatePhone,
				c.typeID, cr.statusID,
				cr.clientReferralID, cr.typeID as referralTypeID, cr.memberID,
				cr.callUID, cr.issueDesc, CONVERT(varchar, cr.clientReferralDate, 101) as clientReferralDate,
				crt.clientReferralType, crt.isReferral as isLawyerReferral, 
				crt.isAgency as isAgencyReferral,
				crs.statusName, 
				crs.canEditClient as referralCanEditClient, 
				crs.canEditFilter as referralCanEditFilter, 
				crs.canEditLawyer as referralCanEditLawyer,
				crs.canRefer, crs.isReferred, crs.isAgency, crs.isPending,
				c.dateCreated, c.createdBy, c.clientParentID,
				c.dateLastUpdated, c.relationToClient,
				rc.caseID, rc.enteredByMemberID, rc.notesTxt as caseNotesTxt,
				rc.caseFees, rc.dateCaseOpened, rc.dateCaseClosed,
				rc.dateCreated as dateCaseCreated, rc.dateLastUpdated as dateCaseLastUpdated,
				(m.firstName  + 
				case
					when m.middlename is not null and len(m.middlename) > 0  then
						' ' + left(m.middleName, 1) + ' '
					else
						' '
				end  + m.lastName) as memberName,

				ISNULL(caseFeesTotal.collectedFeeTotal,0) as collectedFeeTotal,
				ISNULL(caseFeesTotal.referralDuesTotal,0) as referralDuesTotal,
				ISNULL(caseFeesTotal.amtToBePaidTotal,0) as amtToBePaidTotal,
				ISNULL(caseFeesTotal.paidToDateTotal,0) as paidToDateTotal,
				<cfif local.qryReferralSettings.collectClientFeeFE>
					ISNULL(clientFeesTotal.totalClientFee,0) as totalClientFee,
					ISNULL(clientFeesTotal.clientFeePaid,0) as clientFeePaid,
					ISNULL(clientFeesTotal.clientFeeDue,0) as clientFeeDue,
					ISNULL(caseFeesTotal.amtToBePaidTotal,0) + ISNULL(clientFeesTotal.clientFeeDue,0) as totalFeesDue,
					ISNULL(caseFeesTotal.paidToDateTotal,0) + ISNULL(clientFeesTotal.clientFeePaid,0) as totalFeesPaid,
				<cfelse>
					ISNULL(caseFeesTotal.amtToBePaidTotal,0) as totalFeesDue,
					ISNULL(caseFeesTotal.paidToDateTotal,0) as totalFeesPaid,
				</cfif>
				ROW_NUMBER() OVER ( ORDER BY 
				<cfif len(arguments.sort)>
					<cfif arguments.sort EQ "referralid">
						cr.clientReferralID 
					<cfelseif arguments.sort EQ "clientname">
						c.lastName + ', ' + c.firstName 
					<cfelseif arguments.sort EQ "referralDate">
						cr.clientReferralDate 
					<cfelseif arguments.sort EQ "collectedfromdate">
						caseFeesTotal.collectedFeeTotal 
					<cfelseif arguments.sort EQ "reffees">
						caseFeesTotal.referralDuesTotal 
					<cfelseif arguments.sort EQ "clientfees">
						clientFeesTotal.totalClientFee
					<cfelseif arguments.sort EQ "feesdue">
						<cfif local.qryReferralSettings.collectClientFeeFE>
							ISNULL(caseFeesTotal.amtToBePaidTotal,0) + ISNULL(clientFeesTotal.clientFeeDue,0)
						<cfelse>
							caseFeesTotal.amtToBePaidTotal
						</cfif>
					<cfelseif arguments.sort EQ "feespaidtodate">
						<cfif local.qryReferralSettings.collectClientFeeFE>
							ISNULL(caseFeesTotal.paidToDateTotal,0) + ISNULL(clientFeesTotal.clientFeePaid,0)
						<cfelse>
							caseFeesTotal.paidToDateTotal
						</cfif>
					</cfif>
					<cfif len(arguments.orderBy)>
							#arguments.orderBy#
					</cfif>
				<cfelse>
					cr.clientReferralID
				</cfif>
				) AS RowNum
			INTO ##tempReferralData
			FROM dbo.ref_clients c
			INNER JOIN dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
				<cfif len(arguments.filterReferralID)>
					and cr.clientReferralID like @filterReferralID
				</cfif>
			INNER JOIN dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID
				and crs.isDeleted <> 1
				and crs.isClosed <> 1
				<cfif arguments.filterStatus>
					and crs.clientReferralStatusID = @filterStatus
				</cfif>		
			INNER JOIN dbo.ref_clientTypes ct on ct.clientTypeID = c.typeID
				and ct.clientType = 'Client'
			INNER JOIN dbo.ref_clientReferralTypes crt on crt.clientReferralTypeID = cr.typeID
			INNER JOIN dbo.ref_cases rc on rc.referralID = @referralID and rc.clientReferralID = cr.clientReferralID
				<cfif arguments.refDateRange>
					and rc.dateCaseOpened >= @dateCutoff
				</cfif>
			INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberid = cr.memberid
			INNER JOIN dbo.ams_members m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID
				and m2.memberID = @memberID
			OUTER APPLY dbo.fn_tr_getCaseFeeTotalsByCaseId(@orgID,rc.caseID) as caseFeesTotal
			<cfif local.qryReferralSettings.collectClientFeeFE>
				OUTER APPLY dbo.fn_ref_totalClientFeeAndPaid(@orgID,c.clientID) as clientFeesTotal
			</cfif>
			WHERE c.referralID = @referralID
			<cfif len(arguments.filterClientLastName)>
				and c.lastName like @filterClientLastName
			</cfif>	
			<cfif len(arguments.filterClientFirstName)>
				and c.firstName like @filterClientFirstName
			</cfif>				
			<cfif len(arguments.filterfeesDue) and isNumeric(arguments.filterfeesDue)>
				AND ISNULL(caseFeesTotal.amtToBePaidTotal,0) + ISNULL(clientFeesTotal.clientFeeDue,0) = @filterfeesDue
			</cfif>
			<cfif len(arguments.filterfeesDue) and NOT isNumeric(arguments.filterfeesDue)>
				AND 1=0
			</cfif>;

			SET @totalCount = @@ROWCOUNT;
			
			SELECT *, @totalCount AS totalCount
			FROM ##tempReferralData
			WHERE RowNum >= @startLimit 
			AND RowNum <= @endLimit			
			ORDER BY RowNum;

			IF OBJECT_ID('tempdb..##tempReferralData') IS NOT NULL
				DROP TABLE ##tempReferralData;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfscript>
			local.totalCount = arrayLen(local.arrResults) ? local.arrResults[1].totalCount : 0;
			if(local.totalCount % local.count == 0){
				local.totalPages =  int(local.totalCount/local.count);
			}else{
				local.totalPages =  int(local.totalCount/local.count) + 1;
			}
			
			if(local.totalPages == 0){
				local.totalPages = 1;
			}
		</cfscript>
		
		<cfset local.gridJSON = { totalCount = int(local.totalCount), totalPages = int(local.totalPages),  currentPage = int(local.currentPage),
			sort = arguments.sort, orderby = arguments.orderBy, data = local.arrResults }>
		<cfset local.data.result = local.gridJSON>
		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>
		
	<cffunction name="getMemberReferralHistoryGridData" access="public" output="false" returntype="struct">
		<cfargument name="refDateRange" type="numeric" required="false" default="0" />
		<cfargument name="filterReferralID" type="string" required="false" default="" />
		<cfargument name="filterClientLastName" type="string" required="false" default="" />
		<cfargument name="filterClientFirstName" type="string" required="false" default="" />
		<cfargument name="filterStatus" type="numeric" required="false" default="0" />
		<cfargument name="filterAmountDue" type="string" required="false" default=""/>		
		<cfargument name="start" type="numeric" required="false" default="1" />
		<cfargument name="count" type="numeric" required="false" default="1" />
		<cfargument name="sort" type="string" required="true" default="referralid"/>
		<cfargument name="orderBy" type="string" required="true" default="desc"/>		
		
		<cfset var local = structNew() />

		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcUser.memberData.IdentifiedAsMemberID gt 0>
			<cfset local.useMID = session.cfcUser.memberData.IdentifiedAsMemberID>
		<cfelse>
			<cfset local.useMID = session.cfcuser.memberdata.memberid>
		</cfif>
		
		<cfquery name="local.qryGetSiteID" datasource="#application.dsn.membercentral.dsn#">
			select dbo.fn_getSiteIDFromSiteCode('#session.mcstruct.siteCode#') as siteID
		</cfquery>

		<cfset local.qryReferralSettings = getReferralSettings(local.qryGetSiteID.siteID)>
		<cfset local.referralID = local.qryReferralSettings.referralID>

		<cfscript>
			local.count = arguments.count;
			if(arguments.start != '') {
				local.currentPage = arguments.start;
			} else {
				local.currentPage = 1;
			}
			local.startLimit = (local.currentPage - 1) * local.count + 1;
			local.endLimit = local.startLimit + (local.count - 1);
		</cfscript>
		
		<cfquery name="local.arrResults" datasource="#application.dsn.membercentral.dsn#" returntype="array">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @memberID int, @referralID int, @filterReferralID varchar(100), @filterClientLastName varchar(200), 
				@filterClientFirstName varchar(200), @filterStatus int, @orgID int, @startLimit int, @endLimit int, 
				@totalCount int;
			SET @memberID = <cfqueryparam value="#local.useMID#" cfsqltype="cf_sql_integer" />;
			SET @referralID = <cfqueryparam value="#local.referralID#" cfsqltype="cf_sql_integer" />;
			SET @filterReferralID = <cfqueryparam value="%#arguments.filterReferralID#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientLastName = <cfqueryparam value="%#arguments.filterClientLastName#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientFirstName = <cfqueryparam value="%#arguments.filterClientFirstName#%" cfsqltype="cf_sql_varchar" />;
			set @filterStatus = <cfqueryparam value="#arguments.filterStatus#" cfsqltype="cf_sql_integer" />;
			SET @orgID = dbo.fn_getOrgIDFromSiteID(<cfqueryparam value="#local.qryGetSiteID.siteID#" cfsqltype="cf_sql_integer" />);

			<cfif isDefined("arguments.refDateRange") and arguments.refDateRange>
				DECLARE @dateCutoff datetime, @daysToLookBack int;
				SET @daysToLookBack = <cfqueryparam value="#arguments.refDateRange#" cfsqltype="cf_sql_integer" />;
				SET @dateCutoff = dateadd(dd,-@daysToLookBack,getdate());
			</cfif>
			set @startLimit = <cfqueryparam value="#local.startLimit#" cfsqltype="cf_sql_integer" />;
			set @endLimit = <cfqueryparam value="#local.endLimit#" cfsqltype="cf_sql_integer" />;
			
			IF OBJECT_ID('tempdb..##tempReferralData') IS NOT NULL
				DROP TABLE ##tempReferralData;
			IF OBJECT_ID('tempdb..##tmpClientFeeTrans') IS NOT NULL 
				DROP TABLE ##tmpClientFeeTrans; 
			IF OBJECT_ID('tempdb..##tmpRefFeeTrans') IS NOT NULL 
				DROP TABLE ##tmpRefFeeTrans; 
			IF OBJECT_ID('tempdb..##tmpClientsForFee') IS NOT NULL 
				DROP TABLE ##tmpClientsForFee; 
			IF OBJECT_ID('tempdb..##tmpClientsForFeeResult') IS NOT NULL 
				DROP TABLE ##tmpClientsForFeeResult; 
			CREATE TABLE ##tmpClientFeeTrans (traItemID int, duePending decimal(18,2));
			CREATE TABLE ##tmpRefFeeTrans (caseID int, duePending decimal(18,2));
			CREATE TABLE ##tmpClientsForFee (traItemID int PRIMARY KEY);
			CREATE TABLE ##tmpClientsForFeeResult (traItemID int, transactionID int);
			
			select isCase = case when rc.caseID is not null then 1 else 0 end,	
				m.memberID as mID,					
				c.clientID,	c.referralID, 
				c.firstName, c.middleName, c.lastName,
				c.lastName + ', ' + c.firstName as clientName,
				c.businessName,	
				c.address1, c.address2,	c.address3,
				c.city,	c.state, c.postalCode, c.countryID,
				c.email,c.homePhone, c.cellPhone, c.alternatePhone,
				c.typeID as clientTypeId,
				crs.statusName, crs.isReferred, crs.isAgency, crs.isPending,
				c.dateCreated as clentDateCreated,	c.createdBy, c.dateLastUpdated as clientDateLastUpdated, 
				c.clientParentID,
				cr.clientReferralID, cr.memberID as crMemberId, cr.enteredByMemberID as crEnteredByMemberID,
				cr.sourceID,
				cr.communicateLanguageID, cr.issueDesc, 
				isNull(cr.sendSurvey, 0) as sendSurvey, isNull(cr.sendNewsBlog,0) as sendNewsBlog, 
				cr.statusID, cr.typeID, 
				CONVERT(varchar, cr.clientReferralDate, 101) as clientReferralDate,
				cr.clientReferralDate as clientReferralDateTime,  cr.dateCreated, cr.lastUpdatedBy, cr.dateLastUpdated,
				rep.clientID as repID,	rep.firstName as repFirstName, rep.lastName as repLastName,
				rep.lastName + ', ' + rep.firstName as repName,
				rep.address1 as repAddress1, rep.address2 as repAddress2, rep.address3 as repAddress3,
				rep.city as repCity, rep.state as repState, rep.postalCode as repPostalCode, rep.countryID as repCountryID,
				rep.email as repEmail, rep.homePhone as repHomePhone, rep.cellPhone as repCellPhone, rep.alternatePhone as repAlternatePhone,
				rep.typeID as repTypeID, rep.relationToClient, rep.clientParentID as repParentID,
				m.memberID, m.firstName as memFirstName, m.middleName as memMiddleName, m.lastName as memLastname, m.memberNumber,
				m.prefix, m.suffix,
				(m.firstName  + 
				case
					when m.middlename is not null and len(m.middlename) > 0  then
						' ' + left(m.middleName, 1) + ' '
					else  
						' '
				end  + m.lastName) as memberName,
				datediff(day,rc.dateLastUpdated,getDate()) as daysElapsed
				, rc.caseID, rc.enteredByMemberID, rc.notesTxt as caseNotesTxt,
				rc.caseFees, rc.dateCaseOpened, rc.dateCaseClosed,
				rc.dateCreated as dateCaseCreated, rc.dateLastUpdated as dateCaseLastUpdated,
				CAST(0 as decimal(18,2)) as duePending
			INTO ##tempReferralData 
			FROM dbo.ref_clients c
			INNER JOIN dbo.ref_clientTypes ct on ct.clientTypeID = c.typeID
				and ct.clientType = 'Client'
			INNER JOIN dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
			INNER JOIN dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID
				AND crs.isCoordination = 0
			LEFT OUTER JOIN dbo.ref_clients rep on rep.referralID = @referralID and rep.clientID = cr.representativeID					
			INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberid = cr.memberID
			INNER JOIN dbo.ams_members m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID
				and m2.memberID = @memberID
			LEFT OUTER JOIN dbo.ref_cases rc on rc.referralID = @referralID and rc.clientReferralID = cr.clientReferralID
			WHERE c.referralID = @referralID
			<cfif len(arguments.filterClientLastName)>
				AND c.lastName like @filterClientLastName
			</cfif>	
			<cfif len(arguments.filterClientFirstName)>
				AND c.firstName like @filterClientFirstName
			</cfif>
			<cfif val(arguments.refDateRange)>
				AND cr.clientReferralDate >= @dateCutoff
			</cfif>
			<cfif len(arguments.filterReferralID)>
				AND cr.clientReferralID like @filterReferralID
			</cfif>
			<cfif arguments.filterStatus>
				AND crs.clientReferralStatusID = @filterStatus
			</cfif>;

			SET @totalCount = @@ROWCOUNT;

			-- ref fees
			INSERT INTO ##tmpClientsForFee (traItemID)
			SELECT DISTINCT cf.collectedFeeID
			FROM ##tempReferralData AS tmp
			INNER JOIN dbo.ref_collectedFees AS cf ON cf.referralID = @referralID 
				AND cf.caseID = tmp.caseID;

			EXEC dbo.ref_clientReferralTransactionsByBulk @orgID=@orgID, @feeType='referralFee';

			INSERT INTO ##tmpRefFeeTrans (caseID, duePending)
			select cf.caseID, SUM(ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount- cache_pendingPaymentAllocatedAmount)
			from ##tmpClientsForFeeResult as fees
			inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fees.transactionid
			inner join dbo.ref_collectedFees as cf on cf.referralID = @referralID and cf.collectedFeeID = fees.traItemID
			group by cf.caseID;

			-- clear tables
			TRUNCATE TABLE ##tmpClientsForFee;
			TRUNCATE TABLE ##tmpClientsForFeeResult;

			<cfif local.qryReferralSettings.collectClientFeeFE>
				-- client fees
				INSERT INTO ##tmpClientsForFee (traItemID)
				SELECT DISTINCT ISNULL(clientParentID,clientID)
				FROM ##tempReferralData;

				EXEC dbo.ref_clientReferralTransactionsByBulk @orgID=@orgID, @feeType='clientReferralFee';

				INSERT INTO ##tmpClientFeeTrans (traItemID, duePending)
				select fees.traItemID, SUM(ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount- cache_pendingPaymentAllocatedAmount)
				from ##tmpClientsForFeeResult as fees
				inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fees.transactionid
				group by fees.traItemID;
			</cfif>

			UPDATE tmp
			SET tmp.duePending = ISNULL(tmp2.duePending,0) <cfif local.qryReferralSettings.collectClientFeeFE> + ISNULL(tmp3.duePending,0)</cfif>
			FROM ##tempReferralData AS tmp
			LEFT OUTER JOIN ##tmpRefFeeTrans AS tmp2 ON tmp2.caseID = tmp.caseID
			<cfif local.qryReferralSettings.collectClientFeeFE>
				LEFT OUTER JOIN ##tmpClientFeeTrans AS tmp3 ON tmp3.traItemID = ISNULL(tmp.clientParentID,tmp.clientID)
			</cfif>;
			
			WITH ResultsCTE as (
				SELECT ROW_NUMBER() OVER ( ORDER BY 
					<cfif len(arguments.sort)>
						<cfif arguments.sort EQ "referralid">
							clientReferralID 
						<cfelseif arguments.sort EQ "clientname">
							lastName + firstName
						<cfelseif arguments.sort EQ "description">
							issueDesc 
						<cfelseif arguments.sort EQ "status">
							statusName 
						<cfelseif arguments.sort EQ "iscase">
							case when caseID is not null then 1 else 0 end	
						<cfelseif arguments.sort EQ "isamountdue">
							duePending	
						<cfelseif arguments.sort EQ "referraldate">
							clientReferralDateTime
						</cfif>
						<cfif len(arguments.orderBy)>
							 #arguments.orderBy#
						</cfif>
					<cfelse>
						clientReferralID
					</cfif>
				) AS RowNum,* from ##tempReferralData 
				where 1=1
				<cfif len(arguments.filterAmountDue)>
					<cfif arguments.filterAmountDue EQ "Yes">
						and duePending is not null and duePending > 0                       
					<cfelse>
						and (duePending is null or duePending <= 0)
					</cfif>
				</cfif> 
			)
			SELECT *, @totalCount AS totalCount
			FROM ResultsCTE
			WHERE RowNum >= @startLimit AND RowNum <= @endLimit
			ORDER BY RowNum;

			IF OBJECT_ID('tempdb..##tempReferralData') IS NOT NULL
				DROP TABLE ##tempReferralData;
			IF OBJECT_ID('tempdb..##tmpClientFeeTrans') IS NOT NULL 
				DROP TABLE ##tmpClientFeeTrans; 
			IF OBJECT_ID('tempdb..##tmpRefFeeTrans') IS NOT NULL 
				DROP TABLE ##tmpRefFeeTrans; 
			IF OBJECT_ID('tempdb..##tmpClientsForFee') IS NOT NULL 
				DROP TABLE ##tmpClientsForFee; 
			IF OBJECT_ID('tempdb..##tmpClientsForFeeResult') IS NOT NULL 
				DROP TABLE ##tmpClientsForFeeResult; 

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfscript>
			local.totalCount = arrayLen(local.arrResults) ? local.arrResults[1].totalCount : 0;
			if(local.totalCount % local.count == 0) {
				local.totalPages =  int(local.totalCount/local.count);
			} else {
				local.totalPages =  int(local.totalCount/local.count) + 1;
			}
			
			if(local.totalPages == 0){
				local.totalPages = 1;
			}
		</cfscript>
		
		<cfset local.gridJSON = { totalCount = int(local.totalCount), totalPages = int(local.totalPages), currentPage = int(local.currentPage),
			sort = arguments.sort, orderby = arguments.orderBy, data = local.arrResults }>
		<cfset local.data.result = local.gridJSON>
		<cfset local.data.success = true>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getMemberReferralHistory" output="false" returntype="query" hint="returns all referral history">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="refDateRange" type="numeric" required="false" default="0" />
		<cfargument name="filterReferralID" type="string" required="false" default="" />
		<cfargument name="filterClientLastName" type="string" required="false" default="" />
		<cfargument name="filterClientFirstName" type="string" required="false" default="" />
		<cfargument name="filterStatus" type="numeric" required="false" default="0" />
		<cfargument name="filterAmountDue" type="string" required="false" default="" />

		<cfset var local = structNew()>

		<cfset local.qryReferralSettings = getReferralSettings(siteID=arguments.siteID)>
		<cfset local.referralID = local.qryReferralSettings.referralID>

		<cfquery name="local.qryReferralHistory" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @memberID int, @referralID int, @filterReferralID varchar(100), @filterClientLastName varchar(200), 
				@filterClientFirstName varchar(200), @filterStatus int, @orgID int;
			set @memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="cf_sql_integer" />;
			set @referralID = <cfqueryparam value="#local.referralID#" cfsqltype="cf_sql_integer" />;
			set @filterReferralID = <cfqueryparam value="%#arguments.filterReferralID#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientLastName = <cfqueryparam value="%#arguments.filterClientLastName#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientFirstName = <cfqueryparam value="%#arguments.filterClientFirstName#%" cfsqltype="cf_sql_varchar" />;
			set @filterStatus = <cfqueryparam value="#arguments.filterStatus#" cfsqltype="cf_sql_integer" />;
			set @orgID = dbo.fn_getOrgIDFromSiteID(<cfqueryparam value="#arguments.siteID#" cfsqltype="cf_sql_integer" />);

			IF OBJECT_ID('tempdb..##tempReferralData') IS NOT NULL
				DROP TABLE ##tempReferralData;

			<cfif isDefined("arguments.refDateRange") and arguments.refDateRange>
				declare @dateCutoff datetime, @daysToLookBack int;
				set @daysToLookBack = <cfqueryparam value="#arguments.refDateRange#" cfsqltype="cf_sql_integer" />;
				set @dateCutoff = dateadd(dd,-@daysToLookBack,getdate());
			</cfif>

			select
				isCase = case when rc.caseID is not null then 1 else 0 end,	
				m.memberID as mID,					
				c.clientID,	c.referralID, 
				c.firstName, c.middleName, c.lastName,
				c.lastName + ', ' + c.firstName as clientName,
				c.businessName,	
				c.address1, c.address2,	c.address3,
				c.city,	c.state, c.postalCode, c.countryID,
				c.email,c.homePhone, c.cellPhone, c.alternatePhone,
				c.typeID, cr.statusID, 
				crs.statusName, crs.isReferred, crs.isAgency, crs.isPending,
				c.dateCreated,	c.createdBy, c.dateLastUpdated,
				c.clientParentID,
				cr.clientReferralID, cr.memberID, cr.enteredByMemberID,
				cr.sourceID,
				cr.communicateLanguageID, cr.issueDesc, 
				isNull(cr.sendSurvey, 0) as sendSurvey, isNull(cr.sendNewsBlog,0) as sendNewsBlog,
				cr.clientReferralDate, cr.lastUpdatedBy,
				rep.clientID as repID,	rep.firstName as repFirstName, rep.lastName as repLastName,
				rep.lastName + ', ' + rep.firstName as repName,
				rep.address1 as repAddress1, rep.address2 as repAddress2, rep.address3 as repAddress3,
				rep.city as repCity, rep.state as repState, rep.postalCode as repPostalCode, rep.countryID as repCountryID,
				rep.email as repEmail, rep.homePhone as repHomePhone, rep.cellPhone as repCellPhone, rep.alternatePhone as repAlternatePhone,
				rep.typeID as repTypeID, rep.relationToClient, rep.clientParentID as repParentID,
				m.firstName as memFirstName, m.middleName as memMiddleName, m.lastName as memLastname, m.memberNumber,
				m.prefix, m.suffix,
				(m.firstName  + 
				case
					when m.middlename is not null and len(m.middlename) > 0  then
						' ' + left(m.middleName, 1) + ' '
					else
						' '
				end  + m.lastName) as memberName,
				datediff(day,rc.dateLastUpdated,getDate()) as daysElapsed
				, rc.caseID, rc.notesTxt as caseNotesTxt,
				rc.caseFees, rc.dateCaseOpened, rc.dateCaseClosed,
				rc.dateCreated as dateCaseCreated, rc.dateLastUpdated as dateCaseLastUpdated,
				ISNULL((
					select sum(ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount)
					from dbo.tr_applications tra 
					inner join dbo.tr_transactionSales ts on ts.orgID = @orgID 
						and ts.transactionID = tra.transactionID 
					inner join dbo.tr_transactions t on t.transactionID = ts.transactionID					
					inner join dbo.ref_collectedFees cf on cf.referralID = @referralID and cf.collectedFeeID = tra.itemID
					where tra.orgID = @orgID 
					and cf.caseID = rc.caseID
				),0)
				<cfif local.qryReferralSettings.collectClientFeeFE>
					+ ISNULL((
						SELECT clientFeeDue
						FROM dbo.fn_ref_totalClientFeeAndPaid(@orgID,c.clientID)
					),0)
				</cfif>
				as duePending
			into ##tempReferralData					
			from dbo.ref_clients c
			INNER JOIN dbo.ref_clientTypes ct on ct.clientTypeID = c.typeID
				and ct.clientType = 'Client'
			LEFT OUTER JOIN dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
				<cfif isDefined("arguments.refDateRange") and val(arguments.refDateRange)>
					and cr.clientReferralDate >= @dateCutoff
				</cfif>
				<cfif len(arguments.filterReferralID)>
					and cr.clientReferralID like @filterReferralID
				</cfif>
			LEFT OUTER JOIN dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID
				<cfif arguments.filterStatus>
					and crs.clientReferralStatusID = @filterStatus
				</cfif>
			LEFT OUTER JOIN dbo.ref_clients rep on rep.referralID = @referralID and rep.clientID = cr.representativeID					
			INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberid = cr.memberID
			INNER JOIN dbo.ams_members m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID
				and m2.memberID = @memberID
			LEFT OUTER JOIN dbo.ref_cases rc on rc.referralID = @referralID and rc.clientReferralID = cr.clientReferralID
			where c.referralID = @referralID
			<cfif len(arguments.filterClientLastName)>
				and c.lastName like @filterClientLastName
			</cfif>
			<cfif len(arguments.filterClientFirstName)>
				and c.firstName like @filterClientFirstName
			</cfif>;
			
			select *
			from ##tempReferralData 
			where 1=1				
			<cfif len(arguments.filterAmountDue)>
				<cfif arguments.filterAmountDue EQ "Yes">
					and duePending is not null and duePending > 0
				<cfelse>
					and (duePending is null or duePending <= 0)
				</cfif>
			</cfif>		
			order by clientReferralID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn local.qryReferralHistory>
	</cffunction>

	<cffunction name="saveClientReferral" access="public" output="false" returntype="void" >
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.clientID = arguments.event.getValue('clientID')>
		<cfset local.clientParentID = val(arguments.event.getValue('clientParentID',0))>
		<cfset local.repID = val(arguments.event.getValue('repID',0))>
		<cfset local.repParentID = val(arguments.event.getValue('repParentID',0))>
		<cfset local.clientReferralID = arguments.event.getValue('clientReferralID')>
		<cfset local.memberID = arguments.event.getValue('memberID')>
		<cfset local.memberLoggedInID = arguments.event.getValue('memberLoggedInID')>		
		<cfset local.referralAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Referrals', siteID=arguments.event.getValue('mc_siteinfo.siteID'))>
		<cfset local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
		<cfset local.attorneyCustomFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='Referrals', areaName='Attorney', 
													csrid=local.referralAdminSiteResourceID, detailID=0, hideAdminOnly=1)>		
		<cfset local.attorneyCustomFieldsArr = xmlParse(local.attorneyCustomFieldsXML.returnXML).xmlRoot.xmlChildren>

		<cfset local.referralChangeArr =  arrayNew(1)>
		<!--- put attorney custom fields and field types into array --->
		<cfset local.arrAttorneyCustomFields = []>
		<cfif arrayLen(local.attorneyCustomFieldsArr) AND arguments.event.valueExists('hasAttorneyFields_#local.clientReferralID#')>
			<cfloop array="#local.attorneyCustomFieldsArr#" index="local.thisfield">
				<cfset local.tmpAtt = local.thisfield.xmlattributes>

				<cfset local.tmpStr = { fieldID=local.tmpAtt.fieldID,
										displayTypeCode=local.tmpAtt.displayTypeCode, 
										dataTypeCode=local.tmpAtt.dataTypeCode, 
										fieldText=local.tmpAtt.fieldText, 
										value=arguments.event.getTrimValue('cf_#local.tmpAtt.fieldID#_','') }>
				<cfset arrayAppend(local.arrAttorneyCustomFields,local.tmpStr)>
			</cfloop>
			<cfset local.referralChangeArr.addAll(local.objResourceCustomFields.getFieldChanges(arrFieldData=local.arrAttorneyCustomFields, itemID=local.clientReferralID, itemType='AttorneyCustom', prependUsageName="Attorney Field"))>
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySaveClientReferral">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @clientID int = 0, @repID int = 0, @clientParentID int, @repParentID int,
					@referralID int = <cfqueryparam value="#arguments.event.getValue('referralID')#" cfsqltype="cf_sql_integer" >,
					@clientReferralID int = <cfqueryparam value="#local.clientReferralID#" cfsqltype="cf_sql_integer" />,
					<!--- client info --->
					@firstName varchar(75) = <cfqueryparam value="#arguments.event.getValue('firstName')#" cfsqltype="cf_sql_varchar" />,
					@middleName varchar(25) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('middleName'))#" cfsqltype="cf_sql_varchar" />,''),
					@lastName varchar(75) = <cfqueryparam value="#arguments.event.getValue('lastName')#" cfsqltype="cf_sql_varchar" />,
					@businessName varchar(100) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('businessName',''))#" cfsqltype="cf_sql_varchar" />,''),
					@address1 varchar(100) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('address1',''))#" cfsqltype="cf_sql_varchar" />,''),
					@address2 varchar(100) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('address2',''))#" cfsqltype="cf_sql_varchar" />,''),
					@city varchar(100) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('city',''))#" cfsqltype="cf_sql_varchar" />,''),
					@state int = NULLIF(<cfqueryparam value="#val(trim(arguments.event.getValue('state',0)))#" cfsqltype="cf_sql_integer" />,0),
					@postalCode varchar(25) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('postalCode',''))#" cfsqltype="cf_sql_varchar" />,''),
					@email varchar(255) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('email',''))#" cfsqltype="cf_sql_varchar" />,''),
					@homePhone varchar(40) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('homePhone',''))#" cfsqltype="cf_sql_varchar" />,''),
					@cellPhone varchar(40) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('cellPhone',''))#" cfsqltype="cf_sql_varchar" />,''),
					@alternatePhone varchar(40) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('alternatePhone',''))#" cfsqltype="cf_sql_varchar" />,''),
					@homePhoneE164 varchar(40) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('homePhoneE164',''))#" cfsqltype="cf_sql_varchar" />,''),
					@cellPhoneE164 varchar(40) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('cellPhoneE164',''))#" cfsqltype="cf_sql_varchar" />,''),
					@alternatePhoneE164 varchar(40) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('alternatePhoneE164',''))#" cfsqltype="cf_sql_varchar" />,''),
					@countryID int = NULLIF(<cfqueryparam value="#val(trim(arguments.event.getValue('countryID',0)))#" cfsqltype="cf_sql_integer" />,0),
					<!--- representative info --->
					@repFirstName varchar(75) = <cfqueryparam value="#arguments.event.getValue('repFirstName')#" cfsqltype="cf_sql_varchar" />,
					@repLastName varchar(75) = <cfqueryparam value="#arguments.event.getValue('repLastName')#" cfsqltype="cf_sql_varchar" />,
					@repAddress1 varchar(100) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('repAddress1',''))#" cfsqltype="cf_sql_varchar" />,''),
					@repAddress2 varchar(100) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('repAddress2',''))#" cfsqltype="cf_sql_varchar" />,''),
					@repCity varchar(100) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('repCity',''))#" cfsqltype="cf_sql_varchar" />,''),
					@repState int = NULLIF(<cfqueryparam value="#val(trim(arguments.event.getValue('repState',0)))#" cfsqltype="cf_sql_integer" />,0),
					@repPostalCode varchar(25) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('repPostalCode',''))#" cfsqltype="cf_sql_varchar" />,''),
					@repEmail varchar(255) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('repEmail',''))#" cfsqltype="cf_sql_varchar" />,''),
					@repHomePhone varchar(40) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('repHomePhone',''))#" cfsqltype="cf_sql_varchar" />,''),
					@repCellPhone varchar(40) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('repCellPhone',''))#" cfsqltype="cf_sql_varchar" />,''),
					@repAlternatePhone varchar(40) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('repAlternatePhone',''))#" cfsqltype="cf_sql_varchar" />,''),
					@repHomePhoneE164 varchar(40) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('repHomePhoneE164',''))#" cfsqltype="cf_sql_varchar" />,''),
					@repCellPhoneE164 varchar(40) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('repCellPhoneE164',''))#" cfsqltype="cf_sql_varchar" />,''),
					@repAlternatePhoneE164 varchar(40) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('repAlternatePhoneE164',''))#" cfsqltype="cf_sql_varchar" />,''),
					@relationToClient varchar(100) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('relationToClient',''))#" cfsqltype="cf_sql_varchar" />,''),
					@memberLoggedInID int = NULLIF(<cfqueryparam value="#local.memberLoggedInID#" cfsqltype="cf_sql_integer" />,0),
					<!--- client referral info --->
					@sourceID int = <cfqueryparam value="#arguments.event.getValue('sourceID')#" cfsqltype="cf_sql_integer" />,
					@otherSource varchar(100) = NULLIF(<cfqueryparam value="#trim(arguments.event.getValue('otherSource',''))#" cfsqltype="cf_sql_varchar" />,''),
					@communicateLanguageID int = <cfqueryparam value="#arguments.event.getValue('communicateLanguageID')#" cfsqltype="cf_sql_integer" />,
					@issueDesc varchar(max) = <cfqueryparam value="#arguments.event.getValue('issueDesc')#" cfsqltype="cf_sql_varchar" />,
					@caseFeeTypeID int = NULLIF(<cfqueryparam value="#val(trim(arguments.event.getValue('caseFeeTypeID',0)))#" cfsqltype="cf_sql_integer" />,0),
					@sendSurvey bit = <cfqueryparam value="#val(arguments.event.getValue('sendSurvey',0))#" cfsqltype="cf_sql_bit" />,
					@sendNewsBlog bit = <cfqueryparam value="#val(arguments.event.getValue('sendNewsBlog',0))#" cfsqltype="cf_sql_bit" />,
					@statusID int = NULLIF(<cfqueryparam value="#val(arguments.event.getValue('statusID',0))#" cfsqltype="cf_sql_integer" />,0);

				<!--- variables to be used within customFields script --->
				DECLARE @valueID int, @fieldID int, @detail varchar(max), @dataID int;

				<cfif local.clientParentID and local.memberLoggedInID eq local.memberID>
					SET @clientID = <cfqueryparam value="#local.clientID#" cfsqltype="cf_sql_integer" />;
					SET @clientParentID = NULLIF(<cfqueryparam value="#local.clientParentID#" cfsqltype="cf_sql_integer" />,0);
				<cfelse>
					<!--- new client record to be created (with current client as parent) --->
					SET @clientID = 0;
					SET @clientParentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('clientID')#">;
				</cfif>

				<cfif NOT local.repID>
					SET @repID = 0;
					SET @repParentID = NULLIF(<cfqueryparam value="#local.repParentID#" cfsqltype="cf_sql_integer" />,0);
				<cfelseif local.repID and local.memberLoggedInID eq local.memberID>
					SET @repID = <cfqueryparam value="#local.repID#" cfsqltype="cf_sql_integer" />;
					SET @repParentID = NULLIF(<cfqueryparam value="#local.repParentID#" cfsqltype="cf_sql_integer" />,0);
				<cfelseif local.repID and NOT local.repParentID and local.memberLoggedInID neq local.memberID>
					<!--- new representative record to be created (with current rep as parent) --->
					SET @repID = 0;
					SET @repParentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.repID#">;
				</cfif>

				BEGIN TRAN;

					EXEC dbo.ref_updateClientReferral @referralID=@referralID, @clientReferralID=@clientReferralID, @clientID=@clientID,
						@firstName=@firstName, @middleName=@middleName, @lastName=@lastName, @businessName=@businessName,
						@address1=@address1, @address2=@address2, @city=@city, @state=@state, @postalCode=@postalCode, @countryID=@countryID,
						@email=@email, @homePhone=@homePhone, @cellPhone=@cellPhone, @alternatePhone=@alternatePhone, 
						@homePhoneE164=@homePhoneE164, @cellPhoneE164=@cellPhoneE164, @alternatePhoneE164=@alternatePhoneE164, @clientParentID=@clientParentID,
						@repID=@repID, @repFirstName=@repFirstName, @repLastName=@repLastName, @repAddress1=@repAddress1, @repAddress2=@repAddress2,
						@repCity=@repCity, @repState=@repState, @repPostalCode=@repPostalCode, @repEmail=@repEmail, @repHomePhone=@repHomePhone,
						@repCellPhone=@repCellPhone, @repAlternatePhone=@repAlternatePhone, 
						@repHomePhoneE164=@repHomePhoneE164, @repCellPhoneE164=@repCellPhoneE164, @repAlternatePhoneE164=@repAlternatePhoneE164, @relationToClient=@relationToClient, @repParentID=@repParentID,
						@sourceID=@sourceID, @otherSource=@otherSource, @communicateLanguageID=@communicateLanguageID, @issueDesc=@issueDesc, @agencyID=NULL,
						@caseFeeTypeID=@caseFeeTypeID, @callTypeID=NULL, @sendSurvey=@sendSurvey, @sendNewsBlog=@sendNewsBlog, @statusID=@statusID,
						@panelID=NULL, @counselorMemberID=NULL, @updateMode='FE', @enteredByMemberID=@memberLoggedInID;

					<cfif arrayLen(local.arrAttorneyCustomFields)>
						<cfloop array="#local.arrAttorneyCustomFields#" index="local.cf">
							<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.cf.displayTypeCode)>
								<cfset local.tempSQL = editRef_cf_option(itemType='AttorneyCustom', itemID=local.clientReferralID, fieldID=local.cf.fieldID, valueIDList=local.cf.value, itemIDSQLVar='@clientReferralID')>
								#preserveSingleQuotes(local.tempSQL)#
							<cfelse>
								<cfset local.tempSQL = editRef_cf_nonOption(itemType='AttorneyCustom', itemID=local.clientReferralID, fieldID=local.cf.fieldID, customText=local.cf.value, itemIDSQLVar='@clientReferralID')>
								#preserveSingleQuotes(local.tempSQL)#
							</cfif>
						</cfloop>
					</cfif>
				COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	
		<cftry>
			<cfif arrayLen(local.referralChangeArr)>
				<cfset createObject('component','model.system.platform.history').addReferralUpdateHistory(orgID=int(arguments.event.getValue('mc_siteinfo.orgid')),
					clientReferralID=int(local.clientReferralID), actorMemberID=int(arguments.event.getValue('memberLoggedInID')), 
					mainMessage="Attorney Field Values Changed (Member)", changes=local.referralChangeArr)>
			</cfif>

			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local) />
			</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="saveClientReferralStatus" access="public" output="false" returntype="void" >
		<cfargument name="Event" type="any" />

		<cfset var local = structNew() />
		<cfset local.clientReferralID = arguments.event.getValue('clientReferralID') />
		<cfif (NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcUser.memberData.IdentifiedAsMemberID gt 0)>
			<cfset local.useMID = session.cfcUser.memberData.IdentifiedAsMemberID>
		<cfelse>
			<cfset local.useMID = session.cfcuser.memberdata.memberid>
		</cfif>

		<cfquery name="local.updateClientReferralData" datasource="#application.dsn.membercentral.dsn#">	
			update dbo.ref_clientReferrals
			set statusID = <cfqueryparam value="#arguments.event.getValue('statusID')#" cfsqltype="cf_sql_integer" />,	
				lastUpdatedBy = <cfqueryparam value="#local.useMID#" cfsqltype="cf_sql_integer" />,
				dateLastUpdated = getDate()																	
			where clientReferralID = <cfqueryparam value="#local.clientReferralID#" cfsqltype="cf_sql_integer"  />
		</cfquery>
	</cffunction>	
	
	<cffunction name="addCase" access="public" output="false" returntype="numeric" hint="Create a new case" >
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew()>
		<cfset local.caseid = 0>
		
		<cfquery name="local.qryCreateCase" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @clientReferralID int = <cfqueryparam value="#arguments.event.getValue('clientReferralID')#" cfsqltype="cf_sql_integer" />;
			DECLARE @referralID int;

			SELECT @referralID = referralID
			FROM dbo.ref_clientReferrals
			WHERE clientReferralID = @clientReferralID;

			insert into dbo.ref_cases (referralID, clientReferralID, enteredByMemberID, dateCaseOpened, dateCreated, feeStructureID)
			values (@referralID, @clientReferralID, <cfqueryparam value="#arguments.event.getValue('memberLoggedInID')#" cfsqltype="cf_sql_integer" />,
				getDate(), getDate(), 
				<cfif val(arguments.event.getValue('feeStructureID',''))>
					<cfqueryparam value="#val(arguments.event.getValue('feeStructureID'))#" cfsqltype="cf_sql_integer" />
				<cfelse>
					NULL
				</cfif>
			);

			select scope_identity() as newCaseID;
		</cfquery>
		
		<cfif val(local.qryCreateCase.newCaseID)>
			<cfset local.caseid = local.qryCreateCase.newCaseID />
			<cfset createObject('component','model.system.platform.history').addReferralUpdateHistory(orgID=int(arguments.event.getValue('mc_siteinfo.orgid')),
				clientReferralID=int(arguments.event.getValue('clientReferralID')), actorMemberID=int(arguments.event.getValue('memberLoggedInID')), 
				mainMessage="Case Retained")>			
		</cfif>
		
		<cfreturn local.caseid>		
	</cffunction>	
	
	<cffunction name="addFeeCollection" access="public" output="false" returntype="numeric" hint="Add fee">
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew()>
		<cfset local.collectedFeeID = 0>
		<cfset local.insertFeePercent = 1>
		<cfif val(arguments.event.getValue('referralAmount'))>
			<cfset local.insertFeePercent = 0>
		</cfif>
		
		<cfquery name="local.qryAddFee" datasource="#application.dsn.membercentral.dsn#">		
			SET NOCOUNT ON;

			DECLARE @caseID int, @referralID int;
			SET @caseID = <cfqueryparam value="#arguments.event.getValue('caseID')#" cfsqltype="cf_sql_integer">;
			SELECT @referralID = referralID FROM dbo.ref_cases where caseID = @caseID;

			insert into dbo.ref_collectedFees(referralID, caseID, enteredByMemberID, collectedFee, 
				<cfif len(trim(arguments.event.getValue('filingFee','')))>filingFee,</cfif>
				collectedFeeDate, <cfif val(arguments.event.getValue('referralFeePercent')) and local.insertFeePercent>referralFeePercent,</cfif>
				<cfif not local.insertFeePercent>referralAmount,</cfif> dateCreated)
			values (@referralID, @caseID, 
				<cfqueryparam value="#arguments.event.getValue('memberLoggedInID')#" cfsqltype="cf_sql_integer" />,
				<cfqueryparam value="#replace(arguments.event.getValue('collectedFee'),',','','ALL')#" cfsqltype="CF_SQL_DECIMAL" scale="2" />,
				<cfif len(trim(arguments.event.getValue('filingFee','')))>
					<cfqueryparam value="#replace(arguments.event.getValue('filingFee'),',','','ALL')#" cfsqltype="CF_SQL_DECIMAL" scale="2" />,
				</cfif>
				getDate(),
				<cfif len(trim(arguments.event.getValue('referralFeePercent'))) and val(arguments.event.getValue('referralFeePercent')) and local.insertFeePercent>
					<cfqueryparam value="#val(arguments.event.getValue('referralFeePercent'))#" cfsqltype="cf_sql_decimal" scale="2">,
				</cfif>
				<cfif not local.insertFeePercent>
					<cfqueryparam value="#replace(arguments.event.getValue('referralAmount'),',','','ALL')#" cfsqltype="CF_SQL_DECIMAL" scale="2" />,
				</cfif>						
				getDate()
			);
			
			select SCOPE_IDENTITY() as newCollectedFeeID;
		</cfquery>
		
		<cfif val(local.qryAddFee.newCollectedFeeID)>
			<cfset local.collectedFeeID = local.qryAddFee.newCollectedFeeID>
		</cfif>
		
		<cfreturn local.collectedFeeID>
	</cffunction>		
	
	<cffunction name="getReferralSettings" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true" />
		
		<cfset var qryReferralInfo = "">
		
		<cfquery name="qryReferralInfo" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			select r.referralID, r.title, r.applicationInstanceID, r.emailRecipient, r.mainContentID, r.GLAccountID, r.dateCreated, 
				r.clientMailTopTxt, r.clientMailBottomTxt, r.memberMailTopTxt, r.memberMailBottomTxt, r.systemMailCaseActivityTxt,
				r.feeInformationTopTxt, r.referralStatementInstructions, r.dspPanelList, r.allowPanelMgmt, r.rcPanelInstructionsTxt,
				r.panelMemberStatusID, r.maxNumberOfPanels, r.panelMemberApplicationStatusID, r.allowFeeTypeMgmt, r.dspLessFilingFeeCosts,
				r.dspForwardingFeeAmount, r.dspMonthReportLink, r.feeDeclarationTxt, r.monthlyReportTopTxt, r.monthlyReportBottomTxt, 
				r.deductFilingFee, r.memEmailOptOutGroupID, r.counselorGroupID, r.collectClientFeeFE, r.allowFeeDiscrepancy,
				r.feeDiscrepancyAmt, r.collectClientFeeFEOverrideTxt, r.feeStructureTypeID, ai.siteID, ai.siteResourceID,  
				ai.applicationInstanceName, ai.applicationInstanceDesc, ai.dateCreated, ai.settingsXML, at.applicationTypeID, 
				at.resourceTypeID, at.applicationTypeName, at.applicationTypeDesc, at.suggestedPageName, at.settingsXML, g.groupName, 
				g.groupPathExpanded as memEmailOptOutGroupPath, ag.groupName as counselorAssignmentGroupName, 
				ag.groupPathExpanded as counselorAssignmentGroupPath, mp.merchantProfileID as fePayProfileID, 
				pr.profileCode as fePayProfileCode, sf.referralsSMS, r.feReferralCenterInstructionForMemberID
			from dbo.ref_referrals as r
			INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = r.applicationInstanceID		
				and ai.siteID = @siteID
			INNER JOIN dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID
			INNER JOIN dbo.sites as s on s.siteID =  ai.siteID
			LEFT OUTER JOIN dbo.ref_merchantProfiles mp 
				INNER JOIN dbo.mp_profiles as pr on pr.profileID = mp.merchantProfileID
				on mp.applicationInstanceID = r.feApplicationInstanceID
			LEFT OUTER JOIN dbo.ams_groups as g on g.groupID = r.memEmailOptOutGroupID
				and g.orgID = s.orgID
			LEFT OUTER JOIN dbo.ams_groups as ag on	ag.groupID = r.counselorGroupID
				and ag.orgID = s.orgID
			LEFT OUTER JOIN dbo.siteFeatures as sf on sf.siteID = s.siteID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryReferralInfo>
	</cffunction>
	
	<cffunction name="getClient" output="false" returntype="query" hint="">
		<cfargument name="clientReferralID" type="numeric" required="true" />
		
		<cfset var qryClient = "">
		
		<cfquery name="qryClient" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @clientReferralID int, @referralID int;
			DECLARE @refClientIDs TABLE (clientID int);
			SET @clientReferralID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.clientReferralID#">;
			SELECT @referralID = referralID FROM dbo.ref_clientReferrals WHERE clientReferralID = @clientReferralID;

			WITH refClientIDs as (
				select c.clientID, c.clientParentID
				from dbo.ref_clients c
				inner join dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
					and cr.clientReferralID = @clientReferralID
				where c.referralID =  @referralID
					union all
				select c.clientID, c.clientParentID
				from refClientIDs rc
				inner join dbo.ref_clients c on c.referralID =  @referralID and c.clientID = rc.clientParentID
			)
			INSERT INTO @refClientIDs (clientID)
			SELECT clientID
			FROM refClientIDs;

			select 
				c.clientID,	c.referralID, 
				c.firstName, c.middleName, c.lastName,
				c.lastName + ', ' + c.firstName as clientName,
				c.businessName,	
				c.address1, c.address2,	c.address3,
				c.city,	c.state, c.postalCode, c.countryID,
				c.email,c.homePhone, c.cellPhone, c.alternatePhone,
				c.homePhoneE164, c.cellPhoneE164, c.alternatePhoneE164,
				c.typeID, cr.statusID, 
				crs.statusName, crs.canEditClient as referralCanEditClient , crs.canEditFilter as referralCanEditFilter, 
				crs.canEditLawyer as referralCanEditLawyer, crs.canRefer, crs.isReferred, crs.isAgency, crs.isPending,
				crs.isOpen, crs.isClosed, crs.isDeleted, crs.isRetainedCase, crs.isConsultation, crs.cannotReopen,
				c.dateCreated,	c.createdBy, c.dateLastUpdated,
				c.clientParentID,
				cr.clientReferralID, cr.memberID, cr.enteredByMemberID,
				cr.sourceID, cr.otherSource,
				cr.communicateLanguageID, cr.issueDesc, cr.typeID as referralTypeID, cr.feeTypeID,
				cr.callUID, cr.agencyID, a.name as agencyName,
				crt.clientReferralType, crt.isReferral as isLawyerReferral, crt.isAgency as isAgencyReferral,
				isNull(cr.sendSurvey, 0) as sendSurvey, isNull(cr.sendNewsBlog,0) as sendNewsBlog, cr.statusID, cr.typeID, 
				cr.clientReferralDate, cr.dateCreated, cr.lastUpdatedBy, cr.dateLastUpdated,
				rep.clientID as repID,	rep.firstName as repFirstName, rep.lastName as repLastName,
				rep.lastName + ', ' + rep.firstName as repName,
				rep.address1 as repAddress1, rep.address2 as repAddress2, rep.address3 as repAddress3,
				rep.city as repCity, rep.state as repState, rep.postalCode as repPostalCode, rep.countryID as repCountryID,
				rep.email as repEmail, rep.homePhone as repHomePhone, rep.cellPhone as repCellPhone, rep.alternatePhone as repAlternatePhone,
				rep.homePhoneE164 as repHomePhoneE164, rep.cellPhoneE164 as repCellPhoneE164, rep.alternatePhoneE164 as repAlternatePhoneE164,
				rep.typeID as repTypeID, rep.relationToClient, rep.clientParentID as repParentID,
				m.memberID, m.firstName as memFirstName, m.middleName as memMiddleName, m.lastName as memLastname,
				(m.firstName  + 
				case
					when m.middlename is not null and len(m.middlename) > 0  then
						' ' + left(m.middleName, 1) + ' '
					else
						' '
				end  + m.lastName) as memberName,
				rc.caseID, rc.enteredByMemberID, rc.notesTxt as caseNotesTxt,
				rc.caseFees, rc.dateCaseOpened, rc.dateCaseClosed,
				rc.dateCreated as dateCaseCreated, rc.dateLastUpdated as dateCaseLastUpdated,
				rc.feeStructureID, p.panelID as primPanelID, p.name as primPanelName, 
				p.referralFeePercent, p.deductExpenseDesc, p.referralAmount,
				rl.languageID, rl.languageName,
				crs2.clientReferralSource as sourceName
			from dbo.ref_clients c
			INNER JOIN dbo.ref_clientReferrals cr on cr.clientID = c.clientID
				and cr.clientReferralID = @clientReferralID
			LEFT OUTER JOIN dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID
			LEFT OUTER JOIN dbo.ref_clients rep on rep.referralID = @referralID and rep.clientID = cr.representativeID
			LEFT OUTER JOIN dbo.ref_clientReferralTypes crt on crt.clientReferralTypeID = cr.typeID
			INNER JOIN dbo.ams_members m on m.memberid = cr.memberid
			INNER JOIN dbo.ams_members m2 on m2.memberid = m.activeMemberID					
			LEFT OUTER JOIN dbo.ref_cases rc on rc.referralID = @referralID and rc.clientReferralID = cr.clientReferralID
			LEFT OUTER JOIN dbo.ref_agencies a on a.agencyID = cr.agencyID
			outer apply (	
				select top 1 srh.panelID1 as panelID, srh.clientID
				from searchMC.dbo.tblSearchReferralHistory as srh
				inner join @refClientIDs as rc on rc.clientID = srh.clientID
			) as paramTbl
			left join dbo.ref_panels p on p.panelID = paramTbl.panelID
			LEFT OUTER JOIN dbo.ref_languages rl on rl.languageID = cr.communicateLanguageID
			LEFT OUTER JOIN dbo.ref_clientReferralSources crs2 on crs2.clientReferralSourceID = cr.sourceID
			where c.referralID = @referralID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryClient>
	</cffunction>	
	
	<cffunction name="getCaseData" output="false" returntype="query" hint="">
		<cfargument name="caseID" type="numeric" required="true" />
		
		<cfset var local = structNew() />
		
		<cfquery name="local.qryCase" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @caseID int, @referralID int;
			SET @caseID = <cfqueryparam value="#arguments.caseID#" cfsqltype="cf_sql_integer">;
			SELECT @referralID = referralID FROM dbo.ref_cases WHERE caseID = @caseID;

			select 
				c.clientID,	c.referralID, 
				c.firstName, c.middleName, c.lastName,
				c.lastName + ', ' + c.firstName as clientName,
				c.businessName,	
				c.address1, c.address2,	c.address3,
				c.city,	c.state, c.postalCode, c.countryID,
				c.email,c.homePhone, c.cellPhone, c.alternatePhone,
				c.typeID, cr.statusID, 
				crs.statusName, crs.canEditClient as referralCanEditClient , crs.canEditFilter as referralCanEditFilter, 
				crs.canEditLawyer as referralCanEditLawyer, crs.canRefer, crs.isReferred, crs.isAgency, crs.isPending,
				crs.isOpen, crs.isClosed, crs.isDeleted, crs.isRetainedCase,
				c.dateCreated,	c.createdBy, c.dateLastUpdated,
				c.clientParentID,
				cr.clientReferralID, cr.memberID, cr.enteredByMemberID,
				cr.sourceID, cr.otherSource,
				cr.communicateLanguageID, cr.issueDesc, cr.typeID as referralTypeID, 
				cr.callUID, cr.agencyID, a.name as agencyName,
				crt.clientReferralType, crt.isReferral as isLawyerReferral, crt.isAgency as isAgencyReferral,
				isNull(cr.sendSurvey, 0) as sendSurvey, isNull(cr.sendNewsBlog,0) as sendNewsBlog, cr.statusID, cr.typeID, 
				cr.clientReferralDate, cr.dateCreated, cr.lastUpdatedBy, cr.dateLastUpdated,
				rep.clientID as repID,	rep.firstName as repFirstName, rep.lastName as repLastName,
				rep.lastName + ', ' + rep.firstName as repName,
				rep.address1 as repAddress1, rep.address2 as repAddress2, rep.address3 as repAddress3,
				rep.city as repCity, rep.state as repState, rep.postalCode as repPostalCode, rep.countryID as repCountryID,
				rep.email as repEmail, rep.homePhone as repHomePhone, rep.cellPhone as repCellPhone, rep.alternatePhone as repAlternatePhone,
				rep.typeID as repTypeID, rep.relationToClient, rep.clientParentID as repParentID,
				m.memberID, m.firstName as memFirstName, m.middleName as memMiddleName, m.lastName as memLastname,
				(m.firstName  + 
				case
					when m.middlename is not null and len(m.middlename) > 0  then
						' ' + left(m.middleName, 1) + ' '
					else
						' '
				end  + m.lastName) as memberName,
				rc.caseID, m3.memberID as caseEnteredByMemberID, rc.notesTxt as caseNotesTxt,
				rc.caseFees, rc.dateCaseOpened, rc.dateCaseClosed,
				rc.dateCreated as dateCaseCreated, rc.dateLastUpdated as dateCaseLastUpdated, rc.feeStructureID						
			from dbo.ref_clients c
			LEFT OUTER JOIN dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
			LEFT OUTER JOIN dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID
			LEFT OUTER JOIN dbo.ref_clients rep on rep.referralID = @referralID and rep.clientID = cr.representativeID
			LEFT OUTER JOIN dbo.ref_clientReferralTypes crt on crt.clientReferralTypeID = cr.typeID
			LEFT OUTER JOIN dbo.ams_members m on m.memberid = cr.memberid 
				and m.memberid = m.activememberID 
				and m.status <> 'D'	
			INNER JOIN dbo.ref_cases rc on rc.referralID = @referralID and rc.clientReferralID = cr.clientReferralID
				and rc.caseID = @caseID
			INNER JOIN dbo.ams_members m2 on m2.memberid = rc.enteredByMemberID
			INNER JOIN dbo.ams_members m3 on m3.memberid = m2.activememberID and m3.status <> 'D'				
			LEFT OUTER JOIN dbo.ref_agencies a on a.agencyID = cr.agencyID																	
			where c.referralID = @referralID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>	
		
		<cfreturn local.qryCase>	
	</cffunction>	
	
	<cffunction name="getPanelFees" access="public" output="true" returntype="struct" hint="return fees percentage or fee fixed amount">
		<cfargument name="memberid" type="numeric" required="yes">
		<cfargument name="clientID" type="numeric" required="yes">
		<cfargument name="clientParentID" type="numeric" required="yes">
		<cfargument name="qryGetReferralFilterData" type="query" required="no">

		<cfscript>
			var local = structNew();
			local.returnStruct = structNew();
			local.qryMemberPanels = getMemberPanels(arguments.memberid);
			local.panelList = valueList(local.qryMemberPanels.panelid);
			local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
			if (arguments.keyExists("qryGetReferralFilterData"))
				local.qryGetReferralFilterData = duplicate(arguments.qryGetReferralFilterData);
			else {
				local.thisClientID = arguments.clientID;
				if(arguments.clientParentID GT 0)
					local.thisClientID = arguments.clientParentID;
				local.qryGetReferralFilterData = local.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID);
			}
		</cfscript>
		
		<cfset local.returnStruct.panelID = 0 />
		<cfset local.returnStruct.referralFeePercent = 0>
		<cfset local.returnStruct.referralAmount = 0>		
		
		<cfloop query="local.qryGetReferralFilterData">
			<cfif local.qryGetReferralFilterData.elementID is "PANELID1" and listFindNoCase(local.panelList,val(local.qryGetReferralFilterData.elementValue))>
				<cfquery name="local.qryGetPanelFees" dbtype="query">
					select panelID, referralFeePercent, referralAmount 
					from [local].qryMemberPanels
					where panelID = <cfqueryparam value="#val(local.qryGetReferralFilterData.elementValue)#" cfsqltype="cf_sql_integer" />
				</cfquery>
				
				<cfif local.qryGetPanelFees.recordCount>
					<cfset local.returnStruct.panelID = local.qryGetPanelFees.panelID />
					<cfset local.returnStruct.referralFeePercent = local.qryGetPanelFees.referralFeePercent />
					<cfset local.returnStruct.referralAmount = local.qryGetPanelFees.referralAmount />
					<cfreturn local.returnStruct />							
				</cfif>
			<cfelseif local.qryGetReferralFilterData.elementID is "PANELID1" and not listFindNoCase(local.panelList,val(local.qryGetReferralFilterData.elementValue))>
				<!--- In case the memberid is no located in any of the panel lists, return this default struct --->	
				<cfset local.qryGetPanelData = local.objAdminReferrals.getPanelByID(panelID=val(local.qryGetReferralFilterData.elementValue)) />
				<cfif local.qryGetPanelData.recordCount>
					<cfset local.returnStruct.panelID = local.qryGetPanelData.panelID />
					<cfset local.returnStruct.referralFeePercent = local.qryGetPanelData.referralFeePercent />
					<cfset local.returnStruct.referralAmount = local.qryGetPanelData.referralAmount />						
				</cfif>						
			</cfif>	
			<cfif local.qryGetReferralFilterData.elementID is "PANELID2" and listFindNoCase(local.panelList,val(local.qryGetReferralFilterData.elementValue))>
				<cfquery name="local.qryGetPanelFees" dbtype="query">
					select panelID, referralFeePercent, referralAmount 
					from [local].qryMemberPanels
					where panelID = <cfqueryparam value="#val(local.qryGetReferralFilterData.elementValue)#" cfsqltype="cf_sql_integer" />
				</cfquery>
				
				<cfif local.qryGetPanelFees.recordCount>
					<cfset local.returnStruct.panelID = local.qryGetPanelFees.panelID />
					<cfset local.returnStruct.referralFeePercent = local.qryGetPanelFees.referralFeePercent />
					<cfset local.returnStruct.referralAmount = local.qryGetPanelFees.referralAmount />
					<cfreturn local.returnStruct />
				</cfif>	
			</cfif>	
			<cfif local.qryGetReferralFilterData.elementID is "PANELID3" and listFindNoCase(local.panelList,val(local.qryGetReferralFilterData.elementValue))>
				<cfquery name="local.qryGetPanelFees" dbtype="query">
					select panelID, referralFeePercent, referralAmount 
					from [local].qryMemberPanels
					where panelID = <cfqueryparam value="#val(local.qryGetReferralFilterData.elementValue)#" cfsqltype="cf_sql_integer" />
				</cfquery>
				
				<cfif local.qryGetPanelFees.recordCount>
					<cfset local.returnStruct.panelID = local.qryGetPanelFees.panelID />
					<cfset local.returnStruct.referralFeePercent = local.qryGetPanelFees.referralFeePercent />
					<cfset local.returnStruct.referralAmount = local.qryGetPanelFees.referralAmount />
					<cfreturn local.returnStruct />	
				</cfif>
			</cfif>									
		</cfloop> <!--- //loop query=local.qryGetReferralFilterData --->

		<cfreturn local.returnStruct />	
	</cffunction>

	<cffunction name="getReferralFeeStructureLevels" access="private" output="false" returntype="query">
		<cfargument name="referralID" type="numeric" required="true">
		<cfargument name="panelID" type="numeric" required="false" default="0">
		<cfargument name="feeStructureID" type="numeric" required="false" default="0">

		<cfset var qryReferralFeeStructureLevels = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryReferralFeeStructureLevels">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			IF OBJECT_ID('tempdb..##tmpRefFees') IS NOT NULL 
				DROP TABLE ##tmpRefFees;
			CREATE TABLE ##tmpRefFees (levelID int PRIMARY KEY, cumulativeChargedAmount decimal(18,2), cumulativeFeePercent decimal(4,2), rangeInitialChargeFeePercent decimal(4,2),
				amtRangeStart decimal(18,2), amtRangeEnd decimal(18,2), amtRangeDiff decimal(18,2), rowNum int);

			DECLARE @referralID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.referralID#">,
				@panelID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.panelID#">,
				@feeStructureID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.feeStructureID#">,
				@feeStructureTypeName varchar(255);

			INSERT INTO ##tmpRefFees (levelID, cumulativeChargedAmount, cumulativeFeePercent, rangeInitialChargeFeePercent, amtRangeStart)
			SELECT l.levelID, l.cumulativeChargedAmount, l.cumulativeFeePercent, l.rangeInitialChargeFeePercent, l.cumulativeChargedAmount
			FROM dbo.ref_feeStructureLevels AS l 
			INNER JOIN dbo.ref_feeStructures AS fs ON fs.feeStructureID = l.feeStructureID
			WHERE fs.referralID = @referralID
			<cfif arguments.feeStructureID>
				AND fs.feeStructureID = @feeStructureID
			<cfelse>
				AND fs.[status] = 'A'
				<cfif arguments.panelID>
					AND fs.panelID = @panelID
				<cfelse>
					AND fs.panelID IS NULL
				</cfif>
			</cfif>;

			IF @@ROWCOUNT > 0 AND NOT EXISTS (SELECT 1 FROM ##tmpRefFees WHERE cumulativeChargedAmount = 0)
				INSERT INTO ##tmpRefFees (levelID, cumulativeChargedAmount, cumulativeFeePercent, rangeInitialChargeFeePercent, amtRangeStart)
				VALUES (0, 0, 0, 0, 0);

			-- update rowNum
			UPDATE tmp
			SET tmp.rowNum = tmp2.rowNum
			FROM ##tmpRefFees AS tmp
			INNER JOIN (
				SELECT levelID, ROW_NUMBER() OVER (ORDER BY cumulativeChargedAmount) AS rowNum
				FROM ##tmpRefFees
			) AS tmp2 ON tmp2.levelID = tmp.levelID;

			-- update amtRangeStart/End
			UPDATE tmpStart
			SET tmpStart.amtRangeEnd = tmpEnd.amtRangeStart,
				tmpStart.amtRangeDiff = tmpEnd.amtRangeStart - tmpStart.amtRangeStart
			FROM ##tmpRefFees AS tmpStart
			INNER JOIN ##tmpRefFees AS tmpEnd ON tmpEnd.rowNum = tmpStart.rowNum + 1;

			SELECT TOP 1 @feeStructureTypeName = t.feeStructureTypeName
			FROM ##tmpRefFees AS tmp
			INNER JOIN dbo.ref_feeStructureLevels AS l ON l.levelID = tmp.levelID
			INNER JOIN dbo.ref_feeStructures AS f ON f.feeStructureID = l.feeStructureID
			INNER JOIN dbo.ref_feeStructureTypes AS t ON t.feeStructureTypeID = f.feeStructureTypeID;

			SELECT levelID, cumulativeChargedAmount, cumulativeFeePercent, rangeInitialChargeFeePercent, 
				amtRangeStart, amtRangeEnd, amtRangeDiff, @feeStructureTypeName AS feeStructureTypeName, 
				rowNum
			FROM ##tmpRefFees
			ORDER BY rowNum;

			IF OBJECT_ID('tempdb..##tmpRefFees') IS NOT NULL 
				DROP TABLE ##tmpRefFees;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryReferralFeeStructureLevels>
	</cffunction>

	<cffunction name="getPanelFeeStructureLevelsByID" access="public" output="false" returntype="query">
		<cfargument name="panelID" type="numeric" required="false" default="0" />
		<cfargument name="referralID" type="numeric" required="false" default="0" />
		
		<cfset var local = structNew() />		
		
		<cfquery name="local.qryGetPanelFeeStructureLevels" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT l.levelID, l.feeStructureID, l.cumulativeChargedAmount, l.cumulativeFeePercent, 
				l.rangeInitialChargeFeePercent, fs.feeStructureTypeID, l.dateCreated
			FROM dbo.ref_feeStructureLevels as l 
			INNER JOIN dbo.ref_feeStructures as fs on fs.feeStructureID = l.feeStructureID
				AND fs.[status] = 'A'
			<cfif arguments.panelID and not arguments.referralID>
				AND fs.panelID = <cfqueryparam value="#arguments.panelID#" cfsqltype="cf_sql_integer" />
			<cfelse>
				AND fs.referralID = <cfqueryparam value="#arguments.referralID#" cfsqltype="cf_sql_integer" />
				AND fs.panelID is null
			</cfif>;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif not local.qryGetPanelFeeStructureLevels.recordCount>
			<cfset local.qryGetPanelFeeStructureLevels = queryNew("levelID,feeStructureID,cumulativeChargedAmount,cumulativeFeePercent,rangeInitialChargeFeePercent,feeStructureTypeID,dateCreated", 
							"integer,integer,integer,integer,integer,integer,date")>
			<cfif arguments.referralID and not arguments.panelID>							
				<cfset queryAddRow(local.qryGetPanelFeeStructureLevels)>
				<cfset querySetCell(local.qryGetPanelFeeStructureLevels,"levelID",0)>
				<cfset querySetCell(local.qryGetPanelFeeStructureLevels,"feeStructureID",0)>
				<cfset querySetCell(local.qryGetPanelFeeStructureLevels,"cumulativeChargedAmount",0)>
				<cfset querySetCell(local.qryGetPanelFeeStructureLevels,"cumulativeFeePercent",0)>
				<cfset querySetCell(local.qryGetPanelFeeStructureLevels,"rangeInitialChargeFeePercent",0)>
				<cfset querySetCell(local.qryGetPanelFeeStructureLevels,"feeStructureTypeID",1)>
				<cfset querySetCell(local.qryGetPanelFeeStructureLevels,"dateCreated",now())>
			</cfif>
		</cfif>
		
		<cfreturn local.qryGetPanelFeeStructureLevels />
	</cffunction>	

	<cffunction name="getCaseFeeStructureLevelsByID" access="public" output="false" returntype="query">
		<cfargument name="feeStructureID" type="numeric" required="true" />
		
		<cfset var local = structNew() />		
		
		<cfquery name="local.qryGetCaseFeeStructureLevels" datasource="#application.dsn.membercentral.dsn#">
		select 
			l.levelID,
			l.feeStructureID,
			l.cumulativeChargedAmount,
			l.cumulativeFeePercent,
			l.rangeInitialChargeFeePercent,
			l.dateCreated,
			fs.panelID
		from
			ref_feeStructureLevels l 
			INNER JOIN ref_feeStructures fs on
				fs.feeStructureID = l.feeStructureID
				and fs.feeStructureID =  <cfqueryparam value="#arguments.feeStructureID#" cfsqltype="cf_sql_integer" />		
		</cfquery>

		<cfif not local.qryGetCaseFeeStructureLevels.recordCount>
			<cfset local.qryGetCaseFeeStructureLevels = queryNew("levelID,feeStructureID,cumulativeChargedAmount,cumulativeFeePercent,rangeInitialChargeFeePercent,dateCreated,panelID", 
							"integer,integer,integer,integer,integer,date,integer")>
			<cfset queryAddRow(local.qryGetCaseFeeStructureLevels)>
			<cfset querySetCell(local.qryGetCaseFeeStructureLevels,"levelID",0)>
			<cfset querySetCell(local.qryGetCaseFeeStructureLevels,"feeStructureID",0)>
			<cfset querySetCell(local.qryGetCaseFeeStructureLevels,"cumulativeChargedAmount",0)>
			<cfset querySetCell(local.qryGetCaseFeeStructureLevels,"cumulativeFeePercent",0)>
			<cfset querySetCell(local.qryGetCaseFeeStructureLevels,"rangeInitialChargeFeePercent",0)>
			<cfset querySetCell(local.qryGetCaseFeeStructureLevels,"dateCreated",now())>
			<cfset querySetCell(local.qryGetCaseFeeStructureLevels,"panelID",0)>
		</cfif>
		
		<cfreturn local.qryGetCaseFeeStructureLevels />
	</cffunction>		

	<cffunction name="getReferralFeeStructureID" access="public" output="false" returntype="numeric">
		<cfargument name="referralID" type="numeric" required="true" />
		
		<cfset var local = structNew() />		
		
		<cfquery name="local.qryGetReferralFeeStructureID" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON; 
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select 
				feeStructureID
			from 
				dbo.ref_feeStructures
			where
				referralID = <cfqueryparam value="#arguments.referralID#" cfsqltype="cf_sql_integer" />	
				and status = 'A'
				and panelID is null;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;								
		</cfquery>
		
		<cfreturn val(local.qryGetReferralFeeStructureID.feeStructureID) />
	</cffunction>		
	
	<cffunction name="updateOrder" access="public" output="false" returntype="void">
		<cfargument name="caseID" type="numeric" required="true" />	
		<cfargument name="orderNumber" type="string" required="true" />	
		<cfargument name="collectedFeeID" type="numeric" required="true" />	
		
		<cfset var qryUpdateOrder = "">

		<cfquery name="qryUpdateOrder" datasource="#application.dsn.membercentral.dsn#">		
			update dbo.ref_collectedFees
			set orderNumber = <cfqueryparam value="#arguments.orderNumber#" cfsqltype="cf_sql_varchar">
			where caseID = <cfqueryparam value="#val(arguments.caseID)#" cfsqltype="cf_sql_integer">
			and collectedFeeID = <cfqueryparam value="#val(arguments.collectedFeeID)#" cfsqltype="cf_sql_integer">
		</cfquery>
	</cffunction>		

	<cffunction name="recordSale" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true" />

		<cfset var local = structNew() />
		<cfset local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals") />
		<cfset local.strResponse = { success=false, response='' } />
		
		<cfset local.qryGetClientData = local.objAdminReferrals.getClient(clientReferralID=arguments.event.getvalue("clientReferralID",0))>
		<cfset local.panelID = 0>
		<cfset local.thisClientID = val(local.qryGetClientData.rootClientID) ? val(local.qryGetClientData.rootClientID) : val(local.qryGetClientData.clientID)>
		<cfset local.qryGetReferralFilterData = local.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID)>
		<cfloop query="local.qryGetReferralFilterData">
			<cfif local.qryGetReferralFilterData.elementID EQ 'panelid1'>
				<cfset local.panelID = local.qryGetReferralFilterData.elementValue>
				<cfbreak>
			</cfif>
		</cfloop>
		
		<cfset local.feesStruct = structNew()>
		<cfset local.feesStruct = getPanelFees(memberID=arguments.event.getValue('memberid'), clientID=arguments.event.getValue('clientID'), clientParentID=val(arguments.event.getValue('clientParentID')),
										qryGetReferralFilterData=local.qryGetReferralFilterData)>
		
		<cfset arguments.event.setValue('referralFeePercent',local.feesStruct.referralFeePercent) />
		<cfset arguments.event.setValue('referralAmount',local.feesStruct.referralAmount) />
		<cfset local.referralFeePercent = local.feesStruct.referralFeePercent />
		<cfset local.referralAmount = local.feesStruct.referralAmount />
		<cfset local.referralID = arguments.event.getValue('referralID') />
		<cfset local.collectedFee = val(arguments.event.getValue('collectedFee')) />
		<cfset local.filingFee = numberFormat(val(arguments.event.getValue('filingFee',"0.00")), "0.00") />
		<cfset local.qryGetCaseData = getCaseData(val(arguments.event.getValue('caseID'))) />
		<cfset local.qryGetPanelData = local.objAdminReferrals.getPanelByID(panelID=local.panelID) />
		<cfset local.qryReferralSettings = getReferralSettings(arguments.event.getValue('mc_siteinfo.siteid')) />
		<cfset local.deductFilingFee = local.qryReferralSettings.deductFilingFee />
		<cfset local.applicationInstanceID = local.qryReferralSettings.applicationInstanceID>
		<cfset local.caseID = val(arguments.event.getValue('caseID'))>

		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcUser.memberData.IdentifiedAsMemberID gt 0>
			<cfset local.useMID = session.cfcUser.memberData.IdentifiedAsMemberID>
		<cfelse>
			<cfset local.useMID = session.cfcuser.memberdata.memberid>
		</cfif>		
		
		<cfset local.amountDue = local.feesStruct.referralAmount />
		<cfif not val(local.amountDue)>
			<cfif val(local.deductFilingFee) and val(local.filingFee) and local.collectedFee gt local.filingFee>
				<cfset local.collectedFee = local.collectedFee - val(local.filingFee)>
			</cfif>
			<cfset local.amountDue = calculateFees(referralID=local.referralID, panelID=local.panelID, caseID=local.caseID, collectedFee=local.collectedFee, 
										qryGetCaseData=local.qryGetCaseData, qryGetPanelData=local.qryGetPanelData)>
		<cfelse>
			<cfif val(local.deductFilingFee) and val(local.filingFee) and local.amountDue gt local.filingFee>
				<cfset local.amountDue = local.amountDue - val(local.filingFee)>
			</cfif>			
		</cfif>	<!--- //  if not val(local.amountDue) ---> 

		<cfquery name="local.qryGetGLAccountID" datasource="#application.dsn.membercentral.dsn#">
			select dbo.fn_ref_getFeeGLAccountID(<cfqueryparam value="#val(local.panelID)#" cfsqltype="cf_sql_integer" />) as GLAccountID
		</cfquery>
		
		<cfif(val(local.qryGetGLAccountID.GLAccountID))>
			<cfset local.GLAccountID = local.qryGetGLAccountID.GLAccountID />
		<cfelse>			
			<cfset local.GLAccountID = local.qryReferralSettings.GLAccountID />
		</cfif>
		
		<cfquery name="local.qryGetAppType" datasource="#application.dsn.membercentral.dsn#">		
			select applicationTypeID, resourceTypeID
			from dbo.cms_applicationTypes
			where applicationTypeName = 'Referrals'
		</cfquery>

		<!--- record transactions and fee collection --->
		<cfif local.amountDue gte 0.00>
			<cfset local.collectedFeeID = addFeeCollection(event=arguments.event)>
			<cfset local.objAccounting = CreateObject("component","model.system.platform.accountingObj").init()>

			<cfset local.ownedByOrgID = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID>
			<cfset local.recordedOnSiteID = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID>

			<cfset local.xmlSchedule = '<rows><row amt="#NumberFormat(local.amountDue,"9.99")#" dt="#dateformat(now(),"m/d/yyyy")#" /></rows>'>
			
			<cfset local.details = "Referral Fee #arguments.event.getValue('mc_siteinfo.sitecode')#-#local.qryGetCaseData.clientReferralID#:  #local.qryGetCaseData.clientName#">

			<cfquery name="local.qryRecordSale" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @profileID int, @invoiceID int, @assignedToMemberID int, @transactionDate datetime, @invoiceNumber varchar(19),
						@ownedByOrgID int, @siteID int, @recordedByMemberID int, @statsSessionID int, @detail varchar(1000), @amountDue decimal(18,2), 
						@GLAccountID int, @taxAmount decimal(18,2), @xmlSchedule xml, @transactionID int, @orgID int, @itemID int,
						@applicationInstanceID int, @referralMerchantProfiles varchar(1000);
					SET @assignedToMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('memberid')#">;
					SET @transactionDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#now()#">;
					SELECT @profileID = profileID from dbo.fn_tr_getInvoiceProfileForGL(#local.GLAccountID#);
					SET @applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.applicationInstanceID#">;
					SELECT @referralMerchantProfiles = NULLIF(dbo.sortedIntList(merchantProfileID), '')
					FROM dbo.ref_merchantProfiles as r
					WHERE applicationInstanceID =  @applicationInstanceID;

					BEGIN TRAN;

						EXEC dbo.tr_createInvoice @invoiceProfileID=@profileID, @enteredByMemberID=@assignedToMemberID,
							@assignedToMemberID=@assignedToMemberID, @dateBilled=@transactionDate, @dateDue=@transactionDate,
							@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

						IF @referralMerchantProfiles is not null
							EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@referralMerchantProfiles;

						SET @ownedByOrgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ownedByOrgID#">;
						SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.recordedOnSiteID#">;
						SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.useMID#">;
						SET @statsSessionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.statsSessionID#">;
						SET @detail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#left(local.details,500)#">;
						SET @amountDue = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.amountDue#">;
						SET @GLAccountID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.GLAccountID#">;
						SET @invoiceID = @invoiceID;
						SET @taxAmount = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" null="yes">;
						SET @xmlSchedule = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#ToString(local.xmlSchedule)#">;

						EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@ownedByOrgID, @recordedOnSiteID=@siteID,
							@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@recordedByMemberID,
							@statsSessionID=@statsSessionID, @status='Active', @detail=@detail, @parentTransactionID=null,
							@amount=@amountDue, @transactionDate=@transactionDate, @creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, 
							@stateIDForTax=null, @zipForTax=null, @taxAmount=@taxAmount, @bypassTax=0, @bypassInvoiceMessage=0,
							@bypassAccrual=0, @xmlSchedule=@xmlSchedule, @transactionID=@transactionID OUTPUT;

						IF @transactionID = 0 
							RAISERROR('Failed to create sale for Referral Fee.',16,1);

						set @orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="cf_sql_integer" />;
						set @itemID = <cfqueryparam value="#local.collectedFeeID#" cfsqltype="cf_sql_integer" />

						EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Referrals', @transactionID=@transactionID,
							@itemType='ReferralFee', @itemID=@itemID, @subItemID=null;

						EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceID;
					
					COMMIT TRAN;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.recordedSuccess = 1>
		<cfelse>
			<cfset local.recordedSuccess = 0>
		</cfif>

		<cfif local.recordedSuccess>
			<cfset local.strResponse.success = true>
			<cfset createObject('component','model.system.platform.history').addReferralUpdateHistory(orgID=int(arguments.event.getValue('mc_siteinfo.orgid')),
				clientReferralID=int(local.qryGetCaseData.clientReferralID), actorMemberID=int(local.useMID), 
				mainMessage="Attorney Sale Recorded - #dollarFormat(local.amountDue)#")>			
		</cfif>
		
		<cfreturn local.strResponse>
	</cffunction>

	<cffunction name="calculateFees" access="public" output="false" returntype="numeric">
		<cfargument name="referralID" type="numeric" required="true">
		<cfargument name="panelID" type="numeric" required="true">
		<cfargument name="caseID" type="numeric" required="true">
		<cfargument name="collectedFee" type="numeric" required="true">
		<cfargument name="qryGetCaseData" type="query" required="true">
		<cfargument name="qryGetPanelData" type="query" required="true">

		<cfset var local = structNew()>
		<cfset local.amountDue = 0>
		<cfset structAppend(local,duplicate(arguments))>

		<!--- Referral Fee Structure --->
		<cfif val(local.qryGetCaseData.feeStructureID) AND NOT val(local.qryGetPanelData.ovFeeStructure)>
			<cfset local.qryReferralFeeStructureLevels = getReferralFeeStructureLevels(referralID=local.referralID, feeStructureID=local.qryGetCaseData.feeStructureID)>
		<cfelse>
			<cfset local.qryReferralFeeStructureLevels = getReferralFeeStructureLevels(referralID=local.referralID, panelID=local.panelID)>
			<cfif NOT local.qryReferralFeeStructureLevels.recordCount>
				<cfset local.qryReferralFeeStructureLevels = getReferralFeeStructureLevels(referralID=local.referralID)>
			</cfif>
		</cfif>

		<!--- no fee structure --->
		<cfif NOT local.qryReferralFeeStructureLevels.recordCount>
			<cfreturn local.amountDue>
		</cfif>

		<!--- get fee structure type (cumulative or totals) --->
		<cfset local.isFeeStructureTypeTotals = local.qryReferralFeeStructureLevels.feeStructureTypeName EQ "totals" ? true : false>

		<!--- existing case fees --->		
		<cfset local.qryGetCaseFees = CreateObject("component","model.admin.referrals.referrals").getCaseFees(caseID=local.caseID)>
		<cfset local.qryGetCaseFeesTotals = getFeesTotals(qryItems=local.qryGetCaseFees)>
		
		<!--- totals --->
		<cfset local.currentTotal = val(local.qryGetCaseFeesTotals.collectedFeeTotal)>
		<cfset local.runningTotal = local.currentTotal + local.collectedFee>

		<!--- invalid amt --->
		<cfif NOT local.runningTotal>
			<cfreturn local.amountDue>
		</cfif>

		<!--- find the runningTotal fee structure level --->
		<cfquery name="local.qryMatchedFeeLevel" dbtype="query">
			SELECT *
			FROM [local].qryReferralFeeStructureLevels
			WHERE amtRangeStart < <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.runningTotal#">
			AND (
				amtRangeEnd >= <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.runningTotal#">
				OR amtRangeEnd IS NULL
			)
		</cfquery>

		<cfif local.qryMatchedFeeLevel.recordCount>
			<!--- Fee Structure: totals --->
			<cfif local.isFeeStructureTypeTotals>
				<cfset local.amountDue = precisionEvaluate((local.runningTotal * local.qryMatchedFeeLevel.cumulativeFeePercent) / 100)>
				<cfset local.referralDuesTotal =  val(local.qryGetCaseFeesTotals.referralDuesTotal)>
				<cfif local.amountDue GTE local.referralDuesTotal>
					<cfset local.amountDue = local.amountDue - local.referralDuesTotal>
				<cfelse>
					<cfset local.amountDue = 0>
				</cfif>

			<!--- Fee Structure: cumulative --->
			<cfelse>
				<cfquery name="local.qryFeeLevels" dbtype="query">
					SELECT *
					FROM [local].qryReferralFeeStructureLevels
					WHERE rowNum <= <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryMatchedFeeLevel.rowNum#">
					ORDER BY rowNum DESC
				</cfquery>

				<cfset local.newRunningTotal = local.runningTotal>
				<cfset local.amtLeftIntheCollectedFee = local.collectedFee>

				<cfloop query="local.qryFeeLevels">
					<cfset local.amtInThisRange = local.newRunningTotal - local.qryFeeLevels.amtRangeStart>
					<cfset local.amtInThisRange = min(local.amtInThisRange,local.amtLeftIntheCollectedFee)>
					<cfset local.amtLeftIntheCollectedFee = local.amtLeftIntheCollectedFee - local.amtInThisRange>
					<cfset local.newRunningTotal = local.qryFeeLevels.amtRangeStart>

					<!--- charge cumulative fee percent --->
					<cfset local.amtDueInThisRange = precisionEvaluate((local.amtInThisRange * local.qryFeeLevels.cumulativeFeePercent) / 100)>
					<cfset local.amountDue += local.amtDueInThisRange>

					<!--- charge range intial fee  --->
					<cfif local.currentTotal LTE local.qryFeeLevels.amtRangeStart AND local.runningTotal GT local.qryFeeLevels.amtRangeStart AND val(local.qryFeeLevels.rangeInitialChargeFeePercent)>
						<cfset local.rangeInitialChargeFee = precisionEvaluate((local.qryFeeLevels.amtRangeStart * local.qryFeeLevels.rangeInitialChargeFeePercent) / 100)>
						<cfset local.amountDue += local.rangeInitialChargeFee>
					</cfif>

					<cfif local.amtLeftIntheCollectedFee EQ 0>
						<cfbreak>
					</cfif>
				</cfloop>
			</cfif>
		</cfif>
		
		<cfreturn NumberFormat(local.amountDue,"0.00")>
	</cffunction>
	
	<cffunction name="buyNow_parseItem" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true" />
		<cfargument name="strBuyNow" type="struct" required="true" />
		<cfargument name="strBuyNowReturn" type="struct" required="true" />

		<cfscript>
		var local = structNew();
		local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
		
		// custom template overrides
		arguments.strBuyNowReturn.notIdentifiedTemplate = "NotLoggedIn";
		arguments.strBuyNowReturn.notFoundTemplate = "ItemNotFound";
		
		// setup item parsing
		arguments.strBuyNowReturn.ItemType = arguments.strBuyNow.itemType;				// referral
		arguments.strBuyNowReturn.ItemKey = arguments.strBuyNow.itemKey;				// referral-#caseID#|#ordernumber#
		arguments.strBuyNowReturn.ItemID = listRest(arguments.strBuyNow.itemKey,"-");	// #caseID#|#ordernumber#
		arguments.strBuyNowReturn.ItemFolder = "referrals";
		arguments.strBuyNowReturn.thisCFC = this;

		// make sure we have both caseid and ordernumber
		if (listLen(arguments.strBuyNowReturn.ItemID,"|") is not 2) return arguments.strBuyNowReturn;
		
		// parse to get caseid and ordernumber
		arguments.strBuyNowReturn.caseID = GetToken(listRest(arguments.strBuyNow.itemKey,"-"),1,"|");
		arguments.strBuyNowReturn.ordernumber = GetToken(listRest(arguments.strBuyNow.itemKey,"-"),2,"|");

		// make sure ordernumber is valid and not already checked out 
		if (NOT isOrderOKForCheckout(caseid=arguments.strBuyNowReturn.caseid,ordernumber=arguments.strBuyNowReturn.ordernumber)) return arguments.strBuyNowReturn;
		arguments.strBuyNowReturn.appInstanceID = getReferralSettings(arguments.event.getValue('mc_siteinfo.siteid'));
		paramOrderNumber(appInstanceID=arguments.strBuyNowReturn.appInstanceID.applicationInstanceID, orderNumber=arguments.strBuyNowReturn.ordernumber);

		// make sure there are items in the order
		arguments.strBuyNowReturn.qryItems = local.objAdminReferrals.getCaseFeesForAcct(caseid=arguments.strBuyNowReturn.caseid,ordernumber=arguments.strBuyNowReturn.ordernumber);
		if (NOT arguments.strBuyNowReturn.qryItems.recordcount) return arguments.strBuyNowReturn;
		arguments.strBuyNowReturn.qryItemsTotal = getOrderTotals(qryItems=arguments.strBuyNowReturn.qryItems);		

		arguments.strBuyNowReturn.totalTaxAmt = calculateTaxes(caseid=arguments.strBuyNowReturn.caseid,orderNumber=arguments.strBuyNowReturn.ordernumber,qryItems=arguments.strBuyNowReturn.qryItems);

		// basics
		arguments.strBuyNowReturn.itemOK = true;
		arguments.strBuyNowReturn.buyNowPageTitle = "Referral Fee Checkout";
		arguments.strBuyNowReturn.receiptTitle = "Purchase Complete";

		// pricing defaults
		arguments.strBuyNowReturn.showPaymentArea = true;		// payment is required unless overridden below
		arguments.strBuyNowReturn.offerCoupon = false;
		arguments.strBuyNowReturn.noPaymentStatement = "No payment is due for this referral.";
		arguments.strBuyNowReturn.onReceiptStatement = "You have submitted a payment for the following items:";
		arguments.strBuyNowReturn.purchaserTitle = "Purchaser";

		// invoice profile ids
		var qryInvoiceProfiles = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = :orgID;

			select distinct i.invoiceProfileID
			from dbo.tr_invoiceTransactions as it
			inner join dbo.tr_invoices as i on i.orgID = @orgID
				and i.invoiceID = it.invoiceID
			where it.transactionID in (:saleTIDList)
			and it.orgID = @orgID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		",
			{ 	
				orgID = { value=arguments.event.getValue('mc_siteinfo.orgID'), cfsqltype="CF_SQL_INTEGER" },
				saleTIDList = { value="0#valueList(arguments.strBuyNowReturn.qryItems.saleTID)#", list="true", cfsqltype="CF_SQL_INTEGER" }	
			},
			{ datasource="#application.dsn.membercentral.dsn#" }
		);
		var ovInvProfileID = qryInvoiceProfiles.recordCount EQ 1 ? qryInvoiceProfiles.invoiceProfileID : 0;
	
		// merchant profiles allowed
		local.paymentGateways = local.objAdminReferrals.getReferralPaymentProfiles(appInstanceID=arguments.strBuyNowReturn.appInstanceID.applicationInstanceID, ovInvProfileID=ovInvProfileID);

		arguments.strBuyNowReturn.paymentGateways = QueryFilter(local.paymentGateways, function(thisRow) { return NOT listFindNoCase("2,13,14", arguments.thisRow.gatewayID); });
		
		// pricing overrides
		if (arguments.strBuyNowReturn.qryItemsTotal.totalFees lte 0)
			arguments.strBuyNowReturn.showPaymentArea = false;		// dont show payment area if cart total is 0

		// shipping panel
		arguments.strBuyNowReturn.showShippingArea = false;
		</cfscript>
		
		<cfreturn arguments.strBuyNowReturn />
	</cffunction>
	
	<cffunction name="buynow_buy" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true" />
		<cfargument name="strBuyNow" type="struct" required="true" />

		<cfset var local = structNew() />
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting") />
		<cfset local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals") />
		<cfset local.strResponse = { success=false, response='' } />
		<cfset local.referralSettings = getReferralSettings(siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		
		<!---convert times from central (how stored in db) to default timezone of site--->
		<cfset local.objTZ = CreateObject("component","model.system.platform.tsTimeZone") />
		<cfset local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID')) />
		<cfset local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID')) />		
		<cfset local.referralDate = now() />
		<cfif (local.regTimeZone neq "US/Central")>
			<cfset local.referralDate = local.objTZ.convertTimeZone(dateToConvert=local.referralDate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone) />
		</cfif>		

		<!--- get app instance id, orderid --->		
		<cfset local.qryAppInstanceID = arguments.strBuyNow.appInstanceID />
		<cfset arguments.event.setValue('caseID',arguments.strBuyNow.caseID) />
		<cfset arguments.event.setValue('orderNumber',arguments.strBuyNow.orderNumber) />
		
		<!--- update member id for ordernumber --->
		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcUser.memberData.IdentifiedAsMemberID gt 0>
			<cfset local.useMID = session.cfcUser.memberData.IdentifiedAsMemberID>
		<cfelse>
			<cfset local.useMID = session.cfcuser.memberdata.memberid>
		</cfif>

		<!--- tax info --->
		<cfset local.taxInfo = application.mcCacheManager.sessionGetValue(keyname='refCaseTaxInfo#arguments.strBuyNow.caseID#', defaultValue={ "stateIDForTax":0, "zipForTax":0 })>

		<!--- determine payment profileID and profileCode --->
		<cfset arguments.event.paramValue('profileid',0) />
		<cfquery name="local.qryMerchantProfile" dbtype="query">
			select profileID, profileCode, gatewayid, gatewayType, emailRecipient, gatewayClass, enableProcessingFeeDonation, processFeeDonationFeePercent,
				processFeeDonationRenevueGLAccountID, processFeeDonationRevTransDesc, enableSurcharge, surchargePercent, surchargeRevenueGLAccountID,
				processingFeeLabel
			from arguments.strBuyNow.paymentGateways
			where profileid = <cfqueryparam value="#arguments.event.getValue('profileid')#" cfsqltype="cf_sql_integer" />
		</cfquery>

		<!--- amount to charge and accounting totals --->
		<cfset local.amountToCharge = arguments.strBuyNow.qryItemsTotal.totalFees />

		<!--- prepare fields for gateway and send --->
		<cfif local.amountToCharge>
			
			<!--- get fields --->
			<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=local.qryMerchantProfile.profilecode) />
			<cfset local.tmpFields = structNew() />
			<cfloop query="local.qryGatewayProfileFields">
				<cfset structInsert(local.tmpFields,'fld_#local.qryGatewayProfileFields.fieldid#_',arguments.event.getTrimValue('p_#local.qryMerchantProfile.profileID#_fld_#local.qryGatewayProfileFields.fieldid#_','')) />
			</cfloop>

			<!--- get info on file if applicable --->
			<cfset arguments.event.setValue('p_#local.qryMerchantProfile.profileID#_mppid',int(val(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid',0)))) />
			<cfif arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid')>
				<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(mppid=arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid'), 
																							memberID=local.usemid, 
																							profileID=local.qryMerchantProfile.profileID) />
				<cfset structInsert(local.tmpFields,'qryInfoOnFile',local.qrySavedInfoOnFile)>
			</cfif>

			<!--- Surcharge / Processing Fee Donation --->
			<cfset local.additionalFeesInfo = application.objPayments.getAdditionalFeesInfo(qryMerchantProfile=local.qryMerchantProfile, amt=local.amountToCharge, 
				stateIDForTax=val(local.taxInfo.stateIDForTax), zipForTax=local.taxInfo.zipForTax, 
				processingFeeOpted=arguments.event.getValue('processFeeDonation#arguments.event.getValue('profileid')#',0) EQ 1,
				surchargeEligibleCard=arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid') GT 0 AND local.qrySavedInfoOnFile.surchargeEligible EQ 1)>

			<!--- failed --->
			<cfif NOT local.additionalFeesInfo.success>
				<cfset local.strResponse.response = "Unable to get additional payment fees info.">
				<cfreturn local.strResponse>
			</cfif>
			
			<cfset local.finalAmountToCharge = local.additionalFeesInfo.finalAmountToCharge>

			<!--- prepare fields for gateway and send --->
			<cfset local.strTemp = { orgID=arguments.event.getValue('mc_siteinfo.orgID'), siteid=arguments.event.getValue('mc_siteinfo.siteid'), 
									 profileCode=local.qryMerchantProfile.profileCode, assignedToMemberID=local.useMID, recordedByMemberID=local.useMID, 
									 statsSessionID=val(session.cfcUser.statsSessionID), x_amount=local.finalAmountToCharge, 
									 x_description='#arguments.event.getValue('mc_siteinfo.sitename')# Referral', offeredPaymentFee=local.additionalFeesInfo.offeredPaymentFee }>
			<cfset structAppend(local.strTemp,local.tmpFields)>

			<!--- apple or google pay token --->
			<cfif arguments.event.valueExists('p_#local.qryMerchantProfile.profileID#_tokenData') AND len(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_tokenData'))>
				<cfset local.strTemp["tokenData"] = deSerializeJSON(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_tokenData'))>
			</cfif>

			<cfif listFindNoCase("AuthorizeCCCIM",local.qryMerchantProfile.gatewayType)>
				<cfset local.qryLevel3Data = QueryNew("name,desc,itemPriceExcDiscount,itemPriceIncDiscount,discount,qty,total","varchar,varchar,decimal,decimal,decimal,decimal,decimal")>
				<cfset QueryAddRow(local.qryLevel3Data, {
					"name": "Referral Fees",
					"desc": "#arguments.event.getValue('mc_siteinfo.sitename')# Referral",
					"itemPriceExcDiscount": local.amountToCharge,
					"itemPriceIncDiscount": local.amountToCharge,
					"discount": 0,
					"qty": 1,
					"total": local.amountToCharge
				})>
				<cfif local.additionalFeesInfo.additionalFees GT 0>
					<cfset QueryAddRow(local.qryLevel3Data, {
						"name": "#local.additionalFeesInfo.additionalFeesLabel#",
						"desc": "#arguments.event.getValue('mc_siteinfo.sitename')# #local.additionalFeesInfo.additionalFeesLabel#",
						"itemPriceExcDiscount": local.additionalFeesInfo.additionalFees,
						"itemPriceIncDiscount": local.additionalFeesInfo.additionalFees,
						"discount": 0,
						"qty": 1,
						"total": local.additionalFeesInfo.additionalFees
					})>
					<!--- Surcharge --->
					<cfif local.additionalFeesInfo.paymentFeeTypeID EQ 2>
						<cfset local.strTemp['x_surcharge'] = { "amount":local.additionalFeesInfo.additionalFees, "description":"#arguments.event.getValue('mc_siteinfo.sitename')# #local.additionalFeesInfo.additionalFeesLabel#" }>
					</cfif>
				</cfif>
				<cfset local.strTemp["x_items"] = application.objPayments.getLevel3Data(qryLevel3Data=local.qryLevel3Data, gatewayType=local.qryMerchantProfile.gatewayType)>
			</cfif>
			<cfinvoke component="#application.objPayments#" method="chargeAdHoc" argumentcollection="#local.strTemp#" returnvariable="local.paymentResponse">

			<!--- if payment not successful --->
			<cfif local.paymentResponse.responseCode is not 1>
				<cfset local.strResponse.response = local.paymentResponse.publicResponseReasonText>
				<cfreturn local.strResponse />
			</cfif>

			<!--- Surcharge / Record Processing Fee Donation --->
			<cfif local.additionalFeesInfo.additionalFees GT 0 AND local.paymentResponse.keyExists("mc_transactionID") AND val(local.paymentResponse.mc_transactionID)>
				<cfset local.strRecordAdditionalPmtFees = local.objAccounting.recordAdditionalPaymentFees(orgID=arguments.event.getValue('mc_siteinfo.orgID'), 
					siteID=arguments.event.getValue('mc_siteinfo.siteID'), assignedToMemberID=local.useMID, recordedByMemberID=local.useMID, 
					statsSessionID=val(session.cfcuser.statsSessionID), paymentTransactionID=local.paymentResponse.mc_transactionID, 
					GLAccountID=local.additionalFeesInfo.gl, qryAdditionalFees=local.additionalFeesInfo.qryAdditionalFees, 
					paymentFeeTypeID=local.additionalFeesInfo.paymentFeeTypeID)>
				
				<!--- if not successful --->
				<cfif NOT local.strRecordAdditionalPmtFees.success>
					<cfset local.strResponse.response = "Unable to record additional payment fees.">
					<cfreturn local.strResponse>
				</cfif>
			</cfif>
			
		</cfif>	<!--- //if local.amountToCharge --->
		
		<!--- record allocations --->
		<cftry>
			<cfif val(local.amountToCharge) gt 0 and listLen(local.paymentResponse.mc_transactionID)>
				<cfset local.arrPaymentResults = local.paymentResponse.keyExists("paymentResults") ? local.paymentResponse.paymentResults : [ duplicate(local.paymentResponse) ]>
				<cfset local.arrPaymentTransactions = []>
				<cfloop array="#local.arrPaymentResults#" index="local.thisResult">
					<cfif val(local.thisResult.mc_transactionID)>
						<cfset local.arrPaymentTransactions.append({ "mc_transactionID": local.thisResult.mc_transactionID, "x_amount": val(local.thisResult.x_amount) })>
					</cfif>
				</cfloop>
													
				<cfquery name="local.qryAllocateToSale" datasource="#application.dsn.membercentral.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @siteID int, @minPaymentTID int, @unAllocAmount decimal(18,2), @allocAmount decimal(18,2), @minSaleTID int, 
							@saleDue decimal(18,2), @recordedByMemberID int, @statsSessionID int, @nowDate datetime = GETDATE();
						DECLARE @tblSales TABLE (saleTransactionID int, amount decimal(18,2));
						DECLARE @tblPaymentForReallocationTransactions TABLE (paymentTransactionID int, amountAvailable decimal(18,2));

						SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
						SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.useMID#">;
						SET @statsSessionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.statsSessionID#">;

						<cfloop query="arguments.strBuyNow.qryItems">
							INSERT INTO @tblSales (saleTransactionID, amount)
							VALUES (#val(arguments.strBuyNow.qryItems.transactionID)#, #val(arguments.strBuyNow.qryItems.amtToBePaid)#);
						</cfloop>

						<cfloop array="#local.arrPaymentTransactions#" index="local.thisResult">
							INSERT INTO @tblPaymentForReallocationTransactions (paymentTransactionID, amountAvailable)
							VALUES (#val(local.thisResult.mc_transactionID)#, #val(local.thisResult.x_amount)#);
						</cfloop>

						BEGIN TRAN;
							-- allocate payment to sales
							SELECT @minSaleTID = MIN(saleTransactionID) FROM @tblSales;
							WHILE @minSaleTID IS NOT NULL BEGIN
								SELECT @saleDue = NULL, @minPaymentTID=NULL;

								SELECT @saleDue = amount FROM @tblSales WHERE saleTransactionID = @minSaleTID;
								IF @saleDue > 0 BEGIN
									SELECT @minPaymentTID = MIN(paymentTransactionID) FROM @tblPaymentForReallocationTransactions where amountAvailable > 0;
									WHILE @minPaymentTID IS NOT NULL BEGIN
										SELECT @unAllocAmount = NULL, @allocAmount=NULL;

										SELECT @unAllocAmount = amountAvailable 
										FROM @tblPaymentForReallocationTransactions
										WHERE paymentTransactionID = @minPaymentTID;

										-- select minimum of dueAmount and unallocated Payment Amount
										SELECT @allocAmount=MIN(x) FROM (VALUES (@saleDue),(@unAllocAmount)) AS value(x);

										EXEC dbo.tr_allocateToSale @recordedOnSiteID=@siteID, @recordedByMemberID=@recordedByMemberID, 
											@statsSessionID=@statsSessionID, @amount=@allocAmount, @transactionDate=@nowDate, 
											@paymentTransactionID=@minPaymentTID, @saleTransactionID=@minSaleTID;
										
										SET @saleDue = @saleDue - @allocAmount;
										SET @unAllocAmount = @unAllocAmount - @allocAmount

										UPDATE @tblPaymentForReallocationTransactions
										SET amountAvailable = @unAllocAmount
										WHERE paymentTransactionID = @minPaymentTID;

										IF @saleDue = 0
											BREAK;
										
										SELECT @minPaymentTID = MIN(paymentTransactionID) 
											FROM @tblPaymentForReallocationTransactions
											WHERE paymentTransactionID > @minPaymentTID
											AND amountAvailable > 0;
									END
								END
								
								SELECT @minSaleTID = MIN(saleTransactionID) FROM @tblSales WHERE saleTransactionID > @minSaleTID;		
							END
						COMMIT TRAN;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			<cfelse>
				<cfset local.arrPaymentTransactions = []>
			</cfif>
			<cfset local.recordedSuccess = 1 />
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local) />
				<cfset local.strResponse.response = "Failed to allocate payment to sale." />
				<cfreturn local.strResponse />
			</cfcatch>
		</cftry>

		<!--- if success, email --->
		<cfif val(local.amountToCharge) gt 0  and local.recordedSuccess is 1>				

			<cfset local.qryItems = local.objAdminReferrals.getCaseFeesForAcct(caseid=arguments.strBuyNow.caseid, ordernumber=arguments.strBuyNow.ordernumber) />
			<cfset local.qryOrderDetails = getCaseData(caseid=arguments.strBuyNow.caseid) />
			
			<cfset local.referral = application.mcCacheManager.sessionGetValue(keyname='referral', defaultValue={})>
			<cfif structKeyExists(local.referral,"referralMerchantProfile")>
				<cfset structDelete(local.referral,"referralMerchantProfile")>
			</cfif>
			<cfset structInsert(local.referral,"referralMerchantProfile",local.qryMerchantProfile)>
			<cfset application.mcCacheManager.sessionSetValue(keyname='referral', value=local.referral)>

			<cfif arrayLen(local.arrPaymentTransactions)>
				<cfquery name="local.updateOrder" datasource="#application.dsn.membercentral.dsn#">
					update dbo.ref_collectedFees
					set orderCompleted = 1,
						dateLastUpdated = getDate()
					where orderNumber = <cfqueryparam value="#arguments.strBuyNow.orderNumber#" cfsqltype="cf_sql_varchar">
				</cfquery>
			</cfif>
	
			<!--- email --->
			<cfif arrayLen(local.arrPaymentTransactions) OR local.qryMerchantProfile.gatewayClass eq "offline">
				<cfset local.strEmailReceipt = {
					"siteID": arguments.event.getValue('mc_siteInfo.siteID'),
					"siteName": arguments.event.getValue('mc_siteInfo.siteName'),
					"siteCode": arguments.event.getValue('mc_siteInfo.siteCode'),
					"orgID": arguments.event.getValue('mc_siteInfo.orgID'),
					"supportProviderEmail": arguments.event.getValue('mc_siteInfo.supportProviderEmail'),
					"networkEmailFrom": arguments.event.getValue('mc_siteInfo.networkEmailFrom'),
					"showCurrencyType": arguments.event.getValue('mc_siteInfo.showCurrencyType'),
					"defaultCurrencyType": arguments.event.getValue('mc_siteInfo.defaultCurrencyType'),
					"amountToCharge": local.amountToCharge,
					"refEmailRecipient": local.referralSettings.emailRecipient,
					"memberID": local.useMID,
					"isOfflinePayment": local.qryMerchantProfile.gatewayClass eq "offline",
					"paymentInstructions": getPaymentInstructions(profileID=local.qryMerchantProfile.profileID),
					"referralID": local.referralSettings.referralID,
					"referralDate": local.referralDate,
					"qryOrderDetails": local.qryOrderDetails,
					"qryItems": local.qryItems,
					"regTimeZoneName": local.regTimeZoneName,
					"regTimeZone": local.regTimeZone,
					"title": local.referralSettings.title
				}>
				<cfset doEmailPurchaser(strEmailReceipt=local.strEmailReceipt)>
			</cfif>

			<cfset local.strResponse.success = true>
		</cfif>
		
		<cfset local.strResponse.profileCode = local.qryMerchantProfile.profileCode />

		<cfif application.mcCacheManager.sessionValueExists('refCaseTaxInfo#arguments.strBuyNow.caseID#')>
			<cfset application.mcCacheManager.sessionDeleteValue(keyname='refCaseTaxInfo#arguments.strBuyNow.caseID#')>			
		</cfif>
		
		<cfreturn local.strResponse>
	</cffunction>	
	
	<cffunction name="buyNow_receipt" access="public" output="no" returntype="struct">
		<cfargument name="event" type="any" required="true" />
		<cfargument name="strBuyNow" type="struct" required="true" />
	
		<cfset var local = structNew() />
		<cfset local.returnStruct = structNew() />
		<cfset local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals") />

		<!--- Make sure caseid, orderNumber are set in event. --->
		<cfset arguments.event.setValue('orderNumber',arguments.strBuyNow.orderNumber) />
		<cfset arguments.event.setValue('caseid',arguments.strBuyNow.caseid) />
		<cfset local.returnStruct.qryItems = local.objAdminReferrals.getCaseFeesForAcct(caseid=arguments.strBuyNow.caseid,ordernumber=arguments.strBuyNow.ordernumber) />
		
		<cfreturn local.returnStruct />
	</cffunction>
	
	<cffunction name="paramOrderNumber" access="package" output="false" returntype="void" hint="Create Unique Order Number">
		<cfargument name="appInstanceID" type="numeric" required="true" />
		<cfargument name="orderNumber" type="string" required="false" default="#createUUID()#">
		
		<cfset var tmpStr = ''>
		<cfif application.mcCacheManager.sessionValueExists('referral')>
			<cfset application.mcCacheManager.sessionDeleteValue('referral')>			
		</cfif>
		<cfset local.referral = application.mcCacheManager.sessionGetValue(keyname='referral', defaultValue={})>		
		<cfif NOT StructKeyExists(local.referral,arguments.appInstanceID)>
			<cfset tmpStr = { orderNumber=arguments.orderNumber, shippingOption='', currentStep=1}/>
			<cfset StructAppend(tmpStr,initPaymentVars())>
			<cfset StructInsert(local.referral,arguments.appInstanceID,tmpStr)>
		</cfif>
		<cfset application.mcCacheManager.sessionSetValue(keyname='referral', value=local.referral)>
	</cffunction>	
	
	<cffunction name="initPaymentVars" access="package" output="false" returntype="struct" hint="set up default payment variables">
		<cfscript>
		var tmpStr = { 	refCaseID = "",
						refMemberID = "" };
		return tmpStr;
		</cfscript>
	</cffunction>	
	
	<cffunction name="isOrderOKForCheckout" access="private" output="false" returntype="boolean">
		<cfargument name="caseID" type="numeric" required="true" />
		<cfargument name="orderNumber" type="string" required="true" />

		<cfset var qryCheck = "">
		
		<cfquery name="qryCheck" datasource="#application.dsn.memberCentral.dsn#">
			select collectedFeeID
			from dbo.ref_collectedFees
			where orderNumber = <cfqueryparam value="#arguments.orderNumber#" cfsqltype="cf_sql_varchar">
			and caseID = <cfqueryparam value="#arguments.caseID#" cfsqltype="cf_sql_integer">
		</cfquery>

		<cfreturn qryCheck.recordCount>
	</cffunction>	
	
	<cffunction name="getOrderTotals" access="public" output="false" returntype="query">
		<cfargument name="qryItems" type="query" />

		<cfset var qryOrder = "">

		<cfquery name="qryOrder" dbtype="query">
			select sum(amtToBePaid) as totalFees
			from arguments.qryItems
		</cfquery>

		<cfreturn qryOrder>
	</cffunction>
	
	<cffunction name="getFeesTotals" access="public" output="false" returntype="query">
		<cfargument name="qryItems" type="query" />

		<cfset var local = structNew()>

		<cfquery name="local.qryDistinctCaseFees" dbtype="query">
			select collectedFeeID, collectedFee, filingFee, sum(ReferralDues) as ReferralDues, 
				sum(amtToBePaid) as amtToBePaid, sum(paidToDate) as paidToDate
			from arguments.qryItems
			group by collectedFeeID, collectedFee, filingFee
		</cfquery>

		<cfquery name="local.qryCaseFeeTotals" dbtype="query">
			select sum(ReferralDues) as referralDuesTotal, sum(amtToBePaid) as amtToBePaidTotal, sum(collectedFee) as collectedFeeTotal,
				sum(filingFee) as filingFeeTotal, sum(paidToDate) as paidToDateTotal
			from [local].qryDistinctCaseFees
		</cfquery>

		<cfreturn local.qryCaseFeeTotals>
	</cffunction>	

	<cffunction name="getConsultationFeesTotals" access="public" output="false" returntype="query">
		<cfargument name="qryItems" type="query" />

		<cfset var local = structNew()>

		<cfquery name="local.qryDistinctConsultationFees" dbtype="query">
			select saleTID, sum(ReferralDues) as ReferralDues, 
				sum(amtToBePaid) as amtToBePaid, sum(paidToDate) as paidToDate, dateRecorded
			from arguments.qryItems
			group by saleTID, dateRecorded
		</cfquery>

		<cfquery name="local.qryGetConsultationFeesTotals" dbtype="query">
			select sum(ReferralDues) as referralDuesTotal, sum(amtToBePaid) as amtToBePaidTotal,
				sum(paidToDate) as paidToDateTotal
			from [local].qryDistinctConsultationFees
		</cfquery>

		<cfreturn local.qryGetConsultationFeesTotals>
	</cffunction>

	<cffunction name="calculateTaxes" access="public" output="false" returntype="numeric">
		<cfargument name="caseID" type="numeric" required="true" />
		<cfargument name="orderNumber" type="string" required="true" />
		<cfargument name="qryItems" type="query" required="true" />

		<cfset var local = structNew() />
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting") />
		<cfset local.taxableTotal = 0.00 />

		<cfloop query="arguments.qryItems">
			<!--- Items tax --->
			<cfset local.strTaxIndiv = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID = arguments.qryItems.creditGLAccountID,
																				saleAmount = arguments.qryItems.cache_amountAfterAdjustment,
																				transactionDate = now(),
																				stateIDForTax = val(arguments.qryItems.stateIDForTax),
																				zipForTax = arguments.qryItems.zipForTax)>
			<cfif len(trim(local.strTaxIndiv.totalTaxAmt))>
				<cfset local.taxableTotal = local.taxableTotal + numberFormat(local.strTaxIndiv.totalTaxAmt, "0.00") />
			</cfif>
		</cfloop>	<!--- //loop query="arguments.qryItems" --->

		<cfreturn local.taxableTotal />
	</cffunction>
	
	<cffunction name="updateReceiveNewsLetter" access="public" output="false" returntype="numeric" >
		<cfargument name="orgID" type="numeric" required="true" />
		<cfargument name="clientID" type="numeric" required="true" />
		<cfargument name="sendNewsBlog" type="numeric" required="false" />
		<cfargument name="sendSurvey" type="numeric" required="false" />
		
		<cfset var local = structNew() />
		
		<cfset local.updateSubscription = 1 />
		
		<cftry>		
			<cfquery name="local.qryUpdateSubscription" datasource="#application.dsn.membercentral.dsn#">
				update dbo.ref_clientReferrals
				set
					<cfif isDefined("arguments.sendNewsBlog")>
						sendNewsBlog = <cfqueryparam value="#arguments.sendNewsBlog#" cfsqltype="cf_sql_integer"  />,
					</cfif>
					<cfif isDefined("arguments.sendSurvey")>
						sendSurvey = <cfqueryparam value="#arguments.sendSurvey#" cfsqltype="cf_sql_integer"  />,
					</cfif>			
					dateLastUpdated = getDate()																																				
				where clientID = <cfqueryparam value="#arguments.clientID#" cfsqltype="cf_sql_integer"  />;

				select clientReferralID
				from dbo.ref_clientReferrals
				where clientID = <cfqueryparam value="#arguments.clientID#" cfsqltype="cf_sql_integer"  />;
			</cfquery>

			<cfset CreateObject('component', 'model.admin.referrals.referrals').reprocessConditions(orgID=arguments.orgID, clientReferralID=val(local.qryUpdateSubscription.clientReferralID))>
			
			<cfcatch type="database">
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local) />
				<cfset local.updateSubscription = 0 />
			</cfcatch>		
		</cftry>
		
		<cfreturn local.updateSubscription />
	</cffunction>	

	<cffunction name="insertApplication" access="public" output="false" returntype="void" hint="insert fee collection detail into DB">
		<cfargument name="orgID" type="numeric" required="true" />
		<cfargument name="applicationTypeID" type="numeric" required="true" />
		<cfargument name="transactionID" type="numeric" required="true" />
		<cfargument name="itemType" type="string" required="true" />
		<cfargument name="itemID" type="numeric" required="true" />
		<cfargument name="status" type="string" required="true" />

		<cfset var qryInsertApplication = "" />
		
		<cfquery name="qryInsertApplication" datasource="#application.dsn.membercentral.dsn#">						
			set nocount on;					

			declare @thisCount int;
			declare @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="cf_sql_integer" />;
			
			select @thisCount = count(*)
			from dbo.tr_applications
			where orgID = @orgID
			and applicationTypeID = <cfqueryparam value="#arguments.applicationTypeID#" cfsqltype="cf_sql_integer" />
			and transactionID = <cfqueryparam value="#arguments.transactionID#" cfsqltype="cf_sql_integer" />
			and itemType = <cfqueryparam value="#arguments.itemType#" cfsqltype="cf_sql_varchar" />
			and itemID = <cfqueryparam value="#arguments.itemID#" cfsqltype="cf_sql_integer" />
			and status = <cfqueryparam value="#arguments.status#" cfsqltype="cf_sql_varchar" />;
								
			IF @thisCount = 0
				insert into dbo.tr_applications (applicationTypeID, transactionID, itemType, itemID, status, orgID)
				values(
					<cfqueryparam value="#arguments.applicationTypeID#" cfsqltype="cf_sql_integer" />,
					<cfqueryparam value="#arguments.transactionID#" cfsqltype="cf_sql_integer" />,
					<cfqueryparam value="#arguments.itemType#" cfsqltype="cf_sql_varchar" />,
					<cfqueryparam value="#arguments.itemID#" cfsqltype="cf_sql_integer" />,
					<cfqueryparam value="#arguments.status#" cfsqltype="cf_sql_varchar" />,
					@orgID
				);
		</cfquery>
	</cffunction>	

	<cffunction name="doEmailPurchaser" access="private" output="false" returntype="void">
		<cfargument name="strEmailReceipt" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.orderTotal = val(arguments.strEmailReceipt.amountToCharge) />
		<cfset local.objAdminReferrals = CreateObject('component', 'model.admin.referrals.referrals')>

		<cfif len(arguments.strEmailReceipt.refEmailRecipient)>
			<cfset local.sendFrom = trim(listFirst(arguments.strEmailReceipt.refEmailRecipient,";"))>
		<cfelse>
			<cfset local.sendFrom = arguments.strEmailReceipt.supportProviderEmail>
		</cfif>

		<cftry>
			<cfset local.mailCollectionFrom = arguments.strEmailReceipt.networkEmailFrom>
			<cfset local.mailCollectionReplyTo = local.sendFrom>
			<cfset local.mailCollectionSubject = "#arguments.strEmailReceipt.siteName# Referral Fee Payment">			

			<cfset local.qryPurchaser = application.objMember.getMemberInfo(arguments.strEmailReceipt.memberID)>
			<cfset local.memberName = local.qryPurchaser.firstName & " " & local.qryPurchaser.lastName>
			<cfset local.qryPurchaserAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.strEmailReceipt.orgID, memberid=local.qryPurchaser.memberID)>

			<cfset local.pageStyle = "width:550px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;">
			<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;">

			<cfsavecontent variable="local.emailContent">
				<cfoutput>
				<div style="#local.pageStyle#">
					<cfif arguments.strEmailReceipt.isOfflinePayment>
						<div style="font-size:13px;color:##333333;font-weight:bold;">Thank you for your submission!</div>
						<div style="padding-top:10px">
							Here is your receipt for your submission. Please print a copy of this receipt and mail in with your payment.								
						</div>
						<cfif len(arguments.strEmailReceipt.paymentInstructions)>
							<br/>#arguments.strEmailReceipt.paymentInstructions#
						</cfif>
					<cfelse>
						<div style="font-size:13px;color:##333333;font-weight:bold;">Thank you for your payment!</div>
						<div style="padding-top:10px">
							Here is your receipt for your payment. Please print a copy for your records.
						</div>
					</cfif>
					<br/>
					<div style="font-size:13px;color:##333333;font-weight:bold;">Referral #arguments.strEmailReceipt.sitecode#-#numberFormat(arguments.strEmailReceipt.qryOrderDetails.clientReferralID,"0000")# | #arguments.strEmailReceipt.qryOrderDetails.lastName#, #arguments.strEmailReceipt.qryOrderDetails.firstName#</div>
					<div style="padding:10px 0 5px 0;"><strong><cfif arguments.strEmailReceipt.isOfflinePayment>Invoice<cfelse>Payment</cfif> Date: </strong> #dateTimeFormat(arguments.strEmailReceipt.referralDate,"mm/dd/yyyy hh:nn tt",'#arguments.strEmailReceipt.regTimeZone#')# #arguments.strEmailReceipt.regTimeZoneName#</div>
					<br/>

					<table cellpadding="4" cellspacing="0" width="95%" border="0">
					<tr> 
						<td style="#local.tdStyle#;border-bottom:1px solid ##666;" width="*"><b>Collected Fee</b></td>
						<td style="#local.tdStyle#;border-bottom:1px solid ##666;" width="80"><b>Referral Fees</b></td>
						<td style="#local.tdStyle#;border-bottom:1px solid ##666;" width="80" align="right"><b>Amount <cfif arguments.strEmailReceipt.isOfflinePayment>Due<cfelse>Paid</cfif></b></td>
					</tr>
					<cfloop query="arguments.strEmailReceipt.qryItems">
						<tr valign="top">
							<td style="#local.tdStyle#"><cfif val(arguments.strEmailReceipt.qryItems.collectedFee) gte 0>#dollarFormat(arguments.strEmailReceipt.qryItems.collectedFee)#<cfif arguments.strEmailReceipt.showCurrencyType is 1> #arguments.strEmailReceipt.defaultCurrencyType#</cfif><cfelse>N/A</cfif></td>
							<td style="#local.tdStyle#"><cfif val(arguments.strEmailReceipt.qryItems.referralDues) gte 0>#dollarFormat(arguments.strEmailReceipt.qryItems.referralDues)#<cfif arguments.strEmailReceipt.showCurrencyType is 1> #arguments.strEmailReceipt.defaultCurrencyType#</cfif><cfelse>N/A</cfif></td>
							<td style="#local.tdStyle#" align="right"><cfif val(arguments.strEmailReceipt.qryItems.referralDues) gte 0>#dollarFormat(arguments.strEmailReceipt.qryItems.referralDues)#<cfif arguments.strEmailReceipt.showCurrencyType is 1> #arguments.strEmailReceipt.defaultCurrencyType#</cfif><cfelse>N/A</cfif></td>
						</tr>
					</cfloop>
					<tr>
						<td colspan="3">&nbsp;</td>
					</tr>
					<tr> 
						<td></td>
						<td style="#local.tdStyle#;border-top:1px solid ##666;" align="right"><b>Total:</b></td>
						<td style="#local.tdStyle#;border-top:1px solid ##666;" align="right" nowrap><b>#dollarFormat(local.orderTotal)#<cfif arguments.strEmailReceipt.showCurrencyType is 1> #arguments.strEmailReceipt.defaultCurrencyType#</cfif></b></td>
					</tr>
					</table>
					<br/>		
				</div>
				<div style="clear:both"></div>
				</cfoutput>
			</cfsavecontent>

			<cfset local.sednEmailStr = local.objAdminReferrals.sendReferralEmail(referralID=arguments.strEmailReceipt.referralID,
																				siteid=arguments.strEmailReceipt.siteID,
																				recordedByMemberID=local.qryPurchaser.memberID,
																				messageContent=local.emailContent,
																				contentTitle="#arguments.strEmailReceipt.sitename# Payment Confirmation",
																				fromName=arguments.strEmailReceipt.title,
																				fromEmail=local.mailCollectionFrom,
																				replyToEmail=local.mailCollectionReplyTo,
																				subject=local.mailCollectionSubject,
																				refMemberID=local.qryPurchaser.memberID,
																				refMemberName=local.memberName,
																				refMemberEmail=arguments.strEmailReceipt.refEmailRecipient,
																				messageTypeCode="REFERRALMEMBER")>					

	
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="doEmailCloseReferral" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew()>
		<cfscript>
		local.objMemberFieldsets = CreateObject("component","model.system.platform.memberFieldsets");
		local.objAppBaseLink = CreateObject('component', 'model.apploader');
		local.objAdminReferrals = CreateObject('component', 'model.admin.referrals.referrals');
		local.qryGetReferralData = local.objAdminReferrals.getReferralData(orgID=arguments.event.getValue('mc_siteInfo.orgID'),clientReferralID=arguments.event.getValue('clientReferralID'));
		local.qryReferralSettings = getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID'));
		local.thisClientID = local.qryGetReferralData.clientID;
		if(val(local.qryGetReferralData.clientParentID))
			local.thisClientID = val(local.qryGetReferralData.clientParentID);
		local.referralID = local.qryReferralSettings.referralID;
		local.qryGetReferralFilterData = local.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID);
		local.siteResourceID = local.qryReferralSettings.siteResourceID;
		local.qryFieldsetID = local.objAdminReferrals.getLocatorFieldsetID(siteResourceID=local.siteResourceID, area='referralsearch');
		local.xmlFields = local.objMemberFieldsets.getMemberFieldsXML(fieldsetid=val(local.qryFieldsetID.fieldsetID), usage="memberReferralSearch");
		local.arrFieldCodes = XMLSearch(local.xmlFields,"//mf/@fieldCode");
		local.applicationInstanceID = local.qryReferralSettings.applicationInstanceID;
		local.emailRecipient = local.qryReferralSettings.emailRecipient;
		local.appBaseLink = local.objAppBaseLink.getAppBaseLink(applicationInstanceID=local.applicationInstanceID,siteID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID);
		local.memberLocalLink = "/?" & local.appBaseLink;
		local.memberExternalLink = "#arguments.event.getValue('mc_siteInfo.scheme')#://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.memberLocalLink;
		local.optOutMemberIDList = "";	
		if(val(local.qryReferralSettings.memEmailOptOutGroupID)){
			local.qryOptOutMembers = local.objAdminReferrals.getGroupsMembers(orgID=arguments.event.getValue('mc_siteInfo.orgID'),groupID=local.qryReferralSettings.memEmailOptOutGroupID);
			local.optOutMemberIDList = valueList(local.qryOptOutMembers.memberID);
		}	
		</cfscript>

		<cfloop query="local.qryGetReferralData">
			<cfset local.qryMember = application.objMember.getMemberInfo(local.qryGetReferralData.memberID) />
			<cfset local.qryMemberAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberid=local.qryMember.memberID)>
			<cfset local.memOptedOut = listFind(local.optOutMemberIDList, local.qryMember.memberID)>
			<cfset local.memberEmailList = "" />
			<cfif not local.memOptedOut>
				<cfset local.qryGetMemberRefEmails = local.objAdminReferrals.getMemberRefEmails(memberid=local.qryGetReferralData.memberID, referralID=local.referralID) />
				<cfset local.memberEmailList = valueList(local.qryGetMemberRefEmails.email) />
			</cfif>				

			<cfset local.pageStyle = "width:550px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
			<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
			
			<cfset local.panelid1 = "" />
			<cfset local.subpanelid1 = "" />
			<cfset local.radius_dist = "" />
			<cfif len(local.emailRecipient)>
				<cfset local.emailAll = replace(local.emailRecipient,";",",","all")>
				<cfset local.sendFrom = trim(listFirst(local.emailRecipient,";"))>
			<cfelse>
				<cfset local.emailAll = "">
				<cfset local.sendFrom = arguments.event.getValue('mc_siteInfo.supportProviderEmail')>
			</cfif>
			
			<cfif listLen(local.memberEmailList) or len(local.emailAll)>
				<cftry>
					<cfsavecontent variable="local.emailContent">
						<cfoutput>					
						<div style="#local.pageStyle#">
							<p style="font-size:13px;color:##333333;font-weight:bold;">Referral #arguments.event.getValue('mc_siteinfo.sitecode')#-#local.qryGetReferralData.clientReferralID# has been closed.</p>
							<p style="font-size:13px;color:##333333;">This is a notification sent to #local.qryGetReferralData.memberName#:</p>
							<p style="font-size:13px;color:##333333;">
								The following client was referred to you and you have confirmed that the client has not been retained. Referral #arguments.event.getValue('mc_siteinfo.sitecode')#-#local.qryGetReferralData.clientReferralID# is now closed. Please note that if this client retains you in the future, you need to contact us so we may re-open the referral. 							
							</p>
							<br/>
							<table cellpadding="4" cellspacing="0" width="50%" border="1">
							<tr> 
								<td width="25%" style="#local.tdStyle#">Client First Name:</td> 
								<td style="#local.tdStyle#">#local.qryGetReferralData.firstName#</td> 
							</tr> 
							<tr> 
								<td style="#local.tdStyle#">Client Last Name:</td> 
								<td style="#local.tdStyle#">#local.qryGetReferralData.lastName#</td> 
							</tr>
							<tr> 
								<td style="#local.tdStyle#">Client E-mail:</td>
								<td style="#local.tdStyle#">#local.qryGetReferralData.email#</td> 
							</tr> 
							<tr> 
								<td style="#local.tdStyle#">Client Business:</td> 
								<td style="#local.tdStyle#">#local.qryGetReferralData.businessName#</td> 
							</tr>
							<tr> 
								<td style="#local.tdStyle#">Client Language:</td> 
								<td style="#local.tdStyle#">#local.qryGetReferralData.languageName#</td> 
							</tr>
							<tr> 
								<td style="#local.tdStyle#">Client E-mail:</td>
								<td style="#local.tdStyle#">#local.qryGetReferralData.email#</td> 
							</tr> 
							<tr> 
								<td style="#local.tdStyle#">Client E-mail:</td>
								<td style="#local.tdStyle#">#local.qryGetReferralData.email#</td> 
							</tr> 	
							<tr> 
								<td style="#local.tdStyle#">Client Home Phone ##:</td>
								<td style="#local.tdStyle#">#local.qryGetReferralData.homePhone#</td> 
							</tr> 
							<cfif val(local.qryGetReferralData.repID)>
								<tr style="#local.tdStyle#"> 
									<td width="25%" style="#local.tdStyle#">Representative First Name:</td> 
									<td style="#local.tdStyle#">#local.qryGetReferralData.repFirstName#</td> 
								</tr> 
								<tr> 
									<td style="#local.tdStyle#">Representative Last Name:</td> 
									<td style="#local.tdStyle#">#local.qryGetReferralData.repLastName#</td> 
								</tr>
								<tr> 
									<td style="#local.tdStyle#">Representative E-mail:</td>
									<td style="#local.tdStyle#">#local.qryGetReferralData.repEmail#</td> 
								</tr>
								<tr> 
									<td style="#local.tdStyle#">Representative Home Phone ##:</td>
									<td style="#local.tdStyle#">#local.qryGetReferralData.repHomePhone#</td> 
								</tr> 
							</cfif>
							<tr> 
								<td style="#local.tdStyle#">Referral Date:</td>
								<td style="#local.tdStyle#">#dateFormat(local.qryGetReferralData.clientReferralDate,"mm/dd/yyyy")#</td> 
							</tr>
							<cfloop query="local.qryGetReferralFilterData">
								<cfif listFindNoCase("panelid1,panelid2,panelid3,subpanelid1,subpanelid2,subpanelid3",local.qryGetReferralFilterData.elementID)>
									<cfswitch expression="#local.qryGetReferralFilterData.elementID#">
										<cfcase value="panelid1">
											<cfset local.qryGetPanelInfo = local.objAdminReferrals.getPanelByID(panelID=local.qryGetReferralFilterData.elementValue) />
											<cfset local.panelid1 = local.qryGetPanelInfo.name />
										</cfcase>
										<cfcase value="subpanelid1">
											<cfloop list="#local.qryGetReferralFilterData.elementValue#" index="local.thisPanelID">
												<cfset local.qryGetPanelInfo = local.objAdminReferrals.getPanelByID(panelID=local.thisPanelID) />
												<cfset local.subpanelid1 = listAppend(local.subpanelid1,local.qryGetPanelInfo.name) />
											</cfloop>
										</cfcase>
									</cfswitch>
								</cfif>
								<cfif findNoCase('_radius',local.qryGetReferralFilterData.elementID)>
									<cfset local.radius_dist = local.qryGetReferralFilterData.elementValue />
								</cfif>
							</cfloop>
							<tr>
								<td style="#local.tdStyle#">Primary Panel:</td>
								<td style="#local.tdStyle#">#local.panelid1#</td> 
							</tr> 
							<cfif len(trim(local.subpanelid1))>
								<tr> 
									<td style="#local.tdStyle#">Primary Sub-Panels:</td>
									<td style="#local.tdStyle#">#local.subpanelid1#</td>
								</tr>
							</cfif>
							<cfloop query="local.qryGetReferralFilterData">
								<cfloop array="#local.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
									<cfif local.thisfield.xmlattributes.fieldCode is local.qryGetReferralFilterData.elementID and len(trim(local.qryGetReferralFilterData.elementValue))>
										<cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode)>
											<tr>
												<td style="#local.tdStyle#">Distance Information:</td>
												<td style="#local.tdStyle#">Within #val(local.radius_dist)# miles of #htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# #local.qryGetReferralFilterData.elementValue#</td> 
											</tr>
										<cfelse>
											<tr>
												<td style="#local.tdStyle#">#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#:</td>
												<td style="#local.tdStyle#">#local.qryGetReferralFilterData.elementValue#</td> 
											</tr>
										</cfif>
									</cfif>
								</cfloop>
							</cfloop>
							<tr>
								<td style="#local.tdStyle#">Legal Issue Description:</td>
								<td style="#local.tdStyle#">#local.qryGetReferralData.issueDesc#</td> 
							</tr> 					
							</table>
							<br/>
						</div>
						<div style="clear:both"></div>
						</cfoutput>
					</cfsavecontent>

					<cfscript>
						if(not local.memOptedOut){
							local.emailto = local.memberEmailList;
						} else {
							local.emailto = local.emailAll;
						}
						local.arrEmailTo = [];
						local.emailto = replace(local.emailto,",",";","all");
						local.toEmailArr = listToArray(local.emailto,';');
						for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
							local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
						}
					</cfscript>

					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom')},
						emailto=local.arrEmailTo,
						emailreplyto=local.sendFrom,
						emailsubject="#arguments.event.getValue('mc_siteInfo.siteName')# Referral Status Update",
						emailtitle="#arguments.event.getValue('mc_siteinfo.sitename')# Referral Status Notification",
						emailhtmlcontent=local.emailContent,
						siteID=arguments.event.getValue('mc_siteinfo.siteid'),
						memberID=local.qryMember.memberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="REFNOTIFY"),
						sendingSiteResourceID=this.siteResourceID)>

				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local) />
				</cfcatch>
				</cftry>
			</cfif>

		</cfloop>	<!--- //loop query="local.qryGetReferralData --->
	</cffunction>
	
	<cffunction name="doEmailRetainCase" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew()>

		<cfscript>
		local.objMemberFieldsets = CreateObject("component","model.system.platform.memberFieldsets");
		local.objAppBaseLink = CreateObject('component', 'model.apploader');
		local.objAdminReferrals = CreateObject('component', 'model.admin.referrals.referrals');
		local.orgID = arguments.event.getValue('mc_siteInfo.orgID');
		local.siteID = arguments.event.getValue('mc_siteInfo.siteID');
		local.qryGetReferralData = local.objAdminReferrals.getReferralData(orgID=local.orgID,clientReferralID=arguments.event.getValue('clientReferralID'));
		local.thisClientID = local.qryGetReferralData.clientID;
		if(val(local.qryGetReferralData.clientParentID))
			local.thisClientID = val(local.qryGetReferralData.clientParentID);
		local.qryReferralSettings = getReferralSettings(siteID=local.siteID);
		local.referralID = local.qryReferralSettings.referralID;
		local.qryGetReferralFilterData = local.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID);
		local.siteResourceID = local.qryReferralSettings.siteResourceID;
		local.qryFieldsetID = local.objAdminReferrals.getLocatorFieldsetID(siteResourceID=local.siteResourceID, area='referralsearch');
		local.xmlFields = local.objMemberFieldsets.getMemberFieldsXML(fieldsetid=val(local.qryFieldsetID.fieldsetID), usage="memberReferralSearch");
		local.arrFieldCodes = XMLSearch(local.xmlFields,"//mf/@fieldCode");
		local.applicationInstanceID = local.qryReferralSettings.applicationInstanceID;
		local.emailRecipient = local.qryReferralSettings.emailRecipient;
		local.appBaseLink = local.objAppBaseLink.getAppBaseLink(applicationInstanceID=local.applicationInstanceID,siteID=local.siteID);
		local.memberLocalLink = "/?" & local.appBaseLink;
		local.memberExternalLink = "#arguments.event.getValue('mc_siteInfo.scheme')#://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.memberLocalLink;
		local.memberMailTopTxt = local.qryReferralSettings.memberMailTopTxt;
		local.memberMailBottomTxt = local.qryReferralSettings.memberMailBottomTxt;		
		local.optOutMemberIDList = "";
		if(val(local.qryReferralSettings.memEmailOptOutGroupID)){
			local.qryOptOutMembers = local.objAdminReferrals.getGroupsMembers(orgID=local.orgID,groupID=local.qryReferralSettings.memEmailOptOutGroupID);
			local.optOutMemberIDList = valueList(local.qryOptOutMembers.memberID);
		}	
		</cfscript>

		<cfloop query="local.qryGetReferralData">
			<cfset local.qryMember = application.objMember.getMemberInfo(local.qryGetReferralData.memberID) />
			<cfset local.qryMemberAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=local.orgID, memberid=local.qryMember.memberID)>
			<cfset local.memOptedOut = listFind(local.optOutMemberIDList, local.qryMember.memberID)>
			<cfset local.memberEmailList = "" />
			<cfif not local.memOptedOut>
				<cfset local.qryGetMemberRefEmails = local.objAdminReferrals.getMemberRefEmails(memberid=local.qryGetReferralData.memberID, referralID=local.referralID) />
				<cfset local.memberEmailList = valueList(local.qryGetMemberRefEmails.email) />
			</cfif>

			<cfset local.pageStyle = "width:550px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
			<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
			
			<cfset local.panelid1 = "" />
			<cfset local.subpanelid1 = "" />
			<cfset local.radius_dist = "" />
			
			<cfif len(local.emailRecipient)>
				<cfset local.emailAll = replace(local.emailRecipient,";",",","all")>
				<cfset local.sendFrom = trim(listFirst(local.emailRecipient,";"))>
			<cfelse>
				<cfset local.emailAll = "">
				<cfset local.sendFrom = arguments.event.getValue('mc_siteInfo.supportProviderEmail')>
			</cfif>

			<cfif listLen(local.memberEmailList) or listLen(local.emailAll)>
				<cftry>
					<cfsavecontent variable="local.emailContent">
						<cfoutput>
						<div style="#local.pageStyle#">
							<p style="font-size:13px;color:##333333;font-weight:bold;">Referral #arguments.event.getValue('mc_siteinfo.sitecode')#-#local.qryGetReferralData.clientReferralID# has a new Status: #local.qryGetReferralData.statusName#</p>
							<p style="font-size:13px;color:##333333;">Thank you for updating your records.  Referral #arguments.event.getValue('mc_siteinfo.sitecode')#-#local.qryGetReferralData.clientReferralID# has a new status of #local.qryGetReferralData.statusName#. The referral status is now considered to be an open & retained case with your office. Please contact us if this status changes, so we can keep our records accurate.</p>
							<br/>
							<table cellpadding="4" cellspacing="0" width="100%" border="1">
							<tr> 
								<td width="25%" style="#local.tdStyle#">Client First Name:</td> 
								<td style="#local.tdStyle#">#local.qryGetReferralData.firstName#</td> 
							</tr> 
							<tr> 
								<td style="#local.tdStyle#">Client Last Name:</td> 
								<td style="#local.tdStyle#">#local.qryGetReferralData.lastName#</td> 
							</tr>
							<tr> 
								<td style="#local.tdStyle#">Client Business:</td> 
								<td style="#local.tdStyle#">#local.qryGetReferralData.businessName#</td> 
							</tr>
							<tr> 
								<td style="#local.tdStyle#">Client Language:</td> 
								<td style="#local.tdStyle#">#local.qryGetReferralData.languageName#</td> 
							</tr>
							<tr> 
								<td style="#local.tdStyle#">Client E-mail:</td>
								<td style="#local.tdStyle#">#local.qryGetReferralData.email#</td> 
							</tr> 	
							<tr> 
								<td style="#local.tdStyle#">Client Home Phone ##:</td>
								<td style="#local.tdStyle#">#local.qryGetReferralData.homePhone#</td> 
							</tr> 
							<cfif val(local.qryGetReferralData.repID)>
								<tr style="#local.tdStyle#"> 
									<td width="25%" style="#local.tdStyle#">Representative First Name:</td> 
									<td style="#local.tdStyle#">#local.qryGetReferralData.repFirstName#</td> 
								</tr> 
								<tr> 
									<td style="#local.tdStyle#">Representative Last Name:</td> 
									<td style="#local.tdStyle#">#local.qryGetReferralData.repLastName#</td> 
								</tr>
								<tr> 
									<td style="#local.tdStyle#">Representative E-mail:</td>
									<td style="#local.tdStyle#">#local.qryGetReferralData.repEmail#</td> 
								</tr>
								<tr> 
									<td style="#local.tdStyle#">Representative Home Phone ##:</td>
									<td style="#local.tdStyle#">#local.qryGetReferralData.repHomePhone#</td> 
								</tr> 
							</cfif>
							<tr> 
								<td style="#local.tdStyle#">Referral Date:</td>
								<td style="#local.tdStyle#">#dateFormat(local.qryGetReferralData.clientReferralDate,"mm/dd/yyyy")#</td> 
							</tr>
							<cfloop query="local.qryGetReferralFilterData">
								<cfif listFindNoCase("panelid1,panelid2,panelid3,subpanelid1,subpanelid2,subpanelid3",local.qryGetReferralFilterData.elementID)>
									<cfswitch expression="#local.qryGetReferralFilterData.elementID#">
										<cfcase value="panelid1">
											<cfset local.qryGetPanelInfo = local.objAdminReferrals.getPanelByID(panelID=local.qryGetReferralFilterData.elementValue) />
											<cfset local.panelid1 = local.qryGetPanelInfo.name />
										</cfcase>
										<cfcase value="subpanelid1">
											<cfloop list="#local.qryGetReferralFilterData.elementValue#" index="local.thisPanelID">
												<cfset local.qryGetPanelInfo = local.objAdminReferrals.getPanelByID(panelID=local.thisPanelID) />
												<cfset local.subpanelid1 = listAppend(local.subpanelid1,local.qryGetPanelInfo.name) />
											</cfloop>
										</cfcase>
									</cfswitch>
								</cfif>
								<cfif findNoCase('_radius',local.qryGetReferralFilterData.elementID)>
									<cfset local.radius_dist = local.qryGetReferralFilterData.elementValue />
								</cfif>
							</cfloop>
							<tr>
								<td style="#local.tdStyle#">Primary Panel:</td>
								<td style="#local.tdStyle#">#local.panelid1#</td> 
							</tr> 
							<cfif len(trim(local.subpanelid1))>
								<tr>
									<td style="#local.tdStyle#">Primary Sub-Panels:</td>
									<td style="#local.tdStyle#">#local.subpanelid1#</td>
								</tr>
							</cfif>
							<cfloop query="local.qryGetReferralFilterData">
								<cfloop array="#local.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
									<cfif local.thisfield.xmlattributes.fieldCode is local.qryGetReferralFilterData.elementID and len(trim(local.qryGetReferralFilterData.elementValue))>
										<cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode)>
											<tr>
												<td style="#local.tdStyle#">Distance Information:</td>
												<td style="#local.tdStyle#">Within #val(local.radius_dist)# miles of #htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# #local.qryGetReferralFilterData.elementValue#</td> 
											</tr>
										<cfelse>
											<tr>
												<td style="#local.tdStyle#">#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#:</td>
												<td style="#local.tdStyle#">#local.qryGetReferralFilterData.elementValue#</td> 
											</tr>
										</cfif>
									</cfif>
								</cfloop>
							</cfloop>
							<tr>
								<td style="#local.tdStyle#">Legal Issue Description:</td>
								<td style="#local.tdStyle#">#local.qryGetReferralData.issueDesc#</td> 
							</tr> 					
							</table>
							<br/>
							<div style="font-size:13px;color:##333333;">#local.memberMailBottomTxt#</div>	
						</div>
						<div style="clear:both"></div>
						</cfoutput>
					</cfsavecontent>

					<cfscript>
						if(not local.memOptedOut){
							local.emailto = local.memberEmailList;
						} else {
							local.emailto = local.emailAll;
						}
						local.arrEmailTo = [];
						local.emailto = replace(local.emailto,",",";","all");
						local.toEmailArr = listToArray(local.emailto,';');
						for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
							local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
						}
					</cfscript>

					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom')},
						emailto=local.arrEmailTo,
						emailreplyto=local.sendFrom,
						emailsubject="#arguments.event.getValue('mc_siteInfo.siteName')# Referral Status Update",
						emailtitle="#arguments.event.getValue('mc_siteinfo.sitename')# Referral Status Notification",
						emailhtmlcontent=local.emailContent,
						siteID=arguments.event.getValue('mc_siteinfo.siteid'),
						memberID=local.qryMember.memberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="REFNOTIFY"),
						sendingSiteResourceID=this.siteResourceID)>

				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local) />
				</cfcatch>
				</cftry>
			</cfif>

		</cfloop>	<!--- //loop query="local.qryGetReferralData --->
	</cffunction>
	
	<cffunction name="doEmailCaseStatusUpdate" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew() />

		<cfscript>
		local.objMemberFieldsets = CreateObject("component","model.system.platform.memberFieldsets");
		local.objAppBaseLink = CreateObject('component', 'model.apploader');
		local.objAdminReferrals = CreateObject('component', 'model.admin.referrals.referrals');
		local.orgID = arguments.event.getValue('mc_siteInfo.orgID');
		local.siteID = arguments.event.getValue('mc_siteInfo.siteID');
		local.qryGetReferralData = local.objAdminReferrals.getReferralData(orgID=local.orgID,clientReferralID=arguments.event.getValue('clientReferralID'));
		local.thisClientID = local.qryGetReferralData.clientID;
		if(val(local.qryGetReferralData.clientParentID))
			local.thisClientID = val(local.qryGetReferralData.clientParentID);
		local.qryReferralSettings = getReferralSettings(siteID=local.siteID);
		local.referralID = local.qryReferralSettings.referralID;
		local.qryGetReferralFilterData = local.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID);
		local.siteResourceID = local.qryReferralSettings.siteResourceID;
		local.qryFieldsetID = local.objAdminReferrals.getLocatorFieldsetID(siteResourceID=local.siteResourceID, area='referralsearch');
		local.xmlFields = local.objMemberFieldsets.getMemberFieldsXML(fieldsetid=val(local.qryFieldsetID.fieldsetID), usage="memberReferralSearch");
		local.arrFieldCodes = XMLSearch(local.xmlFields,"//mf/@fieldCode");
		local.applicationInstanceID = local.qryReferralSettings.applicationInstanceID;
		local.emailRecipient = local.qryReferralSettings.emailRecipient;
		local.appBaseLink = local.objAppBaseLink.getAppBaseLink(applicationInstanceID=local.applicationInstanceID,siteID=local.siteID);
		local.memberLocalLink = "/?" & local.appBaseLink;
		local.memberExternalLink = "#arguments.event.getValue('mc_siteInfo.scheme')#://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.memberLocalLink;
		local.optOutMemberIDList = "";
		if(val(local.qryReferralSettings.memEmailOptOutGroupID)){
			local.qryOptOutMembers = local.objAdminReferrals.getGroupsMembers(orgID=local.orgID,groupID=local.qryReferralSettings.memEmailOptOutGroupID);
			local.optOutMemberIDList = valueList(local.qryOptOutMembers.memberID);
		}
		</cfscript>

		<cfloop query="local.qryGetReferralData">
			<cfset local.qryMember = application.objMember.getMemberInfo(local.qryGetReferralData.memberID) />
			<cfset local.qryMemberAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=local.orgID, memberid=local.qryMember.memberID)>
			<cfset local.memOptedOut = listFind(local.optOutMemberIDList, local.qryMember.memberID)>
			<cfset local.memberEmailList = "" />
			<cfif not local.memOptedOut>
				<cfset local.qryGetMemberRefEmails = local.objAdminReferrals.getMemberRefEmails(memberid=local.qryGetReferralData.memberID, referralID=local.referralID) />
				<cfset local.memberEmailList = valueList(local.qryGetMemberRefEmails.email) />
			</cfif>

			<cfset local.pageStyle = "width:550px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
			<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
			
			<cfset local.panelid1 = "" />
			<cfset local.subpanelid1 = "" />
			<cfset local.radius_dist = "" />
			
			<cfif len(local.emailRecipient)>
				<cfset local.emailAll = replace(local.emailRecipient,";",",","all")>
				<cfset local.sendFrom = trim(listFirst(local.emailRecipient,";"))>
			<cfelse>
				<cfset local.emailAll = "">
				<cfset local.sendFrom = arguments.event.getValue('mc_siteInfo.supportProviderEmail')>
			</cfif>

			<cfif listLen(local.memberEmailList) or listLen(local.emailAll)>
				<cftry>
					<cfsavecontent variable="local.emailContent">
					<cfoutput>
						<div style="#local.pageStyle#">
							<p style="font-size:13px;color:##333333;font-weight:bold;">Referral #arguments.event.getValue('mc_siteinfo.sitecode')#-#local.qryGetReferralData.clientReferralID# has a new Status: #local.qryGetReferralData.statusName#</p>
							<p style="font-size:13px;color:##333333;">Thank you for updating your records.  Referral #arguments.event.getValue('mc_siteinfo.sitecode')#-#local.qryGetReferralData.clientReferralID# has a new status of #local.qryGetReferralData.statusName#. Please contact us if this status changes, so we can keep our records accurate.</p>
							<br/>
							<table cellpadding="4" cellspacing="0" width="100%" border="1">
							<tr> 
								<td width="25%" style="#local.tdStyle#">Client First Name:</td> 
								<td style="#local.tdStyle#">#local.qryGetReferralData.firstName#</td> 
							</tr> 
							<tr> 
								<td style="#local.tdStyle#">Client Last Name:</td> 
								<td style="#local.tdStyle#">#local.qryGetReferralData.lastName#</td> 
							</tr>
							<tr> 
								<td style="#local.tdStyle#">Client Business:</td> 
								<td style="#local.tdStyle#">#local.qryGetReferralData.businessName#</td> 
							</tr>
							<tr> 
								<td style="#local.tdStyle#">Client Language:</td> 
								<td style="#local.tdStyle#">#local.qryGetReferralData.languageName#</td> 
							</tr>
							<tr> 
								<td style="#local.tdStyle#">Client E-mail:</td>
								<td style="#local.tdStyle#">#local.qryGetReferralData.email#</td> 
							</tr> 	
							<tr> 
								<td style="#local.tdStyle#">Client Home Phone ##:</td>
								<td style="#local.tdStyle#">#local.qryGetReferralData.homePhone#</td> 
							</tr> 
							<cfif val(local.qryGetReferralData.repID)>
								<tr style="#local.tdStyle#"> 
									<td width="25%" style="#local.tdStyle#">Representative First Name:</td> 
									<td style="#local.tdStyle#">#local.qryGetReferralData.repFirstName#</td> 
								</tr> 
								<tr> 
									<td style="#local.tdStyle#">Representative Last Name:</td> 
									<td style="#local.tdStyle#">#local.qryGetReferralData.repLastName#</td> 
								</tr>
								<tr> 
									<td style="#local.tdStyle#">Representative E-mail:</td>
									<td style="#local.tdStyle#">#local.qryGetReferralData.repEmail#</td> 
								</tr>
								<tr> 
									<td style="#local.tdStyle#">Representative Home Phone ##:</td>
									<td style="#local.tdStyle#">#local.qryGetReferralData.repHomePhone#</td> 
								</tr> 
							</cfif>
							<tr> 
								<td style="#local.tdStyle#">Referral Date:</td>
								<td style="#local.tdStyle#">#dateFormat(local.qryGetReferralData.clientReferralDate,"mm/dd/yyyy")#</td> 
							</tr>
							<cfloop query="local.qryGetReferralFilterData">
								<cfif listFindNoCase("panelid1,panelid2,panelid3,subpanelid1,subpanelid2,subpanelid3",local.qryGetReferralFilterData.elementID)>
									<cfswitch expression="#local.qryGetReferralFilterData.elementID#">
										<cfcase value="panelid1">
											<cfset local.qryGetPanelInfo = local.objAdminReferrals.getPanelByID(panelID=local.qryGetReferralFilterData.elementValue) />
											<cfset local.panelid1 = local.qryGetPanelInfo.name />
										</cfcase>
										<cfcase value="subpanelid1">
											<cfloop list="#local.qryGetReferralFilterData.elementValue#" index="local.thisPanelID">
												<cfset local.qryGetPanelInfo = local.objAdminReferrals.getPanelByID(panelID=local.thisPanelID) />
												<cfset local.subpanelid1 = listAppend(local.subpanelid1,local.qryGetPanelInfo.name) />
											</cfloop>
										</cfcase>
									</cfswitch>
								</cfif>
								<cfif findNoCase('_radius',local.qryGetReferralFilterData.elementID)>
									<cfset local.radius_dist = local.qryGetReferralFilterData.elementValue />
								</cfif>
							</cfloop>
							<tr>
								<td style="#local.tdStyle#">Lawyer Name:</td>
								<td style="#local.tdStyle#">#local.qryGetReferralData.memberName#</td> 
							</tr>
							<tr>
								<td style="#local.tdStyle#">Primary Panel:</td>
								<td style="#local.tdStyle#">#local.panelid1#</td> 
							</tr> 
							<cfif len(trim(local.subpanelid1))>
								<tr>
									<td style="#local.tdStyle#">Primary Sub-Panels:</td>
									<td style="#local.tdStyle#">#local.subpanelid1#</td>
								</tr>
							</cfif>
							<cfloop query="local.qryGetReferralFilterData">
								<cfloop array="#local.xmlFields.xmlRoot.xmlChildren#" index="local.thisfield">
									<cfif local.thisfield.xmlattributes.fieldCode is local.qryGetReferralFilterData.elementID and len(trim(local.qryGetReferralFilterData.elementValue))>
										<cfif ReFindNoCase('mat?_[0-9]+_postalcode',local.thisfield.xmlattributes.fieldCode)>
											<tr>
												<td style="#local.tdStyle#">Distance Information:</td>
												<td style="#local.tdStyle#">Within #val(local.radius_dist)# miles of #htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)# #local.qryGetReferralFilterData.elementValue#</td> 
											</tr>
										<cfelse>
											<tr>
												<td style="#local.tdStyle#">#htmlEditFormat(local.thisfield.xmlattributes.fieldLabel)#:</td>
												<td style="#local.tdStyle#">#local.qryGetReferralFilterData.elementValue#</td> 
											</tr>
										</cfif>
									</cfif>
								</cfloop>
							</cfloop>
							<tr>
								<td style="#local.tdStyle#">Legal Issue Description:</td>
								<td style="#local.tdStyle#">#local.qryGetReferralData.issueDesc#</td> 
							</tr> 					
							</table>
							<br/>
						</div>
						<div style="clear:both"></div>
						</cfoutput>
					</cfsavecontent>

					<cfscript>
						if(not local.memOptedOut){
							local.emailto = local.memberEmailList;
						} else {
							local.emailto = local.emailAll;
						}
						local.arrEmailTo = [];
						local.emailto = replace(local.emailto,",",";","all");
						local.toEmailArr = listToArray(local.emailto,';');
						for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
							local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
						}
					</cfscript>

					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom')},
						emailto=local.arrEmailTo,
						emailreplyto=local.sendFrom,
						emailsubject="#arguments.event.getValue('mc_siteInfo.siteName')# - Case Status Update",
						emailtitle="#arguments.event.getValue('mc_siteinfo.sitename')# Case Status Notification",
						emailhtmlcontent=local.emailContent,
						siteID=arguments.event.getValue('mc_siteinfo.siteid'),
						memberID=local.qryMember.memberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="REFNOTIFY"),
						sendingSiteResourceID=this.siteResourceID)>

				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local) />
				</cfcatch>
				</cftry>
			</cfif>

		</cfloop>	<!--- //loop query="local.qryGetReferralData --->
	</cffunction>

	<cffunction name="doEmailCaseFeeCollect" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew() />

		<cfscript>
		local.objMemberFieldsets = CreateObject("component","model.system.platform.memberFieldsets");
		local.objAppBaseLink = CreateObject('component', 'model.apploader');
		local.objAdminReferrals = CreateObject('component', 'model.admin.referrals.referrals');
		local.qryGetReferralData = local.objAdminReferrals.getReferralData(orgID=arguments.event.getValue('mc_siteInfo.orgID'),clientReferralID=arguments.event.getValue('clientReferralID'));
		local.thisClientID = local.qryGetReferralData.clientID;
		if(val(local.qryGetReferralData.clientParentID))
			local.thisClientID = val(local.qryGetReferralData.clientParentID);
		local.qryGetReferralFilterData = local.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID);
		local.qryReferralSettings = getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID'));
		local.siteResourceID = local.qryReferralSettings.siteResourceID;
		local.qryFieldsetID = local.objAdminReferrals.getLocatorFieldsetID(siteResourceID=local.siteResourceID, area='referralsearch');
		local.xmlFields = local.objMemberFieldsets.getMemberFieldsXML(fieldsetid=val(local.qryFieldsetID.fieldsetID), usage="memberReferralSearch");
		local.arrFieldCodes = XMLSearch(local.xmlFields,"//mf/@fieldCode");
		local.applicationInstanceID = local.qryReferralSettings.applicationInstanceID;
		local.emailRecipient = local.qryReferralSettings.emailRecipient;
		local.appBaseLink = local.objAppBaseLink.getAppBaseLink(applicationInstanceID=local.applicationInstanceID,siteID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID);
		local.memberLocalLink = "/?" & local.appBaseLink;
		local.memberExternalLink = "#arguments.event.getValue('mc_siteInfo.scheme')#://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.memberLocalLink;
		local.systemMailCaseActivityTxt = local.qryReferralSettings.systemMailCaseActivityTxt;
		</cfscript>
		
		<cfloop query="local.qryGetReferralData">
			<cfset local.qryMember = application.objMember.getMemberInfo(local.qryGetReferralData.memberID) />
			<cfset local.memberName = local.qryMember.firstName & " "	& local.qryMember.lastName>
			<cfset local.qryMemberAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberid=local.qryMember.memberID)>
			<cfset local.memberEmail = application.objMember.getMainEmail(memberid=local.qryMember.memberID).email />

			<cfset local.pageStyle = "width:550px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
			<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
			
			<cfset local.panelid1 = "" />
			<cfset local.subpanelid1 = "" />
			<cfset local.radius_dist = "" />
			
			<cfif len(local.emailRecipient)>
				<cfset local.emailAll = replace(local.emailRecipient,";",",","all")>
				<cfset local.sendFrom = trim(listFirst(local.emailRecipient,";"))>
			<cfelse>
				<cfset local.emailAll = "">
				<cfset local.sendFrom = arguments.event.getValue('mc_siteInfo.supportProviderEmail')>
			</cfif>

			<cfset local.mailCollectionFrom = arguments.event.getValue('mc_siteInfo.networkEmailFrom')>
			<cfset local.mailCollectionReplyTo = local.sendFrom>
			<cfset local.mailCollectionSubject = "#arguments.event.getValue('mc_siteInfo.siteName')# - Case Activity Notification">			

			<cftry>
				<cfsavecontent variable="local.emailContent">
					<cfoutput>
					<div style="#local.pageStyle#">
						<p style="font-size:13px;color:##333333;font-weight:bold;">Referral #arguments.event.getValue('mc_siteinfo.sitecode')#-#local.qryGetReferralData.clientReferralID# has been updated. #local.qryGetReferralData.memberName# has recorded a payment of client #local.qryGetReferralData.firstName# #local.qryGetReferralData.lastName#.</p>
						<div style="font-size:13px;color:##333333;">#local.systemMailCaseActivityTxt#</div>					
						<br/>
					</div>
					<div style="clear:both"></div>
					</cfoutput>
				</cfsavecontent>
				
				<cfset local.sednEmailStr = local.objAdminReferrals.sendReferralEmail(referralID=local.qryReferralSettings.referralID,
																						siteid=arguments.event.getValue('mc_siteinfo.siteid'),
																						recordedByMemberID=local.qryGetReferralData.enteredByMemberID,
																						messageContent=local.emailContent,
																						contentTitle="#arguments.event.getValue('mc_siteinfo.sitename')# Case Activity Notification",
																						fromName=local.qryReferralSettings.title,
																						fromEmail=local.mailCollectionFrom,
																						replyToEmail=local.mailCollectionReplyTo,
																						subject=local.mailCollectionSubject,
																						refMemberID=local.qryMember.memberID,
																						refMemberName=local.memberName,
																						refMemberEmail=local.emailRecipient,
																						messageTypeCode="REFERRALMEMBER")>

			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local) />
			</cfcatch>
			</cftry>
		</cfloop>	<!--- //loop query="local.qryGetReferralData --->
	</cffunction>

	<cffunction name="doEmailCaseStatement" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew() />

		<cfscript>
		local.objMemberFieldsets = CreateObject("component","model.system.platform.memberFieldsets");
		local.objAppBaseLink = CreateObject('component', 'model.apploader');
		local.objAdminReferrals = CreateObject('component', 'model.admin.referrals.referrals');
		local.qryGetReferralData = local.objAdminReferrals.getReferralData(orgID=arguments.event.getValue('mc_siteInfo.orgID'),clientReferralID=arguments.event.getValue('clientReferralID'));
		local.thisClientID = local.qryGetReferralData.clientID;
		if(val(local.qryGetReferralData.clientParentID))
			local.thisClientID = val(local.qryGetReferralData.clientParentID);		
		local.qryGetCaseFees = local.objAdminReferrals.getCaseFees(caseID=local.qryGetReferralData.caseID);
		local.qryGetCaseFeesTotals = getFeesTotals(qryItems=local.qryGetCaseFees);
		local.applicationInstanceID = getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).applicationInstanceID;
		local.siteResourceID = getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).siteResourceID;
		local.emailRecipient = trim(arguments.event.getValue('_email'));
		local.emailNotes = arguments.event.getValue('_notes');
		local.appBaseLink = local.objAppBaseLink.getAppBaseLink(applicationInstanceID=local.applicationInstanceID,siteID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID);
		local.memberLocalLink = "/?" & local.appBaseLink;
		local.memberExternalLink = "#arguments.event.getValue('mc_siteInfo.scheme')#://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.memberLocalLink;
		local.objPage = CreateObject("component","model.admin.pages.pages");			
		local.qryPageData = local.objPage.getData(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=local.siteResourceID);
		/* local.resourceName = local.qryPageData.qryResourceData.pageName;	*/
		local.resourceName = "referrals";
		</cfscript>

		<cfset local.qryMember = application.objMember.getMemberInfo(local.qryGetReferralData.memberID) />
		<cfset local.qryMemberAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberid=local.qryMember.memberID)>
		<cfset local.memberEmail = application.objMember.getMainEmail(memberid=local.qryMember.memberID).email />
		
		<!--- Get Primary Panel Info --->
		<cfset local.qryGetReferralFilterData = local.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID) />
		<cfloop query="local.qryGetReferralFilterData">
			<cfif listFindNoCase("panelid1",local.qryGetReferralFilterData.elementID)>
				<cfset local.qryGetPanelInfo = local.objAdminReferrals.getPanelByID(panelID=local.qryGetReferralFilterData.elementValue) />
				<cfbreak />
			</cfif>
		</cfloop>
		
		<cfset local.returnPDFStruct = doGenerateCaseStatement(event=arguments.event) />
		
		<cfset local.pageStyle = "width:550px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
		<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
		
		<cfset local.panelid1 = "" />
		<cfset local.subpanelid1 = "" />
		<cfset local.radius_dist = "" />
		<cfset local.emailAll = trim(local.emailRecipient) />

		<cftry>
			<cfif find(";",local.emailRecipient)>
				<cfset local.emailAll = replace(local.emailRecipient,";",",","all") />
				<cfset local.sendFrom = trim(listFirst(local.emailRecipient,";")) />
			<cfelseif find(",",local.emailRecipient)>
				<cfset local.sendFrom = trim(listFirst(local.emailRecipient,",")) />
			<cfelse>
				<cfset local.sendFrom = trim(local.emailRecipient) />
			</cfif>
			<cfif NOT len(local.sendFrom) or NOT isValid("regex",local.sendFrom,application.regEx.email)>
				<cfthrow />
			</cfif>
			<cfcatch type="any">
				<cfset local.sendFrom = arguments.event.getValue('mc_siteInfo.supportProviderEmail') />
			</cfcatch>
		</cftry> 

		<cftry>			
			<cfsavecontent variable="local.emailContentHtml">
				<cfoutput>
				<div>
					#local.qryGetReferralData.memFirstName#,<br/><br/>
					Attached to this e-mail is your Case Statement for referral #arguments.event.getValue('mc_siteInfo.siteName')# - #local.qryGetReferralData.clientReferralID#.<br/><br/>
					<b>Notes:</b><br/>
					#local.emailNotes#			
					<cfif val(local.qryGetCaseFeesTotals.amtToBePaidTotal)>
						<br/><br/>Click here to <a href="#arguments.event.getValue('mc_siteInfo.scheme')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#/#arguments.event.getValue('mainurl')#">pay your dues online</a>.
					</cfif>
				</div>
				</cfoutput>
			</cfsavecontent>
			
			<cfscript>
				local.arrEmailTo = [];
				local.sendFrom = replace(local.sendFrom,",",";","all");
				local.toEmailArr = listToArray(local.sendFrom,';');
				for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
					local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
				}
			</cfscript>

			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom')},
				emailto=local.arrEmailTo,
				emailreplyto=arguments.event.getValue('mc_siteInfo.supportProviderEmail'),
				emailsubject="#arguments.event.getValue('mc_siteInfo.siteName')# #local.qryGetReferralData.clientReferralID# - Case Statement",
				emailtitle="#arguments.event.getValue('mc_siteinfo.sitename')# Case Statement",
				emailhtmlcontent=local.emailContentHtml,
				emailAttachments=[{ file=local.returnPDFStruct.fileName, folderpath=local.returnPDFStruct.folderPath }],
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				memberID=local.qryMember.memberID,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="REFNOTIFY"),
				sendingSiteResourceID=this.siteResourceID)>

			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local) />
			</cfcatch>
		</cftry>
	</cffunction>
	
	<cffunction name="doGenerateCaseStatement" access="public" returntype="struct" output="no">
		<cfargument name="Event" type="any" required="true" />
		<cfargument name="refStruct" type="struct" required="false" />

		<cfset var local = structNew() />		
		<cfset local.returnStruct = { referralFilePath='' } />
		<cfset local.objAdminReferrals = CreateObject('component', 'model.admin.referrals.referrals') />
		<cfset local.qryGetReferralData = local.objAdminReferrals.getReferralData(orgID=arguments.event.getValue('mc_siteInfo.orgID'),clientReferralID=arguments.event.getValue('clientReferralID'))  />
		<cfset local.clientReferralID = arguments.event.getValue('clientReferralID') />
		<cfset local.clientName = local.qryGetReferralData.clientName />
		<cfset local.clientFirstLast = local.qryGetReferralData.firstName & " " &  local.qryGetReferralData.lastName />
		<cfset local.qryGetCaseFees = local.objAdminReferrals.getCaseFees(caseID=local.qryGetReferralData.caseID) />
		<cfset local.qryGetCaseFeesTotals = getFeesTotals(qryItems=local.qryGetCaseFees) />
		<cfset local.refTitle = getReferralSettings(arguments.event.getValue('mc_siteinfo.siteid')).title />
		<cfset local.referralStatementInstructions = getReferralSettings(arguments.event.getValue('mc_siteinfo.siteid')).referralStatementInstructions />
		
		<cfset local.thisClientID = local.qryGetReferralData.clientID />
		<cfif val(local.qryGetReferralData.clientParentID)>
			<cfset local.thisClientID = val(local.qryGetReferralData.clientParentID) />
		</cfif>	
		
		<!--- Get Primary Panel Info --->
		<cfset local.qryGetReferralFilterData = local.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID) />
		<cfloop query="local.qryGetReferralFilterData">
			<cfif listFindNoCase("panelid1",local.qryGetReferralFilterData.elementID)>
				<cfset local.qryGetPanelInfo = local.objAdminReferrals.getPanelByID(panelID=local.qryGetReferralFilterData.elementValue) />
				<cfbreak />
			</cfif>
		</cfloop>
		
		<cfset local.panelName = local.qryGetPanelInfo.name />
		<cfset local.referralFeePercent = local.qryGetPanelInfo.referralFeePercent />
		<cfset local.referralAmount = local.qryGetPanelInfo.referralAmount />
		
		<cfif isDefined("arguments.refStruct")>
			<cfset structAppend(local,arguments.refStruct) />
		</cfif>
		
		<cfset local.margintop = "1" />
		
		<cfsavecontent variable="local.ordHeader">
			<cfoutput>
			<style type="text/css">
			.c { text-align:center; }
			.l { text-align:left; }
			.r { text-align:right; }
			.bt { border-top:1px solid ##000; }
			.bb { border-bottom:1px solid ##000; }
			.bl { border-left:1px solid ##000; }
			.br { border-right:1px solid ##000; }
			.ord1 { font-family:verdana;font-size:12pt;line-height:16pt;font-weight:bold;letter-spacing:2pt; }
			.ord2 { font-family:verdana;font-size:8.5pt;line-height:12pt;padding:2px 0; }
			.ord3 { font-family:verdana;font-size:8.5pt;padding-top:8px; }
			.address { font-size:9pt;font-family:verdana;font-weight:bold;line-height:12pt;margin-left:20px; }
			.status { font-size:9pt;font-family:verdana;font-weight:normal;line-height:12pt;margin-left:20px; }
			.logo { height:180px;overflow:hidden; }
			##header { 
				font-family:Verdana, Arial, Helvetica, sans-serif;
				color:##000000; 
				text-align:center;
				width:100%;
				border-bottom:1px solid ##000000;
			}			
			##header h2 { 
				font-size:12pt;
				line-height:10pt;
			}
			##infotbl { margin-top:6px; }			
			</style>
			<div id="header">
				<br /><br />
				<h2>Referral #local.clientReferralID# - Client #local.clientName#</h2>
			</div>				
			</cfoutput>
		</cfsavecontent>
		
		<!--- local.ordBody styles --->
		<cfset local.pageStyle = "width:100%; padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##000000;" />
		<cfset local.sectionTitleStyle = "line-height:1.5em; font-family:Verdana, Arial, Helvetica, sans-serif; color:##000000; font-weight:bold; font-size:10px;" />
		<cfset local.dataHeaderStyle = "font-weight:bold;margin-left:10px;" />
		<cfset local.dataStyle = "margin-left:30px; padding-bottom:10px;" />
		<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##000000;" />	
		<cfset local.tsAppBB =  "border-bottom:1px solid ##000000;" />	
		<cfset local.tsAppBT =  "border-top:1px solid ##000000;" />			
		
		<cfsavecontent variable="local.ordBody">
			<cfoutput>
				<div style="#local.pageStyle#">	
					<h4 style="#local.sectionTitleStyle#" align="center">#local.refTitle#</h4>
					<table cellpadding="4" cellspacing="0" width="100%" border="0">
					<tr> 
						<td width="45%" style="#local.tdStyle#" valign="top">
							<b>Attorney:</b><br/>
							<div style="margin-left:20px;padding-top:7px;">#local.qryGetReferralData.memberName#</div>
						</td> 
						<td width="10%" style="#local.tdStyle#">&nbsp;&nbsp;&nbsp;</td>
						<td width="45%" style="#local.tdStyle#" valign="top">
							<b>Counselor:</b><br/>
							<div style="margin-left:20px;padding-top:7px;">#local.qryGetReferralData.interviewerName#</div>
						</td> 					
					</tr> 
					<tr> 
						<td colspan="3" style="#local.tdStyle#">&nbsp;&nbsp;&nbsp;</td>
					</tr>
					<tr> 
						<td style="#local.tdStyle#" valign="top">
							<b>Client:</b><br/>
							<div style="margin-left:20px;padding-top:7px;">
								#local.qryGetReferralData.firstName# #local.qryGetReferralData.lastName#<br/>
								<cfif len(trim(local.qryGetReferralData.email))>
									#local.qryGetReferralData.email#<br/>
								</cfif>
								Home: #local.qryGetReferralData.homePhone#<br/>
								Language: #local.qryGetReferralData.languageName#
							</div>							
						</td> 
						<td style="#local.tdStyle#">&nbsp;&nbsp;&nbsp;</td>
						<td style="#local.tdStyle#" valign="top">
							<cfif val(local.qryGetReferralData.repID)>
								<b>Representative:</b><br/>
								<div style="margin-left:20px;padding-top:7px;">
									#local.qryGetReferralData.repFirstName# #local.qryGetReferralData.repLastName#<br/>
									<cfif len(trim(local.qryGetReferralData.repEmail))>
										#local.qryGetReferralData.repEmail#<br/>
									</cfif>
									Home:#local.qryGetReferralData.repHomePhone#
								</div>
							</cfif>						
						</td> 					
					</tr>
					<tr> 
						<td colspan="3" style="#local.tdStyle#">&nbsp;&nbsp;&nbsp;</td>
					</tr>
					<tr> 
						<td style="#local.tdStyle#" valign="top">
							<b>Referral Date:</b><br/>
							<div style="margin-left:20px;padding-top:7px;">
								#dateFormat(local.qryGetReferralData.clientReferralDate,"mm/dd/yyyy")#
							</div>							
						</td> 
						<td style="#local.tdStyle#">&nbsp;&nbsp;&nbsp;</td>
						<td style="#local.tdStyle#" valign="top">
							<b>Accepted by Attorney:</b><br/>
							<div style="margin-left:20px;padding-top:7px;">
								#dateFormat(local.qryGetReferralData.dateCaseOpened,"mm/dd/yyyy")#
							</div>					
						</td> 					
					</tr>
					<tr> 
						<td colspan="3" style="#local.tdStyle#">&nbsp;&nbsp;&nbsp;</td>
					</tr>
					<tr> 
						<td style="#local.tdStyle#" valign="top">
							<b>Closed Date:</b><br/>
							<div style="margin-left:20px;padding-top:7px;">
								#dateFormat(local.qryGetReferralData.dateCaseClosed,"mm/dd/yyyy")#
							</div>							
						</td> 
						<td style="#local.tdStyle#">&nbsp;&nbsp;&nbsp;</td>
						<td style="#local.tdStyle#" valign="top">
							<b>Disposition:</b><br/>
							<div style="margin-left:20px;padding-top:7px;">
								#local.qryGetReferralData.statusName#
							</div>					
						</td> 					
					</tr>  										
					</table>
					<br/>
					<h4 style="#local.sectionTitleStyle#" align="center">Fee Summary for Referral #local.clientReferralID# - Client #local.clientName#</h4>
					<br/>
					<table cellpadding="4" cellspacing="0" width="100%" border="0">
					<tr> 
						<th width="45%" style="#local.tdStyle#">&nbsp;&nbsp;&nbsp;</th> 
						<th width="35%" style="#local.tdStyle#">CURRENT</th>
						<th width="20%" style="#local.tdStyle#">YOUR REVISIONS</th> 					
					</tr>
					<tr> 
						<td style="#local.tdStyle#" valign="bottom">
							Total Attorney Fees					
						</td> 
						<td style="#local.tdStyle# padding-right:25px;" valign="bottom" align="right">
							#numberFormat(local.qryGetReferralData.caseFees,"_$_9,999.99")#
						</td>
						<td style="#local.tsAppBB#" valign="bottom"></td> 					
					</tr>
					<tr> 
						<td colspan="3" style="#local.tdStyle#">&nbsp;&nbsp;&nbsp;</td>
					</tr>
					<tr> 
						<td style="#local.tdStyle#" valign="bottom">
							Total Attorney Fees Collected from Client					
						</td> 
						<td style="#local.tdStyle# padding-right:25px;" valign="bottom" align="right">
							#numberFormat(local.qryGetCaseFeesTotals.collectedFeeTotal,"_$_9,999.99")#
						</td>
						<td style="#local.tsAppBB#" valign="bottom"></td> 					
					</tr>
					<tr> 
						<td colspan="3" style="#local.tdStyle#">&nbsp;&nbsp;&nbsp;</td>
					</tr>					
					<tr> 
						<td style="#local.tdStyle#" valign="bottom">
							The Referral Fee for #local.panelName#				
						</td> 
						<td style="#local.tdStyle# padding-right:25px;" valign="bottom" align="right">
							<cfif val(local.referralAmount) or val(local.referralFeePercent)>
								<cfif val(local.referralFeePercent)>
									#local.referralFeePercent#% of Collected Attorney Fees
								<cfelse>
									#numberFormat(local.referralAmount,"_$_9,999.99")# of Collected Attorney Fees
								</cfif>
							</cfif>
						</td>
						<td style="#local.tsAppBB#" valign="bottom"></td> 					
					</tr>
					<tr> 
						<td colspan="3" style="#local.tdStyle#">&nbsp;&nbsp;&nbsp;</td>
					</tr>					
					<tr> 
						<td style="#local.tdStyle#" valign="bottom">
							Total Referral Fees					
						</td> 
						<td style="#local.tdStyle# padding-right:25px;" valign="bottom" align="right">
							#numberFormat(local.qryGetCaseFeesTotals.referralDuesTotal,"_$_9,999.99")#
						</td>
						<td style="#local.tsAppBB#" valign="bottom"></td>	
					</tr>
					<tr> 
						<td colspan="3" style="#local.tdStyle#">&nbsp;&nbsp;&nbsp;</td>
					</tr>					
					<tr> 
						<td style="#local.tdStyle#" valign="bottom">
							Referral Fees Paid  through #dateFormat(now(),"mm/dd/yyyy")#					
						</td> 
						<td style="#local.tdStyle# padding-right:25px;" valign="bottom" align="right">						
							#numberFormat(val(local.qryGetCaseFeesTotals.amtToBePaidTotal)-val(local.qryGetCaseFeesTotals.referralDuesTotal),"-_$_9,999.99")#
						</td>
						<td style="#local.tsAppBB#" valign="bottom"></td>	
					</tr>
					<tr> 
						<td colspan="3" style="#local.tdStyle#">&nbsp;&nbsp;&nbsp;</td>
					</tr>					
					<tr> 
						<td style="#local.tdStyle#" valign="bottom">
							<b>Amount DUE NOW – Please Remit or Pay Online</b>					
						</td> 
						<td style="#local.tdStyle# padding-right:25px;" valign="bottom" align="right">
							#numberFormat(local.qryGetCaseFeesTotals.amtToBePaidTotal,"_$_9,999.99")#
						</td>
						<td style="#local.tsAppBB#" valign="bottom"></td>	
					</tr>
					<tr> 
						<td colspan="3" style="#local.tdStyle#">&nbsp;&nbsp;&nbsp;</td>
					</tr>																								
					</table>
					<br/><br/><br/><br/>
					<p>#local.referralStatementInstructions#</p>						
				</div>										
			</cfoutput>
		</cfsavecontent>				
			
		<!--- create a PDF --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.headercol = { type="header", evalAtPrint=true, txt=local.ordHeader } >
		<cftry>
			<cfdocument filename="#local.strFolder.folderPath#/un_#local.clientReferralID#_referral.pdf" pagetype="letter" margintop="#local.margintop#" marginbottom=".5" marginright=".75" marginleft=".75" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
				<cfdocumentsection margintop="#local.margintop#" marginbottom=".5" marginright=".75" marginleft=".75">
					<cfdocumentitem attributeCollection="#local.headercol#">
						<cfif cfdocument.totalsectionpagecount gt 1>
							<cfoutput>#replace(local.ordHeader,'<!-- cpp -->','Page #cfdocument.currentsectionpagenumber# of #cfdocument.totalsectionpagecount#')#</cfoutput>
						<cfelse>
							<cfoutput>#local.ordHeader#</cfoutput>
						</cfif>
					</cfdocumentitem>
					<cfoutput>#local.ordBody#</cfoutput>
				</cfdocumentsection>
			</cfdocument>		
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local) />
			</cfcatch>
		</cftry>

		<!--- File Name of Referral --->
		<cfset local.fullname = reReplaceNoCase(local.clientFirstLast,'[^a-zA-Z0-9_\-]','','ALL') />
		<cfset local.refFileNameNoExt = "Referral-#local.clientReferralID# #local.clientFirstLast#" />
		<cfset local.refFileNameNoExt = replace(local.refFileNameNoExt,' ','_','ALL') />

		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.clientReferralID#_referral.pdf","#local.strFolder.folderPath#/#local.refFileNameNoExt#.pdf","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l") />
		
		<cfset local.returnStruct.referralFilePath = "#local.strFolder.folderPath#/#local.refFileNameNoExt#.pdf">
		<cfset local.returnStruct.folderPath = local.strFolder.folderPath>	
		<cfset local.returnStruct.fileName = "#local.refFileNameNoExt#.pdf">	
			
		<cfreturn local.returnStruct />
	</cffunction>	
	
	<cffunction name="doEmailProgressReport" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew() />

		<cfscript>
			local.objMemberFieldsets = CreateObject("component","model.system.platform.memberFieldsets");
			local.objAppBaseLink = CreateObject('component', 'model.apploader');
			local.objAdminReferrals = CreateObject('component', 'model.admin.referrals.referrals');
			local.memberID = arguments.event.getValue('memberID');
		    
			local.applicationInstanceID = getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).applicationInstanceID;
			local.siteResourceID = getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).siteResourceID;

			if(len(trim(arguments.event.getValue('_email')))){
				local.emailRecipient = trim(arguments.event.getValue('_email'));
			} else {
				local.emailRecipient = trim(arguments.event.getValue('_emailTypeEmail',''));
			}
			local.emailNotes = arguments.event.getValue('_notes');
			local.appBaseLink = local.objAppBaseLink.getAppBaseLink(applicationInstanceID=local.applicationInstanceID,siteID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID);
			local.memberLocalLink = "/?" & local.appBaseLink;
			local.memberExternalLink = "#arguments.event.getValue('mc_siteInfo.scheme')#://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.memberLocalLink;
			local.objPage = CreateObject("component","model.admin.pages.pages");			
			local.qryPageData = local.objPage.getData(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=local.siteResourceID);
			/* local.resourceName = local.qryPageData.qryResourceData.pageName;	*/
			local.resourceName = "referrals";
		</cfscript>

		<cfset local.qryMember = application.objMember.getMemberInfo(local.memberID) />
		<cfset local.qryMemberAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberid=local.qryMember.memberID)>
		<cfset local.memberEmail = application.objMember.getMainEmail(memberid=local.qryMember.memberID).email />
		
		<cfset local.returnPDFStruct = doGenerateReferralReport(orgID=arguments.event.getValue('mc_siteInfo.orgID'), siteID=arguments.event.getValue('mc_siteInfo.siteID'), memberID=local.memberID)/>
		
		<cfset local.pageStyle = "width:550px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
		<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
		
		<cfset local.panelid1 = "" />
		<cfset local.subpanelid1 = "" />
		<cfset local.radius_dist = "" />
		<cfset local.emailAll = trim(local.emailRecipient) />

		<cftry>
			<cfif find(";",local.emailRecipient)>
				<cfset local.emailAll = replace(local.emailRecipient,";",",","all") />
				<cfset local.sendFrom = trim(listFirst(local.emailRecipient,";")) />
			<cfelseif find(",",local.emailRecipient)>
				<cfset local.sendFrom = trim(listFirst(local.emailRecipient,",")) />
			<cfelse>
				<cfset local.sendFrom = trim(local.emailRecipient) />
			</cfif>
			<cfif NOT len(local.sendFrom) or NOT isValid("regex",local.sendFrom,application.regEx.email)>
				<cfthrow />
			</cfif>
			<cfcatch type="any">
				<cfset local.sendFrom = arguments.event.getValue('mc_siteInfo.supportProviderEmail') />
			</cfcatch>
		</cftry> 

		<cftry>
			<cfsavecontent variable="local.emailContent">
				<cfoutput>
					<table cellpadding="0" cellspacing="0" border="0" bgcolor="##ffffff" width="95%">
					<tr>
						<td width="10">&nbsp;</td>
						<td align="center" valign="top">
							<table cellspacing="0" cellpadding="4" border="0" width="100%" bgcolor="##B2B38F">
							<tr><td width="10">&nbsp;</td>
								<td style="font-size:14px;font-weight:bold;font-family:arial,verdana,helvetica">Progress Report - Referral #arguments.event.getValue('mc_siteInfo.siteName')#</td>
								<td width="10">&nbsp;</td>
							</tr>
							</table>
							<table cellspacing="0" cellpadding="0" border="0" width="100%" bgcolor="##FFFFCC">
							<tr valign=top>
								<td rowspan="2" width="1" bgcolor="##B2B38F"><span style="width:1px;"></span></td>
								<td width="19">&nbsp;</td>
								<td>
									<table cellspacing="0" cellpadding="0" border="0">
									<tr><td height="20"></td></tr>
									<tr>
										<td style="font-size:12px;font-family:arial,verdana,helvetica">
											#local.qryMember.FirstName#,<br/><br/>
											<p>Attached to this e-mail is your Referral Progress Report.</p>
											<p><b>Notes:</b><br/>#local.emailNotes#</p>			
									
											<br/><br/>
										</td>
									</tr>
									</table>
								</td>
								<td width="19">&nbsp;</td>
								<td rowspan="2" width="1" bgcolor="##B2B38F"><span style="width:1px;"></span></td>
							</tr>
							</table>					
							<table cellspacing="0" cellpadding="0" border="0" width="100%" bgcolor="##B2B38F">
							<tr><td height="1"></td></tr>
							</table>
						</td>
					</tr>
					</table>
				</cfoutput>
			</cfsavecontent>

			<cfscript>
				local.arrEmailTo = [];
				local.sendFrom = replace(local.sendFrom,",",";","all");
				local.toEmailArr = listToArray(local.sendFrom,';');
				for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
					local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
				}
			</cfscript>

			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom')},
				emailto=local.arrEmailTo,
				emailreplyto=arguments.event.getValue('mc_siteInfo.supportProviderEmail'),
				emailsubject="#arguments.event.getValue('mc_siteInfo.siteName')# Progress Report",
				emailtitle="#arguments.event.getValue('mc_siteinfo.sitename')# Progress Report Notification",
				emailhtmlcontent=local.emailContent,
				emailAttachments=[{ file=local.returnPDFStruct.fileName, folderpath=local.returnPDFStruct.folderPath }],
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				memberID=local.qryMember.memberID,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="REFNOTIFY"),
				sendingSiteResourceID=local.siteResourceID)>

		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local) />
		</cfcatch>
		</cftry>
		
	</cffunction>
		
	<cffunction name="doGenerateReferralReport" access="public" returntype="struct" output="no">
		<cfargument name="orgID" type="numeric" required="true" />
		<cfargument name="siteID" type="numeric" required="true" />
		<cfargument name="memberID" type="numeric" required="true" />
		<cfargument name="type" type="string" required="true" default="PDF" />
		<cfscript>
		var local = structNew();	
		local.returnStruct = { referralFileURL='', referralFilePath='' };
		local.objAdminReferrals = createObject('component', 'model.admin.referrals.referrals');
		local.qryReferralSettings = getReferralSettings(siteID=arguments.siteID);
		local.referralID = local.qryReferralSettings.referralID;
		local.refTitle = local.qryReferralSettings.title;
		local.monthlyreportTopTxt=local.qryReferralSettings.monthlyReportTopTxt;
		local.monthlyReportBottomTxt=local.qryReferralSettings.monthlyReportBottomTxt;
		local.qryGetReferralData = getReferralReportData(referralID=local.referralID, memberID=arguments.memberID, orgID=arguments.orgID);
		local.qryMember = application.objMember.getMemberInfo(arguments.memberID);
		local.memberNumber = local.qryMember.memberNumber;
		local.memberName = iif(len(trim(local.qryMember.prefix)),de("#local.qryMember.prefix# "),de("")) 
								& local.qryMember.firstName & " " 
								& local.qryMember.lastname 
								& iif(len(trim(local.qryMember.suffix)),de(" #local.qryMember.suffix#"),de(""));

		/* local.reportContent styles */
		local.pageStyle = "width:1000px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;";
		local.footerStyle = "width:1000px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666; padding-top:500px;";
		local.headerDateStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:7pt;color:##666;font-style: italic; margin-top:-10px;";
		local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;";
		local.sectionTitleStyle = "line-height:1.5em; font-family:Verdana, Arial, Helvetica, sans-serif; color:##000000; font-weight:bold; font-size:10px;";
		local.dataHeaderStyle = "font-weight:bold;margin-left:10px;";
		local.dataStyle = "margin-left:30px; padding-bottom:10px;";
		local.tsAppBB =  "border-bottom:1px solid ##000000;";	
		local.tsAppBT =  "border-top:1px solid ##000000;";									
		local.margintop = "1";
		local.paraStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##666;";
		local.table = "page-break-inside:auto;";
		local.tr = "page-break-inside:avoid; page-break-after:auto;";	
		</cfscript>
			
		<cfsavecontent variable="local.reportHeaderCSS">
			<cfoutput>
			<style type="text/css">
			.c { text-align:center; }
			.l { text-align:left; }
			.r { text-align:right; }
			.bt { border-top:1px solid ##000; }
			.bb { border-bottom:1px solid ##000; }
			.bl { border-left:1px solid ##000; }
			.br { border-right:1px solid ##000; }
			.ord1 { font-family:verdana;font-size:12pt;line-height:16pt;font-weight:bold;letter-spacing:2pt; }
			.ord2 { font-family:verdana;font-size:8.5pt;line-height:12pt;padding:2px 0; }
			.ord3 { font-family:verdana;font-size:8.5pt;padding-top:8px; }
			.address { font-size:9pt;font-family:verdana;font-weight:bold;line-height:12pt;margin-left:20px; }
			.status { font-size:9pt;font-family:verdana;font-weight:normal;line-height:12pt;margin-left:20px; }
			.logo { height:180px;overflow:hidden; }
			##header { 
				font-family:Verdana, Arial, Helvetica, sans-serif;
				color:##000000; 
				text-align:center;
				width:100%;
				border-bottom:1px solid ##000000;
			}			
			##header h2 { 
				font-size:12pt;
				line-height:10pt;
			}
			##infotbl { margin-top:6px; }			
			</style>
			</cfoutput>
		</cfsavecontent>
		<cfsavecontent variable="local.reportHeader">
			<cfoutput>
			<div id="header">
				<cfif arguments.type NEQ 'HTML'><br /></cfif><br />
				<h2>#local.refTitle#<br/><br/>Referral Report - #local.memberName#</h2>
			</div>				
			</cfoutput>
		</cfsavecontent>			

		<cfsavecontent variable="local.reportContent">
			<cfoutput>
			<cfif arguments.type NEQ 'HTML'>
			<html>
			<head>
				<title>Referral Inactivity Notification</title>
			</head>
			<body>
			</cfif>					
			<div style="#local.pageStyle#">
				<cfif arguments.type NEQ 'HTML'>
				<div align="right" style="#local.headerDateStyle#">Data as of #dateFormat(now(),"mm/dd/yyyy")#</div>
				<cfelse>
				<table width="100%" cellpadding="10" cellspacing="0" border="0">
				<tr><td><div align="right" style="#local.headerDateStyle#">Data as of #dateFormat(now(),"mm/dd/yyyy")#</div></td></tr>
				</table>
				</cfif>
				<p style="#local.paraStyle#">#local.monthlyreportTopTxt#</p>
				<table width="100%" cellpadding="10" cellspacing="0" border="1" style="#local.table#">
				<tr>
					<th style="#local.tdStyle#">Ref.##</th>
					<th style="#local.tdStyle#">Ref.Date</th>
					<th style="#local.tdStyle#">Client</th>
					<th style="#local.tdStyle#">Ref Panel</th>
					<th style="#local.tdStyle#">Amount<br/>Enclosed</th>						
					<th style="#local.tdStyle#">Final Pay?<br/>Yes or No</th>
					<th style="#local.tdStyle#">Paid to Date</th>
					<th style="#local.tdStyle#">Atty Fee Rec'd<br/>to Date</th>
					<th style="#local.tdStyle#">Status</th>	
					<th style="#local.tdStyle#">Disposition</th>
				</tr>
				<cfloop query="local.qryGetReferralData">	
					<cfset local.thisClientID = val(local.qryGetReferralData.clientID) />
					<cfif val(local.qryGetReferralData.clientParentID)>
						<cfset local.thisClientID = val(local.qryGetReferralData.clientParentID)/>
					</cfif>
					<cfset local.qryGetReferralFilterData = local.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID) />
					<cfset local.panelid1 = "" />				
					<cfloop query="local.qryGetReferralFilterData">
						<cfif listFindNoCase("panelid1,panelid2,panelid3,subpanelid1,subpanelid2,subpanelid3",local.qryGetReferralFilterData.elementID)>
							<cfswitch expression="#local.qryGetReferralFilterData.elementID#">
								<cfcase value="panelid1">
									<cfset local.qryGetPanelInfo = local.objAdminReferrals.getPanelByID(panelID=local.qryGetReferralFilterData.elementValue) />
									<cfset local.panelid1 = local.qryGetPanelInfo.name />
								</cfcase>
							</cfswitch>
						</cfif>	
					</cfloop>							
					<tr valign="top" style="#local.tr#">
						<td style="#local.tdStyle#">#local.qryGetReferralData.clientReferralID#</td>
						<td style="#local.tdStyle#">#dateFormat(local.qryGetReferralData.dateCreated,"m/d/yyyy")#</td>
						<td style="#local.tdStyle#">#local.qryGetReferralData.clientName#</td>
						<td style="#local.tdStyle#">#local.panelid1#</td>
						<td style="#local.tdStyle#">&nbsp;</td>
						<td style="#local.tdStyle#">&nbsp;</td>
						<td style="#local.tdStyle#">#dollarFormat(local.qryGetReferralData.PaidLRIStoDate)#</td>
						<td style="#local.tdStyle#">&nbsp;</td>
						<td style="#local.tdStyle#">#local.qryGetReferralData.statusName#</td>
						<td style="#local.tdStyle#">&nbsp;</td>
					</tr>
				</cfloop>								 													
				</table>
				<br/><br/>			
				<p style="#local.paraStyle#">#local.monthlyReportBottomTxt#</p>
			</div>
			<div style="clear:both"></div>
			<cfif arguments.type NEQ 'HTML'>		
			</body>
			</html>
			</cfif>	
			</cfoutput>
		</cfsavecontent>		
			
		<cfif arguments.type EQ 'HTML'>
			<cfsavecontent variable="local.reportHTML">
				<cfoutput>
					<html>
						<head>
							<title>Referral Inactivity Notification</title>
							#local.reportHeaderCSS#
							<style>
								@page {
									size: landscape;
								}
								thead.report-header {
									display: table-header-group;
								}
								thead.report-header th{text-align:left;}
								table.report-container {
									page-break-after: always;
								}
								table { page-break-inside:auto }
								tr    { page-break-inside:avoid; page-break-after:auto }
							</style>
						</head>
						<body>
							<table border="0">
								<thead class="report-header">
									<tr><th>#local.reportHeader#</th></tr>
								</thead>
								<tbody class="report-container">
									<tr><td>#local.reportContent#</td></tr>
								</tbody>
							</table>
						</body>
					</html>
				</cfoutput>
			</cfsavecontent>
			<cfset local.returnStruct.htmlcontent = local.reportHTML>
		<cfelse>
			<!--- create a PDF --->
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix='ref')>
			<cfset local.headercol = { type="header", evalAtPrint=true, reportHeader=local.reportHeader } >
			<cfset local.footercol = { type="footer", evalAtPrint=true, pageStyle=local.pageStyle } >
			<cftry>
				<cfdocument filename="#local.strFolder.folderPath#/un_#local.memberNumber#_referralreport.pdf" pagetype="letter" margintop="#local.margintop#" marginbottom=".5" marginright=".75" marginleft=".75" format="PDF" backgroundvisible="Yes" orientation="landscape" unit="in" fontembed="Yes" scale="100">
					<cfdocumentitem attributeCollection="#local.headercol#">
						<cfoutput>#local.reportHeader#</cfoutput>
					</cfdocumentitem>
					<cfoutput>#local.reportContent#</cfoutput>
					<cfdocumentitem attributeCollection="#local.footercol#">
						<cfoutput>
						<html>
						<head>
						</head>
						<body>
							<div align="center" style="#local.pageStyle#">Page #cfdocument.currentpagenumber# of #cfdocument.totalpagecount#</div>
						</body>
						</html>
						</cfoutput>
					</cfdocumentitem>						
				</cfdocument>		
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local) />
				</cfcatch>
			</cftry>

			<!--- File Name of Referral --->
			<cfset local.fullname = reReplaceNoCase(local.memberName,'[^a-zA-Z0-9_\-]','','ALL') />
			<cfset local.refFileNameNoExt = "ReferralReport-#local.memberNumber# #local.memberName#" />
			<cfset local.refFileNameNoExt = replace(local.refFileNameNoExt,' ','_','ALL') />

			<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.memberNumber#_referralreport.pdf","#local.strFolder.folderPath#/#local.refFileNameNoExt#.pdf","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l") />

			<cfset local.returnStruct.referralFilePath = "#local.strFolder.folderPath#/#local.refFileNameNoExt#.pdf">
			<cfset local.returnStruct.folderPath = local.strFolder.folderPath>	
			<cfset local.returnStruct.fileName = "#local.refFileNameNoExt#.pdf">
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getReferralReportData" output="false" returntype="query" hint="returns referral data">
		<cfargument name="referralID" type="numeric" required="true" />
		<cfargument name="memberID" type="numeric" required="true" />`
		<cfargument name="orgID" type="numeric" required="true" />

		<cfset var local = structNew() />
		
		<cfquery name="local.qryReferraldata" datasource="#application.dsn.membercentral.dsn#" result="local.qryReferraldataResult">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpRefData') IS NOT NULL
				DROP TABLE ##tmpRefData;
			IF OBJECT_ID('tempdb..##tmpRefFeeTrans') IS NOT NULL 
				DROP TABLE ##tmpRefFeeTrans; 
			IF OBJECT_ID('tempdb..##tmpClientsForFee') IS NOT NULL 
				DROP TABLE ##tmpClientsForFee; 
			IF OBJECT_ID('tempdb..##tmpClientsForFeeResult') IS NOT NULL 
				DROP TABLE ##tmpClientsForFeeResult;
			CREATE TABLE ##tmpRefFeeTrans (caseID int, amtPaid decimal(18,2));
			CREATE TABLE ##tmpClientsForFee (traItemID int PRIMARY KEY);
			CREATE TABLE ##tmpClientsForFeeResult (traItemID int, transactionID int);

			DECLARE @orgID int, @referralID int, @memberID int;
			DECLARE @legacyTrans table (rowID int identity (1,1), caseID int, importedCollectedFee decimal(18,2));

			set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
			set @referralID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.referralID#">;
			set @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;

			select isCase = case when rc.caseID is not null then 1 else 0 end,	
				m.memberID as mID,
				c.clientID,	c.referralID, 
				c.firstName, c.middleName, c.lastName,
				c.lastName + ', ' + c.firstName as clientName,
				c.businessName,	
				c.address1, c.address2,	c.address3,
				c.city,	c.state, c.postalCode, c.countryID,
				c.email,c.homePhone, c.cellPhone, c.alternatePhone,
				c.typeID as clientTypeId,
				crs.statusName, crs.isReferred, crs.isAgency, crs.isPending,
				c.dateCreated as clentDateCreated,	c.createdBy, c.dateLastUpdated as clientDateLastUpdated, 
				c.clientParentID,
				cr.clientReferralID, cr.memberID as crMemberId, cr.enteredByMemberID as crEnteredByMemberID,
				cr.sourceID,
				cr.communicateLanguageID, cr.issueDesc, 
				isNull(cr.sendSurvey, 0) as sendSurvey, isNull(cr.sendNewsBlog,0) as sendNewsBlog, 
				cr.statusID, cr.typeID, 
				CONVERT(varchar, cr.clientReferralDate, 101) as clientReferralDate,
				cr.clientReferralDate as clientReferralDateTime,  cr.dateCreated, cr.lastUpdatedBy, cr.dateLastUpdated,
				rep.clientID as repID,	rep.firstName as repFirstName, rep.lastName as repLastName,
				rep.lastName + ', ' + rep.firstName as repName,
				rep.address1 as repAddress1, rep.address2 as repAddress2, rep.address3 as repAddress3,
				rep.city as repCity, rep.state as repState, rep.postalCode as repPostalCode, rep.countryID as repCountryID,
				rep.email as repEmail, rep.homePhone as repHomePhone, rep.cellPhone as repCellPhone, rep.alternatePhone as repAlternatePhone,
				rep.typeID as repTypeID, rep.relationToClient, rep.clientParentID as repParentID,
				m.memberID, m.firstName as memFirstName, m.middleName as memMiddleName, m.lastName as memLastname, m.memberNumber,
				m.prefix, m.suffix,
				(m.firstName  + 
				case
					when m.middlename is not null and len(m.middlename) > 0  then
						' ' + left(m.middleName, 1) + ' '
					else  
						' '
				end  + m.lastName) as memberName,
				datediff(day,rc.dateLastUpdated,getDate()) as daysElapsed, rc.caseID, rc.enteredByMemberID, rc.notesTxt as caseNotesTxt,
				rc.caseFees, rc.dateCaseOpened, rc.dateCaseClosed,
				rc.dateCreated as dateCaseCreated, rc.dateLastUpdated as dateCaseLastUpdated
			INTO ##tmpRefData 
			FROM dbo.ref_clients c
			INNER JOIN dbo.ref_clientTypes ct on ct.clientTypeID = c.typeID
				and ct.clientType = 'Client'
			INNER JOIN dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
			INNER JOIN dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID
				AND crs.isReferred = 1
				AND crs.isOpen = 1
			LEFT OUTER JOIN dbo.ref_clients rep on rep.referralID = @referralID and rep.clientID = cr.representativeID					
			INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberid = cr.memberID
			INNER JOIN dbo.ams_members m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID
				and m2.memberID = @memberID
			LEFT OUTER JOIN dbo.ref_cases rc on rc.referralID = @referralID and rc.clientReferralID = cr.clientReferralID
			WHERE c.referralID = @referralID;

			INSERT INTO @legacyTrans (caseID, importedCollectedFee)
			SELECT cf.caseID, cf.importedCollectedFee
			FROM ##tmpRefData AS tmp
			INNER JOIN dbo.ref_collectedFees AS cf ON cf.caseID = tmp.caseID
			LEFT OUTER JOIN dbo.tr_applications AS tra on tra.orgID = @orgID
				AND cf.collectedFeeID = tra.itemID
				AND tra.itemType = 'referralfee'
				AND tra.status = 'A'
			WHERE cf.referralID = @referralID
			AND tra.itemID is null;

			-- ref fees
			INSERT INTO ##tmpClientsForFee (traItemID)
			SELECT DISTINCT cf.collectedFeeID
			FROM ##tmpRefData AS tmp
			INNER JOIN dbo.ref_collectedFees AS cf ON cf.referralID = @referralID 
				AND cf.caseID = tmp.caseID;

			EXEC dbo.ref_clientReferralTransactionsByBulk @orgID=@orgID, @feeType='referralFee';

			INSERT INTO ##tmpRefFeeTrans (caseID, amtPaid)
			select caseID, sum(amtPaid)
			from (
				select cf.caseID, sum(ts.cache_activePaymentAllocatedAmount) as amtPaid
				from ##tmpClientsForFeeResult as fees
				inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fees.transactionid
				inner join dbo.ref_collectedFees as cf on cf.referralID = @referralID and cf.collectedFeeID = fees.traItemID
				group by cf.caseID
					union
				select lt.caseID, sum(lt.importedCollectedFee) as amtPaid
				from @legacyTrans lt
				group by lt.caseID
			) tmp
			group by caseID;

			SELECT r.*, fee.amtPaid AS PaidLRIStoDate
			FROM ##tmpRefData AS r
			LEFT OUTER JOIN ##tmpRefFeeTrans as fee ON fee.caseID = r.caseID
			ORDER BY CASE WHEN r.caseID IS NOT NULL THEN 1 ELSE 0 END, r.clientReferralID;

			IF OBJECT_ID('tempdb..##tmpRefData') IS NOT NULL
				DROP TABLE ##tmpRefData;
			IF OBJECT_ID('tempdb..##tmpRefFeeTrans') IS NOT NULL 
				DROP TABLE ##tmpRefFeeTrans; 
			IF OBJECT_ID('tempdb..##tmpClientsForFee') IS NOT NULL 
				DROP TABLE ##tmpClientsForFee; 
			IF OBJECT_ID('tempdb..##tmpClientsForFeeResult') IS NOT NULL 
				DROP TABLE ##tmpClientsForFeeResult;
				
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryReferraldata>
	</cffunction>							

	<cffunction name="createAppInstance" access="public" output="true" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="baseLink" type="string">
		<cfargument name="appInfo" type="query">

		<cfscript>
			var local = structNew();		
			variables.isCommunityReady = FALSE;
			variables.isMultiInstanceReady = FALSE;			
			arguments.event.paramValue('appTypeID','0');
			local.objAppCreation = CreateObject("component","model.admin.pages.appCreationProcess");
			local.appInfo = arguments.appInfo;
			contructAppInstanceForm(arguments.event,local.appInfo);			
		</cfscript>

		<cfif cgi.request_method eq "POST" AND NOT arguments.event.getValue('error.formErrors')>		
			<cftry>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.createApp">
					SET NOCOUNT ON;

					DECLARE @siteID	int, @languageID int, @sectionID int, @isVisible bit, @pageName varchar(50), @pageTitle varchar(200),
						@pagedesc varchar(400), @zoneID int, @pageTemplateID int, @pageModeID int, @pgResourceTypeID int, 
						@allowReturnAfterLogin bit, @applicationInstanceName varchar(100), @applicationInstanceDesc varchar(200),
						@applicationInstanceID int, @siteResourceID int, @pageID int, @GLAccountID int, @fedefaultGLAccountID int;
					SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
					SET @languageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('lid')#">;
					SET @isVisible = 1;
					SET @pageName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageName')#">;
					SET @pageTitle = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageTitle')#">;
					SET @pagedesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageDesc')#">;
					SET @zoneID = dbo.fn_getZoneID('Main');
					SET @pageTemplateID = NULL;
					SET @pageModeID = <cfif arguments.event.getValue('pageModeID','0') EQ 0>NULL<cfelse><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('pageModeID')#"></cfif>;
					SET @allowReturnAfterLogin = 1;
					SET @applicationInstanceName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('appInstanceName')#">;
					SET @applicationInstanceDesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('appInstanceDesc')#">;
					SET @sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('sectionID')#">;
				    SET @pgResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedPage');
					SET @GLAccountID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('GLAccountID')#">;
					SET @fedefaultGLAccountID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('feGLAccountID')#">;

					EXEC dbo.cms_createApplicationInstanceReferral @siteid=@siteid, @languageID=@languageID, @sectionID=@sectionID, 
						@isVisible=@isVisible, @pageName=@pageName, @pageTitle=@pageTitle, @pagedesc=@pagedesc, @zoneID=@zoneID,
						@pageTemplateID=@pageTemplateID, @pageModeID=@pageModeID, @pgResourceTypeID=@pgResourceTypeID,
						@defaultGLAccountID=@GLAccountID, @fedefaultGLAccountID=@fedefaultGLAccountID, 
						@allowReturnAfterLogin=@allowReturnAfterLogin, @applicationInstanceName=@applicationInstanceName,
						@applicationInstanceDesc=@applicationInstanceDesc, @applicationInstanceID=@applicationInstanceID OUTPUT,
						@siteResourceID=@siteResourceID OUTPUT, @pageID=@pageID OUTPUT;

					select @applicationInstanceID as applicationInstanceID, @siteResourceID as siteResourceID, @pageID as pageID;
				</cfquery>				

				<cfset application.objSiteInfo.triggerClusterWideReload()>
				<cfoutput>
					<script language="javascript">
						top.reloadPageTable();
						top.MCModalUtils.hideModal();
					</script>
				</cfoutput>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cflocation url="#arguments.baseLink#&msg=2" addtoken="false">
			</cfcatch>
			</cftry>	
		<cfelse>
			<cfoutput>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.getMode">
					select dbo.fn_getmodeId('Full') as pageModeID
				</cfquery>
				<cfset arguments.event.setValue('pageModeID',local.getMode.pageModeID)>
				<cfset showAppInstanceForm(arguments.event,local.appInfo)>
			</cfoutput>
		</cfif>		
	</cffunction>	
	
	<cffunction name="showAppInstanceForm" access="public" output="true" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="appInfo" type="query">

		<cfset var local = structNew()>

		<cfset local.appInfo = arguments.appInfo>
		<cfset local.allow = FALSE>
		<cfif local.appInfo.instancesCreated EQ 0>
			<cfset local.allow = TRUE>
		<cfelse>
			<cfif variables.isMultiInstanceReady AND (local.appInfo.maxInstancesPerSite - local.appInfo.instancesCreated) GT 0>
				<cfset local.allow = TRUE>
			</cfif>
		</cfif>

		<cfif local.allow>
			<cfscript>				
			local.objAppCreation = CreateObject("component","model.admin.pages.appCreationProcess");
			local.objSection = CreateObject("component","model.system.platform.section");
			local.objPageAdmin = CreateObject("component","model.admin.pages.pageAdmin");
			local.getSections = local.objSection.getRecursiveSections(siteID=arguments.event.getValue('mc_siteInfo.siteID'));
			local.qryModes = local.objPageAdmin.getAvailableModes();
			local.qryLanguages = local.objPageAdmin.getAvailableLanguages();
			if( local.appInfo.recordCount ) variables.allowPageNameChange = local.appInfo.allowPageNameChange;
            // Prepare GL Account widget data for Default Revenue Account
			if (val(arguments.event.getValue('GLAccountID',0)) gt 0) {
				local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=arguments.event.getValue('GLAccountID'), orgID=arguments.event.getValue('mc_siteInfo.orgID'));
				local.GLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
			} else {
				local.GLAccountPath = "";
			}
			local.strDefaultGLAcctWidgetData = { label="Default Revenue Account", btnTxt="Choose GL Account", glatid=3, widgetMode='GLSelector',
				idFldName="GLAccountID", idFldValue=val(arguments.event.getValue('GLAccountID',0)), pathFldValue=local.GLAccountPath, pathNoneTxt="(no account selected)" };
			local.strDefaultGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strDefaultGLAcctWidgetData);

			// Prepare GL Account widget data for Default Front End Revenue Account
			if (val(arguments.event.getValue('feGLAccountID',0)) gt 0) {
				local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=arguments.event.getValue('feGLAccountID'), orgID=arguments.event.getValue('mc_siteInfo.orgID'));
				local.feGLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
			} else {
				local.feGLAccountPath = "";
			}
			local.strFEGLAcctWidgetData = { label="Default Front End Revenue Account", btnTxt="Choose GL Account", glatid=3, widgetMode='GLSelector',
				idFldName="feGLAccountID", idFldValue=val(arguments.event.getValue('feGLAccountID',0)), pathFldValue=local.feGLAccountPath, pathNoneTxt="(no account selected)" };
			local.strFEGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strFEGLAcctWidgetData);
			</cfscript>
			
			<cfsavecontent variable="local.js">
				<cfoutput>
                <cfif structKeyExists(local, "strDefaultGLAcctWidget") AND structKeyExists(local, "strFEGLAcctWidget")>
					#local.strDefaultGLAcctWidget.js#
    				#local.strFEGLAcctWidget.js#
                </cfif>
				<script language="javascript">
					function setDuplicateMessage(boxEl, messageEl, iconEl, success, message){
						iconEl.toggleClass('fa-circle-check', success).toggleClass('fa-circle-exclamation', !success);
						messageEl.html(message);
						boxEl.toggleClass('text-green', success).toggleClass('text-danger', !success).removeClass('d-none');
					}
					function doesPageExist(pageName) {
						var boxEl = $('##pageBox');
						var messageEl = $('##pageText');
						var iconEl = $('##pageImg');
						var re = /[^a-zA-Z0-9\-_]/;
						mca_hideAlert('err_createapp');

						var existsResult = function(r) {
							if (r.success && r.success.toLowerCase() == 'true'){
								setDuplicateMessage(boxEl, messageEl, iconEl, !r.pageexists, r.pageexists ? 'Page Name already used!' : 'Passed!');
							} else {
								boxEl.addClass('d-none');
								mca_showAlert('err_createapp', 'We were unable to check whether this page name exists.');
							}
						};

						if (pageName.length > 0) {
							if(re.test(pageName)){
								setDuplicateMessage(boxEl, messageEl, iconEl, false, 'Only letters, numbers, underscores, and dashses are allowed');
							}
							else {
								checkPageExists(pageName,existsResult);
							}
						}
						else {
							boxEl.addClass('d-none');
							return false;
						}
					}
					function checkPageExists(pageName,callback) {
						var objParams = { pageID:#val(arguments.event.getValue('pageID',0))#, pageName:pageName };
						TS_AJX('PAGE','pageExists',objParams,callback,callback,10000,callback);
					}
					function validatePageForm() {
						toggleFinishButton(false);
						mca_hideAlert('err_createapp');
						var thisForm = document.forms["frmCreateApp"];	
						var arrPromises = [];					
						var arrReq = new Array();

						if($('##pageName').length && $.trim($('##pageName').val()).length == 0 && $('##pageName').is(':visible')) {
							arrReq[arrReq.length]	= 'Enter a valid name for this page.';
						}
						if($('##pageTitle').length && $.trim($('##pageTitle').val()).length == 0) {
							arrReq[arrReq.length]	= 'Enter a page title.';
						}
						if($('##pageDesc').length && $.trim($('##pageDesc').val()).length == 0) {
							arrReq[arrReq.length]	= 'Enter a page description.';
						}
						if($('##appInstanceName').length && $.trim($('##appInstanceName').val()).length == 0) {
							arrReq[arrReq.length]	= 'Enter a name for this #local.appInfo.applicationTypeDesc#.';
						}
						if($('##appInstanceDesc').length && $.trim($('##appInstanceDesc').val()).length == 0) {
							arrReq[arrReq.length]	= 'Enter a description for this #local.appInfo.applicationTypeDesc#.';
						}

						var GLAccountRegEx = new RegExp("[0-9]*\.?[0-9]*[1-9]", "i");

						if(($('##GLAccountID').length && $.trim($('##GLAccountID').val()).length == 0) || $('##GLAccountID').val() == 0 || (!(GLAccountRegEx.test($.trim($('##GLAccountID').val()))))) {
							arrReq[arrReq.length]	= 'Select a Default GL Account';
						}
						if(($('##feGLAccountID').length && $.trim($('##feGLAccountID').val()).length == 0) || $('##feGLAccountID').val() == 0 || (!(GLAccountRegEx.test($.trim($('##feGLAccountID').val()))))) {
							arrReq[arrReq.length]	= 'Select a Default Front End GL Account';
						}
						if (arrReq.length > 0) {							
							mca_showAlert('err_createapp', arrReq.join('<br/>'), true);
							toggleFinishButton(true);
							return false;
						}
						<cfif variables.allowPageNameChange>
							arrPromises.push(
								new Promise(function(resolve, reject) {
									var checkPageNameResult = function(r) {
										if (r.success && r.success.toLowerCase() == 'true'){
											if(r.pageexists == true) {
												setDuplicateMessage($('##pageBox'), $('##pageText'), $('##pageImg'), false, 'Page Name already used!');
												mca_showAlert('err_createapp', 'Page Name already used.', true);
												toggleFinishButton(true);
												reject();
											}
											else resolve();
										} else {
											mca_showAlert('err_createapp', 'We were unable to check whether this page name exists.');
											toggleFinishButton(true);
											reject();
										}
									};
									if($.trim($('##pageName').val()).length) {
										checkPageExists($.trim($('##pageName').val()),checkPageNameResult);
									}
									else resolve();
								})
							);	
						</cfif>						
						Promise.all(arrPromises).then(function(){
							thisForm.submit();
						}).catch((error) => {
							return false;
						});			
						return false;
					}					
				</script>			
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">
			
			<cfoutput>
			<div class="mt-4">
				<div id="err_createapp" class="alert alert-danger mb-4 mt-2 d-none"></div>

				<cfform action="/?#cgi.QUERY_STRING#" method="POST" name="frmCreateApp" id="frmCreateApp" onsubmit="return validatePageForm();">
					<cfinput type="hidden" name="lid"  id="lid" value="#arguments.event.getValue('lid')#">
					<cfinput type="hidden" name="pageTemplateID"  id="pageTemplateID" value="#arguments.event.getValue('pageTemplateID')#">
					<cfinput type="hidden" name="allowReturnAfterLogin"  id="allowReturnAfterLogin" value="#arguments.event.getValue('allowReturnAfterLogin')#">

					<cfif len(trim(arguments.event.getValue('error.errorMessage')))>
						<div class="alert alert-danger mb-2">
							Correct the following errors:<br/>
							<cfloop list="#arguments.event.getValue('error.errorMessage')#" delimiters="|" index="local.currentMessage">
								- #local.currentMessage#<br />
							</cfloop>
						</div>
					</cfif>	

					<div>
						<div class="form-group">
							<div class="form-label-group">
								<cfif variables.allowPageNameChange>
									<input type="text" name="pageName"  id="pageName" class="form-control" value="#arguments.event.getValue('pageName')#" onblur="doesPageExist(this.value);" maxlength="50">
								<cfelse>
									<input type="text" name="pageName" id="pageName" value="#local.appInfo.suggestedPageName#" class="form-control" readOnly>
								</cfif>						
								<label for="pageName">Page Name*:</label>
							</div>
							<cfif variables.allowPageNameChange>
								<div id="pageBox" class="form-text small mb-2 d-none">
									<i class="fa-solid" id="pageImg"></i> <span id="pageText"></span>
								</div>
							</cfif>	
						</div>
						

						<div id="showSections">
							<div class="form-group ">
								<div class="form-label-group">
									<select name="sectionID" id="sectionID" class="custom-select">
										<cfloop query="local.getSections">
											<option value="#local.getSections.sectionID#"<cfif arguments.event.getValue('sectionID') EQ local.getSections.sectionID> SELECTED</cfif>>#local.getSections.thePathExpanded#</option>
										</cfloop>
									</select>
									<label for="sectionID">Section:</label>
								</div>
							</div>							
						</div>

						<div class="form-group mb-4" >
							<div class="form-label-group">
								<select name="pageModeID" id="pageModeID" class="custom-select">
									<option value="0">No Override</option>
									<cfloop query="local.qryModes">
										<option value="#local.qryModes.modeID#"<cfif arguments.event.getValue('pageModeID') EQ local.qryModes.modeID> SELECTED</cfif>>#local.qryModes.modeName#</option>
									</cfloop>
								</select>
								<label for="pageModeID" >Mode Override:</label>
							</div>
						</div>

						<div class="form-group">
							<div class="form-label-group">
								<input type="text" class="form-control" name="pageTitle"  id="pageTitle" value="#arguments.event.getValue('pageTitle')#">
								<label for="pageTitle">Page Title*:</label>
							</div>
						</div>
				
						<div class="form-group mb-4" >
							<div class="form-label-group">
								<textarea name="pageDesc" id="pageDesc" class="form-control" cols="60" rows="4" maxlength="400">#arguments.event.getValue('pageDesc')#</textarea>
								<label for="pageDesc">Page Description*:</label>
							</div>
						</div>
				
						<div class="form-group">
							<div class="form-label-group">
								<input type="text" class="form-control" name="appInstanceName"  id="appInstanceName"  value="#arguments.event.getValue('appInstanceName')#">
								<label for="appInstanceName">#local.appInfo.applicationTypeDesc# Name*:</label>
							</div>
						</div>
						
						<div class="form-group mb-4">
							<div class="form-label-group">
								<textarea name="appInstanceDesc" id="appInstanceDesc"  class="form-control" cols="60" rows="4" maxlength="400">#arguments.event.getValue('appInstanceDesc')#</textarea>
								<label for="appInstanceDesc">#local.appInfo.applicationTypeDesc# Description*:</label>
							</div>
						</div>

						<div class="form-group">
							#local.strDefaultGLAcctWidget.html#
						</div>

						<div class="form-group">
							#local.strFEGLAcctWidget.html#
						</div>

						<div class="form-group text-right">
							<button type="submit" name="btnSaveReferralAppDetails" id="btnSaveReferralAppDetails" class="btn btn-sm btn-primary d-none">Save Information</button>
						</div>
					</div>
				</cfform>	
			</div>			
			</cfoutput>
		<cfelse>
			<cfoutput>
			<div class="alert alert-warning">
				<h4>Unable to add #local.appInfo.applicationTypeName#</h4>
				<p>You may not add this application to your website at this time.</p>
			</div>	
			</cfoutput>
		</cfif>
	</cffunction>		

	<cffunction name="getPanels" access="public" output="false" returntype="query">
		<cfargument name="referralID" type="numeric" required="true">
		<cfargument name="statusName" type="string" required="false">
		<cfargument name="feDspClientReferral" type="string" required="false">
		
		<cfset var qryGetPanels = "">
		
		<cfquery name="qryGetPanels" datasource="#application.dsn.membercentral.dsn#">
			select p.panelID, p.referralID, p.name
			from dbo.ref_panels p
			INNER JOIN dbo.ref_panelStatus ps on ps.panelStatusID = p.statusID
				and ps.referralID = <cfqueryparam value="#arguments.referralID#" cfsqltype="cf_sql_integer">
			where p.referralID = <cfqueryparam value="#arguments.referralID#" cfsqltype="cf_sql_integer">
			and p.panelParentID IS NULL
			and p.isActive = 1
			and p.allowPanelMgmt = 1
			<cfif structKeyExists(arguments,"statusName")>
				and ps.statusName = <cfqueryparam value="#trim(arguments.statusName)#" cfsqltype="cf_sql_varchar">
			</cfif>
			<cfif structKeyExists(arguments,"feDspClientReferral") AND val(arguments.feDspClientReferral)>
				and p.feDspClientReferral = 1
			</cfif>
			order by p.name		
		</cfquery>

		<cfreturn qryGetPanels>
	</cffunction>
	
	<cffunction name="getSiteSubPanels" access="public" output="false" returntype="query">
		<cfargument name="referralID" type="numeric" required="true" />
		<cfargument name="statusName" type="string" required="false" />
		
		<cfset var local = structNew() />		
		
		<cfquery name="local.qryGetSubPanels" datasource="#application.dsn.membercentral.dsn#">
			select 
				p.panelID,
				p.name,
				p.panelParentID
			from 
				ref_panels p
				INNER JOIN ref_panelStatus ps on
					ps.panelStatusID = p.statusID
					and ps.referralID = <cfqueryparam value="#arguments.referralID#" cfsqltype="cf_sql_integer"  />	
			where
				p.referralID = <cfqueryparam value="#arguments.referralID#" cfsqltype="cf_sql_integer"  />	
				and p.panelParentID IS NOT NULL
				and p.isActive = 1
				<cfif structKeyExists(arguments,"statusName")>
					and ps.statusName = <cfqueryparam value="#trim(arguments.statusName)#" cfsqltype="cf_sql_varchar" />
				</cfif>
			order by
				p.name		
		</cfquery>
		<cfreturn local.qryGetSubPanels />
	</cffunction>
	
	<cffunction name="getMemberPanels" access="public" output="false" returntype="query">
		<cfargument name="memberID" type="numeric" required="true" />
		<cfargument name="isInActive" type="string" required="false" />
		
		<cfset var local = structNew() />		
		
		<cfquery name="local.qryGetMemberPanels" datasource="#application.dsn.membercentral.dsn#">
			select 
				pm.panelMemberID, pm.panelID, pm.memberID, pm.applicationDocumentID, pm.applicationDate, pm.applicationStatusID,
				pm.dateApplicationStatus, pm.statusID, pm.dateStatus, pms.statusName as memberStatusName, 
				p.uid, p.referralID, p.name, p.shortDesc, p.longDesc, p.statusID, p.internalNotes, p.dateCreated, p.dateCommitteeApproved,
				p.dateBoardApproved, p.dateBoardNotified, p.dateReviewed, p.sendMail, p.maxNumMembers, p.referralFeePercent,
				p.deductExpenseDesc, p.referralAmount, p.panelParentID, p.GLAccountID, p.isActive, ps.statusName				
			from
				ref_panelMembers pm
				INNER JOIN ref_panels p on
					p.panelID = pm.panelID
					and p.isActive = 1
				INNER JOIN ref_panelStatus ps on
					ps.panelStatusID = p.statusID	
				INNER JOIN ref_panelMemberStatus pms on
					pms.panelMemberStatusID = pm.statusID 					
					<cfif isDefined("arguments.isInActive")>
						and pms.isInActive = <cfqueryparam value="#val(arguments.isInActive)#" cfsqltype="cf_sql_bit" />
					<cfelse>
						and pms.isActive = 1	
					</cfif>
					
				INNER JOIN ams_members m on
					m.memberid = pm.memberID
				INNER JOIN ams_members m2 on
					m2.memberid = m.activeMemberID
					and m2.memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="cf_sql_integer" />
					and p.panelParentID is null
					and p.allowPanelMgmt = 1
			order by
				p.name		
		</cfquery>
		<cfreturn local.qryGetMemberPanels />
	</cffunction>
	
	<cffunction name="getSubPanels" access="public" output="false" returntype="query">
		<cfargument name="panelID" type="numeric" required="true" />
		<cfargument name="isActive" type="numeric" required="false" />
		<cfargument name="feDspClientReferral" type="numeric" required="false" />


		<cfset var local = structNew() />		
		
		<cfquery name="local.qryGetSubPanels" datasource="#application.dsn.membercentral.dsn#">
			select 
				p.panelID,
				p.name
			from
				ref_panels p
				INNER JOIN ref_panelStatus ps on
					ps.panelStatusID = p.statusID
			where p.panelParentID = <cfqueryparam value="#arguments.panelID#" cfsqltype="cf_sql_integer" />
					and p.isActive = 1
					<cfif isDefined("arguments.isActive") and arguments.isActive>
						and ps.statusName = 'Active'
					</cfif>	
					<cfif structKeyExists(arguments,"feDspClientReferral") AND val(arguments.feDspClientReferral)>
						and p.feDspClientReferral = 1
					</cfif>
			order by
				p.name
		</cfquery>
		<cfreturn local.qryGetSubPanels />
	</cffunction>
	
	<cffunction name="getMemberSubPanels" access="public" output="false" returntype="query">
		<cfargument name="panelID" type="numeric" required="true" />
		<cfargument name="memberID" type="numeric" required="true" />
		
		<cfset var local = structNew() />		
		
		<cfquery name="local.qryGetMemberSubPanels" datasource="#application.dsn.membercentral.dsn#">
			select 
				pm.panelMemberID,
				pm.panelID,
				pm.memberID,
				p.name
			from
				ref_panelMembers pm
				INNER JOIN ref_panels p on
					p.panelID = pm.panelID
					and p.panelParentID = <cfqueryparam value="#arguments.panelID#" cfsqltype="cf_sql_integer" />
					and p.isActive = 1
				INNER JOIN ref_panelStatus ps on
					ps.panelStatusID = p.statusID
				INNER JOIN ref_panelMemberStatus pms on
					pms.panelMemberStatusID = pm.statusID
				INNER JOIN ams_members m on
					m.memberid = pm.memberID
				INNER JOIN ams_members m2 on
					m2.memberid = m.activeMemberID
					and m2.memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="cf_sql_integer" />
			order by
				p.name
		</cfquery>
		<cfreturn local.qryGetMemberSubPanels />
	</cffunction>
	
	<cffunction name="getAllSubPanelsOfMember" access="public" output="false" returntype="query">
		<cfargument name="memberID" type="numeric" required="true" />
		<cfargument name="referralID" type="numeric" required="true" />
		
		<cfset var local = structNew() />		
		
		<cfquery name="local.qryGetAllMemberSubPanels" datasource="#application.dsn.membercentral.dsn#">
			select 
				pm.panelMemberID,
				pm.panelID,
				pm.memberID,
				p.name,
				p.panelParentID
			from
				ref_panelMembers pm
				INNER JOIN ref_panels p on
					p.panelID = pm.panelID
					and p.panelParentID is not null
					and p.isActive = 1
					and p.referralID = <cfqueryparam value="#arguments.referralID#" cfsqltype="cf_sql_integer">	
				INNER JOIN ref_panelStatus ps on
					ps.panelStatusID = p.statusID
				INNER JOIN ref_panelMemberStatus pms on
					pms.panelMemberStatusID = pm.statusID
				INNER JOIN ams_members m on
					m.memberid = pm.memberID
				INNER JOIN ams_members m2 on
					m2.memberid = m.activeMemberID
					and m2.memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="cf_sql_integer" />
				INNER JOIN ref_panels p2 on
					p2.panelID = p.panelParentID
					and p2.isActive = 1
					and p2.allowPanelMgmt = 1
			order by
				p.name
		</cfquery>
		<cfreturn local.qryGetAllMemberSubPanels />
	</cffunction>
	
	<cffunction name="getPanelStatusByName" access="public" output="false" returntype="query">
		<cfargument name="statusName" type="string"  required="true" />
		<cfargument name="referralID" type="numeric" required="true" />
		
		<cfset var local = structNew() />		
		
		<cfquery name="local.qryGetPanelStatus" datasource="#application.dsn.membercentral.dsn#">
		select 
			panelMemberStatusID,
			statusName
		from 	
			dbo.ref_panelMemberStatus
		where
			statusName = <cfqueryparam value="#arguments.statusName#" cfsqltype="cf_sql_varchar"  />	
			and referralID	= <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.referralID#" />										
		</cfquery>
		
		<cfreturn local.qryGetPanelStatus />
	</cffunction>
	
	<cffunction name="saveMemberPanelList" access="public" output="false" returntype="string" >
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew() />
		
		<cfset local.addedPanelList = "">
		<cfset local.removedPanelList = "">
		<cfset local.addedSubPanelList = "">
		<cfset local.removedSubPanelList = "">
		<cfset local.siteID = arguments.event.getValue('mc_siteinfo.siteid')>
		<cfset local.qryReferralSettings = getReferralSettings(local.siteID)>
		<cfset local.referralID = local.qryReferralSettings.referralID>
		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcUser.memberData.IdentifiedAsMemberID gt 0>
			<cfset local.memberID = session.cfcUser.memberData.IdentifiedAsMemberID>
		<cfelse>
			<cfset local.memberID = session.cfcuser.memberdata.memberid>
		</cfif>
		<cfset local.memberName = session.cfcuser.memberdata.firstName & ' ' & session.cfcuser.memberdata.lastName>
		<cfset local.memberNumber = session.cfcuser.memberdata.memberNumber>
		<cfset local.qryGetPanels = getPanels(referralID=local.referralID, statusName="Active")>
		<cfset local.qryGetSiteSubPanels = getSiteSubPanels(referralID=local.referralID, statusName="Active")>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(arguments.event.getValue('mc_siteinfo.siteCode')).mainhostname>
			<cfset local.thisScheme = application.objSiteInfo.getSiteInfo(arguments.event.getValue('mc_siteinfo.siteCode')).scheme>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			<cfset local.thisScheme = application.objPlatform.isRequestSecure() ? 'https' : 'http'>
		</cfif>		

		<cfset local.memberPanelList = "">
		<cfloop collection="#arguments.event.getCollection()#" item="local.thisEl">
			<cfif left(local.thisEl,8) eq "panelID_">
				<cfset local.thisPanelID= getToken(local.thisEl,2,"_")>
				<cfset local.memberPanelList = listAppend(local.memberPanelList, local.thisPanelID)>
			</cfif>
		</cfloop>
		
		<cfif Listlen(local.memberPanelList) gt local.qryReferralSettings.maxNumberOfPanels>
			<cfset local.success = false>
			<cfreturn local.success>
		</cfif>
		
		<cfset local.qryMemberPanels = getMemberPanels(local.memberID)>
		
		<cfquery name="local.qryRemovedPanels" dbtype="query">
			select panelID from [local].qryMemberPanels
			where panelID not in (0#local.memberPanelList#)
		</cfquery>
		<cfset local.removedPanelList = valueList(local.qryRemovedPanels.panelID)>
		
		<cfloop list="#local.memberPanelList#" index="local.thisEl">
			<cfif not Listfind(valueList(local.qryMemberPanels.panelID), local.thisEl)>
				<cfset local.addedPanelList = listAppend(local.addedPanelList, local.thisEl)>
			</cfif>
		</cfloop>
		
		<cfset local.memberSubPanelList = "">
		<cfloop collection="#arguments.event.getCollection()#" item="local.thisEl">
			<cfif Listlen(local.thisEl,"_") eq 4 and ListGetAt(local.thisEl,1,"_") eq "subPanelID" and ListGetAt(local.thisEl,3,"_") eq "panelID"
					and ListFind(local.memberPanelList,ListGetAt(local.thisEl,4,"_"))>
				<cfset local.thisSubPanelID= getToken(local.thisEl,2,"_")>
				<cfset local.memberSubPanelList = listAppend(local.memberSubPanelList, local.thisSubPanelID)>
			</cfif>
		</cfloop>
		
		<cfset local.qryMemberAllSubPanels = getAllSubPanelsOfMember(local.memberID, local.referralID)>
		
		<cfquery name="local.qryRemovedSubPanels" dbtype="query">
			select * from [local].qryMemberAllSubPanels
			where panelID not in (0#local.memberSubPanelList#)
		</cfquery>
		<cfset local.removedSubPanelList = valueList(local.qryRemovedSubPanels.panelID)>
		
		<cfloop list="#local.memberSubPanelList#" index="local.thisEl">
			<cfif not Listfind(valueList(local.qryMemberAllSubPanels.panelID), local.thisEl)>
				<cfset local.addedSubPanelList = listAppend(local.addedSubPanelList, local.thisEl)>
			</cfif>
		</cfloop>
		
		<cfset local.removedPanel = structNew()>
		<cfloop query="local.qryRemovedSubPanels">
			<cfif not structKeyExists(local.removedPanel,"#local.qryRemovedSubPanels.panelParentID#")>
				<cfset local.removedPanel["#local.qryRemovedSubPanels.panelParentID#"] = arrayNew(1)>
			</cfif>
			<cfset local.subPanelStruct = structNew()>
			<cfset local.subPanelStruct.panelID = local.qryRemovedSubPanels.panelID>
			<cfset local.subPanelStruct.name = local.qryRemovedSubPanels.name>
			<cfset arrayAppend(local.removedPanel["#local.qryRemovedSubPanels.panelParentID#"],local.subPanelStruct)>
		</cfloop>
		
		<cfquery name="local.qryAddedSubPanels" dbtype="query">
			select * from [local].qryGetSiteSubPanels
			where panelID in (0#local.addedSubPanelList#)
		</cfquery>

		<cfset local.addedPanel = structNew()>
		<cfloop query="local.qryAddedSubPanels">
			<cfif not structKeyExists(local.addedPanel,"#local.qryAddedSubPanels.panelParentID#")>
				<cfset local.addedPanel["#local.qryAddedSubPanels.panelParentID#"] = arrayNew(1)>
			</cfif>
			<cfset local.subPanelStruct = structNew()>
			<cfset local.subPanelStruct.panelID = local.qryAddedSubPanels.panelID>
			<cfset local.subPanelStruct.name = local.qryAddedSubPanels.name>
			<cfset arrayAppend(local.addedPanel["#local.qryAddedSubPanels.panelParentID#"],local.subPanelStruct)>
		</cfloop>
		
		<cfquery name="local.qryRemovedMemberPanels" dbtype="query">
			select * from [local].qryGetPanels
			where panelID in (0#local.removedPanelList#)
				or panelID in (0#valueList(local.qryRemovedSubPanels.panelParentID)#)
		</cfquery>
		
		<cfquery name="local.qryAddedMemberPanels" dbtype="query">
			select * from [local].qryGetPanels
			where panelID in (0#local.addedPanelList#)
				or panelID in (0#valueList(local.qryAddedSubPanels.panelParentID)#)
		</cfquery>
		
		<cfset local.removedPanelMemberStatusID = getPanelStatusByName(statusName="Removed",referralID=local.referralID).panelMemberStatusID />
		<cfset local.activePanelMemberStatusID = getPanelStatusByName(statusName="Active",referralID=local.referralID).panelMemberStatusID />

		<cftry>
		
			<cfquery name="local.qryUpdatePanelList" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					declare @memberID int, @removedPanelMemberStatusID int, @activePanelMemberStatusID int, @applicationStatusID int;
					set @memberID = <cfqueryparam value="#local.memberID#" cfsqltype="cf_sql_integer">;
					set @removedPanelMemberStatusID = <cfqueryparam value="#local.removedPanelMemberStatusID#" cfsqltype="cf_sql_integer">;
					
					<cfif local.qryReferralSettings.panelMemberStatusID neq ''>
						set @activePanelMemberStatusID = <cfqueryparam value="#local.qryReferralSettings.panelMemberStatusID#" cfsqltype="cf_sql_integer">;
					<cfelse>
						set @activePanelMemberStatusID = <cfqueryparam value="#local.activePanelMemberStatusID#" cfsqltype="cf_sql_integer">;
					</cfif>
					<cfif local.qryReferralSettings.panelMemberApplicationStatusID neq ''>
						set @applicationStatusID = <cfqueryparam value="#local.qryReferralSettings.panelMemberApplicationStatusID#" cfsqltype="cf_sql_integer">;
					<cfelse>
						set @applicationStatusID = NULL;
					</cfif>

					BEGIN TRAN;
					
						-- remove panels
						update 
							dbo.ref_panelMembers 
						set 
							statusID = @removedPanelMemberStatusID
						where
							memberID = @memberID 
							and panelID in (0#local.removedPanelList#);
							
						-- update any removed/inactive panel to active panel if it exists in added panel list
						update 
							dbo.ref_panelMembers 
						set 
							statusID = @activePanelMemberStatusID,
							applicationStatusID = @applicationStatusID
						where
							memberID = @memberID
							and panelID in (0#local.addedPanelList#);
						
						-- insert new panels
						<cfloop list="#local.addedPanelList#" index="local.thisEl">
							if not exists(select panelMemberID from dbo.ref_panelMembers where memberID = @memberID 
									and panelID = <cfqueryparam value="#local.thisEl#" cfsqltype="cf_sql_integer">)
							 begin
							 
								insert into dbo.ref_panelMembers(
									panelID,
									memberID,
									applicationStatusID,
									statusID,
									createdBy,
									dateCreated
								)
								values (
									<cfqueryparam value="#local.thisEl#" cfsqltype="cf_sql_integer"  />,
									@memberID,
									@applicationStatusID,
									@activePanelMemberStatusID,
									@memberID,
									getdate()
								)
							
							 end
						</cfloop>
						
						-- remove subpanels
						delete from dbo.ref_panelMembers
						where memberID = @memberID 
							and panelID in (0#local.removedSubPanelList#);
						
						-- insert subpanels
						<cfloop list="#local.addedSubPanelList#" index="local.thisEl">
							if not exists(select panelMemberID from dbo.ref_panelMembers where memberID = @memberID 
									and panelID = <cfqueryparam value="#local.thisEl#" cfsqltype="cf_sql_integer">)
							 begin
							 
								insert into dbo.ref_panelMembers(
									panelID,
									memberID,
									applicationStatusID,
									statusID,
									createdBy,
									dateCreated
								)
								values (
									<cfqueryparam value="#local.thisEl#" cfsqltype="cf_sql_integer"  />,
									@memberID,
									@applicationStatusID,
									@activePanelMemberStatusID,
									@memberID,
									getdate()
								)
							
							 end
						</cfloop>
					
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			
			<cfset local.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<!--- Log Panel/Sub-panel changes --->
		<cfset local.arrHistoryMessages = ArrayNew(1)>
		<cfif local.qryAddedMemberPanels.RecordCount>
			<cfloop query="local.qryAddedMemberPanels">
				<cfset ArrayAppend(local.arrHistoryMessages, "Panel #local.qryAddedMemberPanels.name# was added")>									
				<cfif structKeyExists(local.addedPanel,"#local.qryAddedMemberPanels.panelID#") and arrayLen(local.addedPanel["#local.qryAddedMemberPanels.panelID#"])>
					<cfloop array="#local.addedPanel["#local.qryAddedMemberPanels.panelID#"]#" index="local.thisEl">
						<cfset ArrayAppend(local.arrHistoryMessages, "Sub-Panel #local.thisEl.name# was added")>
					</cfloop>
				</cfif>
			</cfloop>
		</cfif>

		<cfif local.qryRemovedMemberPanels.RecordCount>
			<cfloop query="local.qryRemovedMemberPanels">
				<cfset ArrayAppend(local.arrHistoryMessages, "Panel #local.qryRemovedMemberPanels.name# was removed")>
				<cfif structKeyExists(local.removedPanel,"#local.qryRemovedMemberPanels.panelID#") and arrayLen(local.removedPanel["#local.qryRemovedMemberPanels.panelID#"])>
					<cfloop array="#local.removedPanel["#local.qryRemovedMemberPanels.panelID#"]#" index="local.thisEl">
						<cfset ArrayAppend(local.arrHistoryMessages, "Sub-Panel #local.thisEl.name# was removed")>
					</cfloop>
				</cfif>
			</cfloop>
		</cfif>

		<cfif arrayLen(local.arrHistoryMessages)>
			<cfset createObject('component','model.system.platform.history').addPanelMemberUpdateHistory(orgID=arguments.event.getValue('mc_siteInfo.orgid'),
				actorMemberID=local.memberID, receiverMemberID=local.memberID, mainMessage="Member Panel Information Updated", messages=local.arrHistoryMessages)>
		</cfif>

		<cfset local.pageStyle = "width:1000px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;" />
		
		<!--- if success true && any changes made --->
		<cfif local.success and (local.qryAddedMemberPanels.recordcount or local.qryRemovedMemberPanels.recordcount)>
			<cfsavecontent variable="local.referralSaveChangeContent">
				<cfoutput>
					<div style="#local.pageStyle#">

						<div style="font-size:10pt;font-weight:bold; margin-bottom:15px;">
							Member Name: <a href="#local.thisScheme#://#local.thisHostname#/?pg=admin&jumpToTool=MemberAdmin%7Csearch%7Cedit&memberID=#local.memberID#">#local.memberName# (#local.memberNumber#)</a><br>
						</div>

						<div style="border-bottom:1px solid black;font-weight:bold; margin-bottom:15px;">Added Panel/SubPanel</div>
						
						<cfif local.qryAddedMemberPanels.RecordCount>		
							<cfloop query="local.qryAddedMemberPanels">
								<div style="width:100%;">
									#local.qryAddedMemberPanels.currentRow#. - #local.qryAddedMemberPanels.name#
								</div>										
								<cfif structKeyExists(local.addedPanel,"#local.qryAddedMemberPanels.panelID#") and arrayLen(local.addedPanel["#local.qryAddedMemberPanels.panelID#"])>
									<ul style="clear:both;padding:0 40px;">
										<cfloop array="#local.addedPanel["#local.qryAddedMemberPanels.panelID#"]#" index="local.thisEl">
											<li>#local.thisEl.name#</li>
										</cfloop>
									</ul>
								</cfif>
							</cfloop>
						<cfelse>
							<div style="width:100%;">No Panel/SubPanel added.</div>
						</cfif>
						
						<div style="clear:both !important;height:20px;"></div>	
						<div style="border-bottom:1px solid black;font-weight:bold; margin-bottom:15px; margin-top:15px;">Removed Panel/SubPanel</div>	
						<cfif local.qryRemovedMemberPanels.RecordCount>
							<cfloop query="local.qryRemovedMemberPanels">
								<div style="width:100%;">
									#local.qryRemovedMemberPanels.currentRow#. - #local.qryRemovedMemberPanels.name#
								</div>
								<cfif structKeyExists(local.removedPanel,"#local.qryRemovedMemberPanels.panelID#") and arrayLen(local.removedPanel["#local.qryRemovedMemberPanels.panelID#"])>
									<ul style="clear:both;padding:0 40px;">
										<cfloop array="#local.removedPanel["#local.qryRemovedMemberPanels.panelID#"]#" index="local.thisEl">
											<li>#local.thisEl.name#</li>
										</cfloop>
									</ul>
								</cfif>
							</cfloop>
						<cfelse>
							<div style="width:100%;">No Panel/SubPanel removed.</div>
						</cfif>
						<div style="clear:both;"></div>
					</div>
				</cfoutput>
			</cfsavecontent>

			<cfset local.emailtitle = "#arguments.event.getValue('mc_siteInfo.siteName')# Referral Panels Notification"/>

			<cfscript>
				local.arrEmailTo = [];
				local.emailRecipient = replace(local.qryReferralSettings.emailRecipient,",",";","all");
				local.toEmailArr = listToArray(local.emailRecipient,';');
				for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
					local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
				}
			</cfscript>

			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom')},
				emailto=local.arrEmailTo,
				emailreplyto=arguments.event.getValue('mc_siteInfo.supportProviderEmail'),
				emailsubject=local.emailtitle,
				emailtitle=local.emailtitle,
				emailhtmlcontent=local.referralSaveChangeContent,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				memberID=arguments.event.getValue('mc_siteinfo.sysmemberid'),
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="REFNOTIFY"),
				sendingSiteResourceID=this.siteResourceID)>
		</cfif>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="getClientReferralCustomFieldWithData" access="public" output="false" returnType="array" hint="I get client referral custom fields with selected value">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="arrResourceFields" type="array" required="true">
		<cfargument name="objCustomFields" type="any" required="true">
		
		<cfset var local = structNew()>
		<cfset local.thisResourceFieldsArr = arrayNew(1)>

		<cfif arguments.itemID GT 0>
			<cfset local.qryFieldData = arguments.objCustomFields.getResponses(itemType=arguments.itemType, itemID=arguments.itemID)>
		</cfif>
		
		<cfloop array="#arguments.arrResourceFields#" index="local.thisfield">
			<cfset local.tmpFieldsStr = structNew()>
			<cfset local.tmpFieldsStr['fieldID'] = local.thisfield.xmlattributes.fieldID>
			<cfset local.tmpFieldsStr['itemID'] = arguments.itemID>
			<cfset local.tmpFieldsStr['attributes'] = local.thisfield.xmlattributes>
			<cfset local.tmpFieldsStr['attributes']['fieldText'] = local.tmpFieldsStr['attributes']['fieldText']>
			<cfset local.tmpFieldsStr['attributes']['fieldReference'] = local.tmpFieldsStr['attributes']['fieldReference']>
			<cfset local.tmpFieldsStr['dataTypeCode'] = local.thisfield.xmlattributes.dataTypeCode>
			<cfset local.tmpFieldsStr['displayTypeCode'] = local.thisfield.xmlattributes.displayTypeCode>
			<cfset local.tmpFieldsStr['fieldTypeCode'] = local.thisfield.xmlattributes.fieldTypeCode>
			<cfset local.tmpFieldsStr['supportAmt'] = val(local.thisfield.xmlattributes.supportAmt)>
			<cfset local.tmpFieldsStr['supportQty'] = val(local.thisfield.xmlattributes.supportQty)>
			<cfset local.tmpFieldsStr['isRequired'] = val(local.thisfield.xmlattributes.isRequired)>
			<cfset local.tmpFieldsStr['requiredMsg'] = local.thisfield.xmlattributes.requiredMsg>
			<cfset local.tmpFieldsStr['children'] = arrayNew(1)>
			<cfset local.tmpFieldsStr['allOptionEmptyOrDisabled'] = 0>
			<cfset local.tmpFieldsStr['value'] = "">

			<cfif arguments.itemID GT 0 AND local.qryFieldData.recordCount>
				<cfset var thisFieldID = local.tmpFieldsStr.fieldID>
				<cfset local.qryFieldValues = QueryFilter(local.qryFieldData,function(thisRow) { return arguments.thisRow.fieldID EQ thisFieldID; })>
				<cfif local.qryFieldValues.recordCount>
					<cfif listFind("SELECT,RADIO,CHECKBOX",local.tmpFieldsStr.displayTypeCode)>
						<cfset local.tmpFieldsStr['value'] = valueList(local.qryFieldValues.customValue)>
					<cfelse>
						<cfset local.tmpFieldsStr['value'] = local.qryFieldValues.customValue>
						<cfif local.thisfield.xmlattributes.displayTypeCode is 'TEXTBOX' and local.thisfield.xmlattributes.supportQty is 1>
							<cfset local.tmpFieldsStr['value'] = val(local.tmpFieldsStr['value'])>
						</cfif>
					</cfif>
				</cfif>
			</cfif>

			<cfif local.thisfield.xmlattributes.displayTypeCode eq 'TEXTBOX' and local.thisfield.xmlattributes.supportQty is 1>
				<cfset local.maxQtyAllowed = 99999>
				<cfif local.thisfield.xmlattributes.fieldInventory gt 0>
					<cfif local.thisfield.xmlattributes.fieldInventory lte local.thisfield.xmlattributes.fieldinventoryCount>
						<cfset local.maxQtyAllowed = 0>
						<cfset local.tmpFieldsStr['isRequired'] = 0>
					<cfelse>
						<cfset local.maxQtyAllowed = local.thisfield.xmlattributes.fieldInventory-local.thisfield.xmlattributes.fieldinventoryCount>
						<!--- edit case of non monetary qty-field --->
						<cfif local.thisfield.xmlattributes.supportAmt is 0 and val(local.tmpFieldsStr['value']) gt 0>
							<cfset local.maxQtyAllowed = local.maxQtyAllowed + val(local.tmpFieldsStr['value'])>
						</cfif>
					</cfif>
				</cfif>
				<cfset local.tmpFieldsStr['maxQtyAllowed'] = local.maxQtyAllowed>
			</cfif>

			<!--- dont show question at all if select,radio,checkbox and no options defined --->
			<cfif listFind("SELECT,RADIO,CHECKBOX",local.tmpFieldsStr.displayTypeCode)>
				<cfif arrayLen(local.thisfield.xmlchildren) is 0>
					<cfset local.tmpFieldsStr.allOptionEmptyOrDisabled = 1>
				<cfelse>
					<cfloop array="#local.thisfield.xmlchildren#" index="local.thisoption">
						<cfset local.tmpOptionStr = structNew()>
						<cfset local.tmpOptionStr['attributes'] = local.thisoption.xmlattributes>
						<cfset local.tmpOptionStr['attributes']['fieldValue'] = local.tmpOptionStr['attributes']['fieldValue']>
						<cfset local.tmpOptionStr['unavailable'] = 0>
						
						<!--- skip unavailability check for an already selected option --->
						<cfif len(local.tmpFieldsStr['value']) and listFind(local.tmpFieldsStr['value'],local.thisoption.xmlattributes.valueID)>
							
						<cfelseif local.thisoption.xmlattributes.optionInventory gt 0 and local.thisoption.xmlattributes.optionInventory lte local.thisoption.xmlattributes.optioninventoryCount>
							<cfset local.tmpOptionStr.unavailable = 1>
						</cfif>
						<cfset arrayAppend(local.tmpFieldsStr.children, local.tmpOptionStr)>
					</cfloop>

					<cfif local.tmpFieldsStr.displayTypeCode eq 'CHECKBOX' and len(local.tmpFieldsStr['value'])>
						<cfset local.tmpFieldsStr['value'] = listToArray(local.tmpFieldsStr['value'])>
					</cfif>
				</cfif>
			<!--- append json insanity vars for other display type values --->
			<cfelseif len(local.tmpFieldsStr['value'])>
				<cfset local.tmpFieldsStr['value'] = local.tmpFieldsStr['value']>
			</cfif>
	
			<cfset arrayAppend(local.thisResourceFieldsArr, local.tmpFieldsStr)>
		</cfloop>
		
		<cfreturn local.thisResourceFieldsArr>
	</cffunction> 

	<cffunction name="getReferralFeeStructureType" access="public" output="false" returntype="query">
		<cfargument name="referralID" type="numeric" required="true" />
		
		<cfset var local = structNew() />		
		
		<cfquery name="local.qryGetReferralFeeStructureType" datasource="#application.dsn.membercentral.dsn#">
			SELECT rfst.feeStructureTypeID, rfst.feeStructureTypeName, rfst.feeStructureTypeDesc FROM ref_feeStructureTypes as rfst
			INNER JOIN ref_referrals r on r.feeStructureTypeID = rfst.feeStructureTypeID
			AND referralID	= <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.referralID#" />										
		</cfquery>
		<cfreturn local.qryGetReferralFeeStructureType>
	</cffunction>

	<cffunction name="getFeeStructureTypes" access="public" output="false" returntype="query">
		<cfargument name="typename" type="string" required="false" default="" />
		<cfset var local = structNew() />		
		
		<cfquery name="local.qryGetFeeStructureTypes" datasource="#application.dsn.membercentral.dsn#">
			SELECT feeStructureTypeID, feeStructureTypeName, feeStructureTypeDesc FROM ref_feeStructureTypes
			<cfif len(arguments.typename)>
			WHERE feeStructureTypeName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.typename#" />			
			</cfif>
			ORDER BY feeStructureTypeID								
		</cfquery>
		<cfreturn local.qryGetFeeStructureTypes>
	</cffunction>

	<cffunction name="feeDiscrepancyStatusUpdate" access="public" output="false" returntype="struct">
		<cfargument name="clientReferralID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.reponseStruct.success = true>
		<cfset local.reponseStruct.clientReferralID = arguments.clientReferralID>

		<cftry>
			<cfstoredproc procedure="ref_feeDiscrepancyStatusUpdate" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.clientReferralID#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.itemCount">
			</cfstoredproc>

			<cfcatch type="any">
				<cfset local.reponseStruct.success = false>
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
		</cftry>

		<cfreturn local.reponseStruct>
	</cffunction>

	<cffunction name="addRef_cf_nonOption" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="customText" type="string" required="true">
		<cfargument name="itemIDSQLVar" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfif len(arguments.customText)>
			<cfsavecontent variable="local.addClientCustomFieldSQL">
				<cfoutput>
				set @fieldID = #arguments.fieldID#;
				set @detail = '#replace(arguments.customText,"'","''","ALL")#';
				set @dataID = null;

				EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=#arguments.itemIDSQLVar#, @itemType='#arguments.itemType#', 
						@valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.addClientCustomFieldSQL = "">
		</cfif>

		<cfreturn local.addClientCustomFieldSQL>
	</cffunction>

	<cffunction name="editRef_cf_nonOption" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="customText" type="string" required="true">
		<cfargument name="itemIDSQLVar" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryGetDataID" datasource="#application.dsn.membercentral.dsn#">
			select dataID 
			from dbo.cf_fieldData 
			where fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
			and itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
			and itemType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.itemType#">
		</cfquery>

		<cfif NOT local.qryGetDataID.recordcount>
			<cfset local.editClientCustomFieldSQL = addRef_cf_nonOption(itemType=arguments.itemType, fieldID=arguments.fieldID, customText=arguments.customText, itemIDSQLVar=arguments.itemIDSQLVar)>
		<cfelse>
			<cfsavecontent variable="local.editClientCustomFieldSQL">
				<cfoutput>
				set @dataID = #local.qryGetDataID.dataID#;
					
				<cfif len(arguments.customText)>
					set @detail = '#replace(arguments.customText,"'","''","ALL")#'
					set @fieldID = #arguments.fieldID#;
					set @valueID = null;

					EXEC dbo.cf_createFieldValue @fieldID=@fieldID, @fieldValue=@detail, @amount=0, @inventory=null,
						@enteredByMemberID=NULL, @skipAuditLog=1, @valueID=@valueID OUTPUT;

					UPDATE dbo.cf_fieldData
					SET valueID = @valueID
					WHERE dataID = @dataID;
				<cfelse>
					DELETE FROM dbo.cf_fieldData WHERE dataID = @dataID;
				</cfif>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.editClientCustomFieldSQL>
	</cffunction>

	<cffunction name="addRef_cf_option" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="valueIDList" type="string" required="true">
		<cfargument name="itemIDSQLVar" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.addClientCustomFieldSQL">
			<cfoutput>
			<cfloop list="#arguments.valueIDList#" index="local.valueitem">
				<cfif val(local.valueitem) gt 0>
					set @fieldID = #arguments.fieldID#;
					set @valueID = #val(local.valueitem)#;
					set @dataID = null;
					
					EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=#arguments.itemIDSQLVar#, @itemType='#arguments.itemType#', 
							@valueID=@valueID, @fieldValue=NULL, @dataID=@dataID OUTPUT;
				</cfif>
			</cfloop>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.addClientCustomFieldSQL>
	</cffunction>
	
	<cffunction name="editRef_cf_option" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="valueIDList" type="string" required="true">
		<cfargument name="itemIDSQLVar" type="string" required="true">

		<cfset var local = structNew()>

		<!--- existing options --->
		<cfquery name="local.qryExistingOptions" datasource="#application.dsn.membercentral.dsn#">
			select dataID, valueID
			from dbo.cf_fieldData 
			where fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
			and itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">
			and itemType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.itemType#">
		</cfquery>

		<!--- get any options we need to remove --->
		<cfquery name="local.qryOptionsToRemove" dbtype="query">
			select dataID, valueID
			from [local].qryExistingOptions
			<cfif listLen(arguments.valueIDList)>
				where valueID NOT IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.valueIDList#" list="true">)
			</cfif>
		</cfquery>
		
		<!--- get any options we need to add --->
		<cfif listLen(arguments.valueIDList)>
			<cfquery name="local.qryOptionsToAdd" datasource="#application.dsn.membercentral.dsn#">
				select valueID
				from dbo.cf_fieldValues
				where fieldID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldID#">
				and valueID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.valueIDList#" list="true">)
				<cfif local.qryExistingOptions.recordcount>
					and valueID NOT IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#valueList(local.qryExistingOptions.valueID)#" list="true">)
				</cfif>
			</cfquery>
			<cfset local.optionsToAdd = valueList(local.qryOptionsToAdd.valueID)>
		<cfelse>
			<cfset local.optionsToAdd = "">
		</cfif>

		<cfsavecontent variable="local.editClientCustomFieldSQL">
			<cfoutput>
			<!--- remove options we dont want --->
			<cfloop query="local.qryOptionsToRemove">
				set @dataID = #val(local.qryOptionsToRemove.dataID)#;
				DELETE FROM dbo.cf_fieldData WHERE dataID = @dataID;
			</cfloop>

			<!--- add new options. pass in the new options only --->
			<cfif len(local.optionsToAdd)>
				<cfset local.tempSQL = addRef_cf_option(itemType=arguments.itemType, fieldID=arguments.fieldID, valueIDList=local.optionsToAdd, itemIDSQLVar=arguments.itemIDSQLVar)>
				#local.tempSQL#
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.editClientCustomFieldSQL>
	</cffunction>

	<cffunction name="showRefError" access="public" output="false" returntype="struct">
		<cfscript>
			var local = structNew();
			savecontent variable="local.data" { writeOutput('					
				<div class="alert alert-warning alert-dismissible fade show" role="alert">
					A problem occured during the process. Please contact administrator.
				</div>
			'); }	
			return returnAppStruct(local.data,"echo");
		</cfscript>
	</cffunction>
	
	<cffunction name="getMemberRFPGridData" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="refDateRange" type="numeric" required="false" default="0" />
		<cfargument name="filterReferralID" type="string" required="false" default="" />
		<cfargument name="filterClientLastName" type="string" required="false" default="" />
		<cfargument name="filterClientFirstName" type="string" required="false" default="" />
		<cfargument name="filterStatus" type="numeric" required="false" default="0" />
		<cfargument name="filterfeesDue" type="string" required="false" default=""/>		
		<cfargument name="start" type="numeric" required="false" default="1" />
		<cfargument name="count" type="numeric" required="false" default="1" />
		<cfargument name="sort" type="string" required="true" default="referralid"/>
		<cfargument name="orderBy" type="string" required="true" default="desc"/>	
		<cfargument name="checkedReferral" type="string" required="false" default=""/>	
		
		<cfset var local = structNew() />

		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcUser.memberData.IdentifiedAsMemberID gt 0>
			<cfset local.useMID = session.cfcUser.memberData.IdentifiedAsMemberID>
		<cfelse>
			<cfset local.useMID = session.cfcuser.memberdata.memberid>
		</cfif>

		<cfset local.qryReferralSettings = getReferralSettings(siteID=arguments.mcproxy_siteID)>
				
		<cfscript>
			local.count = arguments.count;
			if(arguments.start != ''){
				local.currentPage = arguments.start;
			}else{
				local.currentPage = 1;
			}
			local.startLimit = (local.currentPage - 1) * local.count + 1;
			local.endLimit = local.startLimit + (local.count - 1);
		</cfscript>
		
		<cfquery name="local.arrResults" datasource="#application.dsn.membercentral.dsn#" returntype="array">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpClientReferrals') IS NOT NULL 
				DROP TABLE ##tmpClientReferrals;
			IF OBJECT_ID('tempdb..##tmpReferralsData') IS NOT NULL 
				DROP TABLE ##tmpReferralsData;
			CREATE TABLE ##tmpClientReferrals (clientReferralID int, clientID int, clientParentID int, caseID int, memberID int);

			declare @totalDues numeric(6,2), @memberID int, @referralID int, 
				@filterReferralID varchar(100), @filterClientLastName varchar(200), @filterClientFirstName varchar(200), 
				@filterfeesDue varchar(50), @filterStatus int, @startLimit int, @endLimit int, @siteID int, @orgID int,
				@totalCount int;

			set @memberID = <cfqueryparam value="#local.useMID#" cfsqltype="cf_sql_integer" />;
			set @referralID = <cfqueryparam value="#local.qryReferralSettings.referralID#" cfsqltype="cf_sql_integer" />;
			set @filterReferralID = <cfqueryparam value="%#arguments.filterReferralID#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientLastName = <cfqueryparam value="%#arguments.filterClientLastName#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientFirstName = <cfqueryparam value="%#arguments.filterClientFirstName#%" cfsqltype="cf_sql_varchar" />;
			set @filterStatus = <cfqueryparam value="#arguments.filterStatus#" cfsqltype="cf_sql_integer" />;
			set @filterfeesDue = <cfqueryparam value="#arguments.filterfeesDue#" cfsqltype="cf_sql_varchar" />;
			set @startLimit = <cfqueryparam value="#local.startLimit#" cfsqltype="cf_sql_integer" />;
			set @endLimit = <cfqueryparam value="#local.endLimit#" cfsqltype="cf_sql_integer" />;
			set @siteID = <cfqueryparam value="#arguments.mcproxy_siteID#" cfsqltype="cf_sql_integer">;
			select @orgID = orgID from dbo.sites where siteID = @siteID;
			
			<cfif arguments.refDateRange>
				declare @dateCutoff datetime, @daysToLookBack int;
				set @daysToLookBack = <cfqueryparam value="#arguments.refDateRange#" cfsqltype="cf_sql_integer" />;
				set @dateCutoff = dateadd(dd,-@daysToLookBack,getdate());
			</cfif>

			INSERT INTO ##tmpClientReferrals (clientReferralID, clientID, clientParentID, caseID, memberID)
			SELECT cr.clientReferralID, c.clientID, c.clientParentID, rc.caseID, m2.memberID
			FROM dbo.ref_clients c
			INNER JOIN dbo.ref_clientReferrals cr on cr.referralID = @referralID AND cr.clientID = c.clientID
				<cfif len(arguments.checkedReferral)>
					AND cr.clientReferralID IN (#arguments.checkedReferral#)
				</cfif>
				<cfif len(arguments.filterReferralID)>
					and cr.clientReferralID like @filterReferralID
				</cfif>
			INNER JOIN dbo.ref_clientReferralStatus crs on crs.referralID = @referralID AND crs.clientReferralStatusID = cr.statusID
				and crs.isDeleted <> 1
				<cfif arguments.filterStatus>
					and crs.clientReferralStatusID = @filterStatus
				</cfif>
			INNER JOIN dbo.ref_clientTypes ct on ct.clientTypeID = c.typeID
				and ct.clientType = 'Client'
			INNER JOIN dbo.ref_clientReferralTypes crt on crt.clientReferralTypeID = cr.typeID
			LEFT OUTER JOIN dbo.ref_cases rc on rc.referralID = @referralID 
				and rc.clientReferralID = cr.clientReferralID
			INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberid = cr.memberid
			INNER JOIN dbo.ams_members m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID 
				and m2.memberID = @memberID
			WHERE c.referralID = @referralID
			<cfif arguments.refDateRange>
				AND cr.clientReferralDate >= @dateCutoff
			</cfif>
			<cfif len(arguments.filterClientLastName)>
				and c.lastName like @filterClientLastName
			</cfif>	
			<cfif len(arguments.filterClientFirstName)>
				and c.firstName like @filterClientFirstName
			</cfif>				
			<cfif len(arguments.filterfeesDue) and NOT isNumeric(arguments.filterfeesDue)>
				AND 1=0
			</cfif>;

			<cfif local.qryReferralSettings.collectClientFeeFE>
				IF OBJECT_ID('tempdb..##tmpClientFeeTrans') IS NOT NULL 
					DROP TABLE ##tmpClientFeeTrans; 
				IF OBJECT_ID('tempdb..##tmpClientsForFee') IS NOT NULL 
					DROP TABLE ##tmpClientsForFee; 
				IF OBJECT_ID('tempdb..##tmpClientsForFeeResult') IS NOT NULL 
					DROP TABLE ##tmpClientsForFeeResult; 
				CREATE TABLE ##tmpClientFeeTrans (traItemID int, totalClientFee decimal(18,2), clientFeePaid decimal(18,2), clientFeeDue decimal(18,2));
				CREATE TABLE ##tmpClientsForFee (traItemID int PRIMARY KEY);
				CREATE TABLE ##tmpClientsForFeeResult (traItemID int, transactionID int);

				INSERT INTO ##tmpClientsForFee (traItemID)
				SELECT DISTINCT ISNULL(clientParentID,clientID)
				FROM ##tmpClientReferrals;

				EXEC dbo.ref_clientReferralTransactionsByBulk @orgID=@orgID, @feeType='clientReferralFee';

				INSERT INTO ##tmpClientFeeTrans (traItemID, totalClientFee, clientFeePaid, clientFeeDue)
				select fees.traItemID, SUM(ts.cache_amountAfterAdjustment), SUM(ts.cache_activePaymentAllocatedAmount),
					SUM(ts.cache_amountAfterAdjustment) - SUM(ts.cache_activePaymentAllocatedAmount)
				from ##tmpClientsForFeeResult as fees
				inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fees.transactionid
				group by fees.traItemID;

				IF OBJECT_ID('tempdb..##tmpClientsForFee') IS NOT NULL 
					DROP TABLE ##tmpClientsForFee; 
				IF OBJECT_ID('tempdb..##tmpClientsForFeeResult') IS NOT NULL 
					DROP TABLE ##tmpClientsForFeeResult; 
			</cfif>
			
			SELECT c.clientID, c.referralID,
				c.firstName, c.middleName, c.lastName,
				c.lastName + ', ' + c.firstName as clientName,
				c.businessName, c.address1, c.address2, c.address3,
				c.city,	c.state, c.postalCode, c.countryID,
				c.email, c.homePhone, c.cellPhone, c.alternatePhone,
				c.typeID, cr.statusID,
				cr.clientReferralID, cr.typeID as referralTypeID, cr.memberID,
				cr.callUID, cr.issueDesc, CONVERT(varchar, cr.clientReferralDate, 101) as clientReferralDate,
				crt.clientReferralType, crt.isReferral as isLawyerReferral, 
				crt.isAgency as isAgencyReferral, 
				crs.statusName, 
				crs.canEditClient as referralCanEditClient, 
				crs.canEditFilter as referralCanEditFilter, 
				crs.canEditLawyer as referralCanEditLawyer,
				crs.canRefer, crs.isReferred, crs.isAgency, crs.isPending,
				c.dateCreated, c.createdBy, c.clientParentID,
				c.dateLastUpdated, c.relationToClient,
				ISNULL(rc.caseID,0) AS caseID, rc.enteredByMemberID, rc.notesTxt as caseNotesTxt,
				rc.caseFees, rc.dateCaseOpened, rc.dateCaseClosed,
				rc.dateCreated as dateCaseCreated, rc.dateLastUpdated as dateCaseLastUpdated,
				(m.firstName  + 
				case
					when m.middlename is not null and len(m.middlename) > 0  then
						' ' + left(m.middleName, 1) + ' '
					else
						' '
				end  + m.lastName) as memberName,
				ISNULL(caseFeesTotal.collectedFeeTotal,0) as collectedFeeTotal,
				ISNULL(caseFeesTotal.referralDuesTotal,0) as referralDuesTotal,
				ISNULL(caseFeesTotal.amtToBePaidTotal,0) as amtToBePaidTotal,
				ISNULL(caseFeesTotal.paidToDateTotal,0) as paidToDateTotal,
				<cfif local.qryReferralSettings.collectClientFeeFE>
					ISNULL(clientFeesTotal.totalClientFee,0) as totalClientFee,
					ISNULL(clientFeesTotal.clientFeePaid,0) as clientFeePaid,
					ISNULL(clientFeesTotal.clientFeeDue,0) as clientFeeDue,
					ISNULL(caseFeesTotal.amtToBePaidTotal,0) + ISNULL(clientFeesTotal.clientFeeDue,0) as totalFeesDue,
				<cfelse>
					ISNULL(caseFeesTotal.amtToBePaidTotal,0) as totalFeesDue,
				</cfif>
				ROW_NUMBER() OVER ( ORDER BY 
					<cfif len(arguments.sort)>
						<cfif arguments.sort EQ "referralid">
							cr.clientReferralID 
						<cfelseif arguments.sort EQ "clientname">
							c.lastName + ', ' + c.firstName 
						<cfelseif arguments.sort EQ "referralDate">
							cr.clientReferralDate 
						<cfelseif arguments.sort EQ "collectedfromdate">
							caseFeesTotal.collectedFeeTotal 
						<cfelseif arguments.sort EQ "feesbilled">
							caseFeesTotal.referralDuesTotal 
						<cfelseif local.qryReferralSettings.collectClientFeeFE AND arguments.sort EQ "clientfeesdue">
							ISNULL(clientFeesTotal.clientFeeDue,0)
						<cfelseif arguments.sort EQ "feesdue">
							caseFeesTotal.amtToBePaidTotal
						<cfelseif arguments.sort EQ "feespaidtodate">
							caseFeesTotal.paidToDateTotal
						</cfif>
						<cfif len(arguments.orderBy)>
							#arguments.orderBy#
						</cfif>
					<cfelse>
						cr.clientReferralID
					</cfif>
				) AS RowNum
			INTO ##tmpReferralsData
			FROM ##tmpClientReferrals as tmp
			INNER JOIN dbo.ref_clients as c on c.clientID = tmp.clientID
			INNER JOIN dbo.ref_clientReferrals as cr on cr.referralID = @referralID AND cr.clientID = c.clientID
				AND cr.clientReferralID = tmp.clientReferralID
			INNER JOIN dbo.ref_clientReferralStatus as crs on crs.referralID = @referralID 
				AND crs.clientReferralStatusID = cr.statusID
			INNER JOIN dbo.ref_clientReferralTypes as crt on crt.clientReferralTypeID = cr.typeID
			LEFT OUTER JOIN dbo.ref_cases as rc on rc.referralID = @referralID 
				AND rc.clientReferralID = cr.clientReferralID
			INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberid = tmp.memberID
			OUTER APPLY dbo.fn_tr_getCaseFeeTotalsByCaseId(@orgID,rc.caseID) as caseFeesTotal
			<cfif local.qryReferralSettings.collectClientFeeFE>
				LEFT OUTER JOIN ##tmpClientFeeTrans AS clientFeesTotal ON clientFeesTotal.traItemID = ISNULL(tmp.clientParentID,tmp.clientID)
			</cfif>
			WHERE c.referralID = @referralID 
			<cfif local.qryReferralSettings.collectClientFeeFE>
				AND ISNULL(caseFeesTotal.amtToBePaidTotal,0) + ISNULL(clientFeesTotal.clientFeeDue,0) > 0
			<cfelse>
				AND ISNULL(caseFeesTotal.amtToBePaidTotal,0) > 0
			</cfif>;

			SET @totalCount = @@ROWCOUNT;

			SELECT *, @totalCount AS totalCount
			FROM ##tmpReferralsData
			WHERE RowNum >= @startLimit 
			AND RowNum <= @endLimit			
			ORDER BY RowNum;


			IF OBJECT_ID('tempdb..##tmpClientReferrals') IS NOT NULL 
				DROP TABLE ##tmpClientReferrals;
			IF OBJECT_ID('tempdb..##tmpReferralsData') IS NOT NULL 
				DROP TABLE ##tmpReferralsData;
			<cfif local.qryReferralSettings.collectClientFeeFE>
				IF OBJECT_ID('tempdb..##tmpClientFeeTrans') IS NOT NULL
					DROP TABLE ##tmpClientFeeTrans;
			</cfif>

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfscript>
			local.totalCount = arrayLen(local.arrResults) ? local.arrResults[1].totalCount : 0;
			
			if (local.totalCount % local.count == 0) local.totalPages =  int(local.totalCount/local.count);
			else local.totalPages =  int(local.totalCount/local.count) + 1;
			
			if(local.totalPages == 0) local.totalPages = 1;

			local.gridJSON = { 
				"totalCount" = int(local.totalCount), 
				"totalPages" = int(local.totalPages),  
				"currentPage" = int(local.currentPage),
				"sort" = arguments.sort,
				"orderby" = arguments.orderBy,
				"data" = local.arrResults
			};

			local.data.result = local.gridJSON;
			local.data.success = true;
		</cfscript>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getMemberRFPXMLData" access="public" output="false" returntype="query">
		<cfargument name="refDateRange" type="numeric" required="false" default="0" />
		<cfargument name="filterReferralID" type="string" required="false" default="" />
		<cfargument name="filterClientLastName" type="string" required="false" default="" />
		<cfargument name="filterClientFirstName" type="string" required="false" default="" />
		<cfargument name="checkedReferral" type="string" required="false" default=""/>	
		
		<cfset var local = structNew() />

		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcUser.memberData.IdentifiedAsMemberID gt 0>
			<cfset local.useMID = session.cfcUser.memberData.IdentifiedAsMemberID>
		<cfelse>
			<cfset local.useMID = session.cfcuser.memberdata.memberid>
		</cfif>
		
		<cfquery name="local.qryGetSiteID" datasource="#application.dsn.membercentral.dsn#">
			select dbo.fn_getSiteIDFromSiteCode('#session.mcstruct.siteCode#') as siteID
		</cfquery>

		<cfset local.qryReferralSettings = getReferralSettings(local.qryGetSiteID.siteID)>
		<cfset local.referralID = local.qryReferralSettings.referralID>

		<cfif len(arguments.checkedReferral)>
			<cfset arguments.checkedReferral = arguments.checkedReferral.listFilter(
				function(crid) {
					return IsNumeric(arguments.crid);
				}
			)>
		</cfif>

		<cfquery name="local.qryResults" datasource="#application.dsn.membercentral.dsn#" returntype="query">			
			SET NOCOUNT ON;

			declare @totalDues numeric(6,2), @memberID int, @referralID int, @filterReferralID varchar(100), @filterClientLastName varchar(200), 
				@filterClientFirstName varchar(200), @filterfeesDue varchar(50), @filterStatus int, @startLimit int, @endLimit int, @siteID int, @orgID int;
			set @memberID = <cfqueryparam value="#local.useMID#" cfsqltype="cf_sql_integer" />;
			set @referralID = <cfqueryparam value="#local.referralID#" cfsqltype="cf_sql_integer" />;
			set @filterReferralID = <cfqueryparam value="%#arguments.filterReferralID#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientLastName = <cfqueryparam value="%#arguments.filterClientLastName#%" cfsqltype="cf_sql_varchar" />;
			set @filterClientFirstName = <cfqueryparam value="%#arguments.filterClientFirstName#%" cfsqltype="cf_sql_varchar" />;
			set @siteID = <cfqueryparam value="#local.qryGetSiteID.siteID#" cfsqltype="cf_sql_integer">;
			select @orgID = orgID from dbo.sites where siteID = @siteID;
			
			<cfif arguments.refDateRange>
				declare @dateCutoff datetime, @daysToLookBack int;
				set @daysToLookBack = <cfqueryparam value="#arguments.refDateRange#" cfsqltype="cf_sql_integer" />;
				set @dateCutoff = dateadd(dd,-@daysToLookBack,getdate());
			</cfif>
			
			WITH ResultsCTE as (
				SELECT ROW_NUMBER() OVER ( ORDER BY 
					cr.clientReferralID
				) AS RowNum,
					c.clientID,	c.referralID,
					c.firstName, c.middleName, c.lastName,
					c.lastName + ', ' + c.firstName as clientName,
					c.businessName, c.address1, c.address2, c.address3,
					c.city,	c.state, c.postalCode, c.countryID,
					c.email, c.homePhone, c.cellPhone, c.alternatePhone,
					c.typeID, cr.statusID,
					cr.clientReferralID, cr.typeID as referralTypeID, cr.memberID,
					cr.callUID, cr.issueDesc, CONVERT(varchar, cr.clientReferralDate, 101) as clientReferralDate,
					crt.clientReferralType, crt.isReferral as isLawyerReferral, 
					crt.isAgency as isAgencyReferral, 
					crs.statusName, 
					crs.canEditClient as referralCanEditClient, 
					crs.canEditFilter as referralCanEditFilter, 
					crs.canEditLawyer as referralCanEditLawyer,
					crs.canRefer, crs.isReferred, crs.isAgency, crs.isPending,
					c.dateCreated, c.createdBy, c.clientParentID,
					c.dateLastUpdated, c.relationToClient,
					ISNULL(rc.caseID,0) AS caseID, rc.enteredByMemberID, rc.notesTxt as caseNotesTxt,
					rc.caseFees, rc.dateCaseOpened, rc.dateCaseClosed,
					rc.dateCreated as dateCaseCreated, rc.dateLastUpdated as dateCaseLastUpdated,
					(m.firstName  + 
					case
						when m.middlename is not null and len(m.middlename) > 0  then
							' ' + left(m.middleName, 1) + ' '
						else
							' '
					end  + m.lastName) as memberName,
					ISNULL(caseFeesTotal.collectedFeeTotal,0) as collectedFeeTotal,
					ISNULL(caseFeesTotal.referralDuesTotal,0) as referralDuesTotal,
					ISNULL(caseFeesTotal.amtToBePaidTotal,0) as amtToBePaidTotal,
					ISNULL(caseFeesTotal.paidToDateTotal,0) as paidToDateTotal,
					<cfif local.qryReferralSettings.collectClientFeeFE>
						ISNULL(clientFeesTotal.totalClientFee,0) as totalClientFee,
						ISNULL(clientFeesTotal.clientFeePaid,0) as clientFeePaid,
						ISNULL(clientFeesTotal.clientFeeDue,0) as clientFeeDue,
						ISNULL(caseFeesTotal.amtToBePaidTotal,0) + ISNULL(clientFeesTotal.clientFeeDue,0) as totalFeesDue
					<cfelse>
						ISNULL(caseFeesTotal.amtToBePaidTotal,0) as totalFeesDue
					</cfif>				
			FROM dbo.ref_clients c
			INNER JOIN dbo.ref_clientReferrals cr on cr.referralID = @referralID AND cr.clientID = c.clientID
				<cfif len(arguments.checkedReferral)>
					AND cr.clientReferralID IN (#arguments.checkedReferral#)
				</cfif>
				<cfif len(arguments.filterReferralID)>
					and cr.clientReferralID like @filterReferralID
				</cfif>
			INNER JOIN dbo.ref_clientReferralStatus crs on crs.clientReferralStatusID = cr.statusID
				AND crs.isDeleted <> 1
			INNER JOIN dbo.ref_clientTypes ct on ct.clientTypeID = c.typeID
				AND ct.clientType = 'Client'
			INNER JOIN dbo.ref_clientReferralTypes crt on crt.clientReferralTypeID = cr.typeID
			LEFT OUTER JOIN dbo.ref_cases rc on rc.referralID = @referralID 
				AND rc.clientReferralID = cr.clientReferralID
			INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberid = cr.memberid
			INNER JOIN dbo.ams_members m2 on m2.orgID = @orgID and  m2.memberid = m.activeMemberID 
				and m2.memberID = @memberID
			OUTER APPLY dbo.fn_tr_getCaseFeeTotalsByCaseId(@orgID,rc.caseID) as caseFeesTotal
			<cfif local.qryReferralSettings.collectClientFeeFE>
				OUTER APPLY dbo.fn_ref_totalClientFeeAndPaid(@orgID,c.clientID) as clientFeesTotal
			</cfif>
			WHERE c.referralID = @referralID 
			<cfif local.qryReferralSettings.collectClientFeeFE>
				AND ISNULL(caseFeesTotal.amtToBePaidTotal,0) + ISNULL(clientFeesTotal.clientFeeDue,0) > 0
			<cfelse>
				AND ISNULL(caseFeesTotal.amtToBePaidTotal,0) > 0
			</cfif>
			<cfif len(arguments.filterClientLastName)>
				and c.lastName like @filterClientLastName
			</cfif>	
			<cfif len(arguments.filterClientFirstName)>
				and c.firstName like @filterClientFirstName
			</cfif>
			<cfif arguments.refDateRange>
				AND cr.clientReferralDate >= @dateCutoff
			</cfif>
			)
			SELECT *
			FROM ResultsCTE
		</cfquery>
		
		<cfreturn local.qryResults>
	</cffunction>

	<cffunction name="getAllowedPaymentProfiles" access="private" output="false" returntype="query">		
		<cfargument name="profileID" type="numeric" required="no" default="0">
		<cfargument name="clientReferralIDList" type="string" required="no" default="">

		<cfset var local = structNew()>
		<cfset local.orgID = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID>
		<cfset local.siteID = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID>
		<cfset local.referralSettings = getReferralSettings(siteID=local.siteID)>
		<cfset local.invoiceProfileIDs = getInvoiceProfileIDsFromClientReferralIDs(orgID=local.orgID, referralID=local.referralSettings.referralID, clientReferralIDList=arguments.clientReferralIDList)>
		<cfset local.ovInvProfileID = listLen(local.invoiceProfileIDs) EQ 1 ? val(local.invoiceProfileIDs) : 0>
	
		<cfquery name="local.qryProfiles" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">;
			DECLARE @procFeeSupportedGatewayIDs varchar(10) = '10';	-- AuthorizeCCCIM
			DECLARE @applicationInstanceID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.referralSettings.applicationInstanceID)#">;
			DECLARE @tmpInvoiceProfileProcFeeOverrides TABLE (gatewayID int, enableProcessingFeeDonation bit, 
				processFeeDonationDefaultSelect bit, processFeeDonationFETitle varchar(100), processFeeDonationFEMsg varchar(800));

			<cfif local.ovInvProfileID>
				-- invoice profile processing fee override settings
				INSERT INTO @tmpInvoiceProfileProcFeeOverrides (gatewayID, enableProcessingFeeDonation, processFeeDonationDefaultSelect, processFeeDonationFETitle, processFeeDonationFEMsg)
				SELECT psg.listitem, ip.enableProcessingFeeDonation, ip.processFeeDonationDefaultSelect, pfm.title, pfm.message
				FROM dbo.tr_invoiceProfiles AS ip 
				LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.messageID = ip.solicitationMessageID
				CROSS APPLY dbo.fn_intListToTableInline(@procFeeSupportedGatewayIDs,',') AS psg
				WHERE ip.profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.ovInvProfileID#">;
			</cfif>

			SELECT p.profileID, p.profileCode, g.gatewayid, g.gatewayType, g.gatewayClass, p.tabTitle, p.frontEndOrderBy, 
				CASE WHEN p.enableProcessingFeeDonation = 1 AND ISNULL(tmp.enableProcessingFeeDonation,1) = 1 THEN 1 ELSE 0 END AS enableProcessingFeeDonation,
				p.processFeeDonationFeePercent, ISNULL(tmp.processFeeDonationFETitle,pfm.title) as processFeeDonationFETitle,
				ISNULL(tmp.processFeeDonationFEMsg,pfm.message) as processFeeDonationFEMsg,
				ISNULL(tmp.processFeeDonationDefaultSelect,p.processFeeDonationDefaultSelect) as processFeeDonationDefaultSelect,
				p.processFeeDonationRenevueGLAccountID, p.processFeeDonationRevTransDesc, p.processingFeeLabel, p.processFeeOtherPaymentsFELabel, 
				p.processFeeOtherPaymentsFEDenyLabel, p.enableApplePay, p.enableGooglePay, p.enableSurcharge, p.surchargePercent, p.surchargeRevenueGLAccountID
			FROM dbo.ref_merchantProfiles as rmp
			INNER JOIN dbo.mp_profiles as p on p.profileid = rmp.merchantProfileID
			INNER JOIN dbo.mp_gateways as g on g.gatewayID = p.gatewayID
			LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.messageID = p.solicitationMessageID
			LEFT OUTER JOIN @tmpInvoiceProfileProcFeeOverrides AS tmp ON tmp.gatewayID = g.gatewayID
			WHERE p.status = 'A'
			AND g.isActive = 1
			AND g.gatewayID not in (2,13,14)
			AND rmp.applicationInstanceID = @applicationInstanceID
			<cfif arguments.profileID neq 0>
				AND p.profileID = <cfqueryparam value="#val(arguments.profileID)#" cfsqltype="cf_sql_integer">
			</cfif>
			ORDER BY p.profileID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif NOT local.qryProfiles.recordCount>
			<cfreturn local.qryProfiles>
		</cfif>

		<cfquery name="local.qryProfilesDistinct" dbtype="query">
			SELECT DISTINCT profileID, profileCode, gatewayid, gatewayType, gatewayClass, tabTitle, frontEndOrderBy, enableProcessingFeeDonation,
				processFeeDonationFeePercent, processFeeDonationFETitle, processFeeDonationFEMsg, processFeeDonationDefaultSelect, processFeeDonationRenevueGLAccountID, 
				processFeeDonationRevTransDesc, processingFeeLabel, processFeeOtherPaymentsFELabel, processFeeOtherPaymentsFEDenyLabel, enableApplePay, enableGooglePay, 
				enableSurcharge, surchargePercent, surchargeRevenueGLAccountID
			FROM local.qryProfiles
			ORDER BY frontEndOrderBy
		</cfquery>

		<cfreturn local.qryProfilesDistinct>
	</cffunction>

	<cffunction name="payOutstandingReferralFees" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="referralID" type="numeric" required="yes">
		<cfargument name="profileID" type="numeric" required="yes">
		<cfargument name="totalPrice" type="numeric" required="yes">
		<cfargument name="useMID" type="numeric" required="yes">
		<cfargument name="clientReferralIDList" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.qryMerchantProfile = getAllowedPaymentProfiles(profileID=arguments.profileID, clientReferralIDList=arguments.clientReferralIDList)>
		<cfset local.amountToCharge = arguments.totalPrice>
		<cfset local.strResponse = { "success":false, "response":'', "processingFees":0, "totalPaid":0, 
			"processingFeeLabel":"", "isOfflinePayment":local.qryMerchantProfile.gatewayClass eq "offline",
			"paymentInstructions": getPaymentInstructions(profileID=local.qryMerchantProfile.profileID),
			"arrPaymentTransactions": []
		}>

		<cfset local.stateIDForTax = arguments.event.getValue('stateIDForTax',0)>
		<cfset local.zipForTax = arguments.event.getValue('zipForTax','')>

		<!--- get fields --->
		<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.event.getValue('mc_siteinfo.siteID'), profilecode=local.qryMerchantProfile.profilecode)>
		<cfset local.tmpGatewayFields = structNew()>
		<cfloop query="local.qryGatewayProfileFields">
			<cfset structInsert(local.tmpGatewayFields,'fld_#local.qryGatewayProfileFields.fieldid#_',arguments.event.getTrimValue('p_#local.qryMerchantProfile.profileID#_fld_#local.qryGatewayProfileFields.fieldid#_',''))>
		</cfloop>

		<!--- get info on file if applicable --->
		<cfset arguments.event.setValue('p_#local.qryMerchantProfile.profileID#_mppid',int(val(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid',0))))>
		<cfif arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid') gt 0>
			<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(mppid=arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid'), memberID=arguments.useMID, profileID=local.qryMerchantProfile.profileID)>
			<cfset structInsert(local.tmpGatewayFields,'qryInfoOnFile',local.qrySavedInfoOnFile)>
		</cfif>

		<!--- Surcharge / Processing Fee Donation --->
		<cfset local.additionalFeesInfo = application.objPayments.getAdditionalFeesInfo(qryMerchantProfile=local.qryMerchantProfile, amt=local.amountToCharge, 
			stateIDForTax=local.stateIDForTax, zipForTax=local.zipForTax, processingFeeOpted=arguments.event.getValue('processFeeDonation#arguments.event.getValue('profileid')#',0) EQ 1,
			surchargeEligibleCard=arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid') GT 0 AND local.qrySavedInfoOnFile.surchargeEligible EQ 1)>

		<!--- failed --->
		<cfif NOT local.additionalFeesInfo.success>
			<cfset local.strResponse.response = "Unable to get additional payment fees info.">
			<cfreturn local.strResponse>
		</cfif>
	
		<cfset local.finalAmountToCharge = local.additionalFeesInfo.finalAmountToCharge>

		<!--- prepare fields for gateway and send --->
		<cfset local.strTemp = { orgID=arguments.event.getValue('mc_siteinfo.orgID'), siteid=arguments.event.getValue('mc_siteinfo.siteid'), 
								 profileCode=local.qryMerchantProfile.profileCode, assignedToMemberID=arguments.useMID, recordedByMemberID=arguments.useMID, 
								 statsSessionID=val(session.cfcUser.statsSessionID), x_amount=local.finalAmountToCharge, 
								 x_description='#arguments.event.getValue('mc_siteinfo.sitename')# Referral Fee Payments', offeredPaymentFee=local.additionalFeesInfo.offeredPaymentFee }>
		<cfset structAppend(local.strTemp,local.tmpGatewayFields)>

		<!--- apple or google pay token --->
		<cfif arguments.event.valueExists('p_#local.qryMerchantProfile.profileID#_tokenData') AND len(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_tokenData'))>
			<cfset local.strTemp["tokenData"] = deSerializeJSON(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_tokenData'))>
		</cfif>

		<cfif listFindNoCase("AuthorizeCCCIM",local.qryMerchantProfile.gatewayType)>
			<cfset local.qryLevel3Data = QueryNew("name,desc,itemPriceExcDiscount,itemPriceIncDiscount,discount,qty,total","varchar,varchar,decimal,decimal,decimal,decimal,decimal")>
			<cfset QueryAddRow(local.qryLevel3Data, {
				"name": "Referral Fee Payments",
				"desc": "#arguments.event.getValue('mc_siteinfo.sitename')# Referral Fee Payments",
				"itemPriceExcDiscount": local.amountToCharge,
				"itemPriceIncDiscount": local.amountToCharge,
				"discount": 0,
				"qty": 1,
				"total": local.amountToCharge
			})>
			<cfif local.additionalFeesInfo.additionalFees GT 0>
				<cfset QueryAddRow(local.qryLevel3Data, {
					"name": "#local.additionalFeesInfo.additionalFeesLabel#",
					"desc": "#arguments.event.getValue('mc_siteinfo.sitename')# #local.additionalFeesInfo.additionalFeesLabel#",
					"itemPriceExcDiscount": local.additionalFeesInfo.additionalFees,
					"itemPriceIncDiscount": local.additionalFeesInfo.additionalFees,
					"discount": 0,
					"qty": 1,
					"total": local.additionalFeesInfo.additionalFees
				})>
				<!--- Surcharge --->
				<cfif local.additionalFeesInfo.paymentFeeTypeID EQ 2>
					<cfset local.strTemp['x_surcharge'] = { "amount":local.additionalFeesInfo.additionalFees, "description":"#arguments.event.getValue('mc_siteinfo.sitename')# #local.additionalFeesInfo.additionalFeesLabel#" }>
				</cfif>
			</cfif>
			<cfset local.strTemp["x_items"] = application.objPayments.getLevel3Data(qryLevel3Data=local.qryLevel3Data, gatewayType=local.qryMerchantProfile.gatewayType)>
		</cfif>

		<cfinvoke component="#application.objPayments#" method="chargeAdHoc" argumentcollection="#local.strTemp#" returnvariable="local.paymentResponse">

		<!--- if payment not successful --->
		<cfif local.paymentResponse.responseCode is not 1>
			<cfset local.strResponse.response = local.paymentResponse.responseReasonText>			
			<cfset local.strResponse.success = false>
			<cfreturn local.strResponse>
		<cfelse>
			<cfset local.strResponse.success = true>
			<cfset local.strResponse.response = local.paymentResponse>
			<cfset local.arrPaymentResults = local.paymentResponse.keyExists("paymentResults") ? local.paymentResponse.paymentResults : [ duplicate(local.paymentResponse) ]>
			<cfset local.strResponse.arrPaymentTransactions = []>
			<cfloop array="#local.arrPaymentResults#" index="local.thisResult">
				<cfif val(local.thisResult.mc_transactionID)>
					<cfset local.strResponse.arrPaymentTransactions.append({ "mc_transactionID": local.thisResult.mc_transactionID, "x_amount": val(local.thisResult.x_amount) })>
				</cfif>
			</cfloop>
		</cfif>

		<!--- Record Surcharge / Processing Fee Donation --->
		<cfif local.additionalFeesInfo.additionalFees GT 0 AND local.paymentResponse.keyExists("mc_transactionID") AND val(local.paymentResponse.mc_transactionID)>
			<cfset local.strRecordAdditionalPmtFees = CreateObject("component","model.system.platform.accounting").recordAdditionalPaymentFees(orgID=arguments.event.getValue('mc_siteinfo.orgID'), 
				siteID=arguments.event.getValue('mc_siteinfo.siteID'), assignedToMemberID=arguments.useMID, recordedByMemberID=arguments.useMID, 
				statsSessionID=val(session.cfcuser.statsSessionID), paymentTransactionID=local.paymentResponse.mc_transactionID, 
				GLAccountID=local.additionalFeesInfo.gl, qryAdditionalFees=local.additionalFeesInfo.qryAdditionalFees, 
				paymentFeeTypeID=local.additionalFeesInfo.paymentFeeTypeID)>
			
			<!--- if not successful --->
			<cfif NOT local.strRecordAdditionalPmtFees.success>
				<cfset local.strResponse.response = "Unable to record payment processing fees.">
				<cfset local.strResponse.success = false>
			<cfelse>
				<cfset local.strResponse.success = true>
				<cfset local.strResponse.processingFees = local.additionalFeesInfo.additionalFees>
				<cfset local.strResponse.processingFeeLabel = local.additionalFeesInfo.paymentFeeTypeID EQ 1 ? local.qryMerchantProfile.processingFeeLabel : "Surcharge">
			</cfif>
		</cfif>

		<cfif local.strResponse.success>
			<cfset local.strResponse.totalPaid = local.finalAmountToCharge>
		</cfif>

		<cfreturn local.strResponse>
	</cffunction>

	<cffunction name="recordClientFeeAllocation" access="private" output="false" returntype="boolean">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="referralID" type="numeric" required="yes">
		<cfargument name="arrPaymentTransactions" type="array" required="yes">
		<cfargument name="clientReferralIDList" type="string" required="yes">
		<cfargument name="useMID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery name="local.qryAllocateToSale" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				
				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,
					@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
					@referralID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.referralID#">,
					@recordedByMemberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.useMID#">,
					@statsSessionID bigint = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(session.cfcUser.statsSessionID)#">,  
					@minPaymentTID int, @unAllocAmount decimal(18,2), @allocAmount decimal(18,2), @minSaleTID int, 
					@saleDue decimal(18,2), @nowDate datetime = GETDATE();
				DECLARE @tblPaymentForReallocationTransactions TABLE (paymentTransactionID int, amountAvailable decimal(18,2));
				DECLARE @tmpClientFeeSaleTransactions TABLE (saleTransactionID int, amountDue decimal(18,2))

				INSERT INTO @tmpClientFeeSaleTransactions (saleTransactionID, amountDue)
				SELECT ts.transactionID, ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount
				FROM dbo.ref_clients AS c
				INNER JOIN dbo.ref_clientReferrals AS cr ON cr.referralID = @referralID 
					AND cr.clientID = c.clientID
					AND cr.clientReferralID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#arguments.clientReferralIDList#">)
				CROSS APPLY dbo.fn_ref_clientTransactions (c.clientID,@orgID) AS ct
				INNER JOIN dbo.tr_transactionSales AS ts ON ts.orgID = @orgID AND ts.transactionID = ct.transactionID
				WHERE (ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount) > 0;

				<cfloop array="#arguments.arrPaymentTransactions#" index="local.thisResult">
					INSERT INTO @tblPaymentForReallocationTransactions (paymentTransactionID, amountAvailable)
					VALUES (#val(local.thisResult.mc_transactionID)#, #val(local.thisResult.x_amount)#);
				</cfloop>

				IF EXISTS (SELECT 1 FROM @tmpClientFeeSaleTransactions) BEGIN
					BEGIN TRAN;
						-- allocate payment to sales
						SELECT @minSaleTID = MIN(saleTransactionID) FROM @tmpClientFeeSaleTransactions;
						WHILE @minSaleTID IS NOT NULL BEGIN
							SELECT @saleDue = NULL, @minPaymentTID=NULL;

							SELECT @saleDue = amountDue FROM @tmpClientFeeSaleTransactions WHERE saleTransactionID = @minSaleTID;
							IF @saleDue > 0 BEGIN
								SELECT @minPaymentTID = MIN(paymentTransactionID) FROM @tblPaymentForReallocationTransactions where amountAvailable > 0;
								WHILE @minPaymentTID IS NOT NULL BEGIN
									SELECT @unAllocAmount = NULL, @allocAmount=NULL;

									SELECT @unAllocAmount = amountAvailable 
									FROM @tblPaymentForReallocationTransactions
									WHERE paymentTransactionID = @minPaymentTID;

									-- select minimum of dueAmount and unallocated Payment Amount
									SELECT @allocAmount=MIN(x) FROM (VALUES (@saleDue),(@unAllocAmount)) AS value(x);

									EXEC dbo.tr_allocateToSale @recordedOnSiteID=@siteID, @recordedByMemberID=@recordedByMemberID, 
										@statsSessionID=@statsSessionID, @amount=@allocAmount, @transactionDate=@nowDate, 
										@paymentTransactionID=@minPaymentTID, @saleTransactionID=@minSaleTID;
									
									SET @saleDue = @saleDue - @allocAmount;
									SET @unAllocAmount = @unAllocAmount - @allocAmount

									UPDATE @tblPaymentForReallocationTransactions
									SET amountAvailable = @unAllocAmount
									WHERE paymentTransactionID = @minPaymentTID;

									IF @saleDue = 0
										BREAK;
									
									SELECT @minPaymentTID = MIN(paymentTransactionID) 
										FROM @tblPaymentForReallocationTransactions
										WHERE paymentTransactionID > @minPaymentTID
										AND amountAvailable > 0;
								END
							END
							
							SELECT @minSaleTID = MIN(saleTransactionID) FROM @tmpClientFeeSaleTransactions WHERE saleTransactionID > @minSaleTID;		
						END
					COMMIT TRAN;
				END

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn true>
	</cffunction>

	<cffunction name="recordReferralFeeAllocation" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="paymentObj" type="struct" required="yes">
		<cfargument name="rfpData" type="struct" required="yes">
		<cfargument name="useMID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals")>
		<cfset local.caseId = arguments.rfpData.CASEID>
		<cfset local.ordernumber = createUUID()>
		<cfset local.qryGetCaseFees = local.objAdminReferrals.getCaseFees(caseID=local.caseID) />
		<cfset local.strResponse = { "success"=false, "response"='' }>

		<cfquery name="local.qryDistinctCaseFees" dbtype="query">
			select collectedFeeID, collectedFeeDate, collectedFee, filingFee, sum(referralDues) as referralDues, 
				sum(amtToBePaid) as amtToBePaid
			from local.qryGetCaseFees
			group by collectedFeeID, collectedFeeDate, collectedFee, filingFee
		</cfquery>
		<cfloop query="local.qryDistinctCaseFees">
			<cfset updateOrder(local.caseID,local.orderNumber,local.qryDistinctCaseFees.collectedFeeID)>
		</cfloop>
		<cfscript>	
			// make sure ordernumber is valid and not already checked out 
			if(NOT isOrderOKForCheckout(caseid=local.caseId,ordernumber=local.ordernumber)){
				local.strResponse.response = 'Failed to allocate payment to sale';
			}
			local.appInstanceID = getReferralSettings(arguments.event.getValue('mc_siteinfo.siteid'));
			paramOrderNumber(appInstanceID=local.appInstanceID.applicationInstanceID, orderNumber=local.ordernumber);
			local.qryItems = local.objAdminReferrals.getCaseFeesForAcct(caseid=local.caseId,ordernumber=local.ordernumber);
			if (NOT local.qryItems.recordcount){
				local.strResponse.response = 'Failed to allocate payment to sale';
			}
		</cfscript>
		<cfset local.recordedSuccess = 0 />
		<!--- record allocations --->
		<cftry>
			<cfquery name="local.qryAllocateToSale" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @siteID int, @minPaymentTID int, @unAllocAmount decimal(18,2), @allocAmount decimal(18,2), @minSaleTID int, 
						@saleDue decimal(18,2), @recordedByMemberID int, @statsSessionID int, @nowDate datetime = GETDATE();
					DECLARE @tblSales TABLE (saleTransactionID int, amount decimal(18,2));
					DECLARE @tblPaymentForReallocationTransactions TABLE (paymentTransactionID int, amountAvailable decimal(18,2));

					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.useMID#">;
					SET @statsSessionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.statsSessionID#">;

					<cfloop query="local.qryItems">
						INSERT INTO @tblSales (saleTransactionID, amount)
						VALUES (#val(local.qryItems.transactionID)#, #val(local.qryItems.amtToBePaid)#);
					</cfloop>

					<cfloop array="#arguments.paymentObj.arrPaymentTransactions#" index="local.thisResult">
						INSERT INTO @tblPaymentForReallocationTransactions (paymentTransactionID, amountAvailable)
						VALUES (#val(local.thisResult.mc_transactionID)#, #val(local.thisResult.x_amount)#);
					</cfloop>

					BEGIN TRAN;
						-- allocate payment to sales
						SELECT @minSaleTID = MIN(saleTransactionID) FROM @tblSales;
						WHILE @minSaleTID IS NOT NULL BEGIN
							SELECT @saleDue = NULL, @minPaymentTID=NULL;

							SELECT @saleDue = amount FROM @tblSales WHERE saleTransactionID = @minSaleTID;
							IF @saleDue > 0 BEGIN
								SELECT @minPaymentTID = MIN(paymentTransactionID) FROM @tblPaymentForReallocationTransactions where amountAvailable > 0;
								WHILE @minPaymentTID IS NOT NULL BEGIN
									SELECT @unAllocAmount = NULL, @allocAmount=NULL;

									SELECT @unAllocAmount = amountAvailable 
									FROM @tblPaymentForReallocationTransactions
									WHERE paymentTransactionID = @minPaymentTID;

									-- select minimum of dueAmount and unallocated Payment Amount
									SELECT @allocAmount=MIN(x) FROM (VALUES (@saleDue),(@unAllocAmount)) AS value(x);

									EXEC dbo.tr_allocateToSale @recordedOnSiteID=@siteID, @recordedByMemberID=@recordedByMemberID, 
										@statsSessionID=@statsSessionID, @amount=@allocAmount, @transactionDate=@nowDate, 
										@paymentTransactionID=@minPaymentTID, @saleTransactionID=@minSaleTID;
									
									SET @saleDue = @saleDue - @allocAmount;
									SET @unAllocAmount = @unAllocAmount - @allocAmount

									UPDATE @tblPaymentForReallocationTransactions
									SET amountAvailable = @unAllocAmount
									WHERE paymentTransactionID = @minPaymentTID;

									IF @saleDue = 0
										BREAK;
									
									SELECT @minPaymentTID = MIN(paymentTransactionID) 
										FROM @tblPaymentForReallocationTransactions
										WHERE paymentTransactionID > @minPaymentTID
										AND amountAvailable > 0;
								END
							END
							
							SELECT @minSaleTID = MIN(saleTransactionID) FROM @tblSales WHERE saleTransactionID > @minSaleTID;		
						END
					COMMIT TRAN;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.recordedSuccess = 1>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local) />
			<cfset local.strResponse.response = "Failed to allocate payment to sale." />
		</cfcatch>
		</cftry>
		<cfif local.recordedSuccess is 1>
			<cfquery name="local.updateOrder" datasource="#application.dsn.membercentral.dsn#">
				update dbo.ref_collectedFees
				set orderCompleted = 1,
					dateLastUpdated = getDate()
				where orderNumber = <cfqueryparam value="#local.ordernumber#" cfsqltype="cf_sql_varchar" />
			</cfquery>	
			<cfset local.strResponse.success = true>
		<cfelse>
			<cfset local.strResponse.response = "Failed to allocate payment to sale." />
		</cfif>
		
		<cfreturn local.strResponse>	
	</cffunction>

	<cffunction name="sendReferralReceiptEmail" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="paymentObj" type="struct" required="yes">
		<cfargument name="rfpData" type="any" required="yes">
		<cfargument name="useMID" type="numeric" required="yes">
		<cfargument name="isOfflinePayment" type="boolean" required="yes">

		<cfset var local = structNew()>
		<cfset local.referralSettings = getReferralSettings(siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfset local.refEmailRecipient = local.referralSettings.emailRecipient>
		<cfif len(local.refEmailRecipient)>
			<cfset local.sendFrom = trim(listFirst(local.refEmailRecipient,";"))>
		<cfelse>
			<cfset local.sendFrom = arguments.event.getValue('mc_siteInfo.supportProviderEmail')>
		</cfif>
		
		<cftry>
			<cfset local.mailCollectionFrom = arguments.event.getValue('mc_siteInfo.networkEmailFrom')>
			<cfset local.mailCollectionReplyTo = local.sendFrom>
			<cfset local.mailCollectionSubject = "#arguments.event.getValue('mc_siteInfo.siteName')# Referral Fee Payment">		

			<cfset local.qryPurchaser = application.objMember.getMemberInfo(arguments.useMID)>
			<cfset local.memberName = local.qryPurchaser.firstName & " "	& local.qryPurchaser.lastName>
			<cfset local.qryPurchaserAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberid=local.qryPurchaser.memberID)>
			<cfset local.pageStyle = "width:550px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;">
			<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;">

			<cfsavecontent variable="local.emailContent">
				<cfoutput>
				<div style="#local.pageStyle#">
					<cfif arguments.isOfflinePayment>
						<div style="font-size:13px;color:##333333;font-weight:bold;">Thank you for your submission!</div>
						<div style="padding-top:10px">
							Here is your receipt for your submission. Please print a copy of this receipt and mail in with your payment.								
						</div>
						<cfif len(arguments.paymentObj.paymentInstructions)>
							<br/>#arguments.paymentObj.paymentInstructions#
						</cfif>
					<cfelse>
						<div style="font-size:13px;color:##333333;font-weight:bold;">Thank you for your payment!</div>
						<div style="padding-top:10px">
							Here is your receipt for your payment. Please print a copy for your records.
						</div>
					</cfif>
					<br/>
					<table cellpadding="4" cellspacing="0" width="95%" border="0">
					<tr> 
						<td style="#local.tdStyle#;border-bottom:1px solid ##666;" width="*"><b>Referral ID##</b></td>
						<td style="#local.tdStyle#;border-bottom:1px solid ##666;" width="*"><b>Client Name</b></td>
						<td style="#local.tdStyle#;border-bottom:1px solid ##666;" width="80"><b>Referral Date</b></td>
						<td style="#local.tdStyle#;border-bottom:1px solid ##666;" width="80" align="right"><b>Amount <cfif arguments.isOfflinePayment>Due<cfelse>Paid</cfif></b></td>
					</tr>
					<cfif isArray(arguments.rfpData)>
						<cfloop index="i" from="1" to="#arrayLen(arguments.rfpData)#">
							<tr valign="top">
								<td style="#local.tdStyle#"><a href="#arguments.event.getValue('mainurl')#&ra=editCase&clientReferralID=#arguments.rfpData[i].CLIENTREFERRALID#">#arguments.rfpData[i].CLIENTREFERRALID#</a></td>
								<td style="#local.tdStyle#">#arguments.rfpData[i].CLIENTNAME#</td>
								<td style="#local.tdStyle#">#arguments.rfpData[i].clientReferralDate#</td>
								<td style="#local.tdStyle#" align="right">#arguments.rfpData[i].totalFeesDue#</td>
							</tr>
						</cfloop>
					<cfelseif isQuery(arguments.rfpData)>
						<cfloop query="arguments.rfpData">
							<tr valign="top">
								<td style="#local.tdStyle#"><a href="#arguments.event.getValue('mainurl')#&ra=editCase&clientReferralID=#arguments.rfpData.CLIENTREFERRALID#">#arguments.rfpData.CLIENTREFERRALID#</a></td>
								<td style="#local.tdStyle#">#arguments.rfpData.CLIENTNAME#</td>
								<td style="#local.tdStyle#">#arguments.rfpData.clientReferralDate#</td>
								<td style="#local.tdStyle#" align="right">#arguments.rfpData.totalFeesDue#</td>
							</tr>
						</cfloop>
					</cfif>
					<tr>
						<td colspan="3">&nbsp;</td>
					</tr>
					<cfif val(arguments.paymentObj.processingFees)>
						<tr> 
							<td></td>
							<td></td>
							<td style="#local.tdStyle#;border-top:1px solid ##666;" align="right"><b>Sub Total:</b></td>
							<td style="#local.tdStyle#;border-top:1px solid ##666;" align="right" nowrap><b>#dollarFormat(arguments.paymentObj.totalPaid - arguments.paymentObj.processingFees)#</b></td>
						</tr>
						<tr> 
							<td></td>
							<td></td>
							<td style="#local.tdStyle#;border-top:1px solid ##666;" align="right"><b>#arguments.paymentObj.processingFeeLabel#:</b></td>
							<td style="#local.tdStyle#;border-top:1px solid ##666;" align="right" nowrap><b>#dollarFormat(arguments.paymentObj.processingFees)#</b></td>
						</tr>
					</cfif>
					<tr> 
						<td></td>
						<td></td>
						<td style="#local.tdStyle#;border-top:1px solid ##666;" align="right"><b>Total:</b></td>
						<td style="#local.tdStyle#;border-top:1px solid ##666;" align="right" nowrap><b>#dollarFormat(arguments.paymentObj.totalPaid)#</b></td>
					</tr>
					</table>
					<br/>		
				</div>
				<div style="clear:both"></div>
				</cfoutput>
			</cfsavecontent>

			<cfset local.sendEmailStr = CreateObject("component","model.admin.referrals.referrals").sendReferralEmail(referralID=local.referralSettings.referralID,
				siteid=arguments.event.getValue('mc_siteinfo.siteid'),
				recordedByMemberID=local.qryPurchaser.memberID,
				messageContent=local.emailContent,
				contentTitle="#arguments.event.getValue('mc_siteinfo.sitename')# Payment Confirmation",
				fromName=local.referralSettings.title,
				fromEmail=local.mailCollectionFrom,
				replyToEmail=local.mailCollectionReplyTo,
				subject=local.mailCollectionSubject,
				refMemberID=local.qryPurchaser.memberID,
				refMemberName=local.memberName,
				refMemberEmail=local.refEmailRecipient,
				messageTypeCode="REFERRALMEMBER")>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="getInvoiceProfileIDsFromClientReferralIDs" access="private" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="referralID" type="numeric" required="true">
		<cfargument name="clientReferralIDList" type="string" required="true">

		<cfset qryInvoiceProfiles = "">

		<cfquery name="qryInvoiceProfiles" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int, @referralID int;
			SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
			SET @referralID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.referralID#">;

			SELECT DISTINCT i.invoiceProfileID
			FROM dbo.ref_clients AS c
			INNER JOIN dbo.ref_clientReferrals AS cr ON cr.referralID = @referralID 
				AND cr.clientID = c.clientID
				AND cr.clientReferralID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#arguments.clientReferralIDList#">)
			CROSS APPLY dbo.fn_ref_clientTransactions (c.clientID,@orgID) AS ct
			INNER JOIN dbo.tr_transactionSales AS ts ON ts.orgID = @orgID 
				AND ts.transactionID = ct.transactionID
			INNER JOIN dbo.tr_invoiceTransactions AS it ON it.orgID = @orgID
				AND it.transactionID = ts.transactionID
			inner join dbo.tr_invoices AS i ON i.orgID = @orgID
				AND i.invoiceID = it.invoiceID
			WHERE (ts.cache_amountAfterAdjustment - ts.cache_activePaymentAllocatedAmount) > 0;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn valueList(qryInvoiceProfiles.invoiceProfileID)>
	</cffunction>

	<cffunction name="getPaymentInstructions" access="private" output="false" returntype="string">
		<cfargument name="profileID" type="numeric" required="true">

		<cfset var qryPaymentGateway = "">

		<cfquery name="qryPaymentGateway" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT mpContent.rawContent as paymentInstructions
			FROM dbo.mp_profiles as pr
			INNER JOIN dbo.mp_gateways as ga on ga.gatewayid = pr.gatewayID
			OUTER APPLY dbo.fn_getContent(pr.paymentInstructionsContentID,1) as mpContent
			WHERE pr.profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryPaymentGateway.paymentInstructions>
	</cffunction>

	<cffunction name="getReferralCenterInstruction" access="private" output="false" returntype="string">
		<cfargument name="referralID" type="numeric" required="true">

		<cfset qryInvoiceProfiles = "">

		<cfquery name="qryInvoiceProfiles" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int, @referralID int;
			SET @referralID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.referralID#">;

			select feReferralCenterInstructionForMemberContent.rawContent as feReferralCenterInstructionForMemberContent
			from dbo.ref_referrals as r
			cross apply dbo.fn_getContent(r.feReferralCenterInstructionForMemberID,1) as feReferralCenterInstructionForMemberContent
			where r.referralID = @referralID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn valueList(qryInvoiceProfiles.feReferralCenterInstructionForMemberContent)>
	</cffunction>

	<cffunction name="savePhoneNumber" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="MFAPhNo" type="string" required="yes">
		<cfargument name="messagingServiceID" type="numeric" required="yes">
		<cfargument name="referralID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.objSMSTemplate = createObject("component","model.admin.common.modules.smsTemplate.smsTemplate")>
		<cfset local.qryPreferredLanguage = getPreferredLanguage(
			arguments.referralID
		)>
		<cftry>
			<cfquery name="local.qryMemberPhone" datasource="#application.dsn.platformMail.dsn#">
				SET NOCOUNT ON;

				DECLARE @usageID  INT, @usageTypeID INT, @siteID INT, @phoneNumber VARCHAR(60), @enteredByMemberID INT, @newEntry INT, @messagingServiceID INT, @referenceType VARCHAR(60);
				SET @phoneNumber = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.MFAPhNo#">;
				SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">;
				SET @messagingServiceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.messagingServiceID#">;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
				SET @referenceType = 'REFMEMBERS';


				SELECT TOP 1 @usageID = su.usageID, @usageTypeID = ut.usageTypeID 
				FROM dbo.sms_subuserMessagingServiceUsages su
				INNER JOIN dbo.sms_usageTypes ut
				ON ut.usageTypeID = su.usageTypeID 
					AND ut.usageTypeCode = @referenceType
				WHERE su.siteID  =  @siteID;	
					
				IF NOT EXISTS (SELECT participantID FROM dbo.sms_subuserMessagingServiceUsageParticipants 
					WHERE phoneNumberE164 = @phoneNumber AND messagingServiceID = @messagingServiceID
					AND siteID = @siteID AND usageID = @usageID
				) 
				BEGIN		

					SET @newEntry = 1;
				END ELSE
					SET @newEntry = 0;
					
				SELECT @newEntry as newEntry;
			</cfquery>
			
			<cfif local.qryMemberPhone.newEntry eq 1>
				<cfset local.returnObj = local.objSMSTemplate.saveSMSUsageParticipants(
					arguments.mcproxy_siteID,
					'REFMEMBERS',
					arguments.MFAPhNo,
					local.qryPreferredLanguage.languageID,
					local.objSMSTemplate.getMessagingServiceRecipientStatusIDByName('optinViaWeb'),
					'REFCLIENT',
					session.cfcuser.memberdata.memberid
				)>
				<cfset local.returnStruct.success = true>
				<cfset local.returnStruct.msg = ''>
			<cfelse>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.msg = 'Entered Phone Number already exist'>
			</cfif>
			
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local) />
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.msg = 'Failed to add new number.'>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updatePhoneNumber" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="MFAPhNo" type="string" required="yes">		
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="messagingServiceID" type="numeric" required="yes">
		<cfargument name="referralID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>

		<cftry>
			<cfquery name="local.qryMemberPhone" datasource="#application.dsn.platformMail.dsn#">
				SET NOCOUNT ON;

				DECLARE @usageID INT, @usageTypeID INT, @messagingServiceID INT, @phoneNumber VARCHAR(60), @enteredByMemberID INT, @newEntry INT,
				 @participantID INT, @referenceType VARCHAR(60), @siteID INT;
				SET @phoneNumber = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.MFAPhNo#">;
				SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">;
				SET @participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">;
				SET @messagingServiceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.messagingServiceID#">;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
				SET @referenceType = 'REFMEMBERS';


				SELECT TOP 1 @usageID = su.usageID, @usageTypeID = ut.usageTypeID 
				FROM dbo.sms_subuserMessagingServiceUsages su
				INNER JOIN dbo.sms_usageTypes ut
				ON ut.usageTypeID = su.usageTypeID 
					AND ut.usageTypeCode = @referenceType
				WHERE su.siteID  =  @siteID;	
					
				IF NOT EXISTS (SELECT participantID FROM dbo.sms_subuserMessagingServiceUsageParticipants 
					WHERE phoneNumberE164 = @phoneNumber AND messagingServiceID = @messagingServiceID
					AND siteID = @siteID AND usageID = @usageID AND participantID <> @participantID
				) 
				BEGIN		
					UPDATE dbo.sms_subuserMessagingServiceUsageParticipants 
					SET phoneNumberE164 = @phoneNumber ,
					dateUpdated = getdate()
					WHERE messagingServiceID = @messagingServiceID
					AND siteID = @siteID AND usageID = @usageID AND participantID = @participantID

					SET @newEntry = 1;
				END ELSE
					SET @newEntry = 0;
					
				SELECT @newEntry as newEntry;
			</cfquery>

			<cfif local.qryMemberPhone.newEntry eq 1>
				<cfset local.returnStruct.success = true>
				<cfset local.returnStruct.msg = ''>
			<cfelse>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.msg = 'Entered Phone Number already exist'>
			</cfif>
			
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.msg = 'Failed to update the number.'>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="deletePhoneNumber" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="messagingServiceID" type="numeric" required="yes">
		<cfargument name="phoneNumberE164" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>

		<cftry>
			<cfquery name="local.qryMemberPhone" datasource="#application.dsn.platformMail.dsn#">
				DECLARE @participantID INT, @messagingServiceID INT, @enteredByMemberID INT,
				@siteID INT, @phoneNumberE164 VARCHAR(60);

				SET @participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">;
				SET @messagingServiceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.messagingServiceID#">;
				SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
				SET @phoneNumberE164 = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.phoneNumberE164#">;

				DELETE FROM dbo.sms_subuserMessagingServiceUsageParticipantHistory
				WHERE messagingServiceID = @messagingServiceID
				AND siteID = @siteID AND 
				phoneNumberE164 = @phoneNumberE164;

				DELETE FROM dbo.sms_subuserMessagingServiceUsageParticipants
				WHERE messagingServiceID = @messagingServiceID
				AND siteID = @siteID AND participantID = @participantID 
				AND phoneNumberE164 = @phoneNumberE164;
				
				SELECT @participantID;
			</cfquery>

			<cfset local.returnStruct.success = true>
			<cfset local.returnStruct.msg = ''>
			
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.msg = 'Failed to delete number.'>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getReferralFeePercent" access="public" output="false" returntype="struct">
		<cfargument name="panelID" type="numeric" required="yes">
		<cfargument name="referralID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>

		<cftry>
			<cfquery name="local.qryFeeStructure" datasource="#application.dsn.membercentral.dsn#">
				SELECT TOP 1 rfsl.cumulativeFeePercent
				FROM dbo.ref_feeStructures rfs
				INNER JOIN dbo.ref_feeStructureLevels rfsl ON rfsl.feeStructureID = rfs.feeStructureID
				WHERE panelID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.panelID#">
				AND referralID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.referralID#">
				AND rfs.status = 'A';
			</cfquery>
			<cfset local.returnStruct.cumulativeFeePercent = local.qryFeeStructure.cumulativeFeePercent>			
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.returnStruct.cumulativeFeePercent = ''>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getFeeCalculatedData" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="memberid" type="numeric" required="yes">
		<cfargument name="clientReferralID" type="numeric" required="yes">
		<cfargument name="clientID" type="numeric" required="yes">
		<cfargument name="clientParentID" type="numeric" required="false">
		<cfargument name="referralID" type="numeric" required="yes">
		<cfargument name="collectedFee" type="numeric" required="yes">
		<cfargument name="filingFee" type="numeric" required="yes">
		<cfargument name="caseID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals") />

		<cfset local.qryGetClientData = local.objAdminReferrals.getClient(clientReferralID=arguments.clientReferralID)>
		<cfset local.panelID = 0>
		<cfset local.thisClientID = val(local.qryGetClientData.rootClientID) ? val(local.qryGetClientData.rootClientID) : val(local.qryGetClientData.clientID)>
		<cfset local.qryGetReferralFilterData = local.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID)>
		<cfloop query="local.qryGetReferralFilterData">
			<cfif local.qryGetReferralFilterData.elementID EQ 'panelid1'>
				<cfset local.panelID = local.qryGetReferralFilterData.elementValue>
				<cfbreak>
			</cfif>
		</cfloop>
		
		<cfset local.feesStruct = structNew() />
		<cfset local.feesStruct = getPanelFees(memberid=arguments.memberid, clientID=arguments.clientID, clientParentID=arguments.clientParentID, qryGetReferralFilterData=local.qryGetReferralFilterData)>
		<cfset local.referralFeePercent = local.feesStruct.referralFeePercent>
		<cfset local.referralAmount = local.feesStruct.referralAmount>
		<cfset local.referralID = arguments.referralID>
		<cfset local.collectedFee = val(arguments.collectedFee) />
		<cfset local.filingFee = numberFormat(val(arguments.filingFee), "0.00") />
		<cfset local.qryGetCaseData = getCaseData(val(arguments.caseID)) />
		<cfset local.qryGetPanelData = local.objAdminReferrals.getPanelByID(panelID=local.panelID) />
		<cfset local.qryReferralSettings = getReferralSettings(arguments.mcproxy_siteID) />
		<cfset local.deductFilingFee = local.qryReferralSettings.deductFilingFee />
		<cfset local.caseID = val(arguments.caseID)>

		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcUser.memberData.IdentifiedAsMemberID gt 0>
			<cfset local.useMID = session.cfcUser.memberData.IdentifiedAsMemberID>
		<cfelse>
			<cfset local.useMID = session.cfcuser.memberdata.memberid>
		</cfif>		
		
		<cfset local.amountDue = local.feesStruct.referralAmount />
		<cfif not val(local.amountDue)>
			<cfif val(local.deductFilingFee) and val(local.filingFee) and local.collectedFee gt local.filingFee>
				<cfset local.collectedFee = local.collectedFee - val(local.filingFee)>
			</cfif>
			<cfset local.amountDue = calculateFees(referralID=local.referralID, panelID=local.panelID, caseID=local.caseID, collectedFee=local.collectedFee, 
										qryGetCaseData=local.qryGetCaseData, qryGetPanelData=local.qryGetPanelData)>
		<cfelse>
			<cfif val(local.deductFilingFee) and val(local.filingFee) and local.amountDue gt local.filingFee>
				<cfset local.amountDue = local.amountDue - val(local.filingFee)>
			</cfif>			
		</cfif>	


		<cfscript>
			local.data = StructNew();
			local.data.success = true;
			local.data.amountDue = local.amountDue;
		</cfscript>

		<cfreturn local.data>
	</cffunction>
	<cffunction name="getPreferredLanguage" access="public" output="false" returntype="query">
		<cfargument name="referralID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryLanguage" datasource="#application.dsn.membercentral.dsn#">

			DECLARE @languageID INT;

			IF NOT EXISTS (SELECT TOP 1 languageID
				FROM dbo.ref_languages 
				WHERE isDefault = 1 
				AND referralID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.referralID#">
			) 
			BEGIN				
				SELECT TOP 1 languageID
				FROM dbo.ref_languages 
				WHERE languageName = 'English'
				AND referralID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.referralID#">;
				
			END ELSE
			BEGIN
				SELECT TOP 1 languageID
				FROM dbo.ref_languages 
				WHERE isDefault = 1 
				AND referralID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.referralID#">
			END				
		</cfquery>
		
		<cfreturn local.qryLanguage>
	</cffunction>



</cfcomponent>