<cfcomponent output="no">
	<cffunction name="doEventReg" access="package" output="false" returntype="string">
		<cfargument name="event" type="any">
		<cfargument name="settings" type="struct" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.rc = arguments.event.getCollection()>
		<cfset arguments.event.paramValue('regAction','')>
		<cfset paramRegSession(item=arguments.event.getValue('item',0))>
		<cfset local.objLocator = CreateObject("component","model.system.user.accountLocater")>
		<cfset arguments.event.setValue('mainregurl','#arguments.event.getValue('mainurl')#&panel=reg&item=#arguments.event.getValue('item',0)#')>
		<cfset arguments.event.setValue('locatorurl','#arguments.event.getValue('locatorurl')#&panel=reg&item=#arguments.event.getValue('item',0)#&regaction=locator')>
		<cfset arguments.event.setValue('newregacctformurl','/?event=cms.showResource&resID=#arguments.settings.siteResourceID#&panel=reg&item=#arguments.event.getValue('item',0)#&regaction=newregacct&mode=stream')>
		<cfset arguments.event.setValue('swregresourceurl','?event=cms.showResource&resID=#arguments.settings.siteResourceID#&panel=reg&item=#arguments.event.getValue('item',0)#&mode=stream')>
		<cfset local.viewDirectory = arguments.event.getValue('viewDirectory', 'default')>

		<!--- check for participation in each format and get branding --->
		<cfset local.qrySWP = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(local.rc.mc_siteInfo.siteCode).qryAssociation>

		<cfset local.SWAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin', siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfset local.programType = arguments.event.valueExists('item') ? GetToken(arguments.event.getValue('item'),1,'-') : ''>
		<cfset local.programID = arguments.event.valueExists('item') ? GetToken(arguments.event.getValue('item'),2,'-') : 0>

		<cfif (NOT isValid("integer",local.programID) or local.programID lt 0)>
			<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
		</cfif>

		<cfset local.swReg = application.mcCacheManager.sessionGetValue(keyname="swReg", defaultValue={})>

		<cfif local.programID GT 0 AND NOT listFindNoCase('locator,usemid,newregacct,newacct,editRegCartItem,remreg,cancel,reset,checkout',arguments.event.getValue('regAction'))>
			<!--- get program details --->
			<cfswitch expression="#local.programType#">
				<cfcase value="SWL">
					<cfset local.seminarID = local.programID>

					<!--- step 1 dont base on any user specific info --->
					<cfif local.swReg.currentReg.currentStep is 1>
						<cfset local.strSeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarForCatalog(seminarID=local.seminarID, 
							catalogOrgCode=local.rc.mc_siteinfo.sitecode, billingState='', billingZip='', depoMemberDataID=0, memberID=0)>
					<cfelse>
						<cfset local.strSeminar = CreateObject("component","model.seminarweb.SWLiveSeminars").getSeminarForCatalog(seminarID=local.seminarID, 
							catalogOrgCode=local.rc.mc_siteinfo.sitecode, billingState=local.swReg.currentReg.s3.billingstate, billingZip=local.swReg.currentReg.s3.zipForTax, 
							depoMemberDataID=local.swReg.currentReg.s1.depomemberdataid, memberID=local.swReg.currentReg.s1.memberid)>
					</cfif>
					<cfset local.strProgram = { "qryProgram":local.strSeminar.qrySeminar, "qryPrices":local.strSeminar.qrySeminarPricesBuyNow, "programName":local.strSeminar.qrySeminar.seminarName,
						"programSubTitle":local.strSeminar.qrySeminar.seminarSubTitle, "offerCredit":local.strSeminar.qrySeminar.offerCredit, 
						"showUSD":local.strSeminar.qrySeminar.showUSD, "programType":local.programType, "programLabel":"program" }>
				</cfcase>
				<cfcase value="SWOD">
					<cfset local.seminarID = local.programID>

					<!--- step 1 dont base on any user specific info --->
					<cfif local.swReg.currentReg.currentStep is 1>
						<cfset local.strSeminar = CreateObject("component","model.seminarweb.SWODSeminars").getSeminarForCatalog(seminarID=local.seminarID, 
							catalogOrgCode=local.rc.mc_siteinfo.sitecode, billingState='', billingZip='', depoMemberDataID=0, memberID=0)>
					<cfelse>
						<cfset local.strSeminar = CreateObject("component","model.seminarweb.SWODSeminars").getSeminarForCatalog(seminarID=local.seminarID, 
							catalogOrgCode=local.rc.mc_siteinfo.sitecode, billingState=local.swReg.currentReg.s3.billingstate, billingZip=local.swReg.currentReg.s3.zipForTax, 
							depoMemberDataID=local.swReg.currentReg.s1.depomemberdataid, memberID=local.swReg.currentReg.s1.memberID)>
					</cfif>
					<cfset local.strProgram = { "qryProgram":local.strSeminar.qrySeminar, "qryPrices":local.strSeminar.qrySeminarPricesBuyNow, "programName":local.strSeminar.qrySeminar.seminarName,
						"programSubTitle":local.strSeminar.qrySeminar.seminarSubTitle, "offerCredit":1, "showUSD":local.strSeminar.qrySeminar.showUSD, 
						"programType":local.programType, "programLabel":"program" }>
				</cfcase>
				<cfcase value="SWB">
					<cfset local.bundleID = local.programID>

					<!--- step 1 dont base on any user specific info --->
					<cfif local.swReg.currentReg.currentStep is 1>
						<cfset local.strBundle = CreateObject("component","model.seminarweb.SWBundles").getBundleForCatalog(bundleID=local.bundleID, 
							catalogOrgCode=local.rc.mc_siteinfo.sitecode, billingState='', billingZip='', MCMemberID=0)>
						<cfif local.strBundle.qryBundle.recordcount is 0 or local.strBundle.qryBundle.status neq 'A'>
							<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
						<cfelse>
							<cfset local.qryItems = CreateObject("component","model.seminarweb.SWBundles").getBundledItemsForCatalog(local.strBundle.qryBundle.bundleID,0)>
						</cfif>
					<cfelse>
						<cfset local.strBundle = CreateObject("component","model.seminarweb.SWBundles").getBundleForCatalog(bundleID=local.bundleID, 
							catalogOrgCode=local.rc.mc_siteinfo.sitecode, billingState=local.swReg.currentReg.s3.billingstate, billingZip=local.swReg.currentReg.s3.zipForTax, 
							MCMemberID=local.swReg.currentReg.s1.memberID)>
						<cfif local.strBundle.qryBundle.recordcount is 0 or local.strBundle.qryBundle.status neq 'A'>
							<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
						<cfelse>
							<cfset local.qryItems = CreateObject("component","model.seminarweb.SWBundles").getBundledItemsForCatalog(local.strBundle.qryBundle.bundleID,local.swReg.currentReg.s1.memberID)>
						</cfif>
						<cfset local.qryEnrolledItems = CreateObject("component","model.seminarweb.SWBundles").getEnrolledItems(local.swReg.currentReg.s1.memberID,valueList(local.qryItems.contentID))>

						<cfset var currentRegMemberID = local.swReg.currentReg.s1.memberID>
						<cfset var bundleType = local.strBundle.qryBundle.isSWOD ? "SWOD" : "SWL">
						<cfset var arrIncludedSeminarsList = arrayMap(valueArray(local.qryItems,"contentID"), function(thisSeminarID) {
							return "#bundleType#-#thisSeminarID#";
						})>
						<cfset local.arrMatchedSeminarsInCart = arrayFilter(local.swReg.regCart, function(cartItem) {
							return cartItem.s1.memberID eq currentRegMemberID and arrayFindNoCase(arrIncludedSeminarsList, cartItem.item);
						})>
					</cfif>
					<cfset local.strProgram = { "qryProgram":local.strBundle.qryBundle, "qryPrices":local.strBundle.qryBundlePricesBuyNow, "programName":local.strBundle.qryBundle.bundleName,
						"programSubTitle":local.strBundle.qryBundle.bundleSubTitle, "offerCredit":0, "showUSD":local.strBundle.qryBundle.showUSD, 
						"programType":local.programType, "programLabel":"bundle" }>
				</cfcase>
				<cfdefaultcase>
					<cfsavecontent variable="local.data">
						<cfoutput>Invalid Program.</cfoutput>
					</cfsavecontent>
					<cfreturn local.data>
				</cfdefaultcase>
			</cfswitch>
		</cfif>

		<!--- switch depending on regAction --->
		<cfswitch expression="#arguments.event.getValue('regAction')#">

			<!--- locator --->
			<cfcase value="locator">
				<cfset local.returnStruct = getIdentifiedRegistrants(event=arguments.event, programID=local.programID, programType=local.programType, settings=arguments.settings, SWAdminSiteResourceID=local.SWAdminSiteResourceID)>
				<cfreturn serializeJSON(local.returnStruct)>
			</cfcase>

			<!--- usemid (selected a member from step 1) --->
			<cfcase value="usemid">
				<cfset local.swReg.currentReg = initRegStruct(arguments.event.getValue('item',0))>
				
				<cfif len(trim(arguments.event.getValue('mid','')))>
					<cfif NOT IsNumeric(trim(arguments.event.getValue('mid','')))>
						<cflocation url="/?pg=semwebCatalog" addtoken="false">
					</cfif>
				</cfif>
				<cfset arguments.event.paramValue('mid',int(val(arguments.event.getValue('mid',0))))>

				<cfif arguments.event.getValue('mid') gt 0 AND NOT isMemberInPendingReg(item=arguments.event.getValue('item'), mid=arguments.event.getValue('mid'), swReg=local.swReg)>
					<cfif application.objMember.getMemberInfo(arguments.event.getValue('mid')).recordCount>
						<!--- get depomemberdataid if there is one. otherwise create depoaccount but link only via mcmemberidtemp --->
						<cfset local.TrialSmithAllowedRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Site", functionName="TrialSmithAllowed")>
						<cfquery name="local.qryLinkToDepoMemberDataID" datasource="#application.dsn.membercentral.dsn#">
							SET XACT_ABORT, NOCOUNT ON;
							BEGIN TRY

								DECLARE @memberID int = <cfqueryparam value="#arguments.event.getValue('mid')#" cfsqltype="CF_SQL_INTEGER">,
									@siteID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteID')#" cfsqltype="CF_SQL_INTEGER">,
									@statsSessionID int = <cfqueryparam value="#session.cfcuser.statsSessionID#" cfsqltype="CF_SQL_INTEGER">,
									@depomemberdataid int, @TrialSmithMemberID int, @TrialSmithAllowedRFID int;
								SET @TrialSmithAllowedRFID = <cfqueryparam value="#local.TrialSmithAllowedRFID#" cfsqltype="CF_SQL_INTEGER">;

								EXEC dbo.ams_getTLASITESDepoMemberDataIDByMemberID @memberID=@memberID, @siteID=@siteID, @depomemberdataid=@depomemberdataid OUTPUT;

								IF @depomemberdataid = 0 BEGIN
									EXEC dbo.ams_createDepoTLASITESAccount @siteID=@siteID, @memberID=@memberID, @statsSessionID=@statsSessionID, 
										@TrialSmithAllowedFID=@TrialSmithAllowedRFID, @depomemberdataid=@depomemberdataid OUTPUT, 
										@TrialSmithMemberID=@TrialSmithMemberID OUTPUT;
									IF @depomemberdataid > 0
										UPDATE trialsmith.dbo.depomemberdata
										SET MCmemberIDtemp = @memberID
										WHERE depomemberdataid = @depomemberdataid;
								END

								SELECT @depomemberdataid AS depomemberdataid;

							END TRY
							BEGIN CATCH
								IF @@trancount > 0 ROLLBACK TRANSACTION;
								EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
							END CATCH
						</cfquery>

						<cfset local.swReg.currentReg.s1.memberid = arguments.event.getValue('mid')>
						<cfset local.swReg.currentReg.s1.depomemberdataid = local.qryLinkToDepoMemberDataID.depomemberdataid>

						<cfset local.swReg.currentReg.currentStep = 2>
						<cfset updateRegCache(swReg=local.swReg)>
					</cfif>
					<cflocation url="#arguments.event.getValue('mainregurl')#" addtoken="no">
				<cfelse>
					<cflocation url="#arguments.event.getValue('mainurl')#&panel=showCart" addtoken="no">
				</cfif>
			</cfcase>

			<!--- new reg account --->
			<cfcase value="newregacct">
				<cfset addRegLog(programID:local.programID, programType:local.programType, strLog:{ "mode":"newRegAcct" })>
				<cfset local.qryRegFieldsetID = getSWRegFieldsetID(siteResourceID=local.SWAdminSiteResourceID, area='SWRegNewAcct')>
				<cfsavecontent variable="local.data">
					<cfinclude template="/views/semWebCatalog/#local.viewDirectory#/semwebReg_step1_newAccount.cfm">
				</cfsavecontent>
				<cfreturn local.data>
			</cfcase>

			<!--- newacct (new account from step 1) --->
			<cfcase value="newacct">
				<cfset local.newMemID = local.objLocator.createAccount(event=arguments.event)>
				<cfif local.newMemID gt 0>
					<cfset addRegLog(programID:local.programID, programType:local.programType, strLog:{ "mode":"newRegAcctCreated", "mid":local.newMemID })>
					<!--- create depoaccount but link only via mcmemberidtemp --->
					<cfset local.TrialSmithAllowedRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Site", functionName="TrialSmithAllowed")>
					<cfquery name="local.qryLinkToDepoMemberDataID" datasource="#application.dsn.membercentral.dsn#">
						SET XACT_ABORT, NOCOUNT ON;
						BEGIN TRY

							DECLARE @memberID int = <cfqueryparam value="#local.newMemID#" cfsqltype="CF_SQL_INTEGER">,
								@siteID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteID')#" cfsqltype="CF_SQL_INTEGER">,
								@statsSessionID int = <cfqueryparam value="#session.cfcuser.statsSessionID#" cfsqltype="CF_SQL_INTEGER">,
								@depomemberdataid int, @TrialSmithMemberID int, @TrialSmithAllowedRFID int;
							SET @TrialSmithAllowedRFID = <cfqueryparam value="#local.TrialSmithAllowedRFID#" cfsqltype="CF_SQL_INTEGER">;

							EXEC dbo.ams_createDepoTLASITESAccount @siteID=@siteID, @memberID=@memberID, @statsSessionID=@statsSessionID, 
								@TrialSmithAllowedFID=@TrialSmithAllowedRFID, @depomemberdataid=@depomemberdataid OUTPUT, 
								@TrialSmithMemberID=@TrialSmithMemberID OUTPUT;
							IF @depomemberdataid > 0
								UPDATE trialsmith.dbo.depomemberdata
								SET MCmemberIDtemp = @memberID
								WHERE depomemberdataid = @depomemberdataid;
							
							SELECT @depomemberdataid AS depomemberdataid;

						END TRY
						BEGIN CATCH
							IF @@trancount > 0 ROLLBACK TRANSACTION;
							EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
						END CATCH
					</cfquery>					

					<cfset local.swReg.currentReg.s1.memberid = local.newMemID>
					<cfset local.swReg.currentReg.s1.depomemberdataid = local.qryLinkToDepoMemberDataID.depomemberdataid>
					<cfset local.swReg.currentReg.currentStep = 2>
					<cfset updateRegCache(swReg=local.swReg)>
					<cfsavecontent variable="local.data">
						<cfoutput>
						Please wait...
						<script language="javascript">useNA();</script>
						</cfoutput>
					</cfsavecontent>
					<cfreturn local.data>
				<cfelse>
					<cfsavecontent variable="local.data">
						<cfoutput>We were unable to create a new member account. Try again!</cfoutput>
					</cfsavecontent>
					<cfreturn local.data>
				</cfif>
			</cfcase>

			<!--- edit new reg cart item --->
			<cfcase value="editRegCartItem">
				<cfif arrayLen(local.swReg.regCart) and len(arguments.event.getTrimValue('swrk',''))>
					<cfset local.hasCartItem = false>
					<cfloop array="#local.swReg.regCart#" index="local.itemInCart">
						<cfif local.itemInCart.itemKey eq arguments.event.getTrimValue('swrk')>
							<cfset local.hasCartItem = true>
							<cfset local.swReg.currentreg = assignRegCartItemToCurrentReg(strRegCartItem=duplicate(local.itemInCart))>
							<cfset updateRegCache(swReg=local.swReg)>
							<cfbreak>
						</cfif>
					</cfloop>

					<cfif local.hasCartItem>
						<cflocation url="#arguments.event.getValue('mainregurl')#" addtoken="no">
					<cfelse>
						<cflocation url="#arguments.event.getValue('mainurl')#&panel=showCart" addtoken="no">
					</cfif>
				<cfelse>
					<cflocation url="#arguments.event.getValue('mainurl')#&panel=showCart" addtoken="no">
				</cfif>
			</cfcase>

			<!--- remreg (remove reg from cart) --->
			<cfcase value="remreg">
				<cfset local.hasCartItems = removeRegFromCart(itemkey=arguments.event.getValue('swrk'), swReg=local.swReg)>
				<cfif local.hasCartItems>
					<cflocation url="#arguments.event.getValue('mainurl')#&panel=showCart" addtoken="no">
				<cfelse>
					<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
				</cfif>
			</cfcase>

			<!--- cancel (cancel reg) --->
			<cfcase value="cancel">
				<cfset local.swReg.currentReg = initRegStruct(arguments.event.getValue('item',0))>
				<cfset updateRegCache(swReg=local.swReg)>
				
				<cfswitch expression="#GetToken(arguments.event.getValue('item'),1,'-')#">
					<cfcase value="SWL">
						<cfset local.redirectTo = "#arguments.event.getValue('mainurl')#&panel=showLive&seminarID=#GetToken(arguments.event.getValue('item'),2,'-')#">
					</cfcase>
					<cfcase value="SWOD">
						<cfset local.redirectTo = "#arguments.event.getValue('mainurl')#&panel=showSWOD&seminarID=#GetToken(arguments.event.getValue('item'),2,'-')#">
					</cfcase>
					<cfcase value="SWB">
						<cfset local.redirectTo = "#arguments.event.getValue('mainurl')#&panel=showBundle&bundleID=#GetToken(arguments.event.getValue('item'),2,'-')#">
					</cfcase>
					<cfdefaultcase>
						<cfset local.redirectTo = arguments.event.getValue('mainurl')>
					</cfdefaultcase>
				</cfswitch>

				<cflocation url="#local.redirectTo#" addtoken="no">
			</cfcase>

			<!--- register someone else --->
			<cfcase value="reset">
				<cfset addRegLog(programID:local.programID, programType:local.programType, strLog:{ "mode":"regOther" })>
				<cfset local.swReg.currentReg = initRegStruct(arguments.event.getValue('item',0))>
				<cfset updateRegCache(swReg=local.swReg)>
				<cflocation url="#arguments.event.getValue('mainregurl')#&skair=1" addtoken="false">
			</cfcase>

			<!--- saveS2 (save reg rate) --->
			<cfcase value="saves2">
				<cfset local.strReturn = saveStep2(event=arguments.event, strProgram=local.strProgram, swReg=local.swReg, qrySWP=local.qrySWP)>
				<cfreturn serializeJSON(local.strReturn)>
			</cfcase>

			<!--- saveS3 (save reg billing, email, and fields details) --->
			<cfcase value="saves3">
				<cfset local.strReturn = saveStep3(event=arguments.event, strProgram=local.strProgram, swReg=local.swReg, qrySWP=local.qrySWP)>
				<cfreturn serializeJSON(local.strReturn)>
			</cfcase>

			<!--- saveS4 (save reg credit selections) --->
			<cfcase value="saves4">
				<cfset local.strReturn = saveStep4(event=arguments.event, strProgram=local.strProgram, swReg=local.swReg, qrySWP=local.qrySWP)>
				<cfreturn serializeJSON(local.strReturn)>
			</cfcase>

			<!--- checkout --->
			<cfcase value="checkout">
				<cfset addRegLog(programID:local.programID, programType:local.programType, strLog:{ "mode":"addRegToCart", "mid":local.swReg.currentReg.s1.memberID })>
				<cfset addCurrentRegToCart(item=arguments.event.getValue('item'), qrySWP=local.qrySWP, swReg=local.swReg)>
				<cflocation url="#arguments.event.getValue('mainurl')#&panel=showCart" addtoken="no">
			</cfcase>

			<!--- show step --->
			<cfcase value="showstep">
				<!--- if program item is diff than currentreg item, clear currentReg and start at step 1 --->
				<cfif arguments.event.getValue('item') neq local.swReg.currentReg.item>
					<cfset local.swReg.currentReg = initRegStruct(arguments.event.getValue('item'))>
					<cfset updateRegCache(swReg=local.swReg)>
					<cfsavecontent variable="local.data">
						<cfoutput>
							<script type="text/javascript">
								reloadSWReg();
							</script>
						</cfoutput>
					</cfsavecontent>
					<cfreturn local.data>
				</cfif>
				
				<cfset local.skipStep = false>
				<cfset local.loadNextStep = false>
				<cfset local.finalStep = 5>

				<cfif arguments.event.valueExists('regstp') AND isSimpleValue(arguments.event.getValue('regstp')) AND int(val(arguments.event.getValue('regstp'))) LTE local.swReg.currentReg.currentStep>
					<cfset local.stepNum = int(val(arguments.event.getValue('regstp')))>
				<cfelse>
					<cfset local.stepNum = local.swReg.currentReg.currentStep>
				</cfif>

				<cfswitch expression="#local.stepNum#">
					<cfcase value="2">
						<cfset local.qryCurrentRegMember = getRegistrantInfo(mid=local.swReg.currentReg.s1.memberid)>
						<cfset local.rateID = local.swReg.currentReg.s2.rateID>
					</cfcase>
					<cfcase value="3">
						<cfset local.mainEmail = application.objMember.getMainEmail(memberID=local.swReg.currentReg.s1.memberID).email>

						<!--- set state/postal code/email --->
						<cfif local.swReg.currentReg.currentStep EQ 3>
							<cfset local.step3InfoFound = false>
							<cfloop array="#local.swReg.regCart#" index="local.thisReg">
								<cfif local.thisReg.s1.memberID is local.swReg.currentReg.s1.memberID>
									<cfset local.swReg.currentReg.s3.stateIDforTax = val(local.thisReg.s3.stateIDforTax)>
									<cfset local.swReg.currentReg.s3.zipForTax = local.thisReg.s3.zipForTax>
									<cfset local.swReg.currentReg.s3.billingState = local.thisReg.s3.billingState>
									<cfset local.swReg.currentReg.s3.email = local.thisReg.s3.email>
									<cfset local.step3InfoFound = true>
									<cfbreak>
								</cfif>
							</cfloop>
							<cfif NOT local.step3InfoFound>
								<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDepoMemberData">
									SET NOCOUNT ON;
									SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
									
									SELECT billingState, billingZip
									FROM trialsmith.dbo.depomemberdata
									WHERE depomemberdataid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.swReg.currentReg.s1.depomemberdataid#">;
									
									SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
								</cfquery>
																
								<cfset local.billingState = local.qryDepoMemberData.billingState>
								<cfif local.qrySWP.handlesOwnPayment is 1>
									<cfset local.qryTaxStateZIP = getStateZipForTax(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.swReg.currentReg.s1.memberid)>
									<cfset local.stateIDforTax = val(local.qryTaxStateZIP.stateID)>
									<cfset local.zipForTax = local.qryTaxStateZIP.postalCode>
									<cfif len(local.zipForTax) AND local.stateIDforTax>
										<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=local.zipForTax, billingStateID=local.stateIDforTax)>
										<cfif local.strBillingZip.isvalidzip>
											<cfset local.zipForTax = local.strBillingZip.billingzip>
										<cfelse>
											<cfset local.zipForTax = "">
										</cfif>
									<cfelse>
										<cfset local.zipForTax = "">
									</cfif>
								<cfelse>
									<cfset local.zipForTax = local.qryDepoMemberData.billingZip>
									<cfset local.stateIDforTax = 0>
									<cfif len(local.zipForTax) AND len(local.billingState)>
										<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=local.zipForTax, billingState=local.billingState)>
										<cfif local.strBillingZip.isvalidzip>
											<cfset local.zipForTax = local.strBillingZip.billingzip>
										<cfelse>
											<cfset local.zipForTax = "">
										</cfif>
									<cfelse>
										<cfset local.zipForTax = "">
									</cfif>
								</cfif>

								<cfset local.swReg.currentReg.s3.email = local.mainEmail>
								<cfset local.swReg.currentReg.s3.billingState = local.billingState>
								<cfset local.swReg.currentReg.s3.stateIDforTax = local.stateIDforTax>
								<cfset local.swReg.currentReg.s3.zipForTax = local.zipForTax>
							</cfif>
						</cfif>

						<cfset local.hasFields = false>
						<cfif listFindNoCase("SWL,SWOD",local.programType)>
							<cfset local.strProgramRegFields = createObject("component","model.admin.common.modules.customFields.customFields").renderResourceFields(siteID=arguments.event.getValue('mc_siteinfo.siteid'),
									viewMode="feSWReg", resourceType='SemWebCatalog', areaName='#local.programType#Enrollment', csrid=local.SWAdminSiteResourceID, detailID=local.programID,
									hideAdminOnly=1, itemType='#local.programType#RegCustom', itemID=0, trItemType='', trApplicationType='', strFieldValues=local.swReg.currentReg.s3.custom)>
							<cfset local.hasFields = local.strProgramRegFields.hasFields>
						</cfif>

						<cfif local.qrySWP.handlesOwnPayment is 1>
							<cfset local.hasBillingInfo = local.swReg.currentReg.s3.stateIDforTax GT 0 AND len(local.swReg.currentReg.s3.zipForTax)>
						<cfelse>
							<cfset local.hasBillingInfo = len(local.swReg.currentReg.s3.billingState) AND len(local.swReg.currentReg.s3.zipForTax)>
						</cfif>

						<cfif local.hasBillingInfo AND len(local.mainEmail) AND NOT local.hasFields>
							<cfset local.skipStep = true>
							<cfif local.swReg.currentReg.currentStep EQ 3>
								<cfset local.swReg.currentReg.currentStep = 4>
								<cfset local.loadNextStep = true>
							</cfif>
						</cfif>
					</cfcase>
					<cfcase value="4">
						<cfswitch expression="#local.programType#">
							<cfcase value="SWL">
								<cfif local.strProgram.offerCredit>
									<cfset local.objCredit = CreateObject("component","model.seminarweb.SWCredits")>
									<cfset local.strCredit = local.objCredit.getCreditsforSeminar(seminarID=local.programID, siteCode=arguments.event.getValue('mc_siteinfo.sitecode'))>
									<cfquery name="local.qryIDRequired" dbtype="query">
										select distinct seminarCreditID
										from [local].strCredit.qryCredit
										where isIDRequired = 1
										and creditIDText <> ''
									</cfquery>
									<cfquery name="local.qryCredRequired" dbtype="query">
										select distinct seminarCreditID
										from [local].strCredit.qryCredit
										where isCreditRequired = 1
									</cfquery>
								</cfif>
							</cfcase>
							<cfcase value="SWOD">
								<cfset local.objCredit = CreateObject("component","model.seminarweb.SWCredits")>
								<cfset local.strCredit = local.objCredit.getCreditsforSeminar(seminarID=local.programID, siteCode=arguments.event.getValue('mc_siteinfo.sitecode'))>
								<cfquery name="local.qryIDRequired" dbtype="query">
									select distinct seminarCreditID
									from [local].strCredit.qryCredit
									where isIDRequired = 1
									and creditIDText <> ''
								</cfquery>
								<cfquery name="local.qryCredRequired" dbtype="query">
									select distinct seminarCreditID
									from [local].strCredit.qryCredit
									where isCreditRequired = 1
								</cfquery>
							</cfcase>
							<cfcase value="SWB">
								<cfset local.strProgram.offerCredit = 0>
								<cfset local.objCredit = CreateObject("component","model.seminarweb.SWCredits")>
								<cfset local.strCredit = local.objCredit.getCreditsforBundle(bundleid=local.programID)>
								<cfquery name="local.qryIDRequired" dbtype="query">
									select distinct seminarCreditID
									from [local].strCredit.qryCredit
									where isIDRequired = 1
									and creditIDText <> ''
								</cfquery>
								<cfquery name="local.qryDistSeminars" dbtype="query">
									select distinct contentID, contentName, offerCertificate
									from [local].qryItems
								</cfquery>
								<cfif local.strCredit.qryCredit.recordCount>
									<cfset local.strProgram.offerCredit = 1>
								</cfif>
							</cfcase>
						</cfswitch>

						<!--- if no credit is offered, then skip it and go to the next step --->
						<cfif local.strProgram.offerCredit is 0>
							<cfset local.skipStep = true>
							<cfif local.swReg.currentReg.currentStep EQ 4>
								<cfset local.swReg.currentReg.currentStep = 5>
								<cfset local.loadNextStep = true>
							</cfif>
						<cfelse>
							<cfset local.strCreditSelections = getRegCartItemCreditSelections(swReg=local.swReg)>
						</cfif>
					</cfcase>
				</cfswitch>

				<cfsavecontent variable="local.data">
					<cfif local.loadNextStep>
						<cfoutput>
							<script type="text/javascript">
								<cfif local.finalStep EQ local.swReg.currentReg.currentStep>
									regCartCheckout();
								<cfelse>
									loadSWRegSteps(#local.swReg.currentReg.currentStep#);
								</cfif>
							</script>
						</cfoutput>
					<cfelseif NOT local.skipStep>
						<cfif local.stepNum GTE 1 AND local.stepNum LTE 4>
							<cfinclude template="/views/semWebCatalog/#local.viewDirectory#/semwebReg_step#local.stepNum#.cfm">
						<cfelse>
							<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="false">
						</cfif>
					</cfif>
				</cfsavecontent>

				<cfset updateRegCache(swReg=local.swReg)>

				<cfreturn local.data>
			</cfcase>

			<cfdefaultcase>
				<!--- if SW handles payment and has cart items --->
				<cfif local.qrySWP.handlesOwnPayment is 0 and arrayLen(local.swReg.regCart) and local.swReg.currentReg.currentStep is 1>
					<!--- limitiation: if the association does NOT take the money, all items in the cart must be for the same registrant --->
					<!--- if this item already in cart, redirect to regcart --->
					<cfif isSWItemInPendingReg(item=arguments.event.getValue('item'), swReg=local.swReg)>
						<cflocation url="#arguments.event.getValue('mainurl')#&panel=showCart" addtoken="false">
					<cfelse>
						<!--- skipping choose registrant step --->
						<cflocation url="#arguments.event.getValue("mainregurl")#&regaction=usemid&mid=#local.swReg.regCart[1].s1.memberID#" addtoken="false">
					</cfif>
				</cfif>

				<!--- if event item is diff than currentreg item, clear currentReg and start at step 1 --->
				<!--- else use currentreg/current step. --->
				<cfif arguments.event.getValue('item') neq local.swReg.currentReg.item>
					<cfset local.swReg.currentReg = initRegStruct(arguments.event.getValue('item'))>
				</cfif>
				<cfset updateRegCache(swReg=local.swReg)>
			</cfdefaultcase>
		</cfswitch>

		<!--- good program --->
		<cfswitch expression="#local.programType#">
			<cfcase value="SWL">
				<!--- allow registration? --->
				<cfif local.strSeminar.qrySeminar.recordcount is 0 
					or NOT local.strSeminar.qrySeminar.isPublished
					or now() gte local.strSeminar.qrySeminar.dateStart 
					OR NOT local.strSeminar.qrySeminar.allowRegistrants>
					<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
				</cfif>
				
				<cfset arguments.event.setValue('detailLink','#arguments.event.getValue('mainurl')#&panel=showLive&seminarID=#local.seminarID#')>
				
				<cfset local.programTitle = local.strSeminar.qrySeminar.seminarName>
				<cfset local.programSubTitle = local.strSeminar.qrySeminar.seminarSubTitle>
				<cfset local.programSponsorBy = local.rc.mc_siteinfo.sitecode neq "TS" and local.rc.mc_siteinfo.sitecode neq local.strSeminar.qrySeminar.publisherOrgCode
													? "Co-sponsored by #local.rc.mc_siteinfo.orgName#"
													: "">
				<cfset local.programLabel = "program">
			</cfcase>
			<cfcase value="SWOD">
				<!--- allow registration? --->
				<cfif local.strSeminar.qrySeminar.recordcount is 0 
					or NOT local.strSeminar.qrySeminar.isPublished
					OR NOT local.strSeminar.qrySeminar.allowRegistrants>
					<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
				</cfif>
				
				<cfset arguments.event.setValue('detailLink','#arguments.event.getValue('mainurl')#&panel=showSWOD&seminarID=#local.seminarID#')>			

				<cfset local.programTitle = local.strSeminar.qrySeminar.seminarName>
				<cfset local.programSubTitle = local.strSeminar.qrySeminar.seminarSubTitle>
				<cfset local.programSponsorBy = local.rc.mc_siteinfo.sitecode neq "TS" and local.rc.mc_siteinfo.sitecode neq local.strSeminar.qrySeminar.publisherOrgCode
													? "Co-sponsored by #local.rc.mc_siteinfo.orgName#"
													: "">
				<cfset local.programLabel = "program">
			</cfcase>
			<cfcase value="SWB">
				<!--- allow registration? --->
				<cfif local.strBundle.qryBundle.recordcount is 0 OR local.strBundle.qryBundle.status neq "A">
					<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
				</cfif>
				
				<!--- event detail link --->
				<cfset arguments.event.setValue('detailLink','#arguments.event.getValue('mainurl')#&panel=showBundle&bundleID=#local.bundleID#')>

				<cfset local.programTitle = local.strBundle.qryBundle.bundleName>
				<cfset local.programSubTitle = local.strBundle.qryBundle.bundleSubTitle>
				<cfif local.rc.mc_siteinfo.sitecode neq "TS">
					<cfsavecontent variable="local.programSponsorBy">
						<cfoutput>
							<b>Published by #local.strBundle.qryBundle.description#</b>
							<cfif local.rc.mc_siteinfo.orgname neq local.strBundle.qryBundle.description><br/>and co-sponsored by #local.rc.mc_siteinfo.orgname#</cfif>
						</cfoutput>
					</cfsavecontent>
				<cfelse>
					<cfset local.programSponsorBy = "">
				</cfif>
				<cfset local.programLabel = "bundle">
			</cfcase>
		</cfswitch>

		<cfif local.swReg.currentReg.currentStep EQ 1>
			<!--- register someone else --->
			<cfif arguments.event.valueExists('skair')>
				<cfset local.showSearchResults = false>
			
			<!--- any valid cookie/identified memberID --->
			<cfelse>
				<cfset local.forceLogin = (xmlSearch(arguments.settings.settingsXML,'string(/settings/setting[@name="forceLoginForSWReg"]/@value)') eq "true" ? true : false)>
				<cfset local.useMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
				<cfif NOT local.useMemberID AND local.forceLogin>
					<cfset application.objWebsite.forceLogin(event=arguments.event)>
				</cfif>
				<cfif NOT local.useMemberID AND cookie.keyExists("mcidme")>
					<cfset local.useMemberID = application.objPlatform.getUseMemberIDFromMCIDMECookie(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
				</cfif>
				<cfif isEligibleForSWProgramReg(orgID=arguments.event.getValue('mc_siteinfo.orgID'), sitecode=arguments.event.getValue("mc_siteinfo.siteCode"),
					programID=local.programID, programType=local.programType, memberID=val(local.useMemberID))>
					<cflocation url="#arguments.event.getValue('mainregurl')#&regaction=usemid&mid=#local.useMemberID#" addtoken="false">
				</cfif>
			</cfif>
		<cfelse>
			<cfset local.qryResultsFieldsetID = getSWRegFieldsetID(siteResourceID=local.SWAdminSiteResourceID, area='SWRegResults')>
			<cfset local.showMemberPhoto = (xmlSearch(arguments.settings.settingsXML,'string(/settings/setting[@name="showSWRegPhoto"]/@value)') EQ 1 ? true : false)>
			<cfset local.qryRegMember = getIdentifiedReg(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.swReg.currentReg.s1.memberID, fieldsetID=local.qryResultsFieldsetID.fieldsetID)>
			<cfset local.strRegMember = getRegInfoFromQry(orgID=arguments.event.getValue('mc_siteinfo.orgID'), programID=local.programID, programType=local.programType, 
					qryMembers=local.qryRegMember, fieldsetID=local.qryResultsFieldsetID.fieldsetID, regMode='addRegInfo')>
			
			<!--- invalid member --->
			<cfif structIsEmpty(local.strRegMember)>
				<cfset local.swReg.currentReg = initRegStruct(arguments.event.getValue('item'))>
				<cfset updateRegCache(swReg=local.swReg)>
				<cflocation url="#arguments.event.getValue('mainregurl')#" addtoken="false">
			</cfif>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput><cfinclude template="/views/semWebCatalog/#local.viewDirectory#/semWebReg.cfm"></cfoutput>
		</cfsavecontent>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getRegCache" access="private" output="false" returntype="struct">
		<cfargument name="item" type="string" required="yes">
		<cfscript>
			paramRegSession(item=arguments.item);
			return application.mcCacheManager.sessionGetValue(keyname="swReg");
		</cfscript>
	</cffunction>

	<cffunction name="paramRegSession" access="package" output="false" returntype="void">
		<cfargument name="item" type="string" required="yes">
		<cfscript>
			if (NOT application.mcCacheManager.sessionValueExists(keyname="swReg")) {
				var swReg = { "currentReg"=initRegStruct(item=arguments.item), "regCart"=arrayNew(1), "cartKey"=CreateUUID() };
				if (isDefined("cookie.semwebCart")) {
					swReg.cartKey = cookie.semwebCart;
					var strRegCart = deSerializeRegCart(cartkey=cookie.semwebCart);
					if (NOT structIsEmpty(strRegCart)) swReg.regCart = strRegCart.arrRegCart;
				}
				application.mcCacheManager.sessionSetValue(keyname="swReg", value=swReg);
			}
		</cfscript>
	</cffunction>

	<cffunction name="updateRegCache" access="private" output="false" returntype="void">
		<cfargument name="swReg" type="struct" required="yes">
		<cfscript>
			paramRegSession(item=0);
			serializeRegCart(cartkey=arguments.swReg.cartKey, regCart=arguments.swReg.regCart);
			application.mcCacheManager.sessionSetValue(keyname="swReg", value=arguments.swReg);
		</cfscript>
	</cffunction>

	<cffunction name="initRegStruct" access="package" output="false" returntype="struct">
		<cfargument name="item" type="string" required="yes">

		<cfset var local = StructNew()>
		
		<cfset local.strReturn = {
			"itemKey":createUUID(),
			"item":arguments.item,
			"isRegCartItem":0,
			"currentStep":1,
			"s1": { "memberID":0, "depomemberdataid":0 },
			"s2": { "rateID":0, "description":'', "price":0, "ratePriceDisplay":'', "salestax":0, "revGL":0, "programname":"", "programSubTitle":"", 
					"discount":0, "discountExcTax":0, "couponIDValidated":0, "strCoupon":{}, "strChildPrograms":{} },
			"s3": { "email":"", "stateIDForTax":0, "zipForTax":'', "billingstate":'', "custom":{} },
			"s4": { "strCredit":{} }
		}>
		
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="assignRegCartItemToCurrentReg" access="private" output="no" returntype="struct">
		<cfargument name="strRegCartItem" type="struct" required="yes">

		<cfset var local = StructNew()>
		<cfset local.strReturn = duplicate(arguments.strRegCartItem)>
		<cfset local.strReturn.isRegCartItem = 1>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getRegCartItemCreditSelections" access="private" output="no" returntype="struct">
		<cfargument name="swReg" type="struct" required="yes">

		<cfif arguments.swReg.currentReg.isRegCartItem is 1>
			<cfreturn arguments.swReg.currentReg.s4.strCredit>
		<cfelse>
			<cfreturn {}>
		</cfif>
	</cffunction>

	<cffunction name="serializeRegCart" access="private" output="no" returntype="void">
		<cfargument name="cartKey" type="string" required="yes">
		<cfargument name="regCart" type="array" required="yes">
		<cfset var local = structNew()>
		<cfwddx action="CFML2WDDX" input="#arguments.regCart#" output="local.wddxRegCart">
		<cfstoredproc datasource="#application.dsn.tlasites_seminarweb.dsn#" procedure="sw_saveRegistrationCart">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.cartKey#">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.wddxRegCart#">
		</cfstoredproc>
		<cfif NOT isDefined("cookie.SEMWEBCART") OR cookie.SEMWEBCART neq arguments.cartKey>
			<cfset local.cookieExpires = dateadd("d",1,now())>
			<cfheader name="Set-Cookie" value="SEMWEBCART=#arguments.cartKey#; expires=#getHTTPTimeString(local.cookieExpires)#; HttpOnly;"/>
		</cfif>
	</cffunction>

	<cffunction name="deSerializeRegCart" access="private" output="no" returntype="struct">
		<cfargument name="cartKey" type="string" required="yes">
		<cfset var local = structNew()>
		<cfstoredproc datasource="#application.dsn.tlasites_seminarweb.dsn#" procedure="sw_getRegistrationCart">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.cartKey#">
			<cfprocresult name="local.wddxRegCart" resultset="1">
		</cfstoredproc>
		<cfif isWddx(local.wddxRegCart.cartContents)>
			<cfwddx action="wddx2cfml" input="#local.wddxRegCart.cartContents#" output="local.arrRegCart">
			<cfreturn { "arrRegCart":local.arrRegCart }>
		<cfelse>
			<cfreturn {}>
		</cfif>
	</cffunction>

	<cffunction name="getIdentifiedRegistrants" access="private" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true">
		<cfargument name="programID" type="numeric" required="true">
		<cfargument name="programType" type="string" required="true">
		<cfargument name="settings" type="struct" required="true">
		<cfargument name="SWAdminSiteResourceID" type="numeric" required="true">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = {
			"strFilters":getRegistrantFilters(event=arguments.event),
			"showMemberPhoto": xmlSearch(arguments.settings.settingsXML,'string(/settings/setting[@name="showSWRegPhoto"]/@value)') EQ 1 ? true : false,
			"arrMembers": [],
			"success": true
		}>
	
		<!--- get result fields --->
		<cfset local.qryResultsFieldsetID = getSWRegFieldsetID(siteResourceID=arguments.SWAdminSiteResourceID, area='SWRegResults')>
	
		<cfquery name="local.qryOrgDefaultEmailTypeID" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	
			SELECT emailTypeID
			FROM dbo.ams_memberEmailTypes
			WHERE orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">
			AND emailTypeOrder = 1
	
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
	
		<!--- save search filters in AcctLocatorSearch --->
		<cfset local.lastALSearch = structNew()>
		<cfset structInsert(local.lastALSearch,'m_firstname',local.returnStruct.strFilters._swfn,true)>
		<cfset structInsert(local.lastALSearch,'m_lastname',local.returnStruct.strFilters._swln,true)>
		<cfset structInsert(local.lastALSearch,'me_#local.qryOrgDefaultEmailTypeID.emailTypeID#_email',local.returnStruct.strFilters._swem,true)>
		<cfset application.mcCacheManager.sessionSetValue(keyname='lastALSearch', value=local.lastALSearch)>
	
		<!--- log lookup form filters --->
		<cfset addRegLog(programID:arguments.programID, programType:arguments.programType, strLog:{ "mode":"regSearch", "filters": { "fn":local.returnStruct.strFilters._swfn, "ln":local.returnStruct.strFilters._swln, "me":local.returnStruct.strFilters._swem } })>
	
		<cfset local.qualifyRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SWProgramRate", functionName="qualify")>
		<cfquery name="local.qryMembers" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	
			IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL
				DROP TABLE ##tmpMembers;
			IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
				DROP TABLE ##tmp_membersForFS;
			IF OBJECT_ID('tempdb..##tmpRateQualifiedMembers') IS NOT NULL
				DROP TABLE ##tmpRateQualifiedMembers;
			CREATE TABLE ##tmpMembers (MFSAutoID int IDENTITY(1,1) not null);
			CREATE TABLE ##tmp_membersForFS (memberID int PRIMARY KEY, lastName varchar(75), firstName varchar(75), 
				memberNumber varchar(50), MCAccountStatus char(1), hasMemberPhotoThumb bit, company varchar(200),
				MCPrimaryEmail varchar(255), hasQualifiedSWRates bit, mc_row int, groupPrintID int);
			CREATE TABLE ##tmpRateQualifiedMembers (memberID int PRIMARY KEY);
	
			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">,
				@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">,
				@siteCode varchar(10) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mc_siteinfo.siteCode')#">,
				@fieldsetID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryResultsFieldsetID.fieldsetID#">,
				@fSWRegFirstName varchar(75) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.returnStruct.strFilters._swfn#">,
				@fSWRegLastName varchar(75) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.returnStruct.strFilters._swln#">,
				@fSWRegEmail varchar(255) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.returnStruct.strFilters._swem#">,
				@programID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">,
				@programType varchar(4) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.programType#">,
				@catalogParticipantID int, @isPriceBasedOnActual bit, @FID int, @outputFieldsXML xml;
	
			SELECT @catalogParticipantID = seminarWeb.dbo.fn_getParticipantIDFromOrgcode(@siteCode);
	
			SELECT @isPriceBasedOnActual = isPriceBasedOnActual 
			FROM seminarWeb.dbo.fn_getSWProgramRateSettings(@catalogParticipantID, @programID, @programType);
	
			set @FID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qualifyRateRFID#">;
	
			-- get fieldset data
			INSERT INTO ##tmp_membersForFS (memberID, lastName, firstName, memberNumber, MCAccountStatus, hasMemberPhotoThumb, company, MCPrimaryEmail, hasQualifiedSWRates, groupPrintID)
			SELECT DISTINCT m.memberID, m.lastName, m.firstName, m.memberNumber, m.status, m.hasMemberPhotoThumb, m.company, me.email, 0, m.groupPrintID
			FROM dbo.ams_members AS m
			INNER JOIN dbo.ams_memberEmails AS me ON me.orgID = @orgID
				AND me.memberID = m.memberID
			INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID 
				and metag.memberID = me.memberID
				AND metag.emailTypeID = me.emailTypeID
			INNER JOIN dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = @orgID
				AND metagt.emailTagTypeID = metag.emailTagTypeID
				AND metagt.emailTagType = 'Primary'
			WHERE m.orgID = @orgID
			AND m.status = 'A'
			AND (m.firstName = @fSWRegFirstName OR m.firstName = @fSWRegLastName
				OR m.lastName = @fSWRegLastName OR m.lastName = @fSWRegFirstName
				OR me.email = @fSWRegEmail);
	
			-- top matches ranked by:
			-- exact match of email, first name, and last name
			UPDATE ##tmp_membersForFS
			SET mc_row = 1
			WHERE firstName = @fSWRegFirstName
			AND lastName = @fSWRegLastName
			AND MCPrimaryEmail = @fSWRegEmail
			AND mc_row IS NULL;
	
			-- exact match of email; first name, last name match but first name equals last name, last name equals first name
			UPDATE ##tmp_membersForFS
			SET mc_row = 2
			WHERE firstName = @fSWRegLastName
			AND lastName = @fSWRegFirstName
			AND MCPrimaryEmail = @fSWRegEmail
			AND mc_row IS NULL;
	
			-- exact match of email, and last name
			UPDATE ##tmp_membersForFS
			SET mc_row = 3
			WHERE lastName = @fSWRegLastName
			AND MCPrimaryEmail = @fSWRegEmail
			AND mc_row IS NULL;
	
			-- exact match of email, and last name matches first name
			UPDATE ##tmp_membersForFS
			SET mc_row = 4
			WHERE lastName = @fSWRegFirstName
			AND MCPrimaryEmail = @fSWRegEmail
			AND mc_row IS NULL;
	
			-- exact match of email
			UPDATE ##tmp_membersForFS
			SET mc_row = 5
			WHERE MCPrimaryEmail = @fSWRegEmail
			AND mc_row IS NULL;
	
			-- exact match of first name and last name
			UPDATE ##tmp_membersForFS
			SET mc_row = 6
			WHERE firstName = @fSWRegFirstName
			AND lastName = @fSWRegLastName
			AND mc_row IS NULL;
	
			-- when first name matches last name and last name matches first name
			UPDATE ##tmp_membersForFS
			SET mc_row = 7
			WHERE firstName = @fSWRegLastName
			AND lastName = @fSWRegFirstName
			AND mc_row IS NULL;
	
			-- remove the matches that don't fall into any of those categories
			DELETE FROM ##tmp_membersForFS
			WHERE mc_row IS NULL;
	
			-- get members having qualified rates
			IF @isPriceBasedOnActual = 1 BEGIN
				<cfswitch expression="#GetToken(arguments.event.getValue('item'),1,'-')#">
					<cfcase value="SWL,SWOD" delimiters=",">
						INSERT INTO ##tmpRateQualifiedMembers (memberID)
						select distinct tmp.memberID
						from seminarWeb.dbo.tblSeminarsAndRates as r
						inner join dbo.cms_siteResources as sr on sr.siteID = @siteID and sr.siteResourceID = r.siteResourceID 
							and sr.siteResourceStatusID = 1
						cross apply ##tmp_membersForFS as tmp
						inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteID = @siteID
							and srfrp.siteresourceID = r.siteResourceID
							and srfrp.functionID = @FID
						inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
							and gprp.rightPrintID = srfrp.rightPrintID
							and gprp.groupPrintID = tmp.groupPrintID
						where r.seminarID = @programID
						and r.isHidden = 0
						and r.participantID = @catalogParticipantID;
					</cfcase>
					<cfcase value="SWB">
						INSERT INTO ##tmpRateQualifiedMembers (memberID)
						select distinct tmp.memberID
						from seminarWeb.dbo.tblBundlesAndRates as r
						inner join dbo.cms_siteResources as sr on sr.siteID = @siteID and sr.siteResourceID = r.siteResourceID 
							and sr.siteResourceStatusID = 1
						cross apply ##tmp_membersForFS as tmp
						inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteID = @siteID
							and srfrp.siteresourceID = r.siteResourceID
							and srfrp.functionID = @FID
						inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
							and gprp.rightPrintID = srfrp.rightPrintID
							and gprp.groupPrintID = tmp.groupPrintID
						where r.bundleID = @programID
						and r.isHidden = 0
						and r.participantID = @catalogParticipantID;
					</cfcase>
				</cfswitch>
	
				UPDATE tmp
				SET tmp.hasQualifiedSWRates = 1
				FROM ##tmp_membersForFS as tmp
				INNER JOIN ##tmpRateQualifiedMembers AS rqm ON rqm.memberID = tmp.memberID;
			END ELSE 
				UPDATE ##tmp_membersForFS
				SET hasQualifiedSWRates = 1;
			
			-- update mc_row
			UPDATE tmp
			SET tmp.mc_row = tmpOrd.newMCRowNum
			FROM ##tmp_membersForFS AS tmp
			INNER JOIN (
				SELECT memberID, ROW_NUMBER () OVER (ORDER BY mc_row ASC, hasQualifiedSWRates DESC, firstName ASC, lastName ASC, company ASC) AS newMCRowNum
				FROM ##tmp_membersForFS
			) tmpOrd ON tmpOrd.memberID = tmp.memberID;
	
			-- include top 5
			DELETE FROM ##tmp_membersForFS
			WHERE mc_row NOT BETWEEN 1 and 5;

			EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='m_lastname,m_firstname,m_membernumber,m_company',
					@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmp_membersForFS', @membersResultTableName='##tmpMembers',
					@linkedMembers=0, @mode='view', @outputFieldsXML=@outputFieldsXML OUTPUT;

			-- return @outputFieldsXML in only the first row to reduce the query payload
			SELECT *, CASE WHEN mc_row = 1 THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
			FROM (
				SELECT tmp.lastName, tmp.firstName, tmp.memberNumber, tmp.MCAccountStatus, tmp.hasMemberPhotoThumb, tmp.company, 
					tmp.hasQualifiedSWRates, tmpM.*, tmp.mc_row
				FROM ##tmp_membersForFS as tmp
				INNER JOIN ##tmpMembers as tmpM on tmpM.memberID = tmp.memberID
			) AS tmp
			ORDER BY mc_row;
			
			IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL
				DROP TABLE ##tmpMembers;
			IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
				DROP TABLE ##tmp_membersForFS;
			IF OBJECT_ID('tempdb..##tmpRateQualifiedMembers') IS NOT NULL
				DROP TABLE ##tmpRateQualifiedMembers;
	
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
	
		<cfset local.returnStruct.arrMembers = getRegInfoFromQry(orgID=arguments.event.getValue('mc_siteinfo.orgID'), programID=arguments.programID, 
			programType=arguments.programType, qryMembers=local.qryMembers, fieldsetID=local.qryResultsFieldsetID.fieldsetID, regMode="regSearch")>
	
		<!--- log search results --->
		<cfif local.qryMembers.recordCount>
			<cfset local.qualifiedMemberIDList = local.returnStruct.arrMembers.reduce(
				function(prev,item){ 
					var mid = arguments.item.swRegStatus EQ 'Y' ? arguments.item.memberID : 0;
					return mid GT 0 ? listAppend(arguments.prev,mid) : arguments.prev;
				}, ''
			)>
			<cfset addRegLog(programID:arguments.programID, programType:arguments.programType, strLog:{ "mode":"regSearchResults", "results": { "midlist":valueList(local.qryMembers.memberID), "qlfymidlist":local.qualifiedMemberIDList } })>
		<cfelse>
			<cfset addRegLog(programID:arguments.programID, programType:arguments.programType, strLog:{ "mode":"regSearchResults", "results": { "midlist":"" } })>
		</cfif>
	
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getRegistrantFilters" access="private" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true">
	
		<cfset local.strFilters = {
			"_swfn": arguments.event.getTrimValue('fSWRegFirstName',''),
			"_swln": arguments.event.getTrimValue('fSWRegLastName',''),
			"_swem": arguments.event.getTrimValue('fSWRegEmail','')
		}>
	
		<cfreturn local.strFilters>
	</cffunction>
	
	<cffunction name="getSWRegFieldsetID" access="private" output="false" returntype="query">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		<cfargument name="area" type="string" required="yes">
		
		<cfset var qryFieldSet = "">
		
		<cfquery name="qryFieldSet" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	
			SELECT TOP 1 fieldsetID, useID
			FROM dbo.ams_memberFieldUsage
			WHERE siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
			AND area = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.area#">
			ORDER BY fieldsetorder, fieldsetID;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryFieldSet>
	</cffunction>

	<cffunction name="getIdentifiedReg" access="private" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="fieldsetID" type="numeric" required="true">

		<cfset var qryMember = "">

		<cfquery name="qryMember" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpMember') IS NOT NULL
				DROP TABLE ##tmpMember;
			IF OBJECT_ID('tempdb..##tmp_memberForFS') IS NOT NULL
				DROP TABLE ##tmp_memberForFS;
			CREATE TABLE ##tmpMember (MFSAutoID int IDENTITY(1,1) not null);
			CREATE TABLE ##tmp_memberForFS (memberID int PRIMARY KEY, lastName varchar(75), firstName varchar(75), 
				memberNumber varchar(50), MCAccountStatus char(1), hasMemberPhotoThumb bit, company varchar(200));

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,
				@memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">,
				@fieldsetID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldsetID#">,
				@outputFieldsXML xml;

			-- get fieldset data
			INSERT INTO ##tmp_memberForFS (memberID, lastName, firstName, memberNumber, MCAccountStatus, hasMemberPhotoThumb, company)
			SELECT memberID, lastName, firstName, memberNumber, status, hasMemberPhotoThumb, company
			FROM dbo.ams_members
			WHERE orgID = @orgID
			AND memberID = @memberID
			AND status = 'A';

			EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='m_lastname,m_firstname,m_membernumber,m_company',
					@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmp_memberForFS', @membersResultTableName='##tmpMember',
					@linkedMembers=0, @mode='view', @outputFieldsXML=@outputFieldsXML OUTPUT;

			SELECT tmp.lastName, tmp.firstName, tmp.memberNumber, tmp.MCAccountStatus, tmp.hasMemberPhotoThumb, tmp.company, tmpM.*,
				@outputFieldsXML AS mc_outputFieldsXML
			FROM ##tmp_memberForFS as tmp
			INNER JOIN ##tmpMember as tmpM on tmpM.memberID = tmp.memberID;
			
			IF OBJECT_ID('tempdb..##tmpMember') IS NOT NULL
				DROP TABLE ##tmpMember;
			IF OBJECT_ID('tempdb..##tmp_memberForFS') IS NOT NULL
				DROP TABLE ##tmp_memberForFS;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryMember>
	</cffunction>
	
	<cffunction name="getRegInfoFromQry" access="private" output="false" returntype="any">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="programID" type="numeric" required="true">
		<cfargument name="programType" type="string" required="true">
		<cfargument name="qryMembers" type="query" required="true">
		<cfargument name="fieldsetID" type="numeric" required="true">
		<cfargument name="regMode" type="string" required="true" hint="addRegInfo,regSearch">
	
		<cfset var local = structNew()>
		<cfset local.arrMembers = []>
	
		<cfif arguments.qryMembers.recordCount>
			<cfset local.xmlResultFields = arguments.qryMembers.mc_outputFieldsXML[1]>
			<cfset local.qryOutputFields = CreateObject("component","model.system.platform.memberFieldsets").getOutputFieldsFromXML(outputFieldsXML=local.xmlResultFields)>
			<cfset local.RecordTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_recordtypeid']")>
			<cfset local.memberTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_membertypeid']")>
			<cfset local.memberStatusInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_status']")>
			<cfset local.LastLoginDateInFS = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,17)='ml_datelastlogin_']")>

			<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.orgID, includeTags=1)>
			<cfset local.strOrgAddressTypes = structNew()>
			<cfloop query="local.orgAddressTypes">
				<cfif local.orgAddressTypes.isTag is 1>
					<cfset local.strOrgAddressTypes["t#local.orgAddressTypes.addressTypeID#"] = local.orgAddressTypes.addressType>
				<cfelse>
					<cfset local.strOrgAddressTypes[local.orgAddressTypes.addressTypeID] = local.orgAddressTypes.addressType>
				</cfif>
			</cfloop>
			<cfset local.mc_combinedAddresses = structNew()>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,3)='mp_' or substring(@fieldCode,1,4)='mat_' or substring(@fieldCode,1,4)='mpt_']")>
			<cfloop array="#local.tmp#" index="local.thisField">
				<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
				<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
					<cfset local.strKey = "t#local.thisATID#">
				<cfelse>
					<cfset local.strKey = local.thisATID>
				</cfif>
				<cfif NOT StructKeyExists(local.mc_combinedAddresses,local.strKey)>
					<cfset local.mc_combinedAddresses[local.strKey] = { addr='', type=local.strOrgAddressTypes[local.strKey] } >
				</cfif>
			</cfloop>

			<!--- remove fields from qryOutputFields that are handled manually --->
			<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
				SELECT *
				FROM [local].qryOutputFields
				WHERE fieldcodeSect NOT IN ('mc','m','ma','mat','mp','mpt')
			</cfquery>
	
			<cfloop query="arguments.qryMembers">
				<cfset local.tmpStr = structNew()>
	
				<cfset local.tmpStr['memberID'] = arguments.qryMembers.memberID>
				<cfset local.tmpStr['company'] = arguments.qryMembers.company>
				<cfset local.tmpStr['mcaccountstatus'] = arguments.qryMembers.MCAccountStatus>
				<cfset local.tmpStr['hasPhoto'] = arguments.qryMembers.hasMemberPhotoThumb>
				<cfif arguments.qryMembers.hasMemberPhotoThumb>
					<cfset local.tmpStr['memberphoto'] = arguments.qryMembers.membernumber & ".jpg">
				<cfelse>
					<cfset local.tmpStr['memberphoto'] = "">
				</cfif>

				<cfset local.tmpStr['mc_combinedName'] = arguments.qryMembers['Extended Name'][arguments.qryMembers.currentrow]>
	
				<!--- combine address fields if there are any --->
				<cfset local.thisMem_mc_combinedAddresses = duplicate(local.mc_combinedAddresses)>
				<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
					<cfsavecontent variable="local.thisATFull">
						<cfoutput>
						<cfif left(local.thisATID,1) eq "t">
							<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_">
							<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_">
						<cfelse>
							<cfset local.MAfcPrefix = "ma_#local.thisATID#_">
							<cfset local.MPfcPrefix = "mp_#local.thisATID#_">
						</cfif>
	
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>
						<cfif arrayLen(local.tmp) is 1 and len(arguments.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow])>#arguments.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow]#<br/> </cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
						<cfif arrayLen(local.tmp) is 1 and len(arguments.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow])>#arguments.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow]#<br/> </cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
						<cfif arrayLen(local.tmp) is 1 and len(arguments.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow])>#arguments.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow]#<br/></cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
						<cfif arrayLen(local.tmp) is 1 and len(arguments.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow])>#arguments.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow]#</cfif>
						<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
						<cfif arrayLen(local.tmp2) is 1 and len(arguments.qryMembers[local.tmp2[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow])>, #arguments.qryMembers[local.tmp2[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow]# </cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
						<cfif arrayLen(local.tmp) is 1 and len(arguments.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow])> #arguments.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow]#<br/></cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#county']")>
						<cfif arrayLen(local.tmp) is 1 and len(arguments.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow])>#arguments.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow]# County<br/></cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#country']")>
						<cfif arrayLen(local.tmp) is 1 and len(arguments.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow])> #arguments.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow]#<br/></cfif>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,#len(local.MPfcPrefix)#)='#local.MPfcPrefix#']")>
						<cfloop array="#local.tmp#" index="local.thisPT">
							<cfif len(arguments.qryMembers[local.thisPT.xmlAttributes.FieldLabel][arguments.qryMembers.currentrow])>
								<div>#local.thisPT.xmlAttributes.FieldLabel#: #arguments.qryMembers[local.thisPT.xmlAttributes.FieldLabel][arguments.qryMembers.currentrow]#</div>
							</cfif>
						</cfloop>
						</cfoutput>
					</cfsavecontent>
					<cfset local.thisATfull = trim(replace(replace(rereplace(local.thisATFull,'[\r\n\t]','','ALL'),'  ',' ','ALL'),' ,',',','ALL'))>
					<cfif left(local.thisATfull,2) eq ", ">
						<cfset local.thisATfull = right(local.thisATfull,len(local.thisATfull)-2)>
					</cfif>
					<cfif len(local.thisATfull)>
						<cfset local.thisMem_mc_combinedAddresses[local.thisATID]['addr'] = local.thisATfull>
					<cfelse>
						<cfset structDelete(local.thisMem_mc_combinedAddresses,local.thisATID,false)>
					</cfif>
				</cfloop>
	
				<!--- get recordtype if available --->
				<cfif arrayLen(local.RecordTypeInFS) is 1 and len(arguments.qryMembers[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow])>
					<cfset local.tmpStr['mc_recordType'] = arguments.qryMembers[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow]>
				<cfelse>
					<cfset local.tmpStr['mc_recordType'] = "">
				</cfif>
				
				<!--- get membertypeid if available --->
				<cfif arrayLen(local.memberTypeInFS) is 1 and len(arguments.qryMembers[local.memberTypeInFS[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow])>
					<cfset local.tmpStr['mc_memberType'] = arguments.qryMembers[local.memberTypeInFS[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow]>
				<cfelse>
					<cfset local.tmpStr['mc_memberType'] = "">
				</cfif>
				
				<!--- get status if available --->
				<cfif arrayLen(local.memberStatusInFS) is 1 and len(arguments.qryMembers[local.memberStatusInFS[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow])>
					<cfset local.tmpStr['mc_memberStatus'] = arguments.qryMembers[local.memberStatusInFS[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow]>
				<cfelse>
					<cfset local.tmpStr['mc_memberStatus'] = "">
				</cfif>
	
				<!--- get last login date if available --->
				<cfif arrayLen(local.LastLoginDateInFS) is 1>
					<cfif len(arguments.qryMembers[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow])>
						<cfset local.tmpStr['mc_lastlogin'] = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: #DateFormat(arguments.qryMembers[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][arguments.qryMembers.currentrow],"m/d/yy")#'>
					<cfelse>
						<cfset local.tmpStr['mc_lastlogin'] = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: <span class="dim"><i>none</i></span>'>
					</cfif>
				<cfelse>
					<cfset local.tmpStr['mc_lastlogin'] = "">
				</cfif>				
	
				<cfif StructCount(local.thisMem_mc_combinedAddresses)>
					<cfsavecontent variable="local.thisMemCombinedAddress">
						<cfoutput>
						<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
							<div><b style="font-size:95%; margin-right:15px;">#local.thisMem_mc_combinedAddresses[local.thisATID]['type']#</b><br/>#local.thisMem_mc_combinedAddresses[local.thisATID]['addr']#</div>
						</cfloop>
						</cfoutput>
					</cfsavecontent>
					<cfset local.tmpStr['mc_combinedAddresses'] = rereplace(replace(replace(local.thisMemCombinedAddress,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
				<cfelse>
					<cfset local.tmpStr['mc_combinedAddresses'] = "">
				</cfif>
	
				<cfif local.qryOutputFieldsForLoop.recordCount>
					<cfsavecontent variable="local.thisMemExtraInfo">
						<cfoutput>
						<cfloop query="local.qryOutputFieldsForLoop">
							<cfset local.currValue = arguments.qryMembers[local.qryOutputFieldsForLoop.fieldLabel][arguments.qryMembers.currentrow]>
							<cfif len(local.currValue)>
								<div>
									#htmlEditFormat(local.qryOutputFieldsForLoop.fieldLabel)#: 
									<cfif left(local.qryOutputFieldsForLoop.fieldCode,13) eq 'acct_balance_'>
										#dollarFormat(local.currValue)#
									<cfelse>
										<cfswitch expression="#local.qryOutputFieldsForLoop.dataTypeCode#">
											<cfcase value="DATE">
												#dateFormat(local.currValue,"m/d/yyyy")#
											</cfcase>
											<cfcase value="STRING,DECIMAL2,INTEGER">
												#local.currValue#
											</cfcase>
											<cfcase value="BIT">
												#YesNoFormat(local.currValue)#
											</cfcase>
										</cfswitch>
									</cfif>
								</div>
							</cfif>
						</cfloop>
						</cfoutput>
					</cfsavecontent>
					<cfset local.tmpStr['mc_extraInfo'] = rereplace(replace(replace(local.thisMemExtraInfo,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
				<cfelse>
					<cfset local.tmpStr['mc_extraInfo'] = "">
				</cfif>
	
				<cfif arguments.regMode EQ 'regSearch'>
					<cfif isMemberInPendingReg(item="#arguments.programType#-#arguments.programID#", mid=local.tmpStr.memberID)>
						<cfset local.tmpStr['swRegStatus'] = 'NC'>
					<cfelseif isMemberRegisteredForProgram(mid=local.tmpStr.memberID, item="#arguments.programType#-#arguments.programID#")>
						<cfset local.tmpStr['swRegStatus'] = 'NA'>
					<cfelseif NOT arguments.qryMembers.hasQualifiedSWRates>
						<cfset local.tmpStr['swRegStatus'] = 'NQ'>
					<cfelse>
						<cfset local.tmpStr['swRegStatus'] = 'Y'>
					</cfif>
				</cfif>
	
				<cfset arrayAppend(local.arrMembers,local.tmpStr)>
			</cfloop>
		</cfif>
	
		<cfif arguments.regMode EQ 'regSearch'>
			<cfreturn local.arrMembers>
		<cfelseif listFindNoCase('addRegInfo',arguments.regMode)>
			<cfreturn arrayLen(local.arrMembers) EQ 1 ? local.arrMembers[1] : {}>
		</cfif>
	</cffunction>

	<cffunction name="isEligibleForSWProgramReg" access="private" output="false" returntype="boolean">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="sitecode" type="string" required="false">
		<cfargument name="programID" type="numeric" required="true">
		<cfargument name="programType" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
	
		<cfset var isEligibile = false>
	
		<cfif arguments.memberID>
			<cfif isMemberInPendingReg(item="#arguments.programType#-#arguments.programID#", mid=arguments.memberID)>
				<cfset isEligibile = false>
			<cfelseif isMemberRegisteredForProgram(mid=arguments.memberID, item="#arguments.programType#-#arguments.programID#")>
				<cfset isEligibile = false>
			<cfelseif NOT doesMemberQualifyForAnyRates(mid=arguments.memberID, programID=arguments.programID, programType=arguments.programType, sitecode=arguments.sitecode)>
				<cfset isEligibile = false>
			<cfelseif NOT application.objUser.isSuperUser(cfcuser=session.cfcuser) OR (application.objUser.isSuperUser(cfcuser=session.cfcuser) AND arguments.sitecode EQ 'MC')>
				<cfset isEligibile = true>
			</cfif>
		</cfif>
	
		<cfreturn isEligibile>
	</cffunction>
	
	<cffunction name="doesMemberQualifyForAnyRates" access="package" output="no" returntype="boolean">
		<cfargument name="mid" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="true">
		<cfargument name="programType" type="string" required="true">
		<cfargument name="sitecode" type="string" required="false">
	
		<cfset var local = {}>
	
		<cfswitch expression="#arguments.programType#">
			<cfcase value="SWL">
				<cfset local.procName = "swl_getPricesForBuyNow">
			</cfcase>
			<cfcase value="SWOD">
				<cfset local.procName = "swod_getPricesForBuyNow">
			</cfcase>
			<cfcase value="SWB">
				<cfset local.procName = "swb_getPricesForBuyNow">
			</cfcase>
		</cfswitch>
	
		<cfstoredproc procedure="#local.procName#" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.sitecode#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mid#">
			<cfprocresult name="local.qrySWRegRates" resultset="1">
		</cfstoredproc>
	
		<cfreturn local.qrySWRegRates.recordcount gt 0>
	</cffunction>

	<cffunction name="removeRegFromCart" access="private" output="no" returntype="boolean">
		<cfargument name="itemkey" type="string" required="yes">
		<cfargument name="swReg" type="struct" required="yes">
		
		<cfset var local = StructNew()>
		<cfset local.swReg = duplicate(arguments.swReg)>
		
		<cfloop from="1" to="#arraylen(local.swReg.regCart)#" index="local.thisRegItemIdx">
			<cfif local.swReg.regCart[local.thisRegItemIdx].itemKey eq arguments.itemKey>
				<cfset arrayDeleteAt(local.swReg.regCart,local.thisRegItemIdx)>
				<cfbreak>
			</cfif>
		</cfloop>

		<cfset updateRegCache(swReg=local.swReg)>

		<cfreturn arrayLen(local.swReg.regCart)>
	</cffunction>

	<cffunction name="isMemberInPendingReg" access="private" output="no" returntype="boolean">
		<cfargument name="item" type="string" required="yes">
		<cfargument name="mid" type="numeric" required="yes">
		<cfargument name="swReg" type="struct" required="no">
		
		<cfset var local = structNew()>
		<cfset local.inCart = false>

		<cfif arguments.keyExists("swReg")>
			<cfset local.swReg = duplicate(arguments.swReg)>
		<cfelse>
			<cfset local.swReg = getRegCache(item=arguments.item)>
		</cfif>
		
		<cfloop array="#local.swReg.regCart#" index="local.thisReg">
			<cfif structKeyExists(local.thisReg.s1,'memberid') and local.thisReg.s1.memberid is arguments.mid>
				<cfif local.thisReg.item is arguments.item>
					<cfset local.inCart = true>
					<cfbreak>
				<cfelseif getToken(local.thisReg.item,1,'-') eq "SWB" and getToken(arguments.item,1,'-') neq "SWB">
					<cfset local.strChildPrograms = local.thisReg.s2?.strChildPrograms ?: {}>
					<cfif structKeyExists(local.strChildPrograms,arguments.item)>
						<cfset local.inCart = true>
						<cfbreak>
					</cfif>
				</cfif>
			</cfif>
		</cfloop>

		<cfreturn local.inCart>
	</cffunction>

	<cffunction name="isSWItemInPendingReg" access="private" output="no" returntype="boolean">
		<cfargument name="item" type="string" required="yes">
		<cfargument name="swReg" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.inCart = false>
		<cfloop array="#arguments.swReg.regCart#" index="local.thisReg">
			<cfif local.thisReg.item is arguments.item>
				<cfset local.inCart = true>
				<cfbreak>
			<cfelseif getToken(local.thisReg.item,1,'-') eq "SWB" and getToken(arguments.item,1,'-') neq "SWB">
				<cfset local.strChildPrograms = local.thisReg.s2?.strChildPrograms ?: {}>
				<cfif structKeyExists(local.strChildPrograms,arguments.item)>
					<cfset local.inCart = true>
					<cfbreak>
				</cfif>
			</cfif>
		</cfloop>
		<cfreturn local.inCart>
	</cffunction>

	<cffunction name="swRegCartToQuery" access="public" output="no" returntype="query">
		<cfset var local = StructNew()>
		<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode)>

		<cfset local.qrySWP = createObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=local.mc_siteinfo.siteCode).qryAssociation>

		<cfset local.swReg = getRegCache(item=0)>
		
		<cfset local.qryRegCart = QueryNew("itemKey,item,programName,programSubTitle,memberid,depomemberdataid,memberName,email,amount,discount,actualAmount,rateName,isRegistered,redeemDetail,childPrograms",
									"varchar,varchar,varchar,varchar,integer,integer,varchar,varchar,double,double,double,varchar,integer,varchar,varchar")>

		<cfif NOT arraylen(local.swReg.regCart)>
			<cfreturn local.qryRegCart>
		</cfif>

		<cfset local.redeemedCouponID = 0>
		<cfset local.strCoupon = StructNew()>
		
		<cfloop array="#local.swReg.regCart#" index="local.thisReg">
			<cfif NOT structIsEmpty(local.thisReg.s2.strCoupon)>
				<cfset local.redeemedCouponID = local.thisReg.s2.strCoupon.couponID>
				<cfset local.strCoupon = duplicate(local.thisReg.s2.strCoupon)>
				<cfbreak>
			</cfif>
		</cfloop>

		<!--- check for qualified cart items  --->
		<cfif local.redeemedCouponID gt 0 and arrayLen(local.swReg.regCart) gt 1>
			<cfset local.itemsXML = "">
			<cfset local.checkCouponRedemption = false>
			
			<cfloop array="#local.swReg.regCart#" index="local.thisReg">
				<cfset local.itemsXML = local.itemsXML & '<item mid="#local.thisReg.s1.memberID#" rateid="#local.thisReg.s2.rateID#" itemtype="#getToken(local.thisReg.item,1,'-')#" />'>
				<cfif NOT local.checkCouponRedemption AND local.thisReg.s2.couponIDValidated NEQ local.redeemedCouponID AND local.thisReg.s2.price gt 0>
					<cfset local.checkCouponRedemption = true>
				</cfif>
			</cfloop>

			<cfxml variable="local.cartItemsXML">
				<cfoutput>
					<cart>
						<cfif local.checkCouponRedemption>#local.itemsXML#</cfif>
					</cart>
				</cfoutput>
			</cfxml>
			<!--- remove the <xml> tag, specifically the encoding. --->
			<cfset local.cartItemsXML = replaceNoCase(toString(local.cartItemsXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

			<cfif local.checkCouponRedemption>
				<cfscript>
				var sqlParams = {
					siteID = { value=local.mc_siteinfo.siteID, cfsqltype="CF_SQL_INTEGER" },
					applicationType = { value="SeminarWeb", cfsqltype="CF_SQL_VARCHAR" },
					cartItemsXML = { value=local.cartItemsXML, cfsqltype="CF_SQL_LONGVARCHAR" },
					couponCode = { value=local.strCoupon.couponCode, cfsqltype="CF_SQL_VARCHAR" }
				};

				// using query to exec proc here due to xml output and luceeV5.2 not supporting xml queryparam
				var qryValidCoupon = queryExecute("
					SET NOCOUNT ON;
					
					DECLARE @siteID int = :siteID, @applicationType varchar(20) = :applicationType, @cartItemsXML xml = :cartItemsXML,
						@couponCode varchar(15) = :couponCode, @couponID int, @couponMessage varchar(200), @qualifiedCartItemsXML xml;

					EXEC dbo.tr_isValidCouponCode @siteID=@siteID, @applicationType=@applicationType, @cartItemsXML=@cartItemsXML, 
						@couponCode=@couponCode, @couponID=@couponID OUTPUT, @couponMessage=@couponMessage OUTPUT, 
						@qualifiedCartItemsXML=@qualifiedCartItemsXML OUTPUT;

					SELECT @couponID AS couponID, @couponMessage AS couponMessage, @qualifiedCartItemsXML AS qualifiedCartItemsXML;
					", 
					sqlParams, { datasource="#application.dsn.membercentral.dsn#" }
				);

				local.couponID = val(qryValidCoupon.couponID);
				local.couponResponse = qryValidCoupon.couponMessage;
				local.qualifiedCartItemsXML = qryValidCoupon.qualifiedCartItemsXML;
				</cfscript>

				<cfif arrayLen(XMLSearch(local.qualifiedCartItemsXML,"/cart/item"))>
					<cfloop array="#local.swReg.regCart#" index="local.thisReg">
						<cfset local.thisCartItemQualified = arrayLen(XMLSearch(local.qualifiedCartItemsXML,"/cart/item[@mid='#local.thisReg.s1.memberID#'][@rateid='#local.thisReg.s2.rateID#'][@itemtype='#getToken(local.thisReg.item,1,'-')#']"))>

						<cfif local.thisReg.s2.couponIDValidated EQ 0>
							<cfset local.thisReg.s2.couponIDValidated = local.couponID>

							<!--- qualified cart item --->
							<cfif local.thisCartItemQualified>
								<cfset local.thisReg.s2.strCoupon = duplicate(local.strCoupon)>
							</cfif>
						
						<!--- remove unqualified cart items --->
						<cfelseif NOT structIsEmpty(local.thisReg.s2.strCoupon) AND NOT local.thisCartItemQualified>
							<cfset local.thisReg.s2.strCoupon = structNew()>
						</cfif>
					</cfloop>
				</cfif>
			</cfif>
		</cfif>

		<cfloop array="#local.swReg.regCart#" index="local.thisReg">
			<!--- promo code applied --->
			<cfif NOT structIsEmpty(local.thisReg.s2.strCoupon)>
				<!--- apply discount to rate revenue --->
				<cfset local.strRateDiscount = getAmountAfterCouponApplied(sitecode=local.mc_siteinfo.sitecode, strReg=duplicate(local.thisReg), handlesOwnPayment=local.qrySWP.handlesOwnPayment)>
				
				<cfset local.thisReg.s2.discount = local.strRateDiscount.discount>
				<cfset local.thisReg.s2.discountExcTax = local.strRateDiscount.discountExcTax>

			<!--- remove promo code applied discounts --->
			<cfelseif val(local.thisReg.s2.discount) gt 0>
				<cfset local.thisReg.s2.discount = 0>
				<cfset local.thisReg.s2.discountExcTax = 0>
			</cfif>
		</cfloop>

		<!--- finally, build query --->
		<cfloop array="#local.swReg.regCart#" index="local.thisReg">
			<cfset local.qryCurrentRegMember = getRegistrantInfo(mid=local.thisReg.s1.memberid)>
			<cfset local.thisTotal = local.thisReg.s2.price + local.thisReg.s2.salestax>

			<cfset local.thisTotal = iif(local.thisTotal lt 0,0,local.thisTotal)>
			<cfset local.thisDiscount = iif(local.thisReg.s2.discount lt 0,0,local.thisReg.s2.discount)>

			<cfif QueryAddRow(local.qryRegCart)>
				<cfset QuerySetCell(local.qryRegCart,"itemKey",local.thisReg.itemKey)>
				<cfset QuerySetCell(local.qryRegCart,"item",local.thisReg.item)>
				<cfset QuerySetCell(local.qryRegCart,"programName",local.thisReg.s2.programName)>
				<cfset QuerySetCell(local.qryRegCart,"programSubTitle",local.thisReg.s2.programSubTitle)>
				<cfset QuerySetCell(local.qryRegCart,"memberid",local.thisReg.s1.memberid)>
				<cfset QuerySetCell(local.qryRegCart,"depomemberdataid",local.thisReg.s1.depomemberdataid)>
				<cfset QuerySetCell(local.qryRegCart,"memberName","#local.qryCurrentRegMember.firstname# #local.qryCurrentRegMember.middlename# #local.qryCurrentRegMember.lastname#")>
				<cfset QuerySetCell(local.qryRegCart,"email",local.thisReg.s3.email)>
				<cfset QuerySetCell(local.qryRegCart,"amount",local.thisTotal)>
				<cfset QuerySetCell(local.qryRegCart,"discount",local.thisDiscount)>
				<cfset QuerySetCell(local.qryRegCart,"actualAmount",local.thisTotal - local.thisDiscount)>
				<cfset QuerySetCell(local.qryRegCart,"rateName",local.thisReg.s2.description)>
				<cfif getToken(local.thisReg.item,1,'-') eq "SWB">
					<cfset local.strChildPrograms = local.thisReg.s2?.strChildPrograms ?: {}>
					<cfset QuerySetCell(local.qryRegCart,"childPrograms",structKeyList(local.strChildPrograms))>
				<cfelse>
					<cfset QuerySetCell(local.qryRegCart,"childPrograms","")>
				</cfif>
				<cfset QuerySetCell(local.qryRegCart,"isRegistered",isMemberRegisteredForProgram(mid=local.thisReg.s1.memberid,item=local.thisReg.item))>
				<cfif NOT structIsEmpty(local.thisReg.s2.strCoupon)>
					<cfset QuerySetCell(local.qryRegCart,"redeemDetail",local.thisReg.s2.strCoupon.redeemDetail)>
				<cfelse>
					<cfset QuerySetCell(local.qryRegCart,"redeemDetail","")>
				</cfif>
			</cfif>
		</cfloop>

		<cfset updateRegCache(swReg=local.swReg)>

		<cfreturn local.qryRegCart>
	</cffunction>

	<cffunction name="addCurrentRegToCart" access="private" output="false" returntype="void">
		<cfargument name="item" type="string" required="yes">
		<cfargument name="qrySWP" type="query" required="yes">
		<cfargument name="swReg" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.swReg = duplicate(arguments.swReg)>

		<cfif local.swReg.currentReg.currentStep EQ 5>
			<cfset local.programType = GetToken(arguments.item,1,'-')>
			<cfset local.programID = GetToken(arguments.item,2,'-')>
			<cfswitch expression="#local.programType#">
				<cfcase value="SWL">
					<cfset local.acctCode = "7001">
				</cfcase>
				<cfcase value="SWOD">
					<cfset local.acctCode = "7000">
				</cfcase>
				<cfcase value="SWB">
					<cfset local.qryBundle = CreateObject("component","model.seminarweb.SWBundles").getBundleByBundleID(bundleID=local.programID, orgcode=arguments.qrySWP.orgCode)>
					<cfif local.qryBundle.isSWOD is 1>
						<cfset local.acctCode = "7000">
					<cfelse>
						<cfset local.acctCode = "7001">
					</cfif>
				</cfcase>
			</cfswitch>

			<!--- if user proceeds to add a bundle that already includes the program included in their cart, we will remove those individual programs --->
			<cfset local.arrCartItemsToBeRemoved = []>
			<cfif local.programType eq "SWB">
				<cfset var currentRegMemberID = local.swReg.currentReg.s1.memberID>
				<cfset var bundleType = local.qryBundle.isSWOD ? "SWOD" : "SWL">
				<cfset local.qryItems = CreateObject("component","model.seminarweb.SWBundles").getBundledItemsForCatalog(bundleID=local.programID,MCMemberID=currentRegMemberID)>
				<cfset var arrIncludedSeminarsList = arrayMap(valueArray(local.qryItems,"contentID"), function(thisSeminarID) {
					return "#bundleType#-#thisSeminarID#";
				})>
				<cfset local.arrMatchedSeminarsInCart = arrayFilter(local.swReg.regCart, function(cartItem) {
					return cartItem.s1.memberID eq currentRegMemberID and arrayFindNoCase(arrIncludedSeminarsList, cartItem.item);
				})>
				<cfset local.arrCartItemsToBeRemoved = arrayMap(local.arrMatchedSeminarsInCart, function(thisCartItem) {
					return thisCartItem.itemKey;
				})>
			</cfif>

			<cfset local.swReg = calculateTaxForRegistrant(swReg=local.swReg, acctCode=local.acctCode, qrySWP=arguments.qrySWP)>

			<cfif local.swReg.currentReg.isRegCartItem is 1>
				<cfloop from="1" to="#arrayLen(local.swReg.regCart)#" index="local.itemNum">
					<cfif local.swReg.regCart[local.itemNum].itemKey eq local.swReg.currentReg.itemKey>
						<cfset local.swReg.regCart[local.itemNum] = duplicate(local.swReg.currentReg)>
						<cfset local.swReg.regCart[local.itemNum].isRegCartItem = 0> 
						<cfbreak>
					</cfif>
				</cfloop>
			<cfelse>
				<cfset arrayAppend(local.swReg.regCart,duplicate(local.swReg.currentReg))>
			</cfif>
			<cfset local.swReg.currentReg = initRegStruct(arguments.item)>
			<cfset updateRegCache(swReg=local.swReg)>

			<cfif local.programType eq "SWB" and arrayLen(local.arrCartItemsToBeRemoved)>
				<cfloop array="#local.arrCartItemsToBeRemoved#" index="local.thisCartItemKey">
					<cfset local.newSWReg = getRegCache(item=0)>
					<cfset local.hasCartItems = removeRegFromCart(itemkey=local.thisCartItemKey, swReg=local.newSWReg)>
				</cfloop>
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="calculateTaxForRegistrant" access="private" output="false" returntype="struct">
		<cfargument name="swReg" type="struct" required="yes">
		<cfargument name="acctCode" type="string" required="yes">
		<cfargument name="qrySWP" type="query" required="yes">

		<cfset var local = structNew()>
		<cfset local.swReg = duplicate(arguments.swReg)>
		
		<cfif arguments.qrySWP.handlesOwnPayment is 1>
			<cfset local.strTax = createObject("component","model.system.platform.accounting").getTaxForUncommittedSale(saleGLAccountID=local.swReg.currentReg.s2.revGL, 
				saleAmount=local.swReg.currentReg.s2.price, transactionDate=now(), stateIDForTax=local.swReg.currentReg.s3.stateIDForTax, 
				zipForTax=local.swReg.currentReg.s3.zipForTax)>
			<cfset local.swReg.currentReg.s2.salestax = local.strTax.totalTaxAmt>
		<cfelse>
			<cfset local.swReg.currentReg.s2.salestax = getTSSalesTaxAmount(orgCode=arguments.qrySWP.orgCode, amountBilled=local.swReg.currentReg.s2.price, 
				billingState=local.swReg.currentReg.s3.billingState, billingZip=local.swReg.currentReg.s3.zipForTax, acctCode=arguments.acctCode).salestax>
		</cfif>

		<cfreturn local.swReg>
	</cffunction>
	
	<cffunction name="getRegCartTotals" access="package" output="no" returntype="query">
		<cfargument name="qryRegCart" type="query" required="no">
		<cfset var local = structNew()>
		<cfif NOT structKeyExists(arguments,"qryRegCart")>
			<cfset arguments.qryRegCart = swRegCartToQuery()>
		</cfif>
		<cfquery name="local.qryAmount" dbtype="query">
			SELECT SUM(amount) as totalAmt, SUM(discount) as totalDiscount, 
				SUM(amount) - SUM(discount) as actualTotal, MAX(redeemDetail) as redeemDetail
			FROM arguments.qryRegCart
		</cfquery>
		<cfreturn local.qryAmount>
	</cffunction>

	<cffunction name="saveStep2" access="private" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfargument name="strProgram" type="struct" required="true">
		<cfargument name="swReg" type="struct" required="true">
		<cfargument name="qrySWP" type="query" required="true">

		<cfset var local = structNew()>
		<cfset local.swReg = duplicate(arguments.swReg)>
		<cfset local.programType = GetToken(local.swReg.currentReg.item,1,'-')>
		<cfset local.programID = GetToken(local.swReg.currentReg.item,2,'-')>

		<cfset var SWRateID = int(val(arguments.event.getValue('sw_rateID',0)))>
		<!--- invalid rate or no changes --->
		<cfif NOT SWRateID OR SWRateID EQ local.swReg.currentReg.s2.rateID>
			<cfset local.strReturn = { "success":true, "totalamt":local.swReg.currentReg.s2.price, 
										"totalamtdisplay":local.swReg.currentReg.s2.ratePriceDisplay, "loadsteps":"" }>
			<cfreturn local.strReturn>
		</cfif>

		<cfset local.qrySelectedRate = arguments.strProgram.qryPrices.filter(function(row) { return arguments.row.rateID EQ SWRateID; })>

		<cfset local.swReg.currentReg.s2.rateid = int(val(local.qrySelectedRate.rateID))>
		<cfset local.swReg.currentReg.s2.description = local.qrySelectedRate.description>
		<cfset local.swReg.currentReg.s2.price = local.qrySelectedRate.price>

		<cfif arguments.qrySWP.handlesOwnPayment is 1>
			<cfset local.revenueGLAccountIDToUse = arguments.qrySWP.revenueGLAccountID>

			<cfif ListFindNoCase("SWL,SWOD",local.programType)>
				<cfset local.qryGetRateAndProgramGLAccountID = getRateandProgramGLAccountID(participantID=arguments.qrySWP.participantID, seminarID=local.programID, rateID=local.swReg.currentReg.s2.rateid)>
				<cfif val(local.qryGetRateAndProgramGLAccountID.GLAccountID) gt 0>
					<cfset local.revenueGLAccountIDToUse = local.qryGetRateAndProgramGLAccountID.GLAccountID>
				</cfif>
			<cfelseif local.programType eq 'SWB'>
				<cfset local.qryGetRateAndBundleGLAccountID = getRateandBundleGLAccountID(participantID=arguments.qrySWP.participantID, bundleID=local.programID, rateID=local.swReg.currentReg.s2.rateid)>
				<cfif val(local.qryGetRateAndBundleGLAccountID.GLAccountID) gt 0>
					<cfset local.revenueGLAccountIDToUse = local.qryGetRateAndBundleGLAccountID.GLAccountID>
				</cfif>
			</cfif>
			<cfset local.swReg.currentReg.s2.revGL = local.revenueGLAccountIDToUse>
		</cfif>

		<cfset local.swReg.currentReg.s2.programname = arguments.strProgram.programName>
		<cfset local.swReg.currentReg.s2.programSubTitle = arguments.strProgram.programSubTitle>
		<cfif local.qrySelectedRate.price eq 0>
			<cfset local.swReg.currentReg.s2.ratePriceDisplay = local.qrySelectedRate.freeRateDisplay>
		<cfelse>
			<cfset local.swReg.currentReg.s2.ratePriceDisplay = "#dollarformat(local.qrySelectedRate.price)##arguments.strProgram.showUSD ? ' USD' : ''#">
		</cfif>
		<cfif local.programType eq 'SWB'>
			<cfset local.qryItems = CreateObject("component","model.seminarweb.SWBundles").getBundledItemsForCatalog(local.programID,0)>
			<cfquery name="local.qryDistSeminars" dbtype="query">
				select distinct contentID, contentName, format
				from [local].qryItems
			</cfquery>
			<cfloop query="local.qryDistSeminars">
				<cfset local.swReg.currentReg.s2.strChildPrograms["#local.qryDistSeminars.format#-#local.qryDistSeminars.contentID#"] = local.qryDistSeminars.contentName>
			</cfloop>
		</cfif>

		<cfset local.loadSteps = "">
		<!--- load next step --->
		<cfif local.swReg.currentReg.currentStep IS 2>
			<cfset local.swReg.currentReg.currentStep = 3>
			<cfset local.loadSteps = listAppend(local.loadSteps,3)>
		</cfif>

		<cfset local.strReturn = { "success":true, "totalamt":local.qrySelectedRate.price, "totalamtdisplay":local.swReg.currentReg.s2.ratePriceDisplay, "loadsteps":local.loadSteps }>
		<cfset updateRegCache(swReg=local.swReg)>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="saveStep3" access="private" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfargument name="strProgram" type="struct" required="true">
		<cfargument name="swReg" type="struct" required="true">
		<cfargument name="qrySWP" type="query" required="true">
		
		<cfset var local = structNew()>
		<cfset local.swReg = duplicate(arguments.swReg)>
		<cfset local.programType = GetToken(local.swReg.currentReg.item,1,'-')>
		<cfset local.programID = GetToken(local.swReg.currentReg.item,2,'-')>

		<cfif listFindNoCase("SWL,SWOD",local.programType)>
			<cfset local.SWAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin', siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
			<cfset local.regCustomFieldsXML = createObject("component","model.admin.common.modules.customFields.customFields").getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
				resourceType='SemWebCatalog', areaName='#local.programType#Enrollment', csrid=local.SWAdminSiteResourceID, detailID=local.programID, hideAdminOnly=1)>
			<cfset local.regCustomFields = xmlParse(local.regCustomFieldsXML.returnXML).xmlRoot>
			
			<cfif arrayLen(local.regCustomFields.xmlChildren)>
				<cfset local.tmpStr = structNew()>
				<cfloop array="#local.regCustomFields.xmlChildren#" index="local.thisfield">
					<cfset local.tmpAtt = local.thisfield.xmlattributes>
					<cfset structInsert(local.tmpStr,local.tmpAtt.fieldID,arguments.event.getValue('cf_#local.tmpAtt.fieldID#_',''))>
				</cfloop>
				<cfset local.swReg.currentReg.s3.custom = duplicate(local.tmpStr)>
			</cfif>
		</cfif>

		<cfif arguments.qrySWP.handlesOwnPayment is 1>
			<cfif local.swReg.currentReg.s3.stateIDforTax is 0 or not len(local.swReg.currentReg.s3.zipForTax)>
				<cfset local.swReg.currentReg.s3.stateIDforTax = val(arguments.event.getValue('stateIDforTax',0))>
				<cfset local.swReg.currentReg.s3.zipForTax = arguments.event.getTrimValue('zipForTax','')>
			</cfif>

			<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=local.swReg.currentReg.s3.zipForTax, billingStateID=local.swReg.currentReg.s3.stateIDforTax)>
			<cfif local.strBillingZip.isvalidzip>
				<cfset local.swReg.currentReg.s3.zipForTax = local.strBillingZip.billingzip>
			<cfelse>
				<cfreturn { "success":false }>
			</cfif>
		<cfelse>
			<cfif NOT len(local.swReg.currentReg.s3.billingState) OR NOT len(local.swReg.currentReg.s3.zipForTax)>
				<cfset local.swReg.currentReg.s3.billingState = arguments.event.getTrimValue('billingState','')>
				<cfset local.swReg.currentReg.s3.zipForTax = arguments.event.getTrimValue('zipForTax','')>
			</cfif>
			<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=local.swReg.currentReg.s3.zipForTax, billingState=local.swReg.currentReg.s3.billingState)>
			<cfif local.strBillingZip.isvalidzip>
				<cfset local.swReg.currentReg.s3.zipForTax = local.strBillingZip.billingzip>
			<cfelse>
				<cfreturn { "success":false }>
			</cfif>
		</cfif>

		<cfif len(arguments.event.getTrimValue('regEmail','')) AND isValid("regex",arguments.event.getTrimValue('regEmail'),application.regEx.email)>
			<cfset local.swReg.currentReg.s3.email = arguments.event.getTrimValue('regEmail')>
		</cfif>

		<cfset local.loadSteps = "">
		<!--- load next step (credits) --->
		<cfif local.swReg.currentReg.currentStep IS 3>	
			<cfset local.swReg.currentReg.currentStep = 4>
			<cfset local.loadSteps = listAppend(local.loadSteps,4)>
		</cfif>

		<cfset local.strReturn = { "success":true, "loadsteps":local.loadSteps }>
		<cfset updateRegCache(swReg=local.swReg)>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="saveStep4" access="private" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfargument name="strProgram" type="struct" required="true">
		<cfargument name="swReg" type="struct" required="true">
		<cfargument name="qrySWP" type="query" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = { "success":true, "checkout":false }>
		<cfset local.swReg = duplicate(arguments.swReg)>
		<cfset local.programType = GetToken(local.swReg.currentReg.item,1,'-')>
		<cfset local.programID = GetToken(local.swReg.currentReg.item,2,'-')>

		<cfset local.strCreditSelections = StructNew()>
		<cfset local.strCreditSelections.creditlinks = arguments.event.getValue('frmcreditlink','')>
		<cfif listlen(local.strCreditSelections.creditlinks)>
			<cfloop from="1" to="#listLen(local.strCreditSelections.CreditLinks)#" index="local.i">
				<cfset local.strCreditSelections[listGetAt(local.strCreditSelections.CreditLinks,local.i)] = arguments.event.getValue('frm#listGetAt(local.strCreditSelections.CreditLinks,local.i)#ID','')>
			</cfloop>
		</cfif>
		<cfset local.swReg.currentReg.s4.strCredit = duplicate(local.strCreditSelections)>

		<cfif local.swReg.currentReg.currentStep IS 4>	
			<cfset local.swReg.currentReg.currentStep = 5>
			<cfset local.strReturn.checkout = true>
		</cfif>

		<cfset updateRegCache(swReg=local.swReg)>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="isMemberRegisteredForProgram" access="private" output="no" returntype="boolean">
		<cfargument name="mid" type="numeric" required="yes">
		<cfargument name="item" type="string" required="yes">

		<cfset var local = structNew()>

		<cfquery name="local.qryRegistered" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;

			DECLARE @memberID int, @programID int;
			SET @programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#GetToken(arguments.item,2,'-')#">;
			SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mid#">;

			<cfif ListFindNoCase("SWL,SWOD",GetToken(arguments.item,1,'-'))>
				SELECT isRegistered = CASE WHEN dbo.fn_getEnrollmentIDForSeminar(@programID, @memberID) > 0 THEN 1 ELSE 0 END;
			<cfelseif GetToken(arguments.item,1,'-') eq 'SWB'>
				SELECT isRegistered = CASE WHEN dbo.fn_getBundleOrderIDForBundle(@programID, @memberID) > 0 THEN 1 ELSE 0 END;
			</cfif>
		</cfquery>

		<cfreturn val(local.qryRegistered.isRegistered)>
	</cffunction>

	<cffunction name="getRegistrantInfo" access="private" output="no" returntype="query">
		<cfargument name="mid" type="numeric" required="yes">

		<cfset var qryMember = "">

		<cfquery name="qryMember" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT top 1 m.firstname, m.middlename, m.lastname, m.company
			FROM dbo.ams_members as m
			WHERE m.memberID = <cfqueryparam value="#arguments.mid#" cfsqltype="CF_SQL_INTEGER">
			AND m.status = 'A'
			AND m.memberID = m.activeMemberID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryMember>
	</cffunction>

	<cffunction name="getStateZipForTax" access="private" output="no" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="memberid" type="numeric" required="yes">

		<cfset var qryStateID = "">

		<cfquery name="qryStateID" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="cf_sql_integer">,
				@memberID int = <cfqueryparam value="#arguments.memberid#" cfsqltype="cf_sql_integer">;

			select ma.stateID, ma.postalCode
			from dbo.ams_memberAddresses as ma
			inner join dbo.ams_memberAddressTags as matag on matag.orgID = @orgID 
				AND matag.memberID = ma.memberID 
				AND matag.addressTypeID = ma.addressTypeID
			inner join dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID
				AND matagt.addressTagTypeID = matag.addressTagTypeID 
				AND matagt.addressTagType = 'Billing'
			where ma.orgID = @orgID
			and ma.memberid = @memberID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryStateID>
	</cffunction>

	<cffunction name="getTSSalesTaxAmount" access="private" output="false" returntype="struct">
		<cfargument name="orgCode" type="string" required="yes">
		<cfargument name="amountBilled" type="numeric" required="yes">
		<cfargument name="billingState" type="string" required="yes">
		<cfargument name="billingZip" type="string" required="yes">
		<cfargument name="acctCode" type="string" required="yes">

		<cfreturn CreateObject("component","model.admin.seminarweb.SWReg").getTSSalesTaxAmount(orgCode=arguments.orgCode, amountBilled=arguments.amountBilled,
			billingState=arguments.billingState, billingZip=arguments.billingZip, acctCode=arguments.acctCode)>
	</cffunction>

	<cffunction name="swRegCartProfiles" access="public" output="no" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="handlesOwnPayment" type="boolean" required="yes">
		<cfargument name="swReg" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset var qryProfiles = "">

		<cfif arguments.handlesOwnPayment>
			<cfset local.swReg = duplicate(arguments.swReg)>

			<!--- get all rev gls --->
			<cfset local.glIDs = "">
			<cfloop array="#local.swReg.regCart#" index="local.thisReg">
				<cfif val(local.thisReg.s2.revGL)>
					<cfset local.glIDs = listRemoveDuplicates(listAppend(local.glIDs,local.thisReg.s2.revGL))>
				</cfif>
			</cfloop>

			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryProfiles">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,
					@procFeeSupportedGatewayIDs varchar(10) = '10';	-- AuthorizeCCCIM
				DECLARE @tmpInvoiceProfiles TABLE (invoiceProfileID int PRIMARY KEY);
				DECLARE @tmpInvoiceProfileProcFeeOverrides TABLE (invoiceProfileID int, gatewayID int, enableProcessingFeeDonation bit, 
					processFeeDonationDefaultSelect bit, processFeeDonationFETitle varchar(100), processFeeDonationFEMsg varchar(800));

				<cfif listLen(local.glIDs)>
					INSERT INTO @tmpInvoiceProfiles (invoiceProfileID)
					SELECT DISTINCT gl.invoiceProfileID
					FROM dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.glIDs#">,',') AS tmp
					INNER JOIN dbo.tr_glAccounts AS gl ON gl.orgID = @orgID
						AND gl.GLAccountID = tmp.listitem;

					-- invoice profile processing fee override settings
					IF @@ROWCOUNT = 1
						INSERT INTO @tmpInvoiceProfileProcFeeOverrides (invoiceProfileID, gatewayID, enableProcessingFeeDonation, processFeeDonationDefaultSelect, processFeeDonationFETitle, processFeeDonationFEMsg)
						SELECT ip.profileID, psg.listItem, ip.enableProcessingFeeDonation, ip.processFeeDonationDefaultSelect, pfm.title, pfm.message
						FROM dbo.tr_invoiceProfiles AS ip 
						INNER JOIN @tmpInvoiceProfiles AS tmp ON tmp.invoiceProfileID = ip.profileID
						LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.messageID = ip.solicitationMessageID
						CROSS APPLY dbo.fn_intListToTableInline(@procFeeSupportedGatewayIDs,',') AS psg
						WHERE ip.orgID = @orgID;
				</cfif>

				SELECT mp.profileID, mp.profileCode, g.gatewayid, g.gatewayType, g.gatewayClass, mp.tabTitle, 
					CASE WHEN mp.enableProcessingFeeDonation = 1 AND ISNULL(tmp.enableProcessingFeeDonation,1) = 1 THEN 1 ELSE 0 END AS enableProcessingFeeDonation,
					mp.processFeeDonationFeePercent, ISNULL(tmp.processFeeDonationFETitle,pfm.title) as processFeeDonationFETitle, 
					ISNULL(tmp.processFeeDonationFEMsg,pfm.message) as processFeeDonationFEMsg, 
					ISNULL(tmp.processFeeDonationDefaultSelect,mp.processFeeDonationDefaultSelect) as processFeeDonationDefaultSelect, 
					mp.processFeeDonationRenevueGLAccountID, mp.processFeeDonationRevTransDesc, mp.processingFeeLabel, mp.processFeeOtherPaymentsFELabel,
					mp.processFeeOtherPaymentsFEDenyLabel, mp.enableApplePay, mp.enableGooglePay, mp.enableSurcharge, mp.surchargePercent, mp.surchargeRevenueGLAccountID
				FROM seminarWeb.dbo.tblParticipantMerchantProfiles as pmp
				INNER JOIN dbo.mp_profiles as mp on mp.profileid = pmp.profileid
				INNER JOIN dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
				LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.messageID = mp.solicitationMessageID
				LEFT OUTER JOIN @tmpInvoiceProfileProcFeeOverrides AS tmp ON tmp.gatewayID = g.gatewayID
				WHERE pmp.participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
				AND mp.status = 'A'
				AND g.isActive = 1
				AND g.gatewayID not in (2,13,14)
				ORDER BY mp.frontEndOrderBy;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelse>
			<!--- hard code the SeminarWeb MP --->
			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryProfiles">
				SELECT top 1 mp.profileID, mp.profileCode, g.gatewayClass, mp.tabTitle
				FROM dbo.mp_profiles as mp
				INNER JOIN dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
				WHERE mp.siteID = dbo.fn_getSiteIDFromSiteCode('TS')
				AND mp.profileCode = 'AuthorizeCCSemWeb'
				AND mp.status = 'A'
				AND g.isActive = 1
			</cfquery>
		</cfif>
		
		<cfreturn qryProfiles>
	</cffunction>

	<cffunction name="getRateAndProgramGLAccountID" access="private" output="no" returntype="query">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="rateID" type="numeric" required="yes">

		<cfset var qryGetRateAndProgramGLAccountID = "">

		<cfquery name="qryGetRateAndProgramGLAccountID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			set nocount on;

			declare @rateGLAccountID int, @programGLAccountID int, @GLAccountID int;

			select @rateGLAccountID = revenueGLAccountID 
			from dbo.tblSeminarsAndRates
			where rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
			and participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">;

			select @programGLAccountID = revenueGLAccountID 
			from dbo.tblSeminars 
			where seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">;
		
			set @GLAccountID = coalesce(@rateGLAccountID,@programGLAccountID);

			select @rateGLAccountID as rateGLAccountID, @programGLAccountID as programGLAccountID, @GLAccountID as GLAccountID;
		</cfquery>

		<cfreturn qryGetRateAndProgramGLAccountID>
	</cffunction>

	<cffunction name="getRateAndBundleGLAccountID" access="private" output="no" returntype="query">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="bundleID" type="numeric" required="yes">
		<cfargument name="rateID" type="numeric" required="yes">

		<cfset var qryGetRateAndBundleGLAccountID = "">

		<cfquery name="qryGetRateAndBundleGLAccountID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			set nocount on;

			declare @rateGLAccountID int, @bundleGLAccountID int, @GLAccountID int;

			select @rateGLAccountID = revenueGLAccountID 
			from dbo.tblBundlesAndRates
			where rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
			and participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">;

			select @bundleGLAccountID = revenueGLAccountID 
			from dbo.tblBundles 
			where bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.bundleID#">;
		
			set @GLAccountID = coalesce(@rateGLAccountID,@bundleGLAccountID);

			select @rateGLAccountID as rateGLAccountID, @bundleGLAccountID as bundleGLAccountID, @GLAccountID as GLAccountID;
		</cfquery>

		<cfreturn qryGetRateAndBundleGLAccountID>
	</cffunction>

	<cffunction name="getMembersFromCart" access="public" output="no" returntype="query">
		<cfargument name="qryRegCart" type="query" required="yes">
		
		<cfset var qryMembers = "">
		
		<cfquery name="qryMembers" dbtype="query">
			SELECT DISTINCT memberID, memberName
			FROM arguments.qryRegCart
			ORDER BY memberName
		</cfquery>
		
		<cfreturn qryMembers>
	</cffunction>

	<cffunction name="addSWReg" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="yes">
		<cfargument name="settings" type="struct" required="true">
		<cfargument name="qrySWP" type="query" required="true">

		<cfscript>
			var local = structNew();
			local.strResponse = { "success"=false, "response"='Invalid payment method' };

			local.objAccounting = CreateObject("component","model.system.platform.accounting");
			local.objAdminEvent = CreateObject("component","model.admin.events.event");
			local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields");

			local.SWAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin', siteID=arguments.event.getValue('mc_siteinfo.siteid'));
			local.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteinfo.orgID'));

			// make sure there are items in the cart
			paramRegSession(item=0);

			local.displayedCurrencyType = arguments.qrySWP.showUSD is 1 ? " USD" : "";

			local.qrySWRegCart = swRegCartToQuery();
			local.swReg = application.mcCacheManager.sessionGetValue(keyname="swReg");

			// no pending registrations
			if (NOT local.qrySWRegCart.recordCount)
				location("/?pg=semwebCatalog", "false");

			// merchant profiles allowed
			local.paymentGateways = swRegCartProfiles(orgID=arguments.event.getValue('mc_siteinfo.orgID'), participantID=arguments.qrySWP.participantID, 
										handlesOwnPayment=arguments.qrySWP.handlesOwnPayment, swReg=local.swReg);

			// get items
			local.arritemsToBuy = [];
			local.qryItemsToBuy = QueryNew("itemKey,item,programName,programSubTitle,memberid,depomemberdataid,memberName,email,amount,discount,rateName",
											"varchar,varchar,varchar,varchar,integer,integer,varchar,varchar,double,double,varchar");
			local.amountToCharge = 0;

			cfloop(query=local.qrySWRegCart){ 
				// check again if member is already registered
				if (val(local.qrySWRegCart.isRegistered))
					location("/?pg=semwebCatalog&panel=showCart", "false");

				arrayAppend(local.arritemsToBuy,local.qrySWRegCart.itemKey);

				QueryAddRow(local.qryItemsToBuy, {
					"itemKey":local.qrySWRegCart.itemKey, "item":local.qrySWRegCart.item, "programName":local.qrySWRegCart.programName, 
					"programSubTitle":local.qrySWRegCart.programSubTitle, "memberid":local.qrySWRegCart.memberid, 
					"depomemberdataid":local.qrySWRegCart.depomemberdataid, "memberName":local.qrySWRegCart.memberName, 
					"email":local.qrySWRegCart.email, "amount":local.qrySWRegCart.amount, "discount":local.qrySWRegCart.discount, 
					"rateName":local.qrySWRegCart.rateName
				});

				local.amountToCharge = local.amountToCharge + (local.qrySWRegCart.amount - local.qrySWRegCart.discount);
			}

			// If NotLoggedIn First Registrant considered as Main Registrant
			if (NOT local.useMID)  {
				local.useMID = listFirst(valueList(local.qrySWRegCart.memberid));
				application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=local.useMID, setCookie=false);
			}
		</cfscript>

		<cfset local.addEnrollmentSuccess = false>
		<cfset local.enrollmentIDList = "">
		<cfset local.bundleOrderIDList = "">

		<cfset local.arrNewPrimaryEmailAddress = arrayNew(1)>
		<cfset local.checkedMIDList = "">
		<cfloop array="#local.arritemsToBuy#" index="local.ItemToBuy">
			<cfset local.thisRegistration = "">
			<cfloop array="#local.swReg.regCart#" index="local.itemInCart">
				<cfif local.itemInCart.itemKey eq local.ItemToBuy>
					<cfset local.thisRegistration = duplicate(local.itemInCart)>
					<cfbreak>
				</cfif>
			</cfloop>

			<cfif NOT listFind(local.checkedMIDList,local.thisRegistration.s1.memberID)>
				<cfset local.overrideEmailAddress = local.thisRegistration.s3.email>
				<cfif len(local.overrideEmailAddress)>
					<cfset local.qryMainEmail = application.objMember.getMainEmail(memberID=local.thisRegistration.s1.memberID)>
					<!--- if no primary email address defined --->
					<cfif NOT len(local.qryMainEmail.email)>
						<cfset arrayAppend(local.arrNewPrimaryEmailAddress,{ "memberID":local.thisRegistration.s1.memberID, "newPrimaryEmailAddress":local.overrideEmailAddress })>
					</cfif>
				</cfif>
			</cfif>
			<cfset local.checkedMIDList = listAppend(local.checkedMIDList,local.thisRegistration.s1.memberID)>
		</cfloop>
		
		<!--- SW handles Reg --->
		<cfif arguments.qrySWP.handlesOwnPayment is 0>
			<cfset application.mcCacheManager.sessionSetValue(keyname='SWChargeDetail', value="")>

			<cfquery name="local.qryUseDepoMemberData" dbtype="query" maxrows="1">
				select depomemberdataid
				from [local].qryItemsToBuy
				where memberid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.useMID#">
			</cfquery>

			<cfif local.qryUseDepoMemberData.recordCount>
				<cfset local.useDepoMemberDataID = val(local.qryUseDepoMemberData.depomemberdataid)>
			<cfelse>
				<cfset local.useDepoMemberDataID = local.qryItemsToBuy.depomemberdataid[1]>
			</cfif>

			<cfif local.amountToCharge is 0>
				<cfset local.strResponse.success = true>
				<cfset local.strResponse.response = "">

			<cfelseif arguments.event.getTrimValue('paymentMethod','') eq "ccNew">
				<cfset local.objBuyNow = CreateObject("component","model.buyNow.BuyNow")>
				<cfset local.qryMemberData = application.objMember.getTSMemberData(depoMemberDataID=application.objUser.isLoggedIn(cfcuser=session.cfcuser) ? session.cfcuser.memberdata.depomemberdataid : local.useDepoMemberDataID)>

				<!--- force TransactionDepoMemberDataID to 0 if not logged in so we record the trans here --->
				<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<cfset local.customerIDToUse = "olddid_#session.cfcuser.memberdata.depomemberdataid#">
					<cfset local.TransactionDepoMemberDataID = local.useDepoMemberDataID>
				<cfelse>
					<cfset local.customerIDToUse = application.objPlatformStats.isValidStatsSessionID() ? "oldsid_#session.cfcuser.statsSessionID#" : "">
					<cfset local.TransactionDepoMemberDataID = 0>
				</cfif>

				<cfif len(local.customerIDToUse) is 0>
					<cfset local.strResponse.response = 'There is a problem with your browser that prevents us from processing the payment. Error code SWR-BNB-InvalidSession.'>
					<cfreturn local.strResponse>
				</cfif>

				<cfset local.strArgs = { "customerid"=local.customerIDToUse, "amount"=local.amountToCharge, 
					"chargeDesc"="#arguments.event.getValue('mc_siteInfo.orgShortName')# - #arguments.qrySWP.brandHomeTab#: #local.qryItemsToBuy.membername#", 
					"merchantProfile"='SW', "TransactionDepoMemberDataID"=local.TransactionDepoMemberDataID }>
				<cfif arguments.event.valueExists('p_SW_tokenData') AND len(arguments.event.getTrimValue('p_SW_tokenData'))>
					<cfset local.strArgs['tokenData'] = deserializeJSON(arguments.event.getTrimValue('p_SW_tokenData'))>
				</cfif>
				<cfset local.paymentResponse = local.objBuyNow.chargeCC_TS(argumentcollection=local.strArgs)>

				<!--- if payment not successful --->
				<cfif NOT local.paymentResponse.ccsuccess>
					<cfset local.strResponse.response = local.paymentResponse.ccresponse>
					<cfreturn local.strResponse>
				<cfelse>
					<cfset local.strResponse.success = true>
					<cfset local.strResponse.response = "">
				</cfif>
			</cfif>
			
			<cfif local.strResponse.success>
				<cfif local.amountToCharge gt 0 and isDefined("local.paymentResponse.strInsertTrans")>
					<cfquery name="local.qryInsertTrans" datasource="#application.dsn.tlasites_trialsmith.dsn#">
						SET NOCOUNT ON;

						DECLARE @useDepoMemberDataID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.useDepoMemberDataID#">;

						INSERT INTO dbo.depotransactions (description, AmountBilled, SalesTaxAmount, datepurchased, depomemberdataid, 
							sourcestate, approvalCode, paymentmethod, ccTransactionID, ccResponseCode, ccResponseReasonCode, isPayment,
							statsSessionID, merchantOrgCode, refundableAmount, isApplePay, isGooglePay)
						select <cfqueryparam value="#local.paymentResponse.strInsertTrans.description#" cfsqltype="CF_SQL_VARCHAR">, 
								<cfqueryparam value="#local.paymentResponse.strInsertTrans.AmountBilled#" cfsqltype="CF_SQL_DOUBLE">,
								0, getdate(), depomemberdataid, tlamemberstate, 
							<cfqueryparam value="#local.paymentResponse.strInsertTrans.approvalCode#" cfsqltype="CF_SQL_VARCHAR">,
							<cfqueryparam value="#local.paymentResponse.strInsertTrans.paymentmethod#" cfsqltype="CF_SQL_VARCHAR">,
							<cfqueryparam value="#local.paymentResponse.strInsertTrans.ccTransactionID#" cfsqltype="CF_SQL_VARCHAR">,
							<cfqueryparam value="#local.paymentResponse.strInsertTrans.ccResponseCode#" cfsqltype="CF_SQL_INTEGER">,
							<cfqueryparam value="#local.paymentResponse.strInsertTrans.ccResponseReasonCode#" cfsqltype="CF_SQL_INTEGER">, 1,
							<cfqueryparam value="#session.cfcuser.statsSessionID#" cfsqltype="CF_SQL_INTEGER">,
							'SW',
							<cfif local.paymentResponse.strInsertTrans.AmountBilled neq 0>
								<cfqueryparam value="#local.paymentResponse.strInsertTrans.AmountBilled * -1#" cfsqltype="CF_SQL_DOUBLE">
							<cfelse>
								null
							</cfif>,
							<cfif local.paymentResponse.strInsertTrans.keyExists("isApplePay")>
								<cfqueryparam value="#local.paymentResponse.strInsertTrans.isApplePay#" cfsqltype="CF_SQL_BIT">
							<cfelse>
								0
							</cfif>,
							<cfif local.paymentResponse.strInsertTrans.keyExists("isGooglePay")>
								<cfqueryparam value="#local.paymentResponse.strInsertTrans.isGooglePay#" cfsqltype="CF_SQL_BIT">
							<cfelse>
								0
							</cfif>
						from dbo.depomemberdata
						where depomemberdataid = @useDepoMemberDataID;

						<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcuser.memberdata.depomemberdataid is local.useDepoMemberDataID>
							UPDATE dbo.ccMemberPaymentProfiles
							SET customerid = <cfqueryparam value="olddid_#local.useDepoMemberDataID#" cfsqltype="CF_SQL_VARCHAR">,
								depomemberdataid = @useDepoMemberDataID
							WHERE payProfileID = <cfqueryparam value="#local.paymentResponse.strInsertTrans.payProfileID#" cfsqltype="CF_SQL_INTEGER">;
						<cfelse>
							INSERT INTO dbo.CustomerNotes (depomemberdataid, NoteTypeID, Note)
							VALUES (@useDepoMemberDataID, 1,
								<cfqueryparam value="#abs(local.paymentResponse.strInsertTrans.AmountBilled)# SW Payment made using Authorize.net Customer Profile CustomerID #local.paymentResponse.strInsertTrans.customerid#" cfsqltype="CF_SQL_VARCHAR">
							);
						</cfif>
					</cfquery>
				</cfif>

				<cfif local.amountToCharge gt 0 and isDefined("local.paymentResponse.accountNumber")>
					<cfset application.mcCacheManager.sessionSetValue(keyname='SWChargeDetail', value=local.paymentResponse.accountNumber)>
				</cfif>

				<cfif arrayLen(local.arrNewPrimaryEmailAddress)>
					<cfset saveRegistrantPrimaryEmailAddress(orgID=arguments.event.getValue('mc_siteinfo.orgID'), siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), arrPrimaryEmailAddress=local.arrNewPrimaryEmailAddress)>
				</cfif>

				<cfsavecontent variable="local.addEnrollmentSQL">
					<cfoutput>
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @siteCode varchar(10), @depomemberdataid int, @billingstate varchar(5), @zipForTax varchar(25), @linksource varchar(50),
							@linkterms varchar(100), @price money, @salestax money, @transactionID int, @enrollmentID int, 
							@programID int, @programName varchar(200), @seminarCreditID int, @idnumber varchar(50), @minAutoID int,
							@contentID int, @itemID int, @couponCode varchar(15), @orderID int, @nowDate datetime = getdate(),
							@savedKey uniqueidentifier, @loggedInMemberID int, @MCMemberID int, @enrollmentIDForBundleCredit int,
							@enrollmentIDList varchar(max), @bundleOrderIDList varchar(max), @statsSessionID int,
							@fieldID int, @detail varchar(max), @dataID int, @valueID int;
						DECLARE @tblTransactions TABLE (transactionID int);
						DECLARE @tmpBundle TABLE (autoID int IDENTITY(1,1), format varchar(4), contentID int, isRegistered bit, enrollmentID int);
						DECLARE @tblEnrollments TABLE (itemID int, itemType varchar(4));

						set @siteCode = '#arguments.event.getValue('mc_siteinfo.siteCode')#';
						set @linksource = '#replace(session.mcstruct.linksource,"'","''","ALL")#';
						set @linkterms = '#replace(session.mcstruct.linkterms,"'","''","ALL")#';
						set @loggedInMemberID = #int(val(local.useMID))#;

						<cfif structKeyExists(cookie,"SWBROWSE") AND isValid('guid',cookie.SWBROWSE)>
							set @savedKey = '#cookie.SWBROWSE#';
						</cfif>

						BEGIN TRAN;
							<cfloop array="#local.arritemsToBuy#" index="local.ItemToBuy">

								<!--- loop over regcart to get full registration for item to buy --->
								<cfset local.thisRegistration = "">
								<cfloop array="#local.swReg.regCart#" index="local.itemInCart">
									<cfif local.itemInCart.itemKey eq local.ItemToBuy>
										<cfset local.thisRegistration = duplicate(local.itemInCart)>
										<cfbreak>
									</cfif>
								</cfloop>

								<cfset local.swItemType = getToken(local.thisRegistration.item,1,'-')>
								<cfset local.programID = getToken(local.thisRegistration.item,2,'-')>
								<cfset local.actualPrice = NumberFormat(local.thisRegistration.s2.price - local.thisRegistration.s2.discountExcTax,'0.00')>
								<cfset local.taxDiscount = NumberFormat(local.thisRegistration.s2.discount - local.thisRegistration.s2.discountExcTax,'0.00')>
								<cfset local.actualSalesTax = NumberFormat(local.thisRegistration.s2.salestax - local.taxDiscount,'0.00')>
								<cfset local.rateAmount = NumberFormat(local.thisRegistration.s2.price + local.thisRegistration.s2.salestax,'0.00')>
								<cfset local.totalAmount = NumberFormat(local.thisRegistration.s2.price + local.thisRegistration.s2.salestax,'0.00')>
								<cfset local.actualRateAmount = NumberFormat(local.actualPrice + local.actualSalesTax,'0.00')>
								<cfset local.actualTotalAmount = NumberFormat(local.totalAmount - local.thisRegistration.s2.discount,'0.00')>

								<cfset local.overrideEmailAddress = local.thisRegistration.s3.email>
								<cfif len(local.overrideEmailAddress)>
									<cfset local.qryMainEmail = application.objMember.getMainEmail(memberID=local.thisRegistration.s1.memberID)>
									<cfif local.qryMainEmail.email EQ local.overrideEmailAddress>
										<cfset local.overrideEmailAddress = "">
									</cfif>
								</cfif>

								<!--- put custom fields and field types into array --->
								<cfif listFindNoCase("SWL,SWOD",local.swItemType)>
									<cfset local.arrCustomFields = []>
									<cfset local.qryProgramCustomFields = local.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
											resourceType='SemWebCatalog', areaName='#local.swItemType#Enrollment', csrid=local.SWAdminSiteResourceID, detailID=local.programID, hideAdminOnly=1)>
									<cfset local.strProgramCustomFields = xmlParse(local.qryProgramCustomFields.returnXML).xmlRoot>
									<cfif arrayLen(local.strProgramCustomFields.xmlChildren)>
										<cfloop array="#local.strProgramCustomFields.xmlChildren#" index="local.thisfield">
											<cfset local.tmpAtt = local.thisfield.xmlattributes>
											<cfset local.tmpStr = { fieldID=local.tmpAtt.fieldID, displayTypeCode=local.tmpAtt.displayTypeCode, value='' }>
											<cfif structKeyExists(local.thisRegistration.s3.custom,local.tmpAtt.fieldID)>
												<cfset local.tmpStr.value = local.thisRegistration.s3.custom[local.tmpAtt.fieldID]>
											</cfif>
											<cfset arrayAppend(local.arrCustomFields,local.tmpStr)>
										</cfloop>
									</cfif>
								</cfif>

								set @programID = #int(val(local.programID))#;
								set @depomemberdataid = #int(val(local.thisRegistration.s1.depomemberdataid))#;
								set @programName =  '#replace(local.thisRegistration.s2.programname,"'","''","ALL")#';
								set @billingstate = '#replace(local.thisRegistration.s3.billingstate,"'","''","ALL")#';
								set @zipForTax = '#replace(local.thisRegistration.s3.zipforTax,"'","''","ALL")#';
								set @price = #val(local.actualPrice)#;
								set @salestax = #val(local.actualSalesTax)#;
								set @MCMemberID = #int(val(local.thisRegistration.s1.memberID))#;
								set @statsSessionID = #session.cfcuser.statsSessionID#;
								
								select @transactionID = null, @enrollmentID = null;

								<cfswitch expression="#local.swItemType#">
									<cfcase value="SWL">
										EXEC dbo.swl_buySeminar @seminarID=@programID, @depomemberdataid=@depomemberdataid, @billingstate=@billingstate, 
											@billingzip=@zipForTax, @linksource=@linksource, @linkterms=@linkterms, @orgcode=@sitecode, @price=@price, 
											@salestax=@salestax, @statsSessionID=@statsSessionID, @transactionID=@transactionID OUTPUT;

										EXEC dbo.swl_addEnrollment @depomemberdataid=@depomemberdataid, @seminarID=@programID, @orgcode=@siteCode, 
											@isFeeExempt=0, @bundleOrderID=null, @transactionID=@transactionID, @bypassFeesCalc=0,
											@MCMemberID=@MCMemberID, @enrollmentID=@enrollmentID OUTPUT;
									</cfcase>
									<cfcase value="SWOD">
										EXEC dbo.swod_buySeminar @seminarID=@programID, @depomemberdataid=@depomemberdataid, @billingstate=@billingstate, 
											@billingzip=@zipForTax, @linksource=@linksource, @linkterms=@linkterms, @orgcode=@sitecode, @price=@price, 
											@salestax=@salestax, @statsSessionID=@statsSessionID, @transactionID=@transactionID OUTPUT;

										EXEC dbo.swod_addEnrollment @depomemberdataid=@depomemberdataid, @seminarID=@programID, @orgcode=@siteCode, 
											@isFeeExempt=0, @bundleOrderID=null, @transactionID=@transactionID, @bypassFeesCalc=0,
											@MCMemberID=@MCMemberID, @enrollmentID=@enrollmentID OUTPUT;
									</cfcase>
									<cfcase value="SWB">
										EXEC dbo.swb_buyBundle @bundleID=@programID, @depomemberdataid=@depomemberdataid, @billingstate=@billingstate, 
											@billingzip=@zipForTax, @linksource=@linksource, @linkterms=@linkterms, @orgcode=@sitecode, @price=@price, 
											@salestax=@salestax, @statsSessionID=@statsSessionID, @transactionID=@transactionID OUTPUT;

										EXEC dbo.swb_addEnrollment @depomemberdataid=@depomemberdataid, @bundleID=@programID, @signuporgcode=@siteCode, 
											@transactionID=@transactionID, @bypassFeesCalc=0, @MCMemberID=@MCMemberID, @orderID=@orderID OUTPUT;
									</cfcase>
								</cfswitch>

								INSERT INTO @tblTransactions (transactionID) VALUES (@transactionID);

								<cfif listFindNoCase('SWL,SWOD',local.swItemType)>
									INSERT INTO @tblEnrollments (itemID, itemType) VALUES (@enrollmentID,'#local.swItemType#');
								<cfelseif local.swItemType eq 'SWB'>
									INSERT INTO @tblEnrollments (itemID, itemType) VALUES (@orderID,'#local.swItemType#');
								</cfif>

								<cfif len(local.overrideEmailAddress)>
									<cfif listFindNoCase('SWL,SWOD',local.swItemType)>
										EXEC memberCentral.dbo.ams_insertUpdateEmailAppOverrides @itemType='semwebreg', @itemID=@enrollmentID, @email='#local.overrideEmailAddress.replace("'","''","all")#';
									<cfelseif local.swItemType eq 'SWB'>
										DELETE FROM @tmpBundle;

										INSERT INTO @tmpBundle (format, contentID, isRegistered)
										SELECT format, contentID, isRegistered
										FROM dbo.swb_getBundledItemsForCatalog(@programID,@loggedInMemberID);

										UPDATE tmpb
										SET tmpb.enrollmentID = e.enrollmentID
										FROM @tmpBundle AS tmpb
										INNER JOIN dbo.tblEnrollments as e on e.seminarID = tmpb.contentID and e.isActive = 1
										INNER JOIN membercentral.dbo.ams_members as m on m.memberID = e.MCMemberID and m.activeMemberID = @loggedInMemberID
										WHERE tmpb.isRegistered = 1;

										SELECT @minAutoID = min(autoID) FROM @tmpBundle;
										WHILE @minAutoID IS NOT NULL BEGIN
											SELECT @contentID = NULL, @itemID = NULL;
											SELECT @contentID = contentID, @itemID = enrollmentID FROM @tmpBundle WHERE autoID = @minAutoID;
											EXEC memberCentral.dbo.ams_insertUpdateEmailAppOverrides @itemType='semwebreg', @itemID=@itemID, @email='#local.overrideEmailAddress.replace("'","''","all")#';
											SELECT @minAutoID = min(autoID) FROM @tmpBundle WHERE autoID > @minAutoID;
										END										
									</cfif>
								</cfif>

								<cfif listFindNoCase('SWL,SWOD',local.swItemType) AND arrayLen(local.arrCustomFields)>
									<cfloop array="#local.arrCustomFields#" index="local.cf">
										<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.cf.displayTypeCode)>
											<cfset local.tempSQL = addSWReg_cf_option(itemType='#local.swItemType#RegCustom', fieldID=local.cf.fieldID, valueIDList=local.cf.value)>
											#preserveSingleQuotes(local.tempSQL)#
										<cfelseif len(local.cf.value)>
											<cfset local.tempSQL = addSWReg_cf_nonOption(itemType='#local.swItemType#RegCustom', fieldID=local.cf.fieldID, customText=local.cf.value)>
											#preserveSingleQuotes(local.tempSQL)#
										</cfif>
									</cfloop>
								</cfif>

								<!--- credits --->
								<cfif listFindNoCase('SWL,SWOD,SWB',local.swItemType) 
										AND structKeyExists(local.thisRegistration.s4.strCredit,"creditlinks") and listlen(local.thisRegistration.s4.strCredit.creditlinks)>
									<cfloop collection="#local.thisRegistration.s4.strCredit#" item="local.key">
										<cfif local.key neq "creditlinks">
											set @seminarCreditID = #int(val(local.key))#;
											set @idnumber = '#replace(local.thisRegistration.s4.strCredit[local.key],"'","''","ALL")#';

											<cfif local.swItemType eq "SWB">
												SET @enrollmentIDForBundleCredit = NULL;

												SELECT @enrollmentIDForBundleCredit = e.enrollmentID
												FROM dbo.tblEnrollments as e
												INNER JOIN dbo.tblSeminarsAndCredit as sac on sac.seminarID = e.seminarID
													AND sac.seminarCreditID = @seminarCreditID
												WHERE e.bundleOrderID = @orderID;

												IF @enrollmentIDForBundleCredit IS NOT NULL
													EXEC dbo.sw_addEnrollmentCredits @enrollmentID=@enrollmentIDForBundleCredit, 
														@seminarCreditID=@seminarCreditID, @idnumber=@idnumber, @recordedByMemberID=@loggedInMemberID;
											<cfelse>
												EXEC dbo.sw_addEnrollmentCredits @enrollmentID=@enrollmentID, @seminarCreditID=@seminarCreditID, @idnumber=@idnumber, 
													@recordedByMemberID=@loggedInMemberID;
											</cfif>
										</cfif>
									</cfloop>
								</cfif>

								<cfif NOT structIsEmpty(local.thisRegistration.s2.strCoupon) and val(local.thisRegistration.s2.discountExcTax) gt 0>
									-- promo code applied
									IF @transactionID IS NOT NULL BEGIN
										set @couponCode = '#replace(local.thisRegistration.s2.strCoupon.couponCode,"'","''","ALL")#';

										UPDATE trialsmith.dbo.depotransactions 
										SET Description = ISNULL(Description,'') + ' ' + @couponCode + ' discounted #DollarFormat(local.thisRegistration.s2.discountExcTax)#)'
										WHERE TransactionID = @transactionID;
									END
								</cfif>

								-- delete from saved programs
								IF @savedKey IS NOT NULL
									DELETE FROM dbo.tblSavedPrograms
									WHERE savedKey = @savedKey
									AND programID = @programID
									AND programType = '#local.swItemType#';
							</cfloop>
						COMMIT TRAN;

						DECLARE @xmlResult xml, @transactionIDList varchar(max);

						select @transactionIDList = COALESCE(@transactionIDList+',','') + cast(transactionID as varchar(10))
						from @tblTransactions;

						select @enrollmentIDList = COALESCE(@enrollmentIDList+',','') + cast(itemID as varchar(10))
						from @tblEnrollments
						where itemType in ('SWL','SWOD');

						select @bundleOrderIDList = COALESCE(@bundleOrderIDList+',','') + cast(itemID as varchar(10))
						from @tblEnrollments
						where itemType = 'SWB';

						SELECT @xmlResult = (
							SELECT (
								select transactionIDList, enrollmentIDList, bundleOrderIDList, success
								from (select @transactionIDList as transactionIDList, ISNULL(@enrollmentIDList,'') as enrollmentIDList, ISNULL(@bundleOrderIDList,'') as bundleOrderIDList, 1 as success) as r
								FOR XML AUTO, root('reg'), TYPE
								)
							FOR XML RAW('regresult'), TYPE
						);
						select @xmlResult as xmlResult;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
					</cfoutput>
				</cfsavecontent>

				<!--- add enrollment(s) --->
				<cftry>
					<cfquery name="local.qryAddEnrollment" datasource="#application.dsn.tlasites_seminarweb.dsn#">
						#preserveSingleQuotes(local.addEnrollmentSQL)#
					</cfquery>

					<cfset local.xmlResult = xmlParse(local.qryAddEnrollment.xmlResult)>
					<cfset local.qryAddEnrollmentSuccess = val(XMLSearch(local.xmlResult,"string(/regresult/reg/r/@success)"))>
					<cfset local.transactionIDList = XMLSearch(local.xmlResult,"string(/regresult/reg/r/@transactionIDList)")>
					<cfset local.enrollmentIDList = XMLSearch(local.xmlResult,"string(/regresult/reg/r/@enrollmentIDList)")>
					<cfset local.bundleOrderIDList = XMLSearch(local.xmlResult,"string(/regresult/reg/r/@bundleOrderIDList)")>
					
					<cfset local.addEnrollmentSuccess = local.qryAddEnrollmentSuccess is 1>
					<cfif NOT local.addEnrollmentSuccess>
						<cfthrow message="qryAddEnrollment did not return a status of 1.">
					</cfif>
				<cfcatch type="any">
					<cfset local.addEnrollmentSuccess = false>
					<cfset local.qryAddEnrollmentSuccess = 0>
					<cfset local.transactionIDList = ''>
					<cfset local.enrollmentIDList = "">
					<cfset local.bundleOrderIDList = "">
					<cfset local.tmpErrStr = { sql=replace(local.addEnrollmentSQL,chr(10),"<br/>","ALL") }>
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local.tmpErrStr)>
					<cfrethrow>
				</cfcatch>
				</cftry>
			</cfif>

			<cfif local.addEnrollmentSuccess>
				<cfquery name="local.strResponse.qryDepoTransactions" datasource="#application.dsn.tlasites_trialsmith.dsn#">
					select transactionID, description, amountBilled as total, salesTaxAmount as tax, datePurchased, transType, AccountCode
					from dbo.depoTransactions 
					where transactionID IN (<cfqueryparam value="#local.transactionIDList#" cfsqltype="CF_SQL_INTEGER" list="yes">)
				</cfquery>
			</cfif>
		
		<!--- Association handles Reg --->
		<cfelseif arguments.qrySWP.handlesOwnPayment is 1>
			<!--- determine payment profileID and profileCode --->
			<cfset arguments.event.paramValue('profileid',0)>
			<cfquery name="local.qryMerchantProfile" dbtype="query">
				SELECT profileid, profileCode, gatewayid, gatewayType, enableProcessingFeeDonation, processFeeDonationFeePercent,
					processFeeDonationRenevueGLAccountID, processFeeDonationRevTransDesc, processingFeeLabel, enableApplePay, enableGooglePay,
					enableSurcharge, surchargePercent, surchargeRevenueGLAccountID
				FROM [local].paymentGateways
				WHERE profileid = <cfqueryparam value="#arguments.event.getValue('profileid')#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>

			<!--- prepare fields for gateway and send --->
			<cfif local.amountToCharge gt 0>
				
				<!--- get fields --->
				<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=local.qryMerchantProfile.profilecode)>
				<cfset local.tmpGatewayFields = structNew()>
				<cfloop query="local.qryGatewayProfileFields">
					<cfset structInsert(local.tmpGatewayFields,'fld_#local.qryGatewayProfileFields.fieldid#_',arguments.event.getTrimValue('p_#local.qryMerchantProfile.profileID#_fld_#local.qryGatewayProfileFields.fieldid#_',''))>
				</cfloop>

				<!--- get info on file if applicable --->
				<cfset arguments.event.setValue('p_#local.qryMerchantProfile.profileID#_mppid',int(val(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid',0))))>
				<cfif arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid') gt 0>
					<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(mppid=arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid'), memberID=local.useMID, profileID=local.qryMerchantProfile.profileID)>
					<cfset structInsert(local.tmpGatewayFields,'qryInfoOnFile',local.qrySavedInfoOnFile)>
				</cfif>

				<!--- Surcharge / Processing Fee Donation --->
				<cfset local.additionalFeesInfo = application.objPayments.getAdditionalFeesInfo(qryMerchantProfile=local.qryMerchantProfile, amt=local.amountToCharge, 
					stateIDForTax=local.swReg.regCart[1].s3.stateIDForTax, zipForTax=local.swReg.regCart[1].s3.zipForTax,
					processingFeeOpted=arguments.event.getValue('processFeeDonation#arguments.event.getValue('profileid')#',0) EQ 1,
					surchargeEligibleCard=arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid') GT 0 AND local.qrySavedInfoOnFile.surchargeEligible EQ 1)>

				<!--- failed --->
				<cfif NOT local.additionalFeesInfo.success>
					<cfset local.strResponse.response = "Unable to get additional payment fees info.">
					<cfreturn local.strResponse>
				</cfif>
				
				<cfset local.finalAmountToCharge = local.additionalFeesInfo.finalAmountToCharge>

				<!--- prepare fields for gateway and send --->
				<cfset local.strTemp = { orgID=arguments.event.getValue('mc_siteinfo.orgID'), siteid=arguments.event.getValue('mc_siteinfo.siteid'), 
										 profileCode=local.qryMerchantProfile.profileCode, assignedToMemberID=local.useMID, recordedByMemberID=local.useMID, 
										 statsSessionID=val(session.cfcUser.statsSessionID), x_amount=local.finalAmountToCharge, 
										 x_description='#arguments.event.getValue('mc_siteinfo.sitename')# Purchase Program',
										 offeredPaymentFee=local.additionalFeesInfo.offeredPaymentFee }>

				<!--- apple or google pay token --->
				<cfif arguments.event.valueExists('p_#local.qryMerchantProfile.profileID#_tokenData') AND len(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_tokenData'))>
					<cfset local.strTemp["tokenData"] = deSerializeJSON(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_tokenData'))>
				</cfif>
				
				<cfif listFindNoCase("AuthorizeCCCIM",local.qryMerchantProfile.gatewayType)>
					<cfset local.qryLevel3Data = QueryNew("name,desc,itemPriceExcDiscount,itemPriceIncDiscount,discount,qty,total","varchar,varchar,decimal,decimal,decimal,decimal,decimal")>
					<cfloop query="local.qryItemsToBuy">
						<cfset local.itemPriceIncDiscount = val(local.qryItemsToBuy.amount) - val(local.qryItemsToBuy.discount)>
						<cfset QueryAddRow(local.qryLevel3Data, {
							"name": local.qryItemsToBuy.programName,
							"desc": "#arguments.event.getValue('mc_siteinfo.sitename')# Purchase Program",
							"itemPriceExcDiscount": local.qryItemsToBuy.amount,
							"itemPriceIncDiscount": local.itemPriceIncDiscount,
							"discount": 0,
							"qty": 1,
							"total": local.itemPriceIncDiscount
						})>
					</cfloop>
					<cfif local.additionalFeesInfo.additionalFees GT 0>
						<cfset QueryAddRow(local.qryLevel3Data, {
							"name": "#local.additionalFeesInfo.additionalFeesLabel#",
							"desc": "#arguments.event.getValue('mc_siteinfo.sitename')# #local.additionalFeesInfo.additionalFeesLabel#",
							"itemPriceExcDiscount": local.additionalFeesInfo.additionalFees,
							"itemPriceIncDiscount": local.additionalFeesInfo.additionalFees,
							"discount": 0,
							"qty": 1,
							"total": local.additionalFeesInfo.additionalFees
						})>
						<!--- Surcharge --->
						<cfif local.additionalFeesInfo.paymentFeeTypeID EQ 2>
							<cfset local.strTemp['x_surcharge'] = { "amount":local.additionalFeesInfo.additionalFees, "description":"#arguments.event.getValue('mc_siteinfo.sitename')# #local.additionalFeesInfo.additionalFeesLabel#" }>
						</cfif>
					</cfif>
					<cfset local.strTemp["x_items"] = application.objPayments.getLevel3Data(qryLevel3Data=local.qryLevel3Data, gatewayType=local.qryMerchantProfile.gatewayType)>
				</cfif>
				<cfset structAppend(local.strTemp,local.tmpGatewayFields)>
				<cfinvoke component="#application.objPayments#" method="chargeAdHoc" argumentcollection="#local.strTemp#" returnvariable="local.paymentResponse">

				<!--- if payment not successful --->
				<cfif local.paymentResponse.responseCode is not 1>
					<cfset local.strResponse.response = local.paymentResponse.publicResponseReasonText>
					<cfreturn local.strResponse>
				</cfif>

				<cfset local.arrPaymentResults = local.paymentResponse.keyExists("paymentResults") ? local.paymentResponse.paymentResults : [ duplicate(local.paymentResponse) ]>

				<!--- Record Surcharge / Processing Fee Donation --->
				<cfif local.additionalFeesInfo.additionalFees GT 0 AND local.paymentResponse.keyExists("mc_transactionID") AND val(local.paymentResponse.mc_transactionID)>
					<cfset local.strRecordAdditionalPmtFees = local.objAccounting.recordAdditionalPaymentFees(orgID=arguments.event.getValue('mc_siteinfo.orgID'), 
						siteID=arguments.event.getValue('mc_siteinfo.siteID'), assignedToMemberID=local.useMID, recordedByMemberID=local.useMID, 
						statsSessionID=val(session.cfcuser.statsSessionID), paymentTransactionID=local.paymentResponse.mc_transactionID, 
						GLAccountID=local.additionalFeesInfo.gl, qryAdditionalFees=local.additionalFeesInfo.qryAdditionalFees, 
						paymentFeeTypeID=local.additionalFeesInfo.paymentFeeTypeID)>
					
					<!--- if not successful --->
					<cfif NOT local.strRecordAdditionalPmtFees.success>
						<cfset local.strResponse.response = "Unable to record additional payment fees.">
						<cfreturn local.strResponse>
					</cfif>

					<cfset local.additionalPaymentFeeInvoiceID = local.strRecordAdditionalPmtFees.invoiceID>
				</cfif>
			</cfif>

			<cfif arrayLen(local.arrNewPrimaryEmailAddress)>
				<cfset saveRegistrantPrimaryEmailAddress(orgID=arguments.event.getValue('mc_siteinfo.orgID'), siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), arrPrimaryEmailAddress=local.arrNewPrimaryEmailAddress)>
			</cfif>

			<cfsavecontent variable="local.addEnrollmentSQL">
				<cfoutput>
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @orgID int, @siteID int, @siteCode varchar(10), @participantID int, @loggedInMemberID int, @statsSessionID int, 
						@depomemberdataid int, @rateTransactionID int, @stateIDforTax int, @zipForTax varchar(25), @GLAccountID int, @enrollmentID int, 
						@paymentTransactionIDList varchar(50), @invoiceProfileID int, @deferredGLAccountID int, @invoiceID int, @invoiceNumber varchar(19), 
						@deferredDateStr varchar(10), @nowDate datetime, @deferredDate datetime, @rateAmount decimal(18,2), @rateTaxAmount decimal(18,2), 
						@invDue decimal(18,2), @xmlSchedule xml, @swMerchantProfiles varchar(1000), @programID int, @programName varchar(200), 
						@itemType varchar(10), @itemID int, @subItemID int, @couponID int, @discountAmount decimal(18,2), @adjTransactionID int, 
						@maxOverallUsageCount int, @maxMemberUsageCount int, @redemptionCount int, @redemptionCountPerMember int, @couponCode varchar(15),
						@SWRedemptionCount int, @orderID int, @internalNotes varchar(500), @seminarCreditID int, @idnumber varchar(50), @invoiceMemberID int, 
						@thisRegistrantMemberID int, @minAutoID int, @contentID int, @bundleItemEnrollmentID int, @minVID int, @invoiceIDList varchar(max), 
						@enrollmentIDList varchar(max), @bundleOrderIDList varchar(max), @savedKey uniqueidentifier, @enrollmentIDForBundleCredit int,
						@fieldID int, @detail varchar(max), @dataID int, @valueID int, @minPaymentTID int, @unAllocAmount decimal(18,2), @allocAmount decimal(18,2);

					DECLARE @tblInvoices TABLE (invoiceID int, invoiceProfileID int, amount decimal(18,2));
					DECLARE @tmpBundle TABLE (autoID int IDENTITY(1,1), format varchar(4), contentID int, isRegistered bit, enrollmentID int);
					DECLARE @tblEnrollments TABLE (itemID int, itemType varchar(4));
					DECLARE @tblPaymentForReallocationTransactions TABLE (paymentTransactionID int, amountAvailable decimal(18,2));
					
					set @nowDate = getdate();
					set @orgID = #arguments.event.getValue('mc_siteinfo.orgID')#;
					set @siteID = #arguments.event.getValue('mc_siteinfo.siteID')#;
					set @siteCode = '#arguments.event.getValue('mc_siteinfo.siteCode')#';
					set @participantID = #int(val(arguments.qrySWP.participantID))#;
					set @loggedInMemberID = #int(val(local.useMID))#;
					set @statsSessionID = #session.cfcUser.statsSessionID#;
					set @deferredDate = @nowDate;
					set @deferredDateStr = convert(varchar(10),@deferredDate,101);

					<cfif isDefined("cookie.SWBROWSE") AND isValid('guid',cookie.SWBROWSE)>
						set @savedKey = '#cookie.SWBROWSE#';
					</cfif>

					select @swMerchantProfiles = dbo.sortedIntList(profileID)
					from seminarWeb.dbo.tblParticipantMerchantProfiles
					where participantID	= @participantID;

					BEGIN TRAN;
						<cfloop array="#local.arritemsToBuy#" index="local.ItemToBuy">

							<!--- loop over regcart to get full registration for item to buy --->
							<cfset local.thisRegistration = "">
							<cfloop array="#local.swReg.regCart#" index="local.itemInCart">
								<cfif local.itemInCart.itemKey eq local.ItemToBuy>
									<cfset local.thisRegistration = duplicate(local.itemInCart)>
									<cfbreak>
								</cfif>
							</cfloop>

							<cfset local.swItemType = getToken(local.thisRegistration.item,1,'-')>
							<cfset local.programID = getToken(local.thisRegistration.item,2,'-')>
							<cfset local.revenueGLAccountIDToUse = arguments.qrySWP.revenueGLAccountID>

							<cfif ListFindNoCase("SWL,SWOD",local.swItemType)>
								<cfset local.qryGetRateAndProgramGLAccountID = getRateandProgramGLAccountID(participantID=arguments.qrySWP.participantID, seminarID=local.programID, rateID=local.thisRegistration.s2.rateid)>
								<cfif val(local.qryGetRateAndProgramGLAccountID.GLAccountID) gt 0>
									<cfset local.revenueGLAccountIDToUse = local.qryGetRateAndProgramGLAccountID.GLAccountID>
								</cfif>
							<cfelseif local.swItemType eq 'SWB'>
								<cfset local.qryGetRateAndBundleGLAccountID = getRateandBundleGLAccountID(participantID=arguments.qrySWP.participantID, bundleID=local.programID, rateID=local.thisRegistration.s2.rateid)>
								<cfif val(local.qryGetRateAndBundleGLAccountID.GLAccountID) gt 0>
									<cfset local.revenueGLAccountIDToUse = local.qryGetRateAndBundleGLAccountID.GLAccountID>
								</cfif>
							</cfif>

							<cfset local.overrideEmailAddress = local.thisRegistration.s3.email>
							<cfif len(local.overrideEmailAddress)>
								<cfset local.qryMainEmail = application.objMember.getMainEmail(memberID=local.thisRegistration.s1.memberID)>
								<cfif local.qryMainEmail.email EQ local.overrideEmailAddress>
									<cfset local.overrideEmailAddress = "">
								</cfif>
							</cfif>

							<!--- put custom fields and field types into array --->
							<cfif listFindNoCase("SWL,SWOD",local.swItemType)>
								<cfset local.arrCustomFields = []>
								<cfset local.qryProgramCustomFields = local.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
										resourceType='SemWebCatalog', areaName='#local.swItemType#Enrollment', csrid=local.SWAdminSiteResourceID, detailID=local.programID, hideAdminOnly=1)>
								<cfset local.strProgramCustomFields = xmlParse(local.qryProgramCustomFields.returnXML).xmlRoot>
								<cfif arrayLen(local.strProgramCustomFields.xmlChildren)>
									<cfloop array="#local.strProgramCustomFields.xmlChildren#" index="local.thisfield">
										<cfset local.tmpAtt = local.thisfield.xmlattributes>
										<cfset local.tmpStr = { fieldID=local.tmpAtt.fieldID, displayTypeCode=local.tmpAtt.displayTypeCode, value='' }>
										<cfif structKeyExists(local.thisRegistration.s3.custom,local.tmpAtt.fieldID)>
											<cfset local.tmpStr.value = local.thisRegistration.s3.custom[local.tmpAtt.fieldID]>
										</cfif>
										<cfset arrayAppend(local.arrCustomFields,local.tmpStr)>
									</cfloop>
								</cfif>
							</cfif>

							set @programID = #int(val(local.programID))#;
							set @depomemberdataid = #int(val(local.thisRegistration.s1.depomemberdataid))#;
							set @GLAccountID = #int(val(local.revenueGLAccountIDToUse))#;
							set @programName =  '#replace(local.thisRegistration.s2.programname,"'","''","ALL")#';
							set @thisRegistrantMemberID = #int(val(local.thisRegistration.s1.memberID))#;
				
							select @invoiceMemberID = case when orgID = @orgID then memberID else @thisRegistrantMemberID end
							from dbo.ams_members 
							where memberID = @loggedInMemberID;
							
							<cfif local.thisRegistration.s3.stateIDforTax gt 0>
								set @stateIDforTax = #int(val(local.thisRegistration.s3.stateIDforTax))#;
							</cfif>
							<cfif len(local.thisRegistration.s3.zipforTax)>
								set @zipForTax = '#replace(local.thisRegistration.s3.zipforTax,"'","''","ALL")#';
							</cfif>

							set @rateAmount = #val(local.thisRegistration.s2.price)#;
							set @rateTaxAmount = #val(local.thisRegistration.s2.salestax)#;

							select @invoiceProfileID = invoiceProfileID 
							from dbo.tr_GLAccounts 
							where glAccountID = @GLAccountID;

							select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;

							-- add enrollment
							select @itemType = null, @enrollmentID = null, @itemID = null, @subItemID = null;

							<cfswitch expression="#local.swItemType#">
								<cfcase value="SWL">
									EXEC seminarWeb.dbo.swl_addEnrollment @depomemberdataid=@depomemberdataid, @seminarID=@programID, 
										@orgcode=@siteCode, @isFeeExempt=0, @bundleOrderID=null, @transactionID=null, 
										@bypassFeesCalc=1, @MCMemberID=@thisRegistrantMemberID, @enrollmentID=@enrollmentID OUTPUT;

									select @itemType = 'SWLRate', @itemID = @enrollmentID, @subItemID = @programID;
								</cfcase>
								<cfcase value="SWOD">
									EXEC seminarWeb.dbo.swod_addEnrollment @depomemberdataid=@depomemberdataid, @seminarID=@programID, 
										@orgcode=@siteCode, @isFeeExempt=0, @bundleOrderID=null, @transactionID=null, 
										@bypassFeesCalc=1, @MCMemberID=@thisRegistrantMemberID, @enrollmentID=@enrollmentID OUTPUT;

									select @itemType = 'SWODRate', @itemID = @enrollmentID, @subItemID = @programID;
								</cfcase>
								<cfcase value="SWB">
									EXEC seminarWeb.dbo.swb_addEnrollment @depomemberdataid=@depomemberdataid, @bundleID=@programID, 
										@signuporgcode=@siteCode, @transactionID=null, @bypassFeesCalc=1, @MCMemberID=@thisRegistrantMemberID, @orderID=@orderID OUTPUT;

									select @itemType = 'SWBRate', @itemID = @orderID, @subItemID = @programID;
								</cfcase>
							</cfswitch>

							INSERT INTO @tblEnrollments (itemID, itemType) VALUES (@itemID,'#local.swItemType#');

							<cfif len(local.overrideEmailAddress)>
								<cfif listFindNoCase('SWL,SWOD',local.swItemType)>
									EXEC dbo.ams_insertUpdateEmailAppOverrides @itemType='semwebreg', @itemID=@enrollmentID, @email='#local.overrideEmailAddress.replace("'","''","all")#';
								<cfelseif local.swItemType eq 'SWB'>
									DELETE FROM @tmpBundle;

									INSERT INTO @tmpBundle (format, contentID, isRegistered)
									SELECT format, contentID, isRegistered
									FROM seminarWeb.dbo.swb_getBundledItemsForCatalog(@programID,@loggedInMemberID);

									UPDATE tmpb
									SET tmpb.enrollmentID = e.enrollmentID
									FROM @tmpBundle AS tmpb
									INNER JOIN seminarweb.dbo.tblEnrollments as e on e.seminarID = tmpb.contentID and e.isActive = 1
									INNER JOIN dbo.ams_members as m on m.memberID = e.MCMemberID and m.activeMemberID = @loggedInMemberID
									WHERE tmpb.isRegistered = 1;

									SELECT @minAutoID = min(autoID) from @tmpBundle;
									WHILE @minAutoID is not null BEGIN
										select @contentID = null, @bundleItemEnrollmentID = null;
										select @contentID = contentID, @bundleItemEnrollmentID = enrollmentID FROM @tmpBundle WHERE autoID = @minAutoID;
										EXEC dbo.ams_insertUpdateEmailAppOverrides @itemType='semwebreg', @itemID=@bundleItemEnrollmentID, @email='#local.overrideEmailAddress.replace("'","''","all")#';
										SELECT @minAutoID = min(autoID) from @tmpBundle WHERE autoID > @minAutoID;
									END
								</cfif>
							</cfif>

							<cfif listFindNoCase('SWL,SWOD',local.swItemType) AND arrayLen(local.arrCustomFields)>
								<cfloop array="#local.arrCustomFields#" index="local.cf">
									<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.cf.displayTypeCode)>
										<cfset local.tempSQL = addSWReg_cf_option(itemType='#local.swItemType#RegCustom', fieldID=local.cf.fieldID, valueIDList=local.cf.value)>
										#preserveSingleQuotes(local.tempSQL)#
									<cfelseif len(local.cf.value)>
										<cfset local.tempSQL = addSWReg_cf_nonOption(itemType='#local.swItemType#RegCustom', fieldID=local.cf.fieldID, customText=local.cf.value)>
										#preserveSingleQuotes(local.tempSQL)#
									</cfif>
								</cfloop>
							</cfif>

							<!--- credits --->
							<cfif listFindNoCase('SWL,SWOD,SWB',local.swItemType) 
									AND structKeyExists(local.thisRegistration.s4.strCredit,"creditlinks") and listlen(local.thisRegistration.s4.strCredit.creditlinks)>
								<cfloop collection="#local.thisRegistration.s4.strCredit#" item="local.key">
									<cfif local.key neq "creditlinks">
										set @seminarCreditID = #int(val(local.key))#;
										set @idnumber = '#replace(local.thisRegistration.s4.strCredit[local.key],"'","''","ALL")#';

										<cfif local.swItemType eq "SWB">
											SET @enrollmentIDForBundleCredit = NULL;
											
											SELECT @enrollmentIDForBundleCredit = e.enrollmentID
											FROM seminarWeb.dbo.tblEnrollments as e
											INNER JOIN seminarWeb.dbo.tblSeminarsAndCredit as sac on sac.seminarID = e.seminarID
												AND sac.seminarCreditID = @seminarCreditID
											WHERE e.bundleOrderID = @orderID;

											IF @enrollmentIDForBundleCredit IS NOT NULL
												EXEC seminarWeb.dbo.sw_addEnrollmentCredits @enrollmentID=@enrollmentIDForBundleCredit, 
													@seminarCreditID=@seminarCreditID, @idnumber=@idnumber, @recordedByMemberID=@loggedInMemberID;
										<cfelse>
											EXEC seminarWeb.dbo.sw_addEnrollmentCredits @enrollmentID=@enrollmentID, @seminarCreditID=@seminarCreditID, @idnumber=@idnumber,
												@recordedByMemberID=@loggedInMemberID;
										</cfif>
									</cfif>
								</cfloop>
							</cfif>

							-- create invoice assigned to payer based on invoice profile
							IF @invoiceID is null BEGIN
								EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
									@assignedToMemberID=@invoiceMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, 
									@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

								INSERT INTO @tblInvoices (invoiceID, invoiceProfileID)
								VALUES (@invoiceID, @invoiceProfileID);

								IF @swMerchantProfiles is not null
									EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@swMerchantProfiles;
							END

							-- handle deferred revenue
							select @xmlSchedule = null, @deferredGLAccountID = null;
							select @deferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@GLAccountID);
							IF @deferredGLAccountID is not null
								set @xmlSchedule = '<rows><row amt="#local.thisRegistration.s2.price#" dt="' + @deferredDateStr + '" /></rows>';

							-- record rate transaction
							EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
								@assignedToMemberID=@thisRegistrantMemberID, @recordedByMemberID=@loggedInMemberID, 
								@statsSessionID=@statsSessionID, @status='Active', @detail=@programName, @parentTransactionID=null, 
								@amount=@rateAmount, @transactionDate=@nowDate, @creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, 
								@stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax, @taxAmount=@rateTaxAmount, @bypassTax=0, @bypassInvoiceMessage=0, 
								@bypassAccrual=0, @xmlSchedule=@xmlSchedule, @transactionID=@rateTransactionID OUTPUT;
							
							EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='SemWebCatalog', @transactionID=@rateTransactionID, 
								@itemType=@itemType, @itemID=@itemID, @subItemID=@subItemID;

							-- promo code applied
							<cfif NOT structIsEmpty(local.thisRegistration.s2.strCoupon) and val(local.thisRegistration.s2.discountExcTax) gt 0>
								set @couponID = #int(val(local.thisRegistration.s2.strCoupon.couponID))#;
								set @discountAmount = #val(local.thisRegistration.s2.discountExcTax)# * - 1;

								-- check if coupon max usage count met
								select @couponCode = couponCode, @maxOverallUsageCount = maxOverallUsageCount, @maxMemberUsageCount = maxMemberUsageCount,
									@SWRedemptionCount = seminarwebXML.value('(/sw/pid/p[text()=sql:variable("@programID")]/@rc)[1]', 'int')
								from dbo.tr_coupons
								where siteID = @siteID
								and couponID = @couponID;

								set @SWRedemptionCount = isnull(@SWRedemptionCount,1);

								; with redemptionsCheck#replace(local.thisRegistration.itemKey,'-','','all')# as(
									select distinct td.itemType, td.itemID, td.redemptionCount, 
										case when m.memberID is not null then td.redemptionCount else 0 end as memberRedemptionCount
									from dbo.tr_coupons as c
									inner join dbo.tr_transactionDiscounts as td on td.orgID = @orgID 
										and td.couponID = c.couponID 
										and td.isActive = 1
										and c.couponID = @couponID
									inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = td.transactionID and t.statusID = 1
									left outer join dbo.ams_members as m on m.orgID = @orgID and m.memberID = t.assignedToMemberID and m.activeMemberID = @thisRegistrantMemberID
								)
								select @redemptionCount = COUNT(itemID), @redemptionCountPerMember = SUM(memberRedemptionCount) 
								from redemptionsCheck#replace(local.thisRegistration.itemKey,'-','','all')#;

								IF @maxOverallUsageCount > 0 AND ISNULL(@redemptionCount,0) + @SWRedemptionCount > @maxOverallUsageCount
									SET @internalNotes = 'Overall Redemptions Count for Coupon Code(' + @couponCode + ') exceeded; ';

								IF @maxMemberUsageCount > 0 AND ISNULL(@redemptionCountPerMember,0) + @SWRedemptionCount > @maxMemberUsageCount
									SET @internalNotes = ISNULL(@internalNotes,'') + 'Overall Redemptions Count Per Member for Coupon Code(' + @couponCode + ') exceeded; ';
								
								IF LEN(ISNULL(@internalNotes,'')) > 0 BEGIN
									<cfif listFindNoCase("SWL,SWOD", local.swItemType)>
										UPDATE seminarweb.dbo.tblEnrollments
										SET internalNotes = ISNULL(internalNotes,'') + @internalNotes
										WHERE enrollmentID = @enrollmentID;
									<cfelseif local.swItemType eq "SWB">
										UPDATE seminarweb.dbo.tblBundleOrders
										SET orderNotes = ISNULL(orderNotes,'') + @internalNotes
										WHERE orderID = @orderID;
									</cfif>
								END

								EXEC dbo.tr_createTransaction_discount @recordedOnSiteID=@siteid, @recordedByMemberID=@loggedInMemberID, 
									@statsSessionID=@statsSessionID, @amount=@discountAmount, @transactionDate=@nowDate, 
									@saleTransactionID=@rateTransactionID, @invoiceID=@invoiceID, @couponID=@couponID, 
									@itemType=@itemType, @itemID=@itemID, @redemptionCount=@SWRedemptionCount, 
									@transactionID=@adjTransactionID OUTPUT;
							</cfif>

							-- delete from saved programs
							IF @savedKey IS NOT NULL
								DELETE FROM seminarWeb.dbo.tblSavedPrograms
								WHERE savedKey = @savedKey
								AND programID = @programID
								AND programType = '#local.swItemType#';

							-- calculate revenue and reg fees
							IF @itemType = 'SWBRate'
								EXEC seminarWeb.dbo.sw_calculateBundleOrderRevenueAndRegFees @bundleOrderID=@itemID, @calcOnly=0, @asOfDate=@nowDate;
							ELSE
								EXEC seminarWeb.dbo.sw_calculateEnrollmentRevenueAndRegFees @enrollmentID=@itemID, @calcOnly=0, @asOfDate=@nowDate;
						</cfloop>

						-- return invoiceIDList
						select @invoiceIDList = COALESCE(@invoiceIDList+',','') + cast(invoiceID as varchar(10))
						from @tblInvoices;

						select @enrollmentIDList = COALESCE(@enrollmentIDList+',','') + cast(itemID as varchar(10))
						from @tblEnrollments
						where itemType in ('SWL','SWOD');

						select @bundleOrderIDList = COALESCE(@bundleOrderIDList+',','') + cast(itemID as varchar(10))
						from @tblEnrollments
						where itemType = 'SWB';
						
						-- close invoices
						IF len(@invoiceIDList) > 0
							EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@loggedInMemberID, @invoiceIDList=@invoiceIDList;
						
						<cfif local.amountToCharge gt 0 and structKeyExists(local,"arrPaymentResults") and arrayLen(local.arrPaymentResults)>
							<cfloop array="#local.arrPaymentResults#" index="local.thisResult">
								<cfif val(local.thisResult.mc_transactionID)>
									insert into @tblPaymentForReallocationTransactions (paymentTransactionID, amountAvailable)
									values (#val(local.thisResult.mc_transactionID)#, #val(local.thisResult.x_amount)#);
								</cfif>
							</cfloop>
							
							-- get final amount of invoices (with tax, etc included)
							update tmp
							set tmp.amount = tmp2.InvDue
							from @tblInvoices as tmp
							inner join (
								select tmp.invoiceID, isnull(sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount),0) as InvDue	
								from @tblInvoices as tmp
								left outer join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = tmp.invoiceID
								group by tmp.invoiceID
							) as tmp2 on tmp2.invoiceID = tmp.invoiceID;

							-- allocate payment to invoices
							select @minVID = min(invoiceID) from @tblInvoices;
							while @minVID is not null BEGIN
								select @invDue = null, @minPaymentTID=null;

								select @invDue = amount from @tblInvoices where invoiceID = @minVID;
								if @invDue > 0 BEGIN
									select @minPaymentTID = min(paymentTransactionID) from @tblPaymentForReallocationTransactions where amountAvailable > 0;
									while @minPaymentTID is not null begin
										select @unAllocAmount = null, @allocAmount=null;

										select @unAllocAmount = amountAvailable 
										from @tblPaymentForReallocationTransactions
										where paymentTransactionID = @minPaymentTID;

										-- select minimum of dueAmount and unallocated Payment Amount
										SELECT @allocAmount=MIN(x) FROM (VALUES (@invDue),(@unAllocAmount)) AS value(x);
										
										EXEC dbo.tr_allocateToInvoice @recordedOnSiteID=@siteid, @recordedByMemberID=@loggedInMemberID, 
											@statsSessionID=@statsSessionID, @amount=@allocAmount, @transactionDate=@nowDate, 
											@paymentTransactionID=@minPaymentTID, @invoiceID=@minVID;

										SET @invDue = @invDue - @allocAmount;
										SET @unAllocAmount = @unAllocAmount - @allocAmount

										update @tblPaymentForReallocationTransactions
										set amountAvailable = @unAllocAmount
										where paymentTransactionID = @minPaymentTID;

										IF @invDue = 0
											BREAK;
										
										select @minPaymentTID = min(paymentTransactionID) 
											from @tblPaymentForReallocationTransactions
											where paymentTransactionID > @minPaymentTID
											and amountAvailable > 0;
									end
								end
								
								select @minVID = min(invoiceID) from @tblInvoices where invoiceID > @minVID;		
							end
						</cfif>
					COMMIT TRAN;

					SELECT @paymentTransactionIDList = STRING_AGG(CAST(paymentTransactionID AS VARCHAR(10)), ',')
					FROM @tblPaymentForReallocationTransactions;

					DECLARE @xmlResult xml;
					SELECT @xmlResult = (
						SELECT (
							select invoiceIDList, enrollmentIDList, bundleOrderIDList, paymentTransactionIDList, success
							from (select @invoiceIDList as invoiceIDList, ISNULL(@enrollmentIDList,'') as enrollmentIDList, ISNULL(@bundleOrderIDList,'') as bundleOrderIDList,
								ISNULL(@paymentTransactionIDList,'') as paymentTransactionIDList, 1 as success) as r
							FOR XML AUTO, root('reg'), TYPE
							)
						FOR XML RAW('regresult'), TYPE
					);
					select @xmlResult as xmlResult;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
				</cfoutput>
			</cfsavecontent>

			<!--- add enrollment(s) --->
			<cftry>
				<cfquery name="local.qryAddEnrollment" datasource="#application.dsn.membercentral.dsn#">
					#preserveSingleQuotes(local.addEnrollmentSQL)#
				</cfquery>
				<cfset local.xmlResult = xmlParse(local.qryAddEnrollment.xmlResult)>

				<cfset local.qryAddEnrollmentSuccess = val(XMLSearch(local.xmlResult,"string(/regresult/reg/r/@success)"))>
				<cfset local.qryAddEnrollmentPayTID = XMLSearch(local.xmlResult,"string(/regresult/reg/r/@paymentTransactionIDList)")>
				<cfset local.qryAddEnrollmentInvoiceIDList = XMLSearch(local.xmlResult,"string(/regresult/reg/r/@invoiceIDList)")>
				<cfset local.enrollmentIDList = XMLSearch(local.xmlResult,"string(/regresult/reg/r/@enrollmentIDList)")>
				<cfset local.bundleOrderIDList = XMLSearch(local.xmlResult,"string(/regresult/reg/r/@bundleOrderIDList)")>
				
				<cfset local.addEnrollmentSuccess = local.qryAddEnrollmentSuccess is 1>
				<cfif NOT local.addEnrollmentSuccess>
					<cfthrow message="qryAddEnrollment did not return a status of 1.">
				</cfif>
			<cfcatch type="any">
				<cfset local.addEnrollmentSuccess = false>
				<cfset local.qryAddEnrollmentSuccess = 0>
				<cfset local.qryAddEnrollmentPayTID = 0>
				<cfset local.qryAddEnrollmentInvoiceIDList = 0>
				<cfset local.enrollmentIDList = "">
				<cfset local.bundleOrderIDList = "">
				<cfset local.tmpErrStr = { sql=replace(local.addEnrollmentSQL,chr(10),"<br/>","ALL") }>
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local.tmpErrStr)>
				<cfrethrow>
			</cfcatch>
			</cftry>
			
			<cfif local.addEnrollmentSuccess>
				<cfquery name="local.strResponse.qryPaymentTransaction" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;

					declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;

					select MIN(t.detail) AS detail, MIN(t.transactionDate) AS transactionDate, SUM(t.amount) AS amount, MIN(ph.gatewayID) AS gatewayID, 
						MIN(ph.gatewayTransactionID) AS gatewayTransactionID, MIN(ph.gatewayApprovalCode) AS gatewayApprovalCode
					from dbo.tr_transactions as t
					inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = t.transactionID
					inner join dbo.tr_paymentHistory as ph on ph.orgID = @orgID and ph.historyID = tp.historyID
					where t.ownedByOrgID = @orgID
					and t.transactionID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#local.qryAddEnrollmentPayTID#">)
					and t.typeID = 2
					and t.statusID in (1,3);
				</cfquery>

				<cfquery name="local.strResponse.qryPaymentGateway" datasource="#application.dsn.membercentral.dsn#">
					SELECT ga.gatewayID, mpContent.rawContent as paymentInstructions
					FROM dbo.mp_profiles as pr
					INNER JOIN dbo.mp_gateways as ga on ga.gatewayid = pr.gatewayID
					OUTER APPLY dbo.fn_getContent(pr.paymentInstructionsContentID,1) as mpContent
					WHERE ga.gatewayID = <cfqueryparam value="#val(local.qryMerchantProfile.gatewayID)#" cfsqltype="cf_sql_integer">
					AND pr.profileID = <cfqueryparam value="#val(local.qryMerchantProfile.profileID)#" cfsqltype="cf_sql_integer">
				</cfquery>

				<cfset local.addRegInvoiceIDList = local.qryAddEnrollmentInvoiceIDList>
				<cfif local.keyExists("additionalPaymentFeeInvoiceID")>
					<cfset local.addRegInvoiceIDList = listAppend(local.addRegInvoiceIDList,local.additionalPaymentFeeInvoiceID)>
					<cfset local.strResponse.qryAdditionalFees = local.additionalFeesInfo.qryAdditionalFees>
				</cfif>

				<cfquery name="local.strResponse.qryInvoices" datasource="#application.dsn.membercentral.dsn#">
					select i.invoiceID, i.invoiceCode, i.fullInvoiceNumber as invoiceNumber
					from dbo.tr_invoices as i
					inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
					inner join dbo.organizations as o on o.orgID = m.orgID
					where i.invoiceID IN (<cfqueryparam value="#local.addRegInvoiceIDList#" cfsqltype="CF_SQL_INTEGER" list="yes">)
				</cfquery>
			</cfif>
		</cfif>

		<!--- send confirmations / remove item(s) from cart --->
		<cfif local.addEnrollmentSuccess>

			<cfquery name="local.qrySeminarEnrollments" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				select e.enrollmentID, e.seminarID, e.titleID, u.depoMemberDataID
				from dbo.tblEnrollments as e
				inner join dbo.tblUsers as u on e.userID = u.userID
				where e.enrollmentID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="0#local.enrollmentIDList#">);
			</cfquery>

			<cfquery name="local.qrySWLBundleEnrollments" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				select e.enrollmentID, bo.bundleID, e.seminarID, e.bundleOrderID, u.depoMemberDataID
				from dbo.tblEnrollments as e
				inner join dbo.tblBundleOrders as bo on bo.orderID = e.bundleOrderID
				inner join dbo.tblUsers as u on e.userID = u.userID
				inner join dbo.tblSeminarsSWLive as swl on swl.seminarID = e.seminarID
				where e.bundleOrderID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="0#local.bundleOrderIDList#">);
			</cfquery>

			<cfloop array="#local.arritemsToBuy#" index="local.ItemToBuy">

				<!--- loop over regcart to get full registration for item to send confirmations --->
				<cfset local.thisRegistration = "">
				<cfset local.swReg = getRegCache(item=0)>
				<cfloop array="#local.swReg.regCart#" index="local.itemInCart">
					<cfif local.itemInCart.itemKey eq local.ItemToBuy>
						<cfset local.thisRegistration = duplicate(local.itemInCart)>
						<cfbreak>
					</cfif>
				</cfloop>

				<!--- remove from cart --->
				<cfset local.hasCartItems = removeRegFromCart(itemkey=local.ItemToBuy, swReg=local.swReg)>

				<cfset local.swItemType = getToken(local.thisRegistration.item,1,'-')>
				<cfset local.programID = getToken(local.thisRegistration.item,2,'-')>

				<cfswitch expression="#local.swItemType#">
					<cfcase value="SWL">
						<cfif NOT structKeyExists(local,"objAdminSWL")>
							<cfset local.objAdminSWL = createObject("component","model.admin.seminarweb.seminarWebSWL")>
						</cfif>

						<cfquery name="local.qryThisEnrollment" dbtype="query">
							SELECT enrollmentID
							FROM [local].qrySeminarEnrollments
							WHERE seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programID#">
							AND depoMemberDataID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisRegistration.s1.depomemberdataid#">;
						</cfquery>

						<cfset local.objAdminSWL.registerSWLUser(enrollmentID=val(local.qryThisEnrollment.enrollmentID), seminarID=local.programID, 
							signUpOrgCode=arguments.event.getValue('mc_siteinfo.sitecode'), performedBy=local.thisRegistration.s1.depomemberdataid,
							sendEmailConnectionInstructions=1)>
					</cfcase>
					<cfcase value="SWOD">
						<cfif NOT structKeyExists(local,"objSWODEmails")>
							<cfset local.objSWODEmails = createObject("component","model.seminarweb.SWODEmails")>
						</cfif>

						<cfquery name="local.qryThisEnrollment" dbtype="query">
							SELECT enrollmentID
							FROM [local].qrySeminarEnrollments
							WHERE seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programID#">
							AND depoMemberDataID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisRegistration.s1.depomemberdataid#">;
						</cfquery>

						<cfset local.objSWODEmails.sendConfirmation(seminarID=local.programID, enrollmentID=val(local.qryThisEnrollment.enrollmentID), 
							performedBy=local.thisRegistration.s1.depomemberdataid, outgoingType="registerInstructions", 
							orgcode=arguments.event.getValue('mc_siteinfo.sitecode'))>
					</cfcase>
					<cfcase value="SWB">
						<cfif NOT structKeyExists(local,"objAdminSWL")>
							<cfset local.objAdminSWL = createObject("component","model.admin.seminarweb.seminarWebSWL")>
						</cfif>
						<cfif NOT structKeyExists(local,"objSWBundles")>
							<cfset local.objSWBundles = createObject("component","model.seminarweb.SWBundles")>
						</cfif>
						<cfif NOT structKeyExists(local,"objSWBundlesEmails")>
							<cfset local.objSWBundlesEmails = createObject("component","model.seminarweb.SWBundlesEmails")>
						</cfif>

						<!--- each SWL program should register at stream provider. supress individual program connection instructions. --->
						<cfquery name="local.qryThisSWLBundleEnrollments" dbtype="query">
							SELECT enrollmentID, seminarID
							FROM [local].qrySWLBundleEnrollments
							WHERE bundleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programID#">
							AND depoMemberDataID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisRegistration.s1.depomemberdataid#">;
						</cfquery>

						<cfloop query="local.qryThisSWLBundleEnrollments">
							<cfset local.objAdminSWL.registerSWLUser(enrollmentID=local.qryThisSWLBundleEnrollments.enrollmentID, seminarID=local.qryThisSWLBundleEnrollments.seminarID, 
								signUpOrgCode=arguments.event.getValue('mc_siteinfo.sitecode'), performedBy=local.thisRegistration.s1.depomemberdataid, sendEmailConnectionInstructions=0)>
						</cfloop>

						<!--- email confirmation of bundle purchase --->
						<cfset local.objSWBundlesEmails.sendConfirmation(bundleID=local.programID, depomemberdataID=local.thisRegistration.s1.depomemberdataid, 
							signUpOrgCode=arguments.event.getValue('mc_siteinfo.sitecode'),performedBy=local.thisRegistration.s1.depomemberdataid, outgoingType="registerInstructions")>
					</cfcase>
				</cfswitch>
			</cfloop>

			<cfset local.strResponse.qryItemsForReceipt = local.qryItemsToBuy>
			<cfset local.strResponse.qrySeminarEnrollments = local.qrySeminarEnrollments>
			<cfset local.strResponse.success = true>
			<cfset local.strResponse.response = "">
		</cfif>
		
		<cfreturn local.strResponse>
	</cffunction>

	<cffunction name="addSWReg_cf_nonOption" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="customText" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfif len(arguments.customText)>
			<cfsavecontent variable="local.addSWRegFieldSQL">
				<cfoutput>
				set @fieldID = #arguments.fieldID#;
				set @detail = '#replace(arguments.customText,"'","''","ALL")#';
				set @dataID = null;

				EXEC membercentral.dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@enrollmentID, @itemType='#arguments.itemType#',
					@valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.addSWRegFieldSQL = "">
		</cfif>

		<cfreturn local.addSWRegFieldSQL>
	</cffunction>

	<cffunction name="addSWReg_cf_option" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="valueIDList" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.addSWRegFieldSQL">
			<cfoutput>
			<cfloop list="#arguments.valueIDList#" index="local.valueitem">
				<cfif val(local.valueitem) gt 0>
					set @fieldID = #arguments.fieldID#;
					set @valueID = #val(local.valueitem)#;
					set @dataID = null;
					
					EXEC membercentral.dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@enrollmentID, @itemType='#arguments.itemType#',
						@valueID=@valueID, @fieldValue=NULL, @dataID=@dataID OUTPUT;
				</cfif>
			</cfloop>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.addSWRegFieldSQL>
	</cffunction>

	<cffunction name="prepareRegReceipt" access="public" output="false" returntype="string">
		<cfargument name="event" type="any" required="true">
		<cfargument name="strAddReg" type="struct" required="true">
		<cfargument name="qrySWP" type="query" required="true">

		<cfscript>
			var local = structNew();
			local.useMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteinfo.orgID'));
			local.strRegReceipt = duplicate(arguments.strAddReg);
			local.strRegProgram = {};

			local.displayedCurrencyType = arguments.qrySWP.showUSD is 1 ? " USD" : "";
		</cfscript>

		<!--- get payment info --->
		<cfquery name="local.totalAmountOnReceipt" dbtype="query">
			select sum(amount) - sum(discount) as totalAmount
			from local.strRegReceipt.qryItemsForReceipt
		</cfquery>

		<!--- ensure query is ordered by program so the grouping will work properly --->
		<cfset local.qryItemsForReceipt = local.strRegReceipt.qryItemsForReceipt>
		<cfquery name="local.qryItemsForReceiptSorted" dbtype="query">
			select itemKey, item, programName, programSubTitle, memberid, memberName, email, amount, discount, depomemberdataid, 
				rateName, amount - discount as discountAppliedTotal
			from [local].qryItemsForReceipt
			ORDER BY memberName, programName
		</cfquery>

		<cfoutput query="local.qryItemsForReceiptSorted" group="item">
			<cfset local.swItemType = getToken(local.qryItemsForReceiptSorted.item,1,'-')>
			<cfset local.mc_siteInfo = arguments.event.getValue('mc_siteInfo')>

			<cfswitch expression="#local.swItemType#">
				<cfcase value="SWL">
					<cfif NOT structKeyExists(local,"objSWLiveSeminars")>
						<cfset local.objSWLiveSeminars = createObject("component","model.seminarweb.SWLiveSeminars")>
					</cfif>
					<cfquery name="local.thisEnrollment" dbtype="query">
						Select qrySE.enrollmentID 
						from local.strRegReceipt.qrySeminarEnrollments as qrySE 
						where qrySE.depomemberdataid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryItemsForReceiptSorted.depomemberdataid#"> 
					</cfquery>
					<cfset local.qryEnrollment = local.objSWLiveSeminars.getEnrollmentByEnrollmentID(enrollmentID = local.thisEnrollment.enrollmentID)>
					<cfset local.qrySeminar = local.objSWLiveSeminars.getSeminarBySeminarID(seminarID=getToken(local.qryItemsForReceiptSorted.item,2,'-'))>
					<cfset local.parsedTime = local.objSWLiveSeminars.parseTimesFromWDDX(seminarWDDXTimeZones=local.qrySeminar.wddxTimeZones, orgWDDXTimeZones=arguments.qrySWP.wddxTimeZones, ifErrStartTime=local.qrySeminar.dateStart, ifErrEndTime=local.qrySeminar.dateEnd)>
					<cfset local.icalURL = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname#/?pg=semwebCatalog&panel=downloadICal&seminarid=#local.qrySeminar.seminarID#&accesscode=#local.qryEnrollment.SWLCode#&mode=stream">
					<cfset local.gcalURL = "#local.mc_siteInfo.scheme#://#local.mc_siteInfo.mainhostname#/?pg=semwebCatalog&panel=downloadGCal&seminarid=#local.qrySeminar.seminarID#&accesscode=#local.qryEnrollment.SWLCode#&mode=stream">
									
					<cfset local.strRegProgram[local.qryItemsForReceiptSorted.item] = { "programName":local.qrySeminar.seminarName, "programSubTitle":local.qrySeminar.seminarSubTitle, 
						"StartDate":local.parsedTime.StartDate, "EndDate":local.parsedTime.EndDate, "TZ":local.parsedTime.TimeZone, "TZstr":local.parsedTime.TimeZoneCompare,
						"icalURL":local.icalURL, "gcalURL":local.gcalURL, "freeRateDisplay":local.qrySeminar.freeRateDisplay }>
				</cfcase>
				<cfcase value="SWOD">
					<cfif NOT structKeyExists(local,"objSWODSeminars")>
						<cfset local.objSWODSeminars = createObject("component","model.seminarweb.SWODSeminars")>
					</cfif>
					<cfset local.qrySeminar = local.objSWODSeminars.getSeminarBySeminarID(seminarID=getToken(local.qryItemsForReceiptSorted.item,2,'-'))>
					<cfset local.strRegProgram[local.qryItemsForReceiptSorted.item] = { "programName":local.qrySeminar.seminarName, "programSubTitle":local.qrySeminar.seminarSubTitle, 
																						"freeRateDisplay":local.qrySeminar.freeRateDisplay }>
				</cfcase>
				<cfcase value="SWB">
					<cfif NOT structKeyExists(local,"objSWBundles")>
						<cfset local.objSWBundles = createObject("component","model.seminarweb.SWBundles")>
					</cfif>
					<cfset local.qryBundle = local.objSWBundles.getBundleByBundleID(bundleID=getToken(local.qryItemsForReceiptSorted.item,2,'-'), orgCode=arguments.event.getValue('mc_siteinfo.sitecode'))>
					<cfset local.strRegProgram[local.qryItemsForReceiptSorted.item] = { "programName":local.qryBundle.bundleName, "programSubTitle":local.qryBundle.bundleSubTitle,
																						"freeRateDisplay":local.qryBundle.freeRateDisplay }>
				</cfcase>
			</cfswitch>
		</cfoutput>

		<!--- Get Purchaser Info --->
		<cfset local.qryPurchaser = application.objMember.getMemberInfo(memberID=local.useMemberID)>
		<cfset local.qryPurchaserAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=local.qryPurchaser.orgID, memberid=local.qryPurchaser.memberID)>
		<cfset local.qryMainEmail = application.objMember.getMainEmail(memberID=local.qryPurchaser.memberID)>
		
		<cfset local.strReceipt.PurchaserMemberID = local.qryPurchaser.memberID>
		<cfset local.strReceipt.PurchaserName = "#local.qryPurchaser.firstname# #local.qryPurchaser.lastname#">
		<cfset local.strReceipt.PurchaserCompany = local.qryPurchaser.company>

		<cfquery name="local.qryOverrideEmail" dbtype="query" maxrows="1">
			select email
			from [local].qryItemsForReceipt
			where memberid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryPurchaser.memberID#">
		</cfquery>

		<cfset local.strReceipt.PurchaserOverrideEmail = local.qryOverrideEmail.email>
		<cfset local.strReceipt.PurchaserEmail = len(local.qryMainEmail.email) gt 0 ? local.qryMainEmail.email : local.strReceipt.PurchaserOverrideEmail>
		
		<cfset local.qryOrgIdentity = application.objOrgInfo.getOrgIdentity(orgIdentityID=arguments.qrySWP.orgIdentityID)>

		<cfsavecontent variable="local.strReceipt.PurchaserAddress">
			<cfoutput>
			<cfif len(local.qryPurchaserAddr.address1)>#local.qryPurchaserAddr.address1#<br/></cfif>
			<cfif local.qryPurchaserAddr.hasAddress2 is 1 and len(local.qryPurchaserAddr.address2)>#local.qryPurchaserAddr.address2#<br/></cfif>
			<cfif local.qryPurchaserAddr.hasAddress3 is 1 and len(local.qryPurchaserAddr.address3)>#local.qryPurchaserAddr.address3#<br/></cfif>
			#local.qryPurchaserAddr.city# #local.qryPurchaserAddr.stateCode# #local.qryPurchaserAddr.postalCode#
			</cfoutput>
		</cfsavecontent>

		<cfif arguments.qrySWP.handlesOwnPayment is 1>
			<!--- gather invoices --->
			<cfset local.objInvoice = CreateObject("component","model.admin.transactions.invoice")>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfset local.arrInvoicePaths = arrayNew(1)>
			<cfloop query="local.strRegReceipt.qryInvoices">
				<cfset local.strInvoice = local.objInvoice.generateInvoice(siteID=arguments.event.getValue('mc_siteinfo.siteid'), invoiceID=local.strRegReceipt.qryInvoices.invoiceID, tmpFolder=local.strFolder.folderPath, encryptFile=true, namedForBundle=false)>
				<cfset arrayAppend(local.arrInvoicePaths, { file:local.strInvoice.displayName, folderpath:local.strFolder.folderPath })>
			</cfloop>
		<cfelse>
			<cfquery name="local.qryDepoTransactions" dbtype="query">
				select sum(total) as totalAmount, max(datePurchased) as datePurchased
				from [local].strRegReceipt.qryDepoTransactions
			</cfquery>
		</cfif>

		<!--- store receipt in session so it can be sent and resent --->
		<cfset local.resendKey = createUUID()>
		<cfset local.tmpStr = { 
			"email"=local.qryOrgIdentity.email,
			"networkEmailFrom"=arguments.event.getValue('mc_siteInfo.networkEmailFrom'),
			"orgname"=arguments.event.getValue('mc_siteinfo.orgname'),
			"siteid"=arguments.event.getValue('mc_siteinfo.siteid'),
			"sitename"=arguments.event.getValue('mc_siteinfo.sitename'),
			"sitecode"=arguments.event.getValue('mc_siteinfo.sitecode'),
			"totalAmountOnReceipt":val(local.totalAmountOnReceipt.totalAmount),
			"qryItemsForReceiptSorted":local.qryItemsForReceiptSorted,
			"strRegProgram":local.strRegProgram,
			"purchaserMemberID"=local.strReceipt.purchaserMemberID,
			"purchaserName"=local.strReceipt.PurchaserName,
			"purchaserCompany"=local.strReceipt.PurchaserCompany,
			"purchaserAddress"=local.strReceipt.PurchaserAddress,
			"purchaserOverrideEmail"=local.strReceipt.PurchaserOverrideEmail,
			"displayedCurrencyType":local.displayedCurrencyType
		}>

		<cfif arguments.qrySWP.handlesOwnPayment is 1>
			<cfif local.strRegReceipt.keyExists('qryAdditionalFees')>
				<cfset local.tmpStr['qryAdditionalFees'] = local.strRegReceipt.qryAdditionalFees>
			</cfif>
			
			<cfset local.tmpStr["arrInvoicePaths"] = local.arrInvoicePaths>
			<cfset local.tmpStr["qryPaymentTransaction"] = local.strRegReceipt.qryPaymentTransaction>
			<cfset local.tmpStr["qryPaymentGateway"] = local.strRegReceipt.qryPaymentGateway>

			<cfsavecontent variable="local.tmpStr['receiptData']">
				<cfinclude template="/views/semWebCatalog/SWRegReceiptForEmail.cfm">
			</cfsavecontent>
		<cfelse>
			<cfset local.tmpStr["qryDepoTransactions"] = local.qryDepoTransactions>
		</cfif>
		
		<cfset local.strSWReceipts = application.mcCacheManager.sessionGetValue(keyname='strSWReceipts', defaultValue={})>
		<cfset local.strSWReceipts.append({ "#local.resendKey#": duplicate(local.tmpStr) })>
		<cfset application.mcCacheManager.sessionSetValue(keyname='strSWReceipts', value=local.strSWReceipts)>
		
		<cfif arguments.qrySWP.handlesOwnPayment is 1 AND len(local.strReceipt.PurchaserEmail)>
			<cfset CreateObject("component","model.admin.seminarWeb.seminarWebSWCommon").sendPaymentReceipt(receiptUUID=local.resendKey, sendToEmail=local.strReceipt.PurchaserEmail)>
		</cfif>

		<!--- clear remembered payer when not logged in --->
		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfset application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0)>
			<cfset application.objPlatform.setSignedCookie(cookiename="mcidme", value="#local.strReceipt.purchaserMemberID#|#arguments.event.getValue('mc_siteinfo.orgID')#|#GetTickCount()#", expires="90")>
		</cfif>

		<!--- return encrypted receipt uid --->
		<cfreturn Replace(URLEncodedFormat(ToBase64(Encrypt(local.resendKey,"M3m18eR_CenTR@l"))),"%","xPcmKx","ALL")>
	</cffunction>

	<cffunction name="showRegReceipt" access="public" output="false" returntype="string">
		<cfargument name="event" type="any" required="true">
		<cfargument name="qrySWP" type="query" required="true">

		<cfset var local = structNew()>
		<cfset local.receiptUUID = decrypt(toString(toBinary(URLDecode(replace(arguments.event.getTrimValue('rk',''),"xPcmKx","%","ALL")))),"M3m18eR_CenTR@l")>

		<cfset local.strSWReceipts = application.mcCacheManager.sessionGetValue(keyname='strSWReceipts', defaultValue={})>
		<cfif len(local.receiptUUID) AND isStruct(local.strSWReceipts) AND structKeyExists(local.strSWReceipts,local.receiptUUID)>
			<cfset local.strReceipt = duplicate(local.strSWReceipts[local.receiptUUID])>
			<cfquery name="local.qryDistinctMembers" dbtype="query">
				SELECT DISTINCT memberID
				FROM local.strReceipt.qryItemsForReceiptSorted
			</cfquery>
			<cfset local.memberNumber = application.objMember.getMemberNumberByMemberID(memberID=local.qryDistinctMembers.memberID,orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
 			<cfset local.memberKey = application.objMergeCodes.generateMemberKey(orgcode=arguments.event.getValue('mc_siteinfo.orgCode'), membernumber=local.memberNumber)>
			<cfscript>
				local.programType = GetToken(local.strReceipt.qryItemsForReceiptSorted.item,1,'-');
				local.programID = GetToken(local.strReceipt.qryItemsForReceiptSorted.item,2,'-');

				switch(local.programType){
					case 'SWL':
						local.programLink =  "/?pg=semwebCatalog&panel=showLive&seminarID=#local.programID#";
						break;
					case 'SWOD':
						local.programLink =  "/?pg=semwebCatalog&panel=showSWOD&seminarID=#local.programID#";
						break;
					case 'SWB':
						local.programLink =  "/?pg=semwebCatalog&panel=showBundle&bundleID=#local.programID#";
						break;
					default :
						local.programLink = arguments.event.getValue('mainurl');
						break;
				}
			</cfscript>		
			<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser) AND local.qryDistinctMembers.recordCount EQ 1 AND local.qryDistinctMembers.memberID EQ session.cfcUser.memberData.memberID>		
				<cfset local.onReceiptStatement = "You have completed the registration(s) listed in the payment receipt below. You can now <a href='#local.programLink#'>Enter the Program</a> or return to <a href='/?pg=semwebCatalog&panel=browse'>#arguments.qrySWP.brandHomeTab#</a>.">				
			<cfelse>
				<cfset local.onReceiptStatement = "You have completed the registration(s) listed in the payment receipt below. You can now <a href='?pg=semwebCatalog&panel=My&mk=#local.memberKey#'>Enter the Program</a> or return to <a href='/?pg=semwebCatalog&panel=browse'>#arguments.qrySWP.brandHomeTab#</a>.">
			</cfif>
			<cfset local.maskedEmailAddress = CreateObject("component","model.system.platform.memberFieldsets").getMaskedEmailAddress(email=local.strReceipt.qryItemsForReceiptSorted.email)>
			
			<cfset local.onReceiptStatementEmail = "A confirmation email with program access instructions was also sent to #local.maskedEmailAddress# <a href='javascript:void(0);' class='swreg-ml-auto qIcon'><i data-original-title='Wrong email? Contact #arguments.qrySWP.supportEmail# or call #arguments.qrySWP.supportPhone#' class='tooltip-icon fa-solid fa-question fa-lg'></i></a>">
			
			<cfsavecontent variable="local.data">
				<cfinclude template="/views/semWebCatalog/#arguments.event.getValue('viewDirectory','default')#/SWRegReceipt.cfm">
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<h4>Unable to display confirmation.</h4>
					<div class="alert">The program registration confirmation you are trying to view is invalid or has expired.</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="offerCouponForSWReg" access="public" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="swReg" type="struct" required="true">
		<cfargument name="totalDiscount" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.offerCoupon = false>

		<cfif arguments.totalDiscount gt 0>
			<cfset local.offerCoupon = false>
		<cfelse>
			<cfset local.strItem = SWRegCartQualifiedForCoupon(swReg=arguments.swReg)>

			<cfif local.strItem.isQualified>
				<cfstoredproc procedure="tr_hasValidCoupons" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
					<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="#local.strItem.applicationType#">
					<cfprocparam type="IN" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.strItem.cartItemsXML#">
					<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="0">
					<cfprocparam type="OUT" cfsqltype="CF_SQL_BIT" variable="local.offerCoupon">
				</cfstoredproc>
			</cfif>
		</cfif>

		<cfreturn local.offerCoupon>
	</cffunction>

	<cffunction name="SWRegCartQualifiedForCoupon" access="public" output="false" returntype="struct">
		<cfargument name="swReg" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "applicationType" = 'SeminarWeb', "isQualified" = false, "cartItemsXML" = '<cart />' }>

		<!--- empty cart --->
		<cfif NOT arrayLen(arguments.swReg.regCart)>
			<cfreturn local.returnStruct>
		</cfif>

		<cfxml variable="local.returnStruct.cartItemsXML">
			<cfoutput>
				<cart>
					<cfloop array="#arguments.swReg.regCart#" index="local.thisReg">
						<cfif local.thisReg.s1.memberID gt 0 and local.thisReg.s2.rateID gt 0 and local.thisReg.s2.price gt 0>
							<item mid="#local.thisReg.s1.memberID#" rateid="#local.thisReg.s2.rateID#" itemtype="#getToken(local.thisReg.item,1,'-')#" />
						</cfif>
					</cfloop>
				</cart>
			</cfoutput>
		</cfxml>

		<!--- remove the <xml> tag, specifically the encoding. --->
		<cfset local.returnStruct.cartItemsXML = replaceNoCase(toString(local.returnStruct.cartItemsXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

		<cfif arrayLen(XMLSearch(local.returnStruct.cartItemsXML,"/cart/item"))>
			<cfset local.returnStruct.isQualified = true>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="applyCouponToRegCart" access="private" output="false" returntype="struct">
		<cfargument name="swReg" type="struct" required="true">
		<cfargument name="qryCoupon" type="query" required="true">
		<cfargument name="qualifiedCartItemsXML" type="xml" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "totalAmount"=0, "discount"=0, "discountAppliedTotal"=0 }>
		<cfset local.swReg = duplicate(arguments.swReg)>

		<!--- empty cart --->
		<cfif NOT arrayLen(local.swReg.regCart) OR NOT arrayLen(XMLSearch(arguments.qualifiedCartItemsXML,"/cart/item"))>
			<cfreturn local.returnStruct>
		</cfif>

		<cfset local.strCoupon = {  couponID = arguments.qryCoupon.couponID,
									couponCode = arguments.qryCoupon.couponCode,
									pctOff = arguments.qryCoupon.pctOff,
									pctOffMaxOff = arguments.qryCoupon.pctOffMaxOff,
									amtOff = arguments.qryCoupon.amtOff,
									redeemDetail = arguments.qryCoupon.redeemDetail,
									invoiceDetail = arguments.qryCoupon.invoiceDetail }>

		<cfloop from="1" to="#arrayLen(local.swReg.regCart)#" index="local.itemNum">
			<cfset local.thisItem = duplicate(local.swReg.regCart[local.itemNum])>

			<cfset local.swReg.regCart[local.itemNum].s2.couponIDValidated = arguments.qryCoupon.couponID>

			<!--- qualified cart item --->
			<cfif arrayLen(XMLSearch(arguments.qualifiedCartItemsXML,"/cart/item[@mid='#local.thisItem.s1.memberID#'][@rateid='#local.thisItem.s2.rateID#'][@itemtype='#getToken(local.thisItem.item,1,'-')#']"))>
				<cfset local.swReg.regCart[local.itemNum].s2.strCoupon = duplicate(local.strCoupon)>
			</cfif>
		</cfloop>
		
		<cfset updateRegCache(swReg=local.swReg)>
		<cfset local.qryRegCart = swRegCartToQuery()>

		<cfquery name="local.qryAmount" dbtype="query">
			select sum(amount) as totalAmount, sum(discount) as totalDiscount
			from [local].qryRegCart
		</cfquery>

		<cfset local.returnStruct = { "totalAmount" = local.qryAmount.totalAmount, "discount" = local.qryAmount.totalDiscount, 
									  "discountAppliedTotal" = NumberFormat(local.qryAmount.totalAmount - local.qryAmount.totalDiscount,"9.99") }>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getAmountAfterCouponApplied" access="private" output="false" returntype="struct">
		<cfargument name="sitecode" type="string" required="true">
		<cfargument name="strReg" type="struct" required="true">
		<cfargument name="handlesOwnPayment" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.strDiscount = { finalAmt=0, totalTax=0, discount=0, discountExcTax=0 }>

		<cfset local.totalAmt = arguments.strReg.s2.price>
		<cfset local.totalAmtIncTax = local.totalAmt + arguments.strReg.s2.salestax>
		<cfset local.totalTax = arguments.strReg.s2.salestax>	

		<cfif val(local.totalAmt) eq 0 OR NOT structKeyExists(arguments.strReg.s2.strCoupon, "couponID")>
			<cfreturn local.strDiscount>
		</cfif>

		<cfif val(arguments.strReg.s2.strCoupon.pctOff) gt 0>
			<cfset local.strDiscount.discount = NumberFormat((local.totalAmt * (arguments.strReg.s2.strCoupon.pctOff / 100)),"9.99")>
		
			<cfif val(arguments.strReg.s2.strCoupon.pctOffMaxOff) gt 0 and max(arguments.strReg.s2.strCoupon.pctOffMaxOff,local.strDiscount.discount) eq local.strDiscount.discount>
				<cfset local.strDiscount.discount = arguments.strReg.s2.strCoupon.pctOffMaxOff>
			</cfif>
		<cfelseif val(arguments.strReg.s2.strCoupon.amtOff) gt 0>
			<cfset local.strDiscount.discount = arguments.strReg.s2.strCoupon.amtOff>
		</cfif>

		<cfset local.strDiscount.discountExcTax = local.strDiscount.discount>
		<cfset local.strDiscount.finalAmt = NumberFormat(local.totalAmt - local.strDiscount.discount,"9.99")>

		<cfif local.strDiscount.finalAmt lte 0>
			<cfset local.strDiscount.finalAmt = 0>
			<cfset local.strDiscount.totalTax = 0>
			<cfset local.strDiscount.discount = local.totalAmtIncTax>
			<cfset local.strDiscount.discountExcTax = local.totalAmt>
		
		<!--- if this cart item taxed --->
		<cfelseif local.strDiscount.finalAmt gt 0 and val(local.totalTax) gt 0>
			<cfif arguments.handlesOwnPayment is 1>
				<cfset local.strTax = CreateObject("component","model.system.platform.accounting").getTaxForUncommittedSale(saleGLAccountID=arguments.strReg.s2.revGL, 
					saleAmount=local.strDiscount.finalAmt, transactionDate=now(), stateIDForTax=arguments.strReg.s3.stateIDforTax, zipForTax=arguments.strReg.s3.zipforTax)>
				<cfset local.taxAmount = local.strTax.totalTaxAmt>
			<cfelse>
				<cfswitch expression="#getToken(arguments.strReg.item,1,'-')#">
					<cfcase value="SWL">
						<cfset local.acctCode = "7001">
					</cfcase>
					<cfcase value="SWOD">
						<cfset local.acctCode = "7000">
					</cfcase>
					<cfcase value="SWB">
						<cfset local.qryBundle = CreateObject("component","model.seminarweb.SWBundles").getBundleByBundleID(bundleID=getToken(arguments.strReg.item,2,'-'), orgcode=arguments.sitecode)>
						<cfif local.qryBundle.isSWOD is 1>
							<cfset local.acctCode = "7000">
						<cfelse>
							<cfset local.acctCode = "7001">
						</cfif>
					</cfcase>
				</cfswitch>
				<cfset local.taxAmount = getTSSalesTaxAmount(orgCode=arguments.sitecode, amountBilled=local.strDiscount.finalAmt, 
					billingState=arguments.strReg.s3.billingstate, billingZip=arguments.strReg.s3.zipforTax, acctCode=local.acctCode).salestax>
			</cfif>

			<cfset local.finalAmtIncTax = NumberFormat(local.strDiscount.finalAmt + local.taxAmount,"9.99")>
			<cfset local.strDiscount.totalTax = local.taxAmount>
			<cfset local.strDiscount.discount = NumberFormat(local.totalAmtIncTax - local.finalAmtIncTax,"9.99")>
		</cfif>

		<cfreturn local.strDiscount>
	</cffunction>

	<cffunction name="validateCouponCode" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="couponCode" type="string" required="true">
		
		<cfscript>
		var local = structNew();
		
		local.returnStruct = { "success"=false, "isvalidcoupon"=false, "couponresponse"="Invalid Promo Code", "totalamt"=0, "discount"=0, "discountappliedtotal"=0 };

		arguments.couponCode = trim(arguments.couponCode);
		
		// if no length 
		if (len(arguments.couponCode) is 0) return local.returnStruct;
		
		local.swReg = getRegCache(item=0);
		local.strItem = SWRegCartQualifiedForCoupon(swReg=local.swReg);

		// unqualified
		if (NOT local.strItem.isQualified) return local.returnStruct;

		var sqlParams = {
			siteID = { value=arguments.mcproxy_siteID, cfsqltype="CF_SQL_INTEGER" },
			applicationType = { value=local.strItem.applicationType, cfsqltype="CF_SQL_VARCHAR" },
			cartItemsXML = { value=local.strItem.cartItemsXML, cfsqltype="CF_SQL_LONGVARCHAR" },
			couponCode = { value=arguments.couponCode, cfsqltype="CF_SQL_VARCHAR" }
		};

		// using query to exec proc here due to xml output and luceeV5.2 not supporting xml queryparam
		var qryValidCoupon = queryExecute("
			SET NOCOUNT ON;
			
			DECLARE @siteID int = :siteID, @applicationType varchar(20) = :applicationType, @cartItemsXML xml = :cartItemsXML,
				@couponCode varchar(15) = :couponCode, @couponID int, @couponMessage varchar(200), @qualifiedCartItemsXML xml;
			EXEC dbo.tr_isValidCouponCode @siteID=@siteID, @applicationType=@applicationType, @cartItemsXML=@cartItemsXML, 
				@couponCode=@couponCode, @couponID=@couponID OUTPUT, @couponMessage=@couponMessage OUTPUT, 
				@qualifiedCartItemsXML=@qualifiedCartItemsXML OUTPUT;
			SELECT @couponID AS couponID, @couponMessage AS couponMessage, @qualifiedCartItemsXML AS qualifiedCartItemsXML;
			", 
			sqlParams, { datasource="#application.dsn.membercentral.dsn#" }
		);

		local.couponID = val(qryValidCoupon.couponID);
		local.returnStruct.couponResponse = qryValidCoupon.couponMessage;
		local.qualifiedCartItemsXML = qryValidCoupon.qualifiedCartItemsXML;

		// valid coupon
		if (local.couponID gt 0) {
			local.qryCoupon = CreateObject("component","model.admin.coupons.coupon").getCouponDetailByCouponID(siteID=arguments.mcproxy_siteID, couponID=local.couponID);
			local.returnStruct.isValidCoupon = true;

			local.strPrice = applyCouponToRegCart(swReg=local.swReg, qryCoupon=local.qryCoupon, qualifiedCartItemsXML=local.qualifiedCartItemsXML);

			local.returnStruct.totalamt = NumberFormat(local.strPrice.totalAmount,"9.99");
			local.returnStruct.discount = NumberFormat(local.strPrice.discount,"9.99");
			local.returnStruct.discountappliedtotal = NumberFormat(local.strPrice.discountAppliedTotal,"9.99");
		}

		local.returnStruct.success = true;
		
		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="removeAppliedCoupon" access="public" output="false" returntype="struct">
		<cfscript>
		var local = structNew();
		
		local.returnStruct = { "success" = false };
		
		// remove applied coupon
		removeAppliedCouponFromRegCart();

		local.returnStruct.success = true;
		
		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="removeAppliedCouponFromRegCart" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.swReg = getRegCache(item=0)>
		
		<!--- empty cart --->
		<cfif NOT arrayLen(local.swReg.regCart)>
			<cfreturn false>
		</cfif>

		<cfloop array="#local.swReg.regCart#" index="local.item">
			<cfset local.item.s2.couponIDValidated = 0>
			<cfset local.item.s2.strCoupon = structNew()>
			<cfset local.item.s2.discount = 0>
			<cfset local.item.s2.discountExcTax = 0>
		</cfloop>

		<cfset updateRegCache(swReg=local.swReg)>
		<cfset local.qryRegCart = swRegCartToQuery()>

		<cfreturn true>
	</cffunction>

	<cffunction name="saveRegistrantPrimaryEmailAddress" access="private" output="false" returntype="void">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="arrPrimaryEmailAddress" type="array" required="true">

		<cfset var local = structNew()>

		<cfloop array="#arguments.arrPrimaryEmailAddress#" index="local.thisReg">
			<cfquery name="local.qryGetPrimaryEmailType" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				DECLARE @memberID int, @orgID int;
				SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisReg.memberID#">;
				SELECT @orgID = orgID from dbo.ams_members where memberID = @memberID;

				SELECT TOP 1 met.emailType
				FROM dbo.ams_memberEmailTags AS metag
				INNER JOIN dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = @orgID
					AND metagt.emailTagTypeID = metag.emailTagTypeID
					AND metagt.emailTagType = 'Primary'
				INNER JOIN dbo.ams_memberEmailTypes as met on met.orgID = @orgID 
					and met.emailTypeID = metag.emailTypeID
				WHERE metag.orgID = @orgID
				AND metag.memberID = @memberID;
			</cfquery>

			<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.thisReg.memberID, sitecode=arguments.sitecode)>
			<cfset local.objSaveMember.setEmail(type=local.qryGetPrimaryEmailType.emailType, value=local.thisReg.newPrimaryEmailAddress)>
			<cfset local.strResult = local.objSaveMember.saveData()>
		</cfloop>
	</cffunction>

	<cffunction name="addRegLog" access="private" output="false" returntype="void">
		<cfargument name="programID" type="numeric" required="true">
		<cfargument name="programType" type="string" required="true">
		<cfargument name="strLog" type="struct" required="true">

		<cfset var qryAddRegLog = "">

		<cfquery name="qryAddRegLog" datasource="#application.dsn.platformstatsMC.dsn#">
			INSERT INTO dbo.sw_registrationLog (programID, programType, statsSessionID, jsonLog, dateEntered)
			VALUES (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">,
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.programType#">,
				<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.statsSessionID#">,
				<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#serializeJSON(arguments.strLog)#">,
				GETDATE()
			);
		</cfquery>
	</cffunction>

</cfcomponent>