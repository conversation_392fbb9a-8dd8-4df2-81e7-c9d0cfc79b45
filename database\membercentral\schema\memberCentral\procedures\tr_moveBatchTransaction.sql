ALTER PROC dbo.tr_moveBatchTransaction
@orgID int,
@transactionID int,
@fromBatchID int,
@toBatchID int,
@skipSysCreatedChecks bit

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @siteID int;
	declare @tblTrans TABLE (transactionID int PRIMARY KEY);

	-- if both batches are same, do nothing
	IF @fromBatchID = @toBatchID
		GOTO on_done;
	
	-- batches must belong to org
	-- both batches must be open
	-- cannot be system created batch
	IF EXISTS (
		select batchID 
		from dbo.tr_batches 
		where batchID in (@fromBatchID,@toBatchID) 
		and (
			orgID <> @orgID
			OR 
			statusID <> 1
			OR 
			(@skipSysCreatedChecks = 0 AND isSystemCreated = 1)
		)
		) RAISERROR('batches are not compatible', 16, 1);

	-- transaction must be a payment,refund
	IF EXISTS (
		select transactionID
		from dbo.tr_transactions
		where transactionID = @transactionID
		and typeID not in (2,4)
		) RAISERROR('transaction must be a payment or refund', 16, 1);


	-- move transactionID and all allocations/WO/Voids from the one batch to the other
	IF OBJECT_ID('tempdb..#tmpBTBatches') IS NOT NULL 
		DROP TABLE #tmpBTBatches;
	IF OBJECT_ID('tempdb..#tmpBTTrans') IS NOT NULL 
		DROP TABLE #tmpBTTrans;
	CREATE TABLE #tmpBTBatches (batchID int PRIMARY KEY);
	-- detail here is 600 to account for special appending in the data
	CREATE TABLE #tmpBTTrans (batchID int, transactionID int, payTID int, debitGLAccountID int, creditGLAccountID int, 
		CashGLAccountID int, RevenueGLAccountID int, CashTransactionID int, RevenueTransactionID int, transactionDate datetime,
		amount decimal(18,2), detail varchar(600), gatewayTransactionID varchar(40), gatewayApprovalCode varchar(20), [type] varchar(20), 
		memberid int, memberNumber varchar(50), memberCompany varchar(200), memberName varchar(230), recordedByMemberID int, 
		recordedByMemberNumber varchar(50), recordedByMemberName varchar(230), deferredRecognitionDate datetime);

	INSERT INTO #tmpBTBatches (batchID) VALUES (@fromBatchID);

	-- this populates #tmpBTTrans
	EXEC dbo.tr_getBatchTransactionsBulk @orgID=@orgID;
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	INSERT INTO @tblTrans (transactionID)
	select transactionID
	from #tmpBTTrans
	where payTID = @transactionID;

	IF OBJECT_ID('tempdb..#tmpBTBatches') IS NOT NULL 
		DROP TABLE #tmpBTBatches;
	IF OBJECT_ID('tempdb..#tmpBTTrans') IS NOT NULL 
		DROP TABLE #tmpBTTrans;

	select @siteID = recordedOnSiteID from dbo.tr_transactions where transactionID = @transactionID;

	IF dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	BEGIN TRAN;
		update bt
		set bt.batchID = @toBatchID
		from dbo.tr_batchTransactions as bt
		inner join @tblTrans as tmp on bt.orgID = @orgID and tmp.transactionID = bt.transactionID;

		-- add transactions to limit checking table
		INSERT INTO platformQueue.dbo.queue_transactionLimitFlagging (transactionID, orgID, siteID)
		SELECT transactionID, @orgID, @siteID
		from @tblTrans;
	COMMIT TRAN;

	on_done:
	IF dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	IF dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
