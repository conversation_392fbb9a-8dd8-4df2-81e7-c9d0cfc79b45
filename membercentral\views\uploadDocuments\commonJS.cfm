<cfif arguments.event.getValue('step') EQ 2>
	<cfsavecontent variable="local.pageJS">
		<cfoutput>
		<script type="text/javascript">
			let uppyDocuments, depoDocsUploadedCount = 0;

			function onChangeDepoDocStates() {
				validateUploadDocsForm('prevalidate');
			}
			function onClickCreditPreference(credType) {
				if (credType.toLowerCase() == 'amazon') $('##depoDocAmazonBucksInfo').show();
				else  $('##depoDocAmazonBucksInfo').hide();
				validateUploadDocsForm('prevalidate');
			}
			function showUploadSection() {
				if (validateUploadDocsForm('validateInitSteps')) {
					$('##awardCredType').html($('input[name="credType"]:checked').val() == 'amazon' ? 'Amazon Credits' : 'Deposition Purchase Credits');
					$('##depoDocOrgDisp').html($('##docState option:selected').text());
					$('.docUploadSteps').hide();
					$('.docUpdTool').show();
					if (uppyDocuments && uppyDocuments.getFiles().length)
						enableStartUploadOfFilesBtn();
				}
			}
			function showLearnMoreSection(){
				$("##learnMoreSection").show();
			}
			async function startUploadDocs() {
				$('##btnUploadDocs').prop('disabled',true).html('Uploading Documents...');
				if (validateUploadDocsForm('final')) {
					let uploadDocsSuccess = await uppyDepoDocUploadPromise(uppyDocuments);
					if(!uploadDocsSuccess) {
						uploadDocsShowErr("An error occured while uploading the documents.");
					}
				}
			}
			function uppyDepoDocUploadPromise(uppyInstance) {
				return uppyInstance.retryAll()
					.then((result) => uppyInstance.upload())
					.then((result) => {
						if (result.failed.length > 0) {
							console.error('Failed ['+uppyInstance.getID()+']:');
							result.failed.forEach((file) => {
								console.error(file.error);
							});
							return false;
						} else {
							return true;
						}
					}).catch(error => {
						console.error('Error ['+uppyInstance.getID()+']:');
						console.log(error);
						return false;
					});
			}
			function validateUploadDocsForm(validateMode) {
				let emailRegEx = new RegExp("#application.regEx.email#","i");
				let arrReq = [];

				let enableButtonWhenError = false, disableButtonWhenError = false, enableButtonWhenNoError = false, 
					disableButtonWhenNoError = false, showErrorMsg = true;
				
				if (validateMode == 'final') {
					enableButtonWhenError = true;
				} else if (validateMode == 'prevalidate') {
					disableButtonWhenError = true;
					enableButtonWhenNoError = true;
					showErrorMsg = false;						
				} else if (validateMode == 'validateInitSteps') {
					disableButtonWhenNoError = true;
				}

				if ($('##docState').val() == '') arrReq.push('Which trial lawyer group gets Credit for your depositions?');
				if (!$('input[name="credType"]').is(':checked')) arrReq.push('What type of Credit do you want for your depositions?');
				else if ($('input[name="credType"]:checked').val() == 'amazon') {
					if (!$('##depoDocAmazonBucksFullName').val().trim().length)
						arrReq.push('Enter the Full Name for Amazon Gift Card Recipient.');
					if (!$('##depoDocAmazonBucksEmail').val().trim().length || !(emailRegEx.test($('##depoDocAmazonBucksEmail').val())))
						arrReq.push('Enter a valid Email Address for Amazon Gift Card Recipient.');
				}
				if ($('##depoDocUploader').is(':visible') && !(uppyDocuments && uppyDocuments.getFiles().length))
					arrReq.push('Drag and drop files to the area below, or click "Add files" to browse your computer.');

				if (arrReq.length) {
					if (enableButtonWhenError) {
						enableStartUploadOfFilesBtn();
					} else if (disableButtonWhenError) {
						disableStartUploadOfFilesBtn();
					}
					if (showErrorMsg) uploadDocsShowErr(arrReq.join('<br/>'));
					else uploadDocsHideErr();
					return false;
				} else {
					if (enableButtonWhenNoError) {
						enableStartUploadOfFilesBtn();
					} else if (disableButtonWhenNoError) {
						disableStartUploadOfFilesBtn();
					}
					uploadDocsHideErr();
					return true;
				}
			}
			function uploadDocsShowErr(errmsg) {
				$('##depoDocUploaderError').html(errmsg).show();
			}
			function uploadDocsHideErr() {
				$('##depoDocUploaderError').html('').hide();
			}
			function enableStartUploadOfFilesBtn() {
				$('##btnUploadDocs').addClass('mc_blinkerbtn').prop('disabled',false).html('Start Upload of Files');
			}
			function disableStartUploadOfFilesBtn() {
				$('##btnUploadDocs').removeClass('mc_blinkerbtn').prop('disabled',true);
			}
			function showUploadSteps() {
				$('##docUploadDocConf').html('').hide();
				$('.docUpdTool').hide();
				$('.docUploadSteps').show(300);
			}
			function uploadDepositionsAgain() {
				$('##docUploadDocConf').html('').hide();
				$('##depoDocUploader,##depoDocUploaderButtonContainer').show(300);
				disableStartUploadOfFilesBtn();
			}
			
			$(function() {
				dynamicallyLoadUppy().then((result) => {
					uppyDocuments = new Uppy({ id: 'uppyDocuments', debug: true, autoProceed: false, locale:{
						strings: {
							exceedsSize: 'Exceeds maximum allowed size of %{size}',
							dropPasteFiles: 'Drop documents here or %{browseFiles}',
						},
					}})
					.use(XHRUpload, { endpoint: '#local.frmUploadLink#', formData: true, fieldName: 'depoDoc', allowedMetaFields: ['docState', 'creditType', 'depoDocAmazonBucksFullName', 'depoDocAmazonBucksEmail'] })
					.on('file-added', (file) => {
						validateUploadDocsForm('prevalidate');
					})
					.on('file-removed', (file) => {
						validateUploadDocsForm('prevalidate');
					})
					.on('upload', (data) => {
						const docState = $('##docState').val();
						const creditType = $('input[name="credType"]:checked').val();
						const depoDocAmazonBucksFullName = creditType == 'amazon' ? $('##depoDocAmazonBucksFullName').val().trim() : '';
						const depoDocAmazonBucksEmail = creditType == 'amazon' ? $('##depoDocAmazonBucksEmail').val().trim() : '';

						const files = uppyDocuments.getFiles();
						files.forEach(file => {
							uppyDocuments.setFileMeta(file.id, {
								docState: docState,
								creditType: creditType,
								depoDocAmazonBucksFullName: depoDocAmazonBucksFullName,
								depoDocAmazonBucksEmail: depoDocAmazonBucksEmail
							});
						});
					})
					.on('upload-success', (file, response) => {
						if (response.body.success) {
							depoDocsUploadedCount += response.body.uploadeddocscount;
						} else {
							if (response.body.errmsg) uploadDocsShowErr(response.body.errmsg);
							else uploadDocsShowErr('We were unable to upload the files.');
						}
					})
					.on('complete', (result) => {
						if (!result.failed.length) uppyDocuments.cancelAll();
						else {
							result.successful.forEach((file) => {uppyDocuments.removeFile(file.id)});
						}
						if (depoDocsUploadedCount > 0) {
							$('##docUploadDocConf')
								.load('#local.showDocUploadConfirmLink#&docsCount='+depoDocsUploadedCount, function() {
									$('html, body').animate({
										scrollTop: $('##docUploadDocConf').offset().top - 175
									}, 750);
									depoDocsUploadedCount = 0;
								})
								.html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i><b>Please Wait...</b>')
								.show();
							$('##depoDocUploader,##depoDocUploaderButtonContainer').hide();
							enableStartUploadOfFilesBtn();
						}
					})
					.on('error', (error) => {
						uploadDocsShowErr('We ran into an issue. ' + error);
					})
					.use(Dashboard, { target: '##depoDocUploader', inline: true, isWide: false, width:'100%', height:'300px', disableThumbnailGenerator: true, hideUploadButton:true, hideRetryButton: true, proudlyDisplayPoweredByUppy:false });
				});
			});
		</script>
		<style type="text/css">
			.depoDocLabel { font-size:18px;line-height:30px;font-weight:600; }
			.depoDocNavTab li a {background-color:##f4f4f4 !important;border-bottom:1px solid ##ddd;color:##036 !important;font-weight:bold;}
			.mc_blinkerbtn { animation: mc_blinker 4000ms linear infinite; }
			@keyframes mc_blinker { 50% {opacity: 0.5;} 75% {opacity: 0.7;} }
		</style>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">
</cfif>