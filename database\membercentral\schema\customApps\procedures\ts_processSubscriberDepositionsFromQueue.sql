ALTER PROC dbo.ts_processSubscriberDepositionsFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpFilteredDocIDs') IS NOT NULL 
		DROP TABLE #tmpFilteredDocIDs;
	IF OBJECT_ID('tempdb..#tmpDepoMemberDataIDs') IS NOT NULL 
		DROP TABLE #tmpDepoMemberDataIDs;
	IF OBJECT_ID('tempdb..#tmpSiteAdmins') IS NOT NULL
		DROP TABLE #tmpSiteAdmins;
	CREATE TABLE #tmpFilteredDocIDs (documentID int);
	CREATE TABLE #tmpDepoMemberDataIDs (depomemberDataID int);
	CREATE TABLE #tmpSiteAdmins (depomemberdataID int);

	DECLARE @readyToProcessStatusID int, @processingItemStatusID int, @firstName varchar(100), @lastName varchar(100), @searchText varchar(400);

	declare @approvedStatusID int 
	select @approvedStatusID=statusID from  trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='tsSubscriberDepos', @queueStatus='readyToProcess', @queueStatusID=@readyToProcessStatusID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='tsSubscriberDepos', @queueStatus='processingItem', @queueStatusID=@processingItemStatusID OUTPUT;

	SELECT @firstName = firstName, @lastName = lastName
	FROM platformQueue.dbo.queue_tsSubscriberDepos
	WHERE itemID = @itemID
	AND statusID = @readyToProcessStatusID;

	IF @firstName IS NULL
		GOTO on_done;

	-- update queue item status
	UPDATE platformQueue.dbo.queue_tsSubscriberDepos
	SET statusID = @processingItemStatusID,
		dateUpdated = GETDATE()
	WHERE itemID = @itemID;

	-- site admins
	INSERT INTO #tmpSiteAdmins (depomemberdataID)
	SELECT DISTINCT np.depomemberdataID
	FROM membercentral.dbo.ams_networkProfiles AS np
	INNER JOIN membercentral.dbo.ams_memberNetworkProfiles AS mnp ON mnp.profileID = np.profileID
		AND mnp.status = 'A'
		AND np.status = 'A'
	INNER JOIN membercentral.dbo.ams_members AS m ON m.memberID = mnp.memberID
	INNER JOIN membercentral.dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.organizations AS org ON org.orgID = mActive.orgID
	INNER JOIN membercentral.dbo.ams_groups AS g ON g.orgID = org.orgID
		AND g.groupCode = 'SiteAdmins'
	INNER JOIN membercentral.dbo.cache_members_groups AS mg ON mg.orgID = g.orgID
		AND mg.memberID = mActive.memberID
		AND mg.groupID = g.groupID;

	-- get matching depomembers
	INSERT INTO #tmpDepoMemberDataIDs (depomemberDataID)
	SELECT DISTINCT d.depomemberdataID
	FROM trialsmith.dbo.depomemberdataUniqueNames AS du
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.uniqueNameID = du.uniqueID
	INNER JOIN trialsmith.dbo.depoTransactions as dt on dt.depoMemberDataID = d.depomemberdataID
	INNER JOIN trialsmith.dbo.membertype as mt on mt.AcctCode = dt.AccountCode
		AND mt.AcctCode NOT IN ('1000','1201','1007')
	LEFT OUTER JOIN #tmpSiteAdmins AS tmp ON tmp.depoMemberDataID = d.depomemberdataID
	WHERE du.firstName = @firstName
	AND du.lastName = @lastName
	AND d.TLAMemberState <> 'CP'
	AND d.adminFlag2 <> 'Y'
	AND d.includeInSubscriberDepos = 1
	AND tmp.depomemberdataID IS NULL;

	IF @@ROWCOUNT > 0 BEGIN
		-- clear existing documents tied to these depomemberdataids
		IF EXISTS (
			select 1 
			FROM searchMC.dbo.ts_subscriberDepositions as tsd
			INNER JOIN #tmpDepoMemberDataIDs AS dm on dm.depomemberDataID = tsd.depomemberdataID)
		DELETE tsd
		FROM searchMC.dbo.ts_subscriberDepositions as tsd
		INNER JOIN #tmpDepoMemberDataIDs AS dm on dm.depomemberDataID = tsd.depomemberdataID;

		-- escape single quotes and double quotes
		SET @firstName = REPLACE(REPLACE(@firstName,'''',''''''),'"','""');
		SET @lastName = REPLACE(REPLACE(@lastName,'''',''''''),'"','""');

		DECLARE @FirstGroup TABLE (AGroup VARCHAR(50));
		INSERT INTO @FirstGroup VALUES ('Taken by');
		INSERT INTO @FirstGroup VALUES ('For the plaintiff');
		INSERT INTO @FirstGroup VALUES ('For the plaintiffs');
		INSERT INTO @FirstGroup VALUES ('For plaintiffs');
		INSERT INTO @FirstGroup VALUES ('For plaintiff');
		INSERT INTO @FirstGroup VALUES ('Represented by');
		INSERT INTO @FirstGroup VALUES ('On Behalf of the Plaintiff');
		INSERT INTO @FirstGroup VALUES ('On Behalf of the Plaintiffs');

		DECLARE @SearchString VARCHAR(3000);
		SET @SearchString = 'tsdoctypeid1xxx and ( NEAR(("Appearances","'+@firstName+'","'+@lastName+'") , 50, TRUE) OR ';

		SELECT @SearchString = @SearchString + 'NEAR(("'+AGroup+'","'+@firstName+'", "'+@lastName+'"), 20, TRUE) OR '
		FROM @FirstGroup;

		SELECT @SearchString = LEFT(@SearchString, LEN(@SearchString) - 3);
		SELECT @SearchString = @SearchString + ')';
		
		INSERT INTO #tmpFilteredDocIDs (documentID)
		SELECT d.documentID
		FROM containstable(search.dbo.depoDocuments,limitedsearchtext, @Searchstring) as ft inner join search.dbo.depoDocuments d on d.id = ft.[KEY];
		
		IF @@ROWCOUNT > 0
			INSERT INTO searchMC.dbo.ts_subscriberDepositions (depomemberdataID, depoDocumentID)
			SELECT dm.depomemberdataID, d.documentID
			FROM #tmpFilteredDocIDs AS tmp
			INNER JOIN trialsmith.dbo.depodocuments AS d ON d.documentID = tmp.documentID
			inner join trialsmith.dbo.depoDocumentStatusHistory as dsh 
				on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
				and dsh.statusID = @approvedStatusID
			OUTER APPLY #tmpDepoMemberDataIDs AS dm;
	END

	-- delete queue item
	DELETE FROM platformQueue.dbo.queue_tsSubscriberDepos
	WHERE itemID = @itemID;

	on_done:

	IF OBJECT_ID('tempdb..#tmpFilteredDocIDs') IS NOT NULL 
		DROP TABLE #tmpFilteredDocIDs;
	IF OBJECT_ID('tempdb..#tmpDepoMemberDataIDs') IS NOT NULL 
		DROP TABLE #tmpDepoMemberDataIDs;
	IF OBJECT_ID('tempdb..#tmpSiteAdmins') IS NOT NULL
		DROP TABLE #tmpSiteAdmins;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
