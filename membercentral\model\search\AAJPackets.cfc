<cfcomponent extends="Bucket">

	<cfset variables.thisBucketTypeID = 44>
	<cfset variables.thisBucketType = "AAJPackets">
	<cfset variables.thisBucketCartItemTypeID = 0> <!--- ignored --->
	<cfset variables.thisBucketMaxPerPage = 5>
	<cfset variables.thisBucketMaxShown = 10000>

	<cffunction name="showHeader" access="private" output="false" returntype="string">
		<cfargument name="bucketName" type="string" required="yes">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var header = "">

		<cfsavecontent variable="header">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/AAJPackets/header.cfm">
		</cfsavecontent>

		<cfreturn header>
	</cffunction>

	<cffunction name="showSearchForm" access="public" output="false" returntype="string">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="searchID" required="no" type="numeric" default="0">
		<cfargument name="viewDirectory" type="string" required="no" default="default">
		
		<cfset var local = StructNew()>
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>
		
		<!--- load search if passed in --->
		<cfset local.strSearchForm = prepSearchForSearchForm(arguments.searchID,arguments.bucketID)>
		
		<cfset showCommonJS(bucket=variables.thisBucketType, bucketID=arguments.bucketID, viewDirectory=arguments.viewDirectory)>

		<cfsavecontent variable="local.data">
			<cfinclude template="/views/search/commonSearchFormJS.cfm">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/AAJPackets/searchForm.cfm">
		</cfsavecontent>	
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="prepSearchForSearchForm" access="private" returntype="struct" output="no" hint="parses the searchXML and populates search form">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
	
		<cfset var local = structNew()>
		
		<cfscript>
		local.returnStruct = StructNew();

		if (arguments.searchID gt 0) {
			// convert origin searchXML to bucket searchXML
			local.searchXML = application.objSearchTranslate.convertSearchXML(arguments.searchID,variables.thisBucketTypeID);

			// read/clean from xml
			local.returnStruct.s_key_all = local.searchXML.search["s_key_all"].xmlText;
			local.returnStruct.s_key_one = local.searchXML.search["s_key_one"].xmlText;
			local.returnStruct.s_key_phrase = local.searchXML.search["s_key_phrase"].xmlText;
			local.returnStruct.s_key_x = local.searchXML.search["s_key_x"].xmlText;
		} else {
			local.returnStruct.s_key_all = '';
			local.returnStruct.s_key_one = '';
			local.returnStruct.s_key_phrase = '';
			local.returnStruct.s_key_x = '';
		}

		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="prepSearch" access="private" returntype="struct" output="no" hint="parses the searchXML and prepares search criteria">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
	
		<cfset var local = structNew()>
		
		<cfscript>
		// convert origin searchXML to bucket searchXML
		local.searchXML = application.objSearchTranslate.convertSearchXML(arguments.searchID,variables.thisBucketTypeID);

		// read/clean from xml
		local.s_key_all = prepareSearchString(local.searchXML.search["s_key_all"].xmlText);
		local.s_key_one = prepareSearchString(local.searchXML.search["s_key_one"].xmlText,true);
		local.s_key_phrase = preparePhraseString(local.searchXML.search["s_key_phrase"].xmlText);
		local.s_key_x = prepareSearchString(local.searchXML.search["s_key_x"].xmlText);

		// prepare keywords
		local.keywordsInclude = "";
		if (Len(local.s_key_all))
			local.keywordsInclude = listAppend(local.keywordsInclude,local.s_key_all,chr(7));
		if (Len(local.s_key_one))
			local.keywordsInclude = listAppend(local.keywordsInclude,local.s_key_one,chr(7));
		if (Len(local.s_key_phrase))
			local.keywordsInclude = listAppend(local.keywordsInclude,local.s_key_phrase,chr(7));
		local.keywordsInclude = Replace(local.keywordsInclude,chr(7)," and ","ALL");

		local.keywordsExclude = "";
		if (Len(local.s_key_x))
			local.keywordsExclude = replaceNoCase(local.s_key_x," and "," or ","all");
		if (len(local.keywordsExclude) and len(local.keywordsInclude)) 
			local.finalKeywords = local.keywordsInclude & " and not " & local.keywordsExclude;
		else if (len(local.keywordsInclude))
			local.finalKeywords = local.keywordsInclude;
		else if (len(local.keywordsExclude))
			local.finalKeywords = "a and not " & local.keywordsExclude;
		else 
			local.finalKeywords = "";

		// return search struct
		local.returnStruct = structNew();
		structInsert(local.returnStruct,"keywords",local.finalKeywords);

		// do i have enough criteria to run a search?
		if (not len(local.finalKeywords))
			structInsert(local.returnStruct,"searchAccepted",false);
		else
			structInsert(local.returnStruct,"searchAccepted",true);

		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="getResultsCount" access="public" returntype="struct" output="no" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="mcproxy_siteID" required="no">
		<cfargument name="searchID" required="no">
		<cfargument name="bucketID" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["itemcount"] = 'N/A'>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>
		
		<cfif 
			(NOT StructKeyExists(arguments, "mcproxy_siteID") OR NOT IsNumeric(arguments.mcproxy_siteID)) OR 
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID)) OR 
			(NOT StructKeyExists(arguments, "bucketID") OR NOT IsNumeric(arguments.bucketID))>
				<cfset local.isValidArguments = false>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = doGetResultsCount(siteID=arguments.mcproxy_siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doGetResultsCount" access="private" returntype="struct" output="no" hint="searches and returns a count">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">

		<cfset var local = StructNew()>
		<cfset local.strSearch = prepSearch(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfset local.returnStruct = StructNew()>

		<cfif NOT local.strSearch.searchAccepted>
			<cfset StructInsert(local.returnStruct,"itemcount",'N/A')>
			<cfset StructInsert(local.returnStruct,"success",true)>
			<cfset saveBucketCount(arguments.searchID,arguments.bucketID,-1)>
		<cfelse>
			<cfset local.cachedItemCount = getCachedBucketCount(arguments.searchID,arguments.bucketID)>
			<cfif local.cachedItemCount gte 0>
				<cfset StructInsert(local.returnStruct,"itemcount",local.cachedItemCount)>
				<cfset StructInsert(local.returnStruct,"success",true)>
			<cfelse>
				<cfquery name="local.qryResults" datasource="#application.dsn.tlasites_trialsmith.dsn#" result="local.qryStat">
					set nocount on;

					declare @itemCount int, @keywords varchar(3000);
					set @keywords = <cfqueryparam value="#local.strSearch.keywords#" cfsqltype="CF_SQL_VARCHAR">;
					declare @approvedStatusID int 
					select @approvedStatusID=statusID from trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

					select @itemCount = count(*)
					from dbo.depoDocuments as d
					inner join dbo.depoDocumentStatusHistory as dsh on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
						and dsh.statusID = @approvedStatusID
					inner join dbo.depodocumenttypes dt on dt.typeid = d.documenttypeid and dt.acctCode = '5020'
					<cfif len(local.strSearch.keywords)>
						and contains((d.expertName,d.keywords),@keywords)
					</cfif>

					select @itemCount as itemCount;
				</cfquery>
				<cfset saveStats(arguments.searchID,'#variables.thisBucketType#.getResultsCount',local.qryStat.ExecutionTime,local.qryResults.itemCount)>
				<cfset saveBucketCount(arguments.searchID,arguments.bucketID,local.qryResults.itemCount)>
	
				<cfset StructInsert(local.returnStruct,"itemcount",local.qryResults.itemCount)>
				<cfset StructInsert(local.returnStruct,"success",true)>
			</cfif>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getNotLoggedInResults" access="private" returntype="struct" output="no" hint="searches and returns not logged in text">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="viewDirectory" type="string" required="no" default="default">
		
		<cfset var local = StructNew()>
		<cfset local.strResultsCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>

		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/AAJPackets/notLoggedInResults.cfm">
		</cfsavecontent>
		<cfset local.stResults = ReReplace(local.stResults,'\s{2,}',' ','ALL')>		

		<cfset local.returnStruct = StructNew()>
		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfset StructInsert(local.returnStruct,"itemcount",local.strResultsCount.itemCount)>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getResults" access="public" returntype="struct" output="no" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="mcproxy_siteID" required="no">
		<cfargument name="searchID" required="no">
		<cfargument name="bucketID" required="no">
		<cfargument name="startRow" required="no">
		<cfargument name="sortType" required="no">
		<cfargument name="viewDirectory" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["errmsg"] = 'Invalid Request'>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>
		
		<cfif 
			(NOT StructKeyExists(arguments, "mcproxy_siteID") OR NOT IsNumeric(arguments.mcproxy_siteID)) OR
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID)) OR
			(NOT StructKeyExists(arguments, "bucketID") OR NOT IsNumeric(arguments.bucketID)) OR
			(NOT StructKeyExists(arguments, "startRow") OR NOT IsNumeric(arguments.startRow)) OR
			NOT StructKeyExists(arguments, "sortType") OR
			(NOT StructKeyExists(arguments, "viewDirectory") OR NOT ListFind(variables.viewDirectories, arguments.viewDirectory))>
				<cfset local.isValidArguments = false>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = doGetResults(siteID=arguments.mcproxy_siteID, searchID=arguments.searchID, bucketID=arguments.bucketID,
				startrow=arguments.startrow, sortType=arguments.sortType, viewDirectory=arguments.viewDirectory)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doGetResults" access="private" returntype="struct" output="no" hint="searches and returns a query result">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="startrow" required="yes" type="numeric">
		<cfargument name="sortType" required="yes" type="string">
		<cfargument name="viewDirectory" required="yes" type="string">

		<cfset var local = StructNew()>

		<cfif NOT variables.initRun>
			<cfset init()>
		</cfif>

		<cfset local.strSearch = prepSearch(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>
		<cfset local.returnStruct = StructNew()>

		<!--- checks.
		1. logged in?
		2. search accepted?
		3. TrialSmithAllowed?
		4. TrialSmithDisabled?
		5. TrialSmithPending?
		6. TrialSmithExpired?
		7. TrialSmithNoPlan?
		8. RestrictToGroup?
		--->
		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfreturn getNotLoggedInResults(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID,viewDirectory=arguments.viewDirectory)>
		<cfelseif NOT local.strSearch.searchAccepted>
			<cfreturn showSearchNotAccepted(searchID=arguments.searchID,bucketID=arguments.bucketID,bucketName=local.qryBucketInfo.bucketName,viewDirectory=arguments.viewDirectory)>
		<cfelseif application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='TrialSmithAllowed') is not 1>
			<cfreturn showTrialsmithNotAllowed(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfelseif application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='TrialSmithDisabled') is 1>
			<cfreturn showTrialsmithDisabled(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfelseif application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='TrialSmithPending') is 1>
			<cfreturn showTrialsmithPending(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfelseif application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='TrialSmithExpired') is 1>
			<cfreturn showTrialsmithExpired(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfelseif application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='TrialSmithNoPlan') is not 0>
			<cfreturn showTrialsmithNoPlan(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfelseif NOT variables.cfcuser_isSiteAdmin AND val(local.qryBucketInfo.restrictToGroupID) GT 0 AND local.qryBucketInfo.isMemberInRestrictedGroup NEQ 1>
			<cfreturn showNotAllowed(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, bucketName=local.qryBucketInfo.bucketName, 
				accessDeniedMessage=local.qryBucketInfo.accessDeniedMessage, viewDirectory=arguments.viewDirectory)>
		<cfelse>
			<cfset local.strCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>

			<cfquery name="local.qryResults" datasource="#application.dsn.tlasites_trialsmith.dsn#" result="local.qryStat">
				set nocount on
				
				<!--- create temp table --->
				DECLARE @tmpResults TABLE (autoid int IDENTITY(1,1), documentid int)
				declare @approvedStatusID int 
				select @approvedStatusID=statusID from trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

				<!--- insert all into temp --->
				INSERT INTO @tmpResults (documentid)
				Select TOP (<cfqueryparam value="#min(arguments.startrow + variables.thisBucketMaxPerPage - 1,variables.thisBucketMaxShown)#" cfsqltype="CF_SQL_INTEGER">) d.documentid
				from dbo.depoDocuments as d
				inner join dbo.depoDocumentStatusHistory as dsh on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
					and dsh.statusID = @approvedStatusID
				inner join dbo.depodocumenttypes dt on dt.typeid = d.documenttypeid and dt.acctCode = '5020'
				<cfif len(local.strSearch.keywords)>
					and contains((d.expertName,d.keywords),<cfqueryparam value="#local.strSearch.keywords#" cfsqltype="CF_SQL_VARCHAR">)
				</cfif>
				<cfswitch expression="#arguments.sortType#">
					<cfcase value="date">
						order by d.DocumentDate desc
					</cfcase>
					<cfcase value="packet">
						order by d.ExpertName
					</cfcase>
					<cfdefaultcase>
						order by d.DocumentDate desc
					</cfdefaultcase>
				</cfswitch>

				<!--- return top x --->
				select TOP (<cfqueryparam value="#variables.thisBucketMaxPerPage#" cfsqltype="CF_SQL_INTEGER">) #local.strCount.itemcount# as itemCount,
					d.documentdate, d.expertName, d.notes
				from @tmpResults as tmp
				inner join dbo.depodocuments AS d on d.documentid = tmp.documentid
				where tmp.autoid >= <cfqueryparam value="#arguments.startrow#" cfsqltype="CF_SQL_INTEGER">
				ORDER BY tmp.autoid
				
				set nocount off
			</cfquery>
			<cfset saveStats(arguments.searchID,'#variables.thisBucketType#.getResults',local.qryStat.ExecutionTime)>

			<!--- adjust maxperpage based on actual data if necessary and get page variables --->
			<cfscript>
			local.MaxPerPage = iif(local.qryResults.recordcount gt variables.thisBucketMaxPerPage,variables.thisBucketMaxPerPage,local.qryResults.recordcount);
			if (local.MaxPerPage gt 0) {
				local.NumTotalPages = Ceiling(local.qryResults.itemCount / variables.thisBucketMaxPerPage);
				local.NumCurrentPage = int((int(arguments.startrow) + variables.thisBucketMaxPerPage - 1) / variables.thisBucketMaxPerPage);
			} else {
				local.NumTotalPages = 0;
				local.NumCurrentPage = 0;
			}
			</cfscript>

			<!--- return content --->
			<cfsavecontent variable="local.stResults">
				<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/AAJPackets/results.cfm">
			</cfsavecontent>

			<cfset local.stResults = ReReplace(local.stResults,'\s{2,}',' ','ALL')>		
			<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
			<cfset StructInsert(local.returnStruct,"itemcount",val(local.qryResults.itemCount))>
			<cfset StructInsert(local.returnStruct,"success",true)>
		
			<cfreturn local.returnStruct>
		</cfif>
	</cffunction>
	
	<cffunction name="saveSearchForm" access="public" output="no" returntype="numeric" hint="saves the form vars to a search and returns the searchid">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="formvars" required="yes" type="struct">

		<cfset var local = structNew()>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfsavecontent variable="local.xmlSearch">
			<cfoutput>
			<search xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2">
				<bid><cfif StructKeyExists(arguments.formvars,"bid")>#val(arguments.formvars.bid)#</cfif></bid>
				<s_key_all><cfif StructKeyExists(arguments.formvars,"s_key_all")>#xmlFormat(trim(arguments.formvars.s_key_all))#</cfif></s_key_all>
				<s_key_one><cfif StructKeyExists(arguments.formvars,"s_key_one")>#xmlFormat(trim(arguments.formvars.s_key_one))#</cfif></s_key_one>
				<s_key_phrase><cfif StructKeyExists(arguments.formvars,"s_key_phrase")>#xmlFormat(trim(arguments.formvars.s_key_phrase))#</cfif></s_key_phrase>
				<s_key_x><cfif StructKeyExists(arguments.formvars,"s_key_x")>#xmlFormat(trim(arguments.formvars.s_key_x))#</cfif></s_key_x>
			</search>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.searchID = saveSearchXML(val(arguments.formvars.bid),local.xmlSearch)>
		
		<cfreturn local.searchID>
	</cffunction>

	<cffunction name="showNotAllowed" access="private" output="false" returntype="struct">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="bucketName" required="yes" type="string">
		<cfargument name="accessDeniedMessage" required="yes" type="string">
		<cfargument name="includeBucketCount" required="no" type="boolean" default="true">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>

		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/AAJPackets/notAllowed.cfm">
		</cfsavecontent>

		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfif arguments.includeBucketCount>
			<cfset local.strCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
			<cfset StructInsert(local.returnStruct,"itemcount",local.strCount.itemCount)>
		<cfelse>
			<cfset StructInsert(local.returnStruct,"itemcount",-1)>
		</cfif>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>
</cfcomponent>