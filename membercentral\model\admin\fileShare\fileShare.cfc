<cfcomponent hint="Website Alias Functions">
	
	<cffunction name="getFSData" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.data">
			set nocount on;

			declare @RTID int;
			select @RTID = dbo.fn_getResourceTypeID('Community');

			SELECT ai.siteresourceID, ai.applicationInstanceID, fs.fileShareID, fs.rootSectionID, fs.showPublicationDate, fs.showAuthor,
				fs.showVersioning, fs.showContributedBy, fs.showFirm, fs.showTags, fs.showTagsAsLinks, fs.showAddress, fs.authorLabel,
				fs.alwaysShowFolders, fs.showBrowseTitle, fs.showInteriorText, fs.showDocDownloadCountTo<PERSON><PERSON><PERSON>, fs.isMultipleSelectSearch,
				fs.columnWidth, fs.notifyEmails, fs.notifyOnAdd, fs.notifyOnUpdate, fs.columnToShow, fs.ignoreRightsCheck, fs.showShareButton,
				ai.applicationInstanceName,
				fileShareName = ai.applicationInstanceName + case WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')' ELSE '' END,
				isnull(communityInstances.applicationInstanceName,'') as communityName, sr.siteResourceStatusID as srStatusID,
				srs.siteResourceStatusDesc as srsStatus
			FROM dbo.fs_fileShare fs
			INNER JOIN dbo.cms_applicationInstances ai ON ai.applicationInstanceID = fs.applicationInstanceID 
				AND ai.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
			INNER JOIN dbo.cms_siteResources sr ON ai.siteResourceID = sr.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses srs ON sr.siteResourceStatusID = srs.siteResourceStatusID		
			INNER JOIN dbo.cms_siteResources AS parentResource ON parentResource.siteResourceID = sr.parentSiteResourceID
			left outer join dbo.cms_siteResources AS grandparentResource
				inner join dbo.cms_applicationInstances AS CommunityInstances on communityInstances.siteResourceID = grandParentResource.siteResourceID
					on grandparentResource.siteResourceID = parentResource.parentSiteResourceID
					and grandparentResource.resourceTypeID = @RTID
			WHERE fs.fileShareID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('fsID')#">;
		</cfquery>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getSectionData" access="public" output="false" returntype="query">
		<cfargument name="sectionID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfset var local = structNew()>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.data">
			SELECT *
			FROM dbo.cms_pageSections
			WHERE sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.sectionID#">
				AND siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
		</cfquery>
		
		<cfreturn local.data />
	</cffunction>
	
	<cffunction name="saveMeta" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local 					= structNew();
			local.returnStruct 	= { success=true };
		</cfscript>	
			
			<cfif arguments.event.getValue('fsID',0) gt 0>
				<cfquery datasource="#application.dsn.membercentral.dsn#">
					UPDATE ai
						SET ai.applicationInstanceName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('newapplicationInstanceName')#">
					FROM dbo.cms_applicationInstances ai
					INNER JOIN dbo.fs_fileShare fs ON ai.applicationInstanceID = fs.applicationInstanceID
						AND fs.fileShareID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('fsID')#"> 
				</cfquery>				
			</cfif>
			
			
		<cfreturn local.returnStruct />
	</cffunction>
	
	<cffunction name="saveFSDisplayOptions" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local 					= structNew();
			local.returnStruct 	= { success=true };

			if (len(arguments.event.getTrimValue('newNotifyEmails',''))) {
				local.arrFSNotifyEmails = listToArray(replace(replace(arguments.event.getTrimValue('newNotifyEmails'),',',';','ALL'),' ','','ALL'),';');
				for (var i=1; i lte arrayLen(local.arrFSNotifyEmails); i++) {
					if (len(local.arrFSNotifyEmails[i]) and not isValid("regex",local.arrFSNotifyEmails[i],application.regEx.email)) {
						arrayDeleteAt(local.arrFSNotifyEmails,i);
					}
				}
				arguments.event.setValue('newNotifyEmails',arrayToList(arrFSNotifyEmails,';'));
			}
		</cfscript>
			
		<cfif arguments.event.getValue('fsID',0) gt 0>
			<cfquery datasource="#application.dsn.membercentral.dsn#">
				UPDATE 	
					dbo.fs_fileShare
				SET 	
					showPublicationDate = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showPublicationDate')#">
					, requirePublicationDate = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('requirePublicationDate')#"> 
					, columnWidth = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('columnWidth')#">
					, recordsperpage = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('recordsperpage')#">												
					, showAuthor = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showAuthor')#">  
					, requireAuthor = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('requireAuthor')#"> 
					, showVersioning = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showVersioning')#">  
					, showContributedBy = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showContributedBy')#">  
					, showFirm = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showFirm')#">  
					, showTags = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showTags')#">  
					, showTagsAsLinks = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showTagsAsLinks')#">  
					, authorLabel = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('newAuthorLabel')#">  
					, alwaysShowFolders = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('alwaysShowFolders')#">
					, showAddress = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showAddress')#">  
					, showBrowseTitle = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showBrowseTitle')#">  
					, showInteriorText = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showInteriorText')#">  
					, showDocDownloadCountToMembers = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showDocDownloadCountToMembers')#">  
					, showCustomFields = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('showCustomFields','')#">  
					, isMultipleSelectSearch = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('isMultipleSelectSearch')#">  
					, notifyEmails = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('newNotifyEmails','')#">  
					, columnToShow = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('columnToShow','')#">  
					, ignoreRightsCheck = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('ignoreRightsCheck')#">  
					, notifyOnAdd = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('notifyOnAdd')#">  
					, notifyOnUpdate = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('notifyOnUpdate')#">  
					, showResultSorting = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showResultSorting')#"> 
					<cfif val(arguments.event.getValue('showResultSorting'))>						
						, defaultResultSorting = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('defaultResultSorting','')#">	
					<cfelse>
						, defaultResultSorting = NULL	
					</cfif>	
					, showShareButton = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showShareButton')#">  
				WHERE 
					fileShareID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('fsID')#">
			</cfquery>			
		</cfif>			
			
		<cfreturn local.returnStruct />
	</cffunction>
	
	<cffunction name="removeFS" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="fileShareID" type="numeric" required="true">
		<cfargument name="fileShareSRID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasDeleteFileShareRights(siteID=arguments.mcproxy_siteID, siteResourceID=arguments.fileShareSRID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfquery name="local.qryRemoveFS" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
						@fileShareID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fileShareID#">,
						@siteResourceID int, @docCount int;
				
					SELECT @docCount = count(d.documentID)
					FROM dbo.cms_documents AS d
					INNER JOIN dbo.cms_siteResources AS sr2 ON sr2.siteID = @siteID AND sr2.siteResourceID = d.siteResourceID 
						AND sr2.siteResourceStatusID in (1,2)
					INNER JOIN dbo.cms_pageSections ps ON ps.sectionID = d.sectionID
					INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID AND sr.siteResourceID = ps.siteResourceID 
						AND sr.siteResourceStatusID = 1
					INNER JOIN dbo.cache_cms_recursivePageSections rps ON d.sectionID = rps.startSectionID
					INNER JOIN dbo.fs_fileShare AS fs ON fs.rootSectionID = rps.sectionID
					WHERE fs.fileShareID = @fileShareID;

					IF @docCount > 0
						RAISERROR('invalid request',16,1);
					
					SELECT @siteResourceID = sr.parentSiteResourceID
					FROM dbo.fs_fileShare AS fs
					INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = fs.applicationInstanceID 
						AND ai.siteID = @siteID
					INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID AND sr.siteResourceID = ai.siteResourceID
					WHERE fs.fileShareID = @fileShareID;
					
					EXEC dbo.cms_deleteSiteResourceAndChildren @siteID=@siteID, @siteResourceID=@siteResourceID;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeSection" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="sectionID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @siteID int, @siteResourceID int, @sectionID int, @childSectionsCount int, @docCount int;

					SELECT @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_siteID#">;
					SELECT @sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.sectionID#">;

					SELECT @docCount = count(d.documentID)
					FROM dbo.cms_documents AS d 
					INNER JOIN dbo.cms_documentLanguages dl on dl.documentID = d.documentID
					INNER JOIN dbo.cms_documentVersions v on v.documentLanguageID = dl.documentLanguageID
						AND v.isActive = 1
					INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = d.siteResourceID
					INNER JOIN dbo.cms_siteResourceStatuses srs ON srs.siteResourceStatusID = sr.siteResourceStatusID
						AND srs.siteResourceStatusDesc in ('Active')
					WHERE d.sectionID = @sectionID;

					SELECT @childSectionsCount = count(ps.sectionID)
					FROM dbo.cms_pageSections AS ps
					INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = ps.siteResourceID
						AND sr.siteResourceStatusID = 1
						AND sr.siteID = @siteID
					WHERE ps.parentSectionID = @sectionID;

					IF @docCount > 0 OR @childSectionsCount > 0
						RAISERROR('invalid request',16,1);

					SELECT @siteResourceID = siteresourceID
					FROM dbo.cms_pageSections 
					WHERE siteID = @siteID
					AND sectionID = @sectionID;

					UPDATE dbo.cms_siteResources
					SET siteResourceStatusID = 3
					WHERE siteID = @siteID
					AND siteResourceID = @siteResourceID;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data />
	</cffunction>

	<cffunction name="updateSection" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="sectionID" type="numeric" required="true">
		<cfargument name="sectionName" type="string" required="true">

		<cfquery datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @siteID int, @sectionID int, @sectionName varchar(50), @currentSectionName varchar(50);

				SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;
				SET @sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.sectionID#">;
				SET @sectionName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.sectionName#">;

				SELECT @currentSectionName=sectionName
				FROM dbo.cms_pageSections
				WHERE siteID = @siteID
				AND sectionID = @sectionID;

				BEGIN TRAN;
					UPDATE dbo.cms_pageSections
					SET sectionName = @sectionName
					WHERE siteID = @siteID
					AND sectionID = @sectionID;

					IF @currentSectionName <> @sectionName
						EXEC dbo.cache_cms_updateRecursivePageSections @siteID = @siteID, @restrictToSectionID = NULL;

				COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="removeDoc" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="documentID" type="numeric" required="true">		
		<cfargument name="fileShareID" type="numeric" required="true">
		<cfargument name="fileShareSRID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfset local.qryDocData = getDocData(documentID=arguments.documentID)>

		<cftry>
			<cfif not hasDeleteFileShareRights(siteID=arguments.mcproxy_siteID, siteResourceID=arguments.fileShareSRID, contributorMemberID=local.qryDocData.contributorMemberID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfset CreateObject("component","model.system.platform.document").deleteDocument(siteID=arguments.mcproxy_siteID, documentID=arguments.documentID)>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data />
	</cffunction>

	<cffunction name="moveCategoryTree" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="treeID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.data.success = CreateObject('component','model.system.platform.category').moveCategoryTree(siteID=arguments.mcproxy_siteID, categoryTreeID=arguments.treeID, dir=arguments.dir)>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
			<cfset local.data.errmsg = 'Category Tree cannot be moved.'>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteCategoryTree" access="public" output="false" returntype="struct" hint="remove category tree">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="treeID" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCategoryTree">
			select ct.categoryTreeID, count(distinct sr.siteResourceID) as catCount
			from dbo.cms_categoryTrees as ct
			left outer join dbo.cms_categories as c
				inner join dbo.cms_categorySiteResources as csr on csr.categoryID = c.categoryID
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = csr.siteResourceID
				inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
					and srs.siteResourceStatusDesc = 'Active'
				on c.categoryTreeID = ct.categoryTreeID
			where ct.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_siteID#">
			and ct.categoryTreeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.treeID#">
			group by ct.categoryTreeID
		</cfquery>

		<cfscript>
			local.data = structNew();
		
			if (local.qryCategoryTree.recordCount AND local.qryCategoryTree.catCount eq 0) {
				local.deleteCategoryTree = CreateObject('component','model.system.platform.category').deleteCategoryTree(categoryTreeID=arguments.treeID, siteID=arguments.mcproxy_siteID);
				if (local.deleteCategoryTree) {
					local.data.success = true;
				} else {
					local.data.success = false;
					local.data.errmsg = 'Category Tree cannot be deleted while still associated with files.';
				}
			} else {
				local.data.success = false;
				local.data.errmsg = 'Category Tree cannot be deleted.';
			}
		</cfscript>

		<cfreturn local.data>	
	</cffunction>

	<cffunction name="deleteCategory" access="public" output="false" returntype="struct" hint="remove category">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="treeID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCategory" maxrows="1">
				select c.categoryID, c.parentCategoryID
				from dbo.cms_categories c
				inner join dbo.cms_categoryTrees ct on ct.categoryTreeID = c.categoryTreeID
					and ct.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.mcproxy_siteID#">
				where c.categoryTreeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.treeID#">
				and c.categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.categoryID#">
			</cfquery>

			<cfif len(local.qryCategory.categoryID)>
				<cfset local.data = CreateObject('component', 'model.system.platform.category').deleteCategory(categoryID=arguments.categoryID)>
			<cfelse>
				<cfset local.data.success = false>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getExtraFSColumns" returntype="query" output="no">
		<cfargument name="siteResourceID" required="Yes" type="numeric">
		
		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		
		<cfquery name="local.qryExtraDocData" datasource="#application.dsn.membercentral.dsn#">
			select *
			from dbo.cms_siteResourceDataColumns 
			where containerSiteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteResourceID#">			
			order by columnOrder
		</cfquery>	

		
		<cfreturn local.qryExtraDocData>
	</cffunction>
	
	<cffunction name="getExtraFSColumnData" returntype="string" output="no">
		<cfargument name="siteResourceID" required="Yes" type="numeric">
		<cfargument name="columnId" required="Yes" type="numeric">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryExtraDocData" datasource="#application.dsn.membercentral.dsn#">
			select *
			from dbo.cms_siteResourceDataColumns as c
			left outer join cms_siteResourceDataColumnValues dcv on dcv.columnID = c.columnID
			left outer join cms_siteResourceData rd on rd.valueID = dcv.valueID
			where rd.itemSiteResourceID =  <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteResourceID#">		
			and c.columnID =  <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.columnId#">		
		</cfquery>	

		
		<cfreturn local.qryExtraDocData.columnValueString>
	</cffunction>

	<cffunction name="doFilterDocuments" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="csrID" type="numeric" required="true">
		<cfargument name="docTypeIDList" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryDepoMemberData" datasource="#application.dsn.membercentral.dsn#">
			select np.depomemberdataid
			from dbo.ams_members as m
			inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
			inner join dbo.ams_memberNetworkProfiles as mnp on mnp.memberID = mActive.memberID and mnp.status = 'A'
			inner join dbo.ams_networkProfiles as np on np.profileID = mnp.profileID and np.status = 'A'
			where mnp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			and m.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
		</cfquery>
		
		<cfif val(local.qryDepoMemberData.depomemberdataid) is 0>
			<cfsavecontent variable="local.data.resulthtml">
				<cfoutput>
					<div class="card card-box mt-5">
						<div class="card-body pb-3">
							<div class="col-sm-12 col-xl-12 p-0">
								<div class="form-group row align-items-center ">
									<label class="col-sm-6 col-form-label b"><h4>Summary of search results</h4></label>
								</div>
								<div class="form-group row align-items-center ">
									<label class="col-sm-10 col-form-label">Member does not have a DepoMemberDataID.</label>
								</div>
								<div class="form-group row align-items-center pl-3">
									<button name="btnCancel" id="btnCancel" type="button" onClick="cancelSummaryScreen();" class="btn btn-sm btn-default btn-outline-primary">Cancel</button>
								</div>
							</div>
						</div>						
					</div>
				</cfoutput>
			</cfsavecontent>
			<cfset local.data.success = false>
			<cfreturn local.data>
		</cfif>

		<cfquery name="local.qryDocResults" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			declare @approvedStatusID int 
			select @approvedStatusID=statusID from trialsmith.dbo.depoDocumentStatuses where statusName = 'Approved'

			select top 1 dt.Description as documentType, count(d.documentid) as documentCount
			from dbo.depoDocuments as d
			inner join dbo.depoDocumentStatusHistory as dsh on dsh.depoDocumentHistoryID = d.currentStatusHistoryID
				and dsh.statusID = @approvedStatusID
			inner join dbo.depoDocumentTypes as dt on dt.TypeID = d.documentTypeID
			inner join dbo.depoDocumentsFilesOnline as dfo on dfo.documentID = d.documentID and dfo.fileType = 'pdf'
			where d.depomemberdataid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.qryDepoMemberData.depomemberdataid)#">
			<cfif len(arguments.docTypeIDList)>
				and d.documentTypeID in (0#arguments.docTypeIDList#)
			<cfelse>
				and d.documentTypeID in (select TypeID from dbo.depoDocumentTypes where updateDisplay = 1 and allowEndUserUploads=1 and categoryid = 1)
			</cfif>
			and not exists (select documentID from membercentral.dbo.cms_documents where importedDocumentID = d.documentID)
			group by dt.Description
			order by dt.Description
		</cfquery>
			
		<cfif local.qryDocResults.recordcount gt 0>			
			<cfquery name="local.qryFileShareCategories" datasource="#application.dsn.membercentral.dsn#">
				select ct.categoryTreeID, ct.categoryTreeName, c.categoryID, c.categoryName
				from dbo.cms_categoryTrees as ct 
				inner join dbo.cms_categories as c on c.categoryTreeID = ct.categoryTreeID
				where ct.controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.csrID#">
				and c.isActive = 1
				order by ct.sortOrder, c.sortOrder
			</cfquery>
			
			<cfsavecontent variable="local.categorySelect">		
				<cfset local.selectId = 0>
				<cfoutput query="local.qryFileShareCategories" group="categoryTreeID">					
					<div class="form-group row align-items-center ">
						<label class="col-sm-3 col-md-3 col-xl-2 col-form-label b">#local.qryFileShareCategories.categoryTreeName# Category:</label>
						<div class="col-sm-8">						
							<select name="frmCategory_#local.qryFileShareCategories.categoryTreeID#" class="form-control form-control-sm fileShareCategory" multiple="yes" data-toggle="custom-select2">
								<option value="#local.qryFileShareCategories.categoryID#">#local.qryFileShareCategories.categoryName#</option>
							</select>
						</div>
					</div>
				</cfoutput>				
			</cfsavecontent>
		</cfif>

		<cfsavecontent variable="local.data.resulthtml">
			<cfoutput>
				<div class="card card-box mt-5">
					<div class="card-body pb-3">
						<div class="col-sm-12 col-xl-12 p-0">
							<div class="form-group row align-items-center ">
								<label class="col-sm-6 col-form-label b"><h4>Summary of search results</h4></label>
							</div>
							<cfif local.qryDocResults.recordcount gt 0>
								<div class="form-group row align-items-center mt-3 pl-3">
									We found the following documents contributed by this member not yet imported into FileShare:
								</div>
								<cfloop query="local.qryDocResults">
									<div class="form-group row align-items-center mt-3 pl-3">
										<b>#local.qryDocResults.documentCount#</b> &nbsp; documents in &nbsp;  <b>#local.qryDocResults.documentType#</b>
									</div>
								</cfloop>								
								<input type="hidden" name="contributingMemberID" value="#arguments.memberID#">
								<input type="hidden" name="depomemberdataid" value="#val(local.qryDepoMemberData.depomemberdataid)#">
								<input type="hidden" name="docTypeIDList" value="#arguments.docTypeIDList#">
								#local.categorySelect#
								<div class="form-group row">
									<div class="col-sm-12 text-right">
										<button type="submit" name="btnImportDoc" id="btnImportDoc" type="submit" onClick="filterDocs();" class="btn btn-sm btn-primary">Import Documents</button>
										<button name="btnCancel" id="btnCancel" type="button" onClick="cancelSummaryScreen();" class="btn btn-sm btn-default btn-outline-primary">Cancel</button>
									</div>
								</div>
							<cfelse>
								<div class="form-group row align-items-center ">
									<label class="col-sm-12 col-form-label">We did not find any documents contributed by this member not yet imported into FileShare.</label>
								</div>
								<div class="form-group row align-items-center pl-3">
									<button name="btnCancel" id="btnCancel" type="button" onClick="cancelSummaryScreen();" class="btn btn-sm btn-default btn-outline-primary">Cancel</button>
								</div>
							</cfif>							
						</div>
					</div>						
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getFileShareCategoryTree" access="public" output="false" returntype="query">
		<cfargument name="categoryTreeID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">

		<cfset var qryCategoryTree = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCategoryTree">
			select ct.categoryTreeID, ct.categoryTreeName 
			from dbo.cms_categoryTrees as ct
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ct.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where ct.controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
			and ct.categoryTreeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryTreeID#">
		</cfquery>

		<cfreturn qryCategoryTree>
	</cffunction>

	<cffunction name="hasDeleteFileShareRights" access="private" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="contributorMemberID" type="numeric" required="false" default="0">

		<cfset var tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=arguments.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

		<cfreturn arguments.contributorMemberID gt 0
			? (tmpRights.fsDeleteAny is 1 or (tmpRights.fsDeleteOwn is 1 AND arguments.contributorMemberID eq session.cfcuser.memberdata.memberID))
			: (tmpRights.fsDeleteAny is 1 or tmpRights.fsDeleteOwn is 1)>
	</cffunction>
	
	<cffunction name="getDocData" access="private" output="false" returntype="query">
		<cfargument name="documentID" type="numeric" required="true">

		<cfset var qryDocument = "">
	
		<cfquery name="qryDocument" datasource="#application.dsn.memberCentral.dsn#">
			SELECT d.documentID, v.contributorMemberID
			FROM dbo.cms_documents AS d 
			INNER JOIN dbo.cms_documentLanguages dl on d.documentID = dl.documentID
			INNER JOIN dbo.cms_documentVersions v on dl.documentLanguageID = v.documentLanguageID
				AND v.isActive = 1
			WHERE d.documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.documentID#">
		</cfquery>
		
		<cfreturn qryDocument>
	</cffunction>

	<cffunction name="addCategoryTreeForFileshare2" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="controllingSiteResourceID" type="numeric" required="true">
		<cfargument name="categoryTreeName" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		
		<cftry>
			<cfquery name="local.qryAddFS2CategoryTree" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				DECLARE @categoryTreeID int;
			
				EXEC dbo.cms_createCategoryTreeForFileShare2
					@siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
					@fs2SiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.controllingSiteResourceID#">,
					@categoryTreeName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.categoryTreeName#">,
					@categoryTreeDesc = '',
					@createSampleCategory = 0,
					@categoryTreeID = @categoryTreeID OUTPUT;

				SELECT @categoryTreeID AS categoryTreeID;
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>
	
</cfcomponent>