CREATE PROC dbo.queue_TSApprovalAutomation_download
@status varchar(60),
@csvfilename varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @selectsql varchar(max) = '';

	SELECT @selectsql = 'SELECT ''TSApprovalAutomation'' as queueType, qs.queueStatus, qi.itemID, qi.depoDocumentID, qi.errorMessage, qi.isNotified,
		qi.dateAdded, qi.dateUpdated, ROW_NUMBER() OVER(order by qi.dateAdded, qi.itemID) as mcCSVorder 
		*FROM* platformQueue.dbo.queue_TSApprovalAutomation as qi
		INNER JOIN platformQueue.dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID and qs.queueStatus = ''' + @status + '''';

	EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@csvfilename, @returnColumns=0;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
