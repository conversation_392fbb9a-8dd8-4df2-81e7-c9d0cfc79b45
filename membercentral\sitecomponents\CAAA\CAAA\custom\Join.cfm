<cfparam name="session.hasSubmittedCAAAJoinForm" default="0">

<cfscript>
	local.arrCustomFields = [];
	local.tmpField = { name="ConfirmationEmailMessage",type="CONTENTOBJ", desc="Content at the top of the email the member/applicant receives", value="Your membership is pending approval by the board. The following information was sent." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="ConfirmationScreenMessage",type="CONTENTOBJ", desc="Confirmation screen message", value="<p>Thank you for joining CAAA!</p><p>Thank you for your interest in the California Applicants' Attorneys Association (CAAA).&nbsp; Your application for membership has been received.&nbsp; Per CAAA&rsquo;s by-laws, all applications must be reviewed and approved by our Board of Directors.&nbsp; Your application will be presented to the Board at their next meeting.&nbsp; CAAA memberships renew on the calendar year.&nbsp; If you have enrolled at the end of the calendar year (November or December) you qualifiy for an extended membership through the end of the following calendar year.</p>" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);	
	local.tmpField = { name="MembershipAgreement",type="CONTENTOBJ", desc="Membership Agreement message", value="By joining CAAA as a Regular member, I certify that I am currently a nonrepresentative of insurance companies or self-insured employers in the defense of workers' compensation cases. I must notify the CAAA State Office if I no longer meet this criteria." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);	

	local.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);

	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
			formName='frmCAAACC',
			formNameDisplay='Membership Application',
			orgEmailTo='',
			memberEmailFrom=''));
</cfscript>
<cfset local.nameOfHiddenField = "memid" & hash(dateformat(now(),'yyyymmdd'))>
<!--- Column Names --->
<cfset local.colDefenseCases = 'Defense Cases'>
<cfset local.colHomeChapter = 'Home Chapter'>
<cfset local.colHomeChapterRO = 'Home Chapter RO'>
<cfset local.colMemberType = 'Contact Type'>
<cfset local.colAssocSpecialties = 'Associate Specialties'>
<cfset local.colLawSchool = 'Law School'>
<cfset local.colPctWC = 'Percentage of Workers Comp'>
<cfset local.colAoP = 'Attorney Areas of Practice'>
<!--- Column Value Strings --->
<cfset local.colValueYesBut = 'Yes, but none of the other types listed'>
<cfset local.colValueAppRegular = 'Applicant-Regular'>
<cfset local.colValueAppAssoc = 'Applicant-Associate'>
<cfset local.colValueAppLS = 'Applicant-Law Student'>
<cfset local.colValueDefenseAtty = 'Defense Attorney'>

<cfset local.memberid = session.cfcUser.memberData.memberID />
<cfset local.qryStates = application.objCommon.getStates()>
<!--- when spam is detected, the page is redirected here --->
<cfif event.getValue('isSubmitted',0) eq 100>
	<cfoutput>
		<div>Error! you Can't Post Here.</div>
	</cfoutput>
	<cfabort>
</cfif>

<cfset variables.applicationReservedURLParams 	= "TestMode">
<cfset local.customPage.baseURL	= "/?#getBaseQueryString(false)#">
<!--- FORM PROTECTION: --->
<cfset local.objCffp = CreateObject("component","model.cfformprotect.cffpVerify").init()>
<cfset local.qryDCExclude = application.objCustomPageUtils.getCustomFieldData(orgID=arguments.event.getValue('mc_siteinfo.orgID'), columnName=local.colDefenseCases, columnValueString=local.colValueYesBut)>

<cfif (event.getValue('isSubmittedJ',0) is 1 and cgi.REQUEST_METHOD eq "POST")>
	<cfif NOT event.valueExists(local.nameOfHiddenField) OR len(event.getValue(local.nameOfHiddenField)) gt 0>
		<cfoutput><div class="tsAppBodyText" style="color:##f00;"><b>The form was not completed in its entirety. Click <a href="javascript:history.go(-1)">here</a> to go back.</b></div></cfoutput>
	<cfelse>
		<cfif NOT local.objCffp.testSubmission(form)>
			<!--- This submission is spam --->
			<cflocation url="#local.customPage.baseURL#&isSubmitted=100" addtoken="no">
		</cfif>
		<cfset local.associationEmail	= "<EMAIL>">
		<cfset local.memberName = "#arguments.event.getTrimValue('prefix','')# #arguments.event.getTrimValue('firstname','')# #arguments.event.getTrimValue('middlename','')# #arguments.event.getTrimValue('lastname','')# #arguments.event.getTrimValue('suffix','')#">
		<!--- -------------------- --->
		<!--- CREEATE MEMBER RECORD --->
		<!--- -------------------- --->
		<cfif session.hasSubmittedCAAAJoinForm EQ 0>
			<cfscript>
			local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=0);
			local.objSaveMember.setDemo(prefix=arguments.event.getTrimValue('prefix',''), firstName=arguments.event.getTrimValue('firstName',''), middleName=arguments.event.getTrimValue('middleName',''), lastName=arguments.event.getTrimValue('lastName',''), suffix=arguments.event.getTrimValue('suffix',''), company=arguments.event.getTrimValue('firm',''));
			local.objSaveMember.setMemberType(memberType='User');
			local.objSaveMember.setAddress(type='Mailing Address', address1=arguments.event.getTrimValue('maddress',''), city=arguments.event.getTrimValue('mcity',''), stateID=arguments.event.getTrimValue('mstateID',0), postalCode=arguments.event.getTrimValue('mzip',''));
			local.objSaveMember.setPhone(addresstype='Mailing Address', type='Phone', value=arguments.event.getTrimValue('mphone',''));
			local.objSaveMember.setPhone(addresstype='Mailing Address', type='Fax', value=arguments.event.getTrimValue('mfax',''));
			local.objSaveMember.setAddress(type='Home Address', address1=arguments.event.getTrimValue('haddress',''), city=arguments.event.getTrimValue('hcity',''), stateID=arguments.event.getTrimValue('hstateID',0), postalCode=arguments.event.getTrimValue('hzip',''));
			local.objSaveMember.setPhone(addressType='Home Address',type='Direct',value=arguments.event.getTrimValue('hphone',''));
			local.objSaveMember.setPhone(addressType='Home Address',type='Cell',value=arguments.event.getTrimValue('hcell',''));
			if (len(arguments.event.getTrimValue('email',''))) {
				local.objSaveMember.setEmail(type='Email', value=arguments.event.getTrimValue('email'));
			}
			if (len(arguments.event.getTrimValue('website',''))) {
				local.objSaveMember.setWebsite(type='Website', value=arguments.event.getTrimValue('website'));
			}
			if (len(arguments.event.getTrimValue('sponsor',''))) {
				local.objSaveMember.setCustomField(field='Sponsor', value=arguments.event.getTrimValue('sponsor'));
			}
			if (arguments.event.getTrimValue('chkNoDir',0) eq 1) {
				local.objSaveMember.setCustomField(field='Do Not List in Directories', value=1);
			}
			if (len(arguments.event.getTrimValue('chapter',''))) {
				local.objSaveMember.setCustomField(field=local.colHomeChapter, value=arguments.event.getTrimValue('chapter'));
				local.objSaveMember.setCustomField(field=local.colHomeChapterRO, value=arguments.event.getTrimValue('chapter'), bypassReadOnly=1);
			}
			if (len(arguments.event.getTrimValue('raceEthnicity',''))) {
				local.objSaveMember.setCustomField(field='Ethnicity', value=arguments.event.getTrimValue('raceEthnicity'));
			}
			if (len(arguments.event.getTrimValue('gender',''))) {
				local.objSaveMember.setCustomField(field='Gender', value=arguments.event.getTrimValue('gender'));
			}
			if (arguments.event.getTrimValue('proType','') eq 'A') {
				if (len(arguments.event.getTrimValue('wcabYear',''))) {
					local.validWCABDate = true;
				} else {
					local.validWCABDate = false;
				}
				if (local.validWCABDate AND ((arguments.event.getTrimValue('selWCDC','') neq local.qryDCExclude.valueID))) {
					local.objSaveMember.setCustomField(field=local.colMemberType, value=local.colValueAppRegular);
				} else {
					local.objSaveMember.setCustomField(field=local.colMemberType, value=local.colValueAppAssoc);
				}
				if (local.validWCABDate) {
					local.objSaveMember.setCustomField(field='First Year Before WCAB', value=arguments.event.getTrimValue('wcabYear'));
				}
				if (arguments.event.getTrimValue('selWCDC','') eq local.qryDCExclude.valueID) {
					local.objSaveMember.setCustomField(field=local.colAssocSpecialties, value=local.colValueDefenseAtty);
				}
				if (len(arguments.event.getTrimValue('lawschool',''))) {
					local.objSaveMember.setCustomField(field=local.colLawSchool, value=arguments.event.getTrimValue('lawschool'));
				}
				local.licenseDate = '#arguments.event.getTrimValue('barMonth','')#/#arguments.event.getTrimValue('barDay','')#/#arguments.event.getTrimValue('barYear','')#';
				if (isValid('date',local.licenseDate)) {
					local.objSaveMember.setProLicense(name='State of California',status='Active',license=arguments.event.getTrimValue('barNumber',''), date=local.licenseDate);
				}
				if (len(arguments.event.getTrimValue('wcPct',''))) {
					local.objSaveMember.setCustomField(field=local.colPctWC, value=arguments.event.getTrimValue('wcPct'));
				}
				if (len(arguments.event.getTrimValue('selWCDC',''))) {
					local.objSaveMember.setCustomField(field='Workers Compensation Defense Cases', value=arguments.event.getTrimValue('selWCDC'));
				}
				if (len(arguments.event.getTrimValue('practiceAreas',''))) {
					local.objSaveMember.setCustomField(field=local.colAoP, value=arguments.event.getTrimValue('practiceAreas'));
				}				
			} else if (arguments.event.getTrimValue('proType','') eq 'O') {
				local.objSaveMember.setCustomField(field=local.colMemberType, value=local.colValueAppAssoc);
				if (len(arguments.event.getTrimValue('bizNatures',''))) {
					local.objSaveMember.setCustomField(field=local.colAssocSpecialties, value=arguments.event.getTrimValue('bizNatures'));
				}
				if (arguments.event.getValue("proRate",'') eq "LSM"){
				local.objSaveMember.setCustomField(field=local.colMemberType, value=local.colValueAppLS);
				local.objSaveMember.setCustomField(field=local.colLawSchool, value=arguments.event.getTrimValue('lawschool_student',''));
				}
			}
			local.strResult = local.objSaveMember.saveData();
			</cfscript>

			<cfif local.strResult.success>
				<cfset local.recordUpdated = true>
				<cfset arguments.event.setValue('membernumber',local.strResult.membernumber)>
			<cfelse>
				<cfset local.recordUpdated = false>
			</cfif>
			<cfif not local.recordUpdated>
				<cfoutput>
					<div class="tsAppBodyText">
						<b>An error occurred while setting up your membership.  We are unable to process your membership online at this time.</b>
						<br>
						Please contact customer support for assistance.
						<br><br>
						We apologize for the inconvenience.
					</div>
				</cfoutput>
			<cfelse>
	 			<cfset session.hasSubmittedCAAAJoinForm = "1">

				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemberLookups">
					set nocount on

					declare @orgID int, @mStateName varchar(50), @hStateName varchar(50), @lsName varchar(max);
					set @orgID = #arguments.event.getValue('mc_siteinfo.orgID')#;

					select @mStateName=Name
					from dbo.ams_states
					where stateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getTrimValue('mstateID',0))#">;

					select @hStateName=Name
					from dbo.ams_states
					where stateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getTrimValue('hstateID',0))#">;

					<cfif len(arguments.event.getTrimValue('lawschool',''))>
						select @lsName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('lawschool')#">;
					<cfelseif len(arguments.event.getTrimValue('lawschool_student',''))>
						select @lsName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('lawschool_student')#">;
					<cfelse>
						set @lsName = '';
					</cfif>

					select @mStateName as mStateName, @hStateName as hStateName, @lsName as lsName;
				</cfquery>

				<!--- send email with collected data --->
				<cfsavecontent variable="local.pdftext">
					<cfoutput>
						<style>
							.docText { font-size:8pt; font-family: Calibri,Arial,Helvetica;}
						</style>
						<p class="docText">CAAA Membership Application submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
						<table cellpadding="2" cellspacing="0" border="0" width="100%">
							<tr><td colspan="2" class="docText"><strong>Member Information</strong></td></tr>
							<tr><td valign="top" class="docText" width="25%">Name:</td><td valign="top" class="docText">#local.memberName#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Firm / Business Name:</td><td valign="top" class="docText">#trim(event.getValue('firm',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Mailing Address:</td><td valign="top" class="docText">#trim(event.getValue('maddress',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">City:</td><td valign="top" class="docText">#trim(event.getValue('mcity',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">State:</td><td valign="top" class="docText">#local.qryMemberLookups.mStateName#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Zip:</td><td valign="top" class="docText">#trim(event.getValue('mzip',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Phone:</td><td valign="top" class="docText">#trim(event.getValue('mphone',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Fax:</td><td valign="top" class="docText">#trim(event.getValue('mfax',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Email:</td><td valign="top" class="docText">#trim(event.getValue('email',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Website:</td><td valign="top" class="docText">#trim(event.getValue('website',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Home Address:</td><td valign="top" class="docText">#trim(event.getValue('haddress',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">City:</td><td valign="top" class="docText">#trim(event.getValue('hcity',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">State:</td><td valign="top" class="docText">#local.qryMemberLookups.hStateName#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Zip:</td><td valign="top" class="docText">#trim(event.getValue('hzip',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Phone:</td><td valign="top" class="docText">#trim(event.getValue('hphone',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Cell:</td><td valign="top" class="docText">#trim(event.getValue('hcell',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Sponsor:</td><td valign="top" class="docText">#trim(event.getValue('sponsor',''))#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Chapter:</td><td valign="top" class="docText">#arguments.event.getTrimValue('chapter','')#&nbsp;</td></tr>
							<tr><td valign="top" class="docText">Please do not list me in the CAAA Directory:</td><td valign="top" class="docText"><cfif event.getValue('chkNoDir',0) eq 1>Yes<cfelse>No</cfif></td></tr>
							<tr><td colspan="2" height="10"></td></tr>
							<tr><td colspan="2" class="docText"><strong>Professional Information</strong></td></tr>
							<cfif (arguments.event.getValue("proType",'') eq "A")>
								<cfif local.validWCABDate AND ((arguments.event.getValue("selWCDC",'') neq local.qryDCExclude.valueID))>
									<tr><td valign="top" class="docText">Member Type:</td><td valign="top" class="docText">Regular Member</td></tr>
								<cfelse>
									<tr><td valign="top" class="docText">Member Type:</td><td valign="top" class="docText">Associate Member</td></tr>
								</cfif>
								<tr><td valign="top" class="docText">CA State Bar Number:</td><td valign="top" class="docText">#trim(event.getValue('barNumber',''))#&nbsp;</td></tr>
								<tr><td valign="top" class="docText">First year before the WCAB:</td><td valign="top" class="docText">#event.getValue('wcabYear','')#&nbsp;</td></tr>
								<tr><td valign="top" class="docText">Law School:</td><td valign="top" class="docText">#local.qryMemberLookups.lsName#&nbsp;</td></tr>
								<tr><td valign="top" class="docText">Date Admitted to CA Bar:</td><td valign="top" class="docText"><cfif local.validWCABDate>#event.getValue('barMonth','')#-#event.getValue('barDay','')#-#event.getValue('barYear','')#</cfif>&nbsp;</td></tr>
								<tr><td valign="top" class="docText">Percentage of Practice in Workers' Compensation:</td><td valign="top" class="docText">#arguments.event.getTrimValue('wcPct','')#</td></tr>
								<tr><td valign="top" class="docText">Handle any Workers' Compensation Defense Cases:</td><td valign="top" class="docText">#arguments.event.getTrimValue('selWCDC','')#</td></tr>
								<tr><td valign="top" class="docText">Areas of Practice:</td><td valign="top" class="docText">#arguments.event.getTrimValue('practiceAreas','')#&nbsp;</td></tr>
							<cfelseif (arguments.event.getValue("proType") eq "O")>
								<tr><td valign="top" class="docText">Member Type:</td><td valign="top" class="docText">Associate Member</td></tr>
								<tr><td valign="top" class="docText">Nature of Business:</td><td valign="top" class="docText">#arguments.event.getTrimValue('bizNatures','')#&nbsp;</td></tr>
								<cfif arguments.event.getValue("proRate",'') eq "LSM">
								<tr><td valign="top" class="docText">Law School:</td><td valign="top" class="docText">#local.qryMemberLookups.lsName#&nbsp;</td></tr>
								</cfif>
							</cfif>
							<tr><td colspan="2" height="10"></td></tr>
							<tr><td colspan="2" class="docText"><strong>Member Fees</strong></td></tr>
							<tr>
								<td valign="top" class="docText">Rate:</td>
								<td valign="top" class="docText">
									<cfswitch expression="#event.getValue('proRate','')#">
										<cfcase value="FYM">
											$350 First Time Member
										</cfcase>
										<cfcase value="WBC">
											$500 Welcome Back to CAAA
										</cfcase>
										<cfcase value="LT3">
											$661.25 Less than three years experience before the WCAB
										</cfcase>
										<cfcase value="OTW">
											$862.50 Less than one-third of practice in workers' compensation
										</cfcase>
										<cfcase value="3WC">
											$891.25 Three years experience before the WCAB and one-third or more of practice is in workers' compensation
										</cfcase>
										<cfcase value="5WC">
											$1144.25 Five years experience before the WCAB and one-third or more of practice is in workers' compensation
										</cfcase>
										<cfcase value="SRM">
											$1,437.50 Sustaining Regular Member
										</cfcase>
										<cfcase value="RPI">
											$1,495 Patron Individual
										</cfcase>
										<cfcase value="RPF">
											$2,875 Patron Firm
										</cfcase>
										<cfcase value="LSM">
											$115 Law Student
										</cfcase>
										<cfcase value="PAM">
											$2,875 Platinum Membership
										</cfcase>
										<cfcase value="GAM">
											$1,782.50 Gold Membership
										</cfcase>
										<cfcase value="SAM">
											$1,035 Silver Membership
										</cfcase>
										<cfcase value="FYA">
											$600 First Time Membership
										</cfcase>
										<cfcase value="ICR">
											$488.75 Interpreters & Court Reporters
										</cfcase>
									</cfswitch>&nbsp;
								</td>
							</tr>
						</table>
					</cfoutput>
				</cfsavecontent>

				<cfset local.uid = createuuid()>
				<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
				<cfdocument filename="#local.strFolder.folderPath#/un_#local.uid#.pdf" pagetype="letter" margintop=".5" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
					<cfoutput>
						<html>
						<head>
						<style>
						body { font-size:8pt; font-family: Calibri,Arial,Helvetica; }
						table,tr,td { font-size:8pt; font-family: Calibri,Arial,Helvetica; }
						h5 { font-size:13pt; font-weight:bold; margin:0 0 0 0;}
						</style>
						</head>
						<body>
							#local.pdftext#
						</body>
						</html>
					</cfoutput>
				</cfdocument>

				<cfset local.strPDF = structNew()>
				<cfset local.strPDF['serverDirectory'] = local.strFolder.folderPath>
				<cfset local.strPDF['serverFile'] = "MembershipApplication_#DateFormat(now(),'m-d-yyyy')#.pdf">
				<cfset local.emailAttachFile = "Membership_Application_Confirmation.pdf">
				<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.uid#.pdf","#local.strFolder.folderPath#/#local.strPDF['serverFile']#","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
				<cffile action="copy" destination="#local.strFolder.folderPath#/#local.emailAttachFile#" source="#local.strFolder.folderPath#/#local.strPDF['serverFile']#">
				<cfset application.objCustomPageUtils.mem_StoreMembershipApplication(memberID=local.strResult.memberID, strPDF=local.strPDF, siteID=arguments.event.getValue('mc_siteInfo.siteid'))>

				<!--- MEMBER EMAIL --->
				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						#local.strPageFields.ConfirmationEmailMessage#
						<div style="height: 0px;">&nbsp;</div>
						#local.pdftext#
					</cfoutput>
				</cfsavecontent>

				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email=local.associationEmail},
					emailto=[{ name="", email=event.getTrimValue('email') }],
					emailreplyto=local.associationEmail,
					emailsubject="CAAA - Your Membership Application",
					emailtitle="#event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
					emailhtmlcontent=local.mailContent,
					siteID=local.siteID,
					memberID=val(local.strResult.memberID),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
				)>
				<cfset local.emailSentToUser = local.responseStruct.success>

				<!--- TLA EMAIL  --->
				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<cfif NOT local.emailSentToUser>
							<p>
							#local.memberName# was not sent email confirmation due to bad data.<br />
							Please contact, and let them know.
							</p>
							<br /><br />
						</cfif>
						#local.pdftext#
					</cfoutput>
				</cfsavecontent>

				<cfscript>
					local.arrEmailTo = [];
					local.associationEmail = replace(local.associationEmail,",",";","all");
					local.toEmailArr = listToArray(local.associationEmail,';');

					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
					}
				</cfscript>
				
				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email="<EMAIL>" },
					emailto=local.arrEmailTo,
					emailreplyto="<EMAIL>",
					emailsubject="CAAA - Membership Application submission from #local.memberName#",
					emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
					emailhtmlcontent=local.mailContent,
					siteID=arguments.event.getTrimValue('mc_siteinfo.siteID'),
					memberID=val(arguments.event.getTrimValue('mc_siteinfo.sysMemberID')),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
				)>
				<!--- And then display the CCInfo --->
				<!--- GATEWAY INFORMATION: --->
				<cfset local.formName = "frmCAAACC">
				<cfset local._paymentProfileCode = 'CreditCard'>
				<cfset local.strPaymentForm = application.objPayments.showGatewayInputForm(
																					siteid=arguments.event.getValue('mc_siteinfo.siteid'),
																					profilecode=local._paymentProfileCode,
																					pmid = local.strResult.memberID,
																					usePopupDIVName='paymentTable'
																					)>

				<cfoutput>
				<script type="text/javascript">
					function getMethodOfPayment() {
						var btnGrp = document.forms['#local.formName#'].payMeth;
						var i = getSelectedRadio(btnGrp);
						if (i == -1) return "";
						else {
							if (btnGrp[i]) return btnGrp[i].value;
							else return btnGrp.value;
						}
					}
					function _validate() {
						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();
						thisForm.btnSubmit.disabled=true;
						#local.strPaymentForm.jsvalidation#
						if (arrReq.length > 0) {
							var msg = 'The following fields are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							thisForm.btnSubmit.disabled=false;
							return false;
						}
						return true;
					}
				</script>
				<script>
					function showAlert(msg){ $('##payerrDIV').html(msg).attr('class','alert').show();  };
				</script>
				<cfif len(local.strPaymentForm.headCode)>
					<cfhtmlhead text="#application.objCommon.minText(local.strPaymentForm.headCode)#">
				</cfif>

				<div id="paymentTable">
					<div id="payerrDIV" style="display:none;margin:6px 0;"></div>
					<div class="form">
						<cfform name="#local.formName#"  id="#local.formName#" method="POST" action="/?pg=Join" onSubmit="return _validate();">
							<cfinput type="hidden" name="isSubmittedC"  id="isSubmittedC" value="1" />
							<cfinput type="hidden" name="#local.nameOfHiddenField#"  id="#local.nameOfHiddenField#" value="" />
							<div>
								<div id="CCInfo" class="">
									<div class="tsAppLegendTitle sectionTitle">Credit Card Information</div>
									<div class="tsAppBodyText optionsInline">
										<cfif len(local.strPaymentForm.inputForm)>
											<div>#local.strPaymentForm.inputForm#</div>
										</cfif>
										<br />
									</div>
									<div class="tsAppBodyText"><b>*CAAA does not refund any membership dues.</b><br /><br /></div>
								</div>
								<div class="PB"><button type="submit" class="tsAppBodyText formButton" name="btnSubmit">Next</button></div>
							</div>
						</cfform>
					</div>
				</div>
				</cfoutput>
			</cfif>
		<cfelse>
			<cfoutput>
				<div class="tsAppBodyText">
					<b>You member record has already been created.</b>
					<br>
					Please contact CAAA's Member Services Department at 916-444-5155 for assistance.
					<br><br>
					We apologize for the inconvenience.
					<br><br>
				</div>
			</cfoutput>
		</cfif>
	</cfif>
<cfelseif event.getValue('isSubmittedC',0) is 1 and cgi.REQUEST_METHOD eq "POST">
	<cfif NOT event.valueExists(local.nameOfHiddenField) OR len(event.getValue(local.nameOfHiddenField)) gt 0>
		<cfoutput><div class="tsAppBodyText" style="color:##f00;"><b>The form was not completed in its entirety. Click <a href="javascript:history.go(-1)">here</a> to go back.</b></div></cfoutput>
	<cfelse>
		<cfoutput>#local.strPageFields.ConfirmationScreenMessage#</cfoutput>
	</cfif>
<cfelse>
	<cfset local.qryOrgMemberFields = application.objCustomPageUtils.getOrgMemberFields(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
	<cfif local.qryOrgMemberFields.usePrefixList is 1>
		<cfset local.qryOrgPrefixes = application.objOrgInfo.getOrgPrefixTypes(orgID=arguments.event.getValue('mc_siteinfo.orgid'))>
	</cfif>
	<cfset local.qryHomeChapters = application.objCustomPageUtils.getCustomFieldData(orgID=arguments.event.getValue('mc_siteinfo.orgID'), columnName=local.colHomeChapter)>
	<cfset local.qryLawSchools = application.objCustomPageUtils.getCustomFieldData(orgID=arguments.event.getValue('mc_siteinfo.orgID'), columnName=local.colLawSchool)>
	<cfset local.qryBizNatures = application.objCustomPageUtils.getCustomFieldData(orgID=arguments.event.getValue('mc_siteinfo.orgID'), columnName=local.colAssocSpecialties)>
	<cfset local.qryAoP = application.objCustomPageUtils.getCustomFieldData(orgID=arguments.event.getValue('mc_siteinfo.orgID'), columnName=local.colAoP)>
	<cfset local.qryWCPercentages = application.objCustomPageUtils.getCustomFieldData(orgID=arguments.event.getValue('mc_siteinfo.orgID'), columnName=local.colPctWC)>

	<cfscript>
		// local.useMID: used for new CIM gateway:
		local.useMID = event.getValue('memberID',0);
		if( application.objUser.isLoggedIn(cfcuser=session.cfcuser) ){
			local.useMID = session.cfcUser.memberData.memberID;
		}
		local.siteID = event.getValue('mc_siteInfo.siteID');
	</cfscript>

	<cfset local.membershipDuesTypeID = application.objCustomPageUtils.sub_getTypeFromUID(siteID=local.siteID, typeUID="868F96D4-228E-491A-8DD8-6D1C5795489C")>
	<cfset local.hasSub = false>
	<cfif event.getValue('msg','') neq 2 and application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		<cfset local.hasSub = application.objCustomPageUtils.sub_hasSubsciptionInType(mcproxy_orgID=arguments.event.getValue('mc_siteinfo.orgID'), mcproxy_siteID=local.siteID, memberID=local.useMID, typeID=local.membershipDuesTypeID)>
	</cfif>
	<cfsavecontent variable="local.js">
		<cfoutput>
		<script type="text/javascript" src="/javascript/global.js"></script>
		<script type="text/javascript">
			function showPro() {
				var theForm = document.forms["frmCAAAJ"];
				var aDiv = document.getElementById('divProA');
				var lDiv = document.getElementById('divProL');
				var oDiv = document.getElementById('divProO');
				var rDiv = document.getElementById('divRates');
				var regDiv = document.getElementById('divRegRates');
				var asDiv = document.getElementById('divAssocRates');
				var optDCExclude = #local.qryDCExclude.valueID#;
				rDiv.style.display = 'block';
				var proType = "";
				for (var i=0; i < theForm.proType.length; i++) {
					if (theForm.proType[i].checked)
					{
						proType = theForm.proType[i].value;
					}
				}
				if (proType == 'A') {
					aDiv.style.display = 'block';
					lDiv.style.display = 'none';
					oDiv.style.display = 'none';
					regDiv.style.display = 'block';
					asDiv.style.display = 'none';
				}
				if (proType == 'O') {
					aDiv.style.display = 'none';
					lDiv.style.display = 'none';
					if(($("[name=proRate]:checked").val()||"") == "LSM"){
						lDiv.style.display = 'block';
					}					
					oDiv.style.display = 'block';
					regDiv.style.display = 'none';
					asDiv.style.display = 'block';
				}
			}
			function chkWCDC() {
				var selWCDC = document.getElementById('selWCDC');
				var spnWCDC = document.getElementById('spnWCDC');
				if (selWCDC.value == "1") {
					spnWCDC.style.display = 'block';
				} else {
					spnWCDC.style.display = 'none';
				}
			}
			function _FB_hasValue(obj, obj_type) {
				if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){ tmp = obj.value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length == 0) return false; else return true; }
				else if (obj_type == 'SELECT'){ for (var i=0; i < obj.length; i++) { if (obj.options[i].selected){ tmp = obj.options[i].value; tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,''); if (tmp.length > 0) return true; } } return false; }
				else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){ if (obj.checked) return true; else return false;	}
				else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){ if (obj.length == undefined && obj.checked) return true; else{for (var i=0; i < obj.length; i++){ if (obj[i].checked) return true; }} return false; }
				else{ return true; }
			}
			function _FB_validateForm() {
				var theForm = document.forms["frmCAAAJ"];
				var proType = "";
				var proRate = "";
				var optDCExclude = #local.qryDCExclude.valueID#;
				$("input[name=btnSubmit]").attr('disabled','disabled');
				for (var i=0; i < theForm.proType.length; i++) {
					if (theForm.proType[i].checked) {
						proType = theForm.proType[i].value;
					}
				}
				for (var i=0; i < theForm.proRate.length; i++) {
					if (theForm.proRate[i].checked) {
						proRate = theForm.proRate[i].value;
					}
				}
				var proTypeSelected = getSelectedRadio(proType);
				var arrReq = new Array();
				if (!_FB_hasValue(theForm['firstname'], 'TEXT')) arrReq[arrReq.length] = 'Please provide your First Name.';
				if (!_FB_hasValue(theForm['lastname'], 'TEXT')) arrReq[arrReq.length] = 'Please provide your Last Name.';
				if (!_FB_hasValue(theForm['maddress'], 'TEXT')) arrReq[arrReq.length] = 'Please provide your Mailing Address.';
				if (!_FB_hasValue(theForm['mcity'], 'TEXT')) arrReq[arrReq.length] = 'Please provide your Mailing City.';
				if (!_FB_hasValue(theForm['mstateID'], 'SELECT')) arrReq[arrReq.length] = 'Please provide your Mailing State.';
				if (!_FB_hasValue(theForm['mzip'], 'TEXT')) arrReq[arrReq.length] = 'Please provide your Mailing Zip Code.';
				if (!_FB_hasValue(theForm['mphone'], 'TEXT')) arrReq[arrReq.length] = 'Please provide your Phone number.';
				if (!_FB_hasValue(theForm['chapter'], 'SELECT')) arrReq[arrReq.length] = 'Please provide your Home Chapter.';
				if (!_FB_hasValue(theForm['proType'], 'RADIO')) arrReq[arrReq.length] = 'Please select your Professional status.';

				if (proType == "A") {
					if (!_FB_hasValue(theForm['barNumber'], 'TEXT')) arrReq[arrReq.length] = 'Please provide your California State Bar Number.';
					if (!_FB_hasValue(theForm['barYear'], 'SELECT')) arrReq[arrReq.length] = 'Please provide the date admitted to CA Bar.';
					if (!_FB_hasValue(theForm['wcabYear'], 'SELECT')) arrReq[arrReq.length] = 'Please provide the first year before the WCAB.';
					if (!_FB_hasValue(theForm['wcPct'], 'SELECT')) arrReq[arrReq.length] = 'Please provide the percentage of practice in Workers Comp.';
					if (!_FB_hasValue(theForm['selWCDC'], 'TEXT')) arrReq[arrReq.length] = 'Please select if you handle Workers Comp defense cases.';
					if (!_FB_hasValue(theForm['regMembAgreement'], 'SINGLE_VALUE_CHECKBOX')) arrReq[arrReq.length] = 'Please accept the Membership Agreement.';
				}
				if (proType == "O" && ($("[name=proRate]:checked").val()||"") == "LSM"){				
					if (!_FB_hasValue(theForm['lawschool_student'], 'SELECT')) arrReq[arrReq.length] = 'Please provide your Law School.';
				}
				if (!_FB_hasValue(theForm['proRate'], 'RADIO')) arrReq[arrReq.length] = 'Please select a Rate.';
				if (arrReq.length > 0) {
					var msg = 'The following fields are required:\n\n';
					for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
					alert(msg);
					$("input[name=btnSubmit]").prop("disabled",false);
					return false;
				}
				return true;
			}
			function getSelectedRadio(buttonGroup) {
				if (buttonGroup[0]) {
					for (var i=0; i<buttonGroup.length; i++) { if (buttonGroup[i].checked) return i }
				} else { if (buttonGroup.checked) return 0; }
				return -1;
			}
		</script>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#local.js#">
		<cfoutput>
		<cfif event.getValue('msg',0) EQ "2">
			<!--- Renewal form is not Open --->
			<div class="bodyText" >
				<br />It looks like you might already be a member! Thank you and please contact CAAA at (800) 648-3132 or <a href="mailto:<EMAIL>"><EMAIL></a> for information about your membership or renewing.
			</div>
		<cfelse>
			<cfif local.hasSub eq true>
				<cflocation url="/?pg=join&msg=2" addtoken="no" />
			</cfif>
			<br>
			<cfform name="frmCAAAJ" id="frmCAAAJ" method="POST" action="/?pg=Join" class="form-horizontal" onsubmit="return _FB_validateForm();">
				<cfinput type="hidden" name="isSubmittedJ"  id="isSubmittedJ" value="1" />
				<cfinput type="hidden" name="#local.nameOfHiddenField#"  id="#local.nameOfHiddenField#" value="" />
				<fieldset class="section">
					<legend class="tsAppLegendTitle sectionTitle">Member Information</legend>
					<div class="container-fluid">
						<div class="row-fluid">
							<cfif local.qryOrgMemberFields.hasPrefix is 1>
								<div class="span2">
									<label class="tsAppBodyText questionText" for="prefix">Prefix:</label>
									<cfif local.qryOrgMemberFields.usePrefixList is 1>
										<select class="tsAppBodyText input-block-level" id="prefix" name="prefix">
											<option value="">Choose...</option>
											<cfloop query="local.qryOrgPrefixes">
												<option value="#prefix#">#prefix#</option>
											</cfloop>
										</select>
									<cfelse>
										<input value="" name="prefix" id="prefix" type="text" placeholder="Prefix">
									</cfif>
								</div>
							</cfif>
							<div class="span3">
								<label class="tsAppBodyText questionText" for="firstname">* First Name:</label>
								<input type="text" name="firstname" id="firstname" placeholder="First Name" class="input-block-level" required="required">
							</div>
							<cfif local.qryOrgMemberFields.hasMiddleName is 1>
								<div class="span3">
									<label class="tsAppBodyText questionText" for="middlename">Middle Name:</label>
									<input type="text" name="middlename" id="middlename" placeholder="Middle Name" class="input-block-level">
								</div>
							</cfif>
							<div class="span3">
								<label class="tsAppBodyText questionText" for="lastname">* Last Name:</label>
								<input type="text" name="lastname" id="lastname" placeholder="Last Name" class="input-block-level" required="required">
							</div>
							<cfif local.qryOrgMemberFields.hasSuffix is 1>
								<div class="span1">
									<label class="tsAppBodyText questionText" for="suffix">Suffix:</label>
									<input type="text" name="suffix" id="suffix" class="input-block-level">
								</div>
							</cfif>
						</div>
					</div>
				</fieldset>
				<br>
				<fieldset class="section">
					<legend class="tsAppLegendTitle sectionTitle">Business / Firm Information</legend>
					<div class="container-fluid">
						<label class="tsAppBodyText questionText" for="firm">Firm / Business Name:</label>
						<input type="text" name="firm" id="firm" placeholder="Name of your Firm or Business" class="input-block-level">
						<br><br>
						<label class="tsAppBodyText questionText" for="maddress">* Mailing Address:</label>
						<input type="text" name="maddress" id="maddress" placeholder="123 Main St, Suite 100" class="input-block-level" required="required">
						<br><br>
						<div class="row-fluid">
							<div class="span5">
								<label class="tsAppBodyText questionText" for="mcity">* City:</label>
								<input type="text" name="mcity" id="mcity" placeholder="City" class="input-block-level">
							</div>
							<div class="span4">
								<label class="tsAppBodyText questionText" for="mstateID">* State:</label>
								<select name="mstateID" id="mstateID" class="tsAppBodyText input-block-level">
									<option value="">Choose...</option>
									<cfset local.currentCountryID = 0>
									<cfloop query="local.qryStates">
										<cfif local.qryStates.countryID neq local.currentCountryID>
											<cfset local.currentCountryID = local.qryStates.countryID>
											<optgroup label="#local.qryStates.country#">
										</cfif>
										<option value="#local.qryStates.stateID#">#local.qryStates.stateName# (#local.qryStates.stateCode#)</option>
										<cfif local.qryStates.currentrow eq local.qryStates.recordcount or local.qryStates.countryID[local.qryStates.currentrow+1] neq local.currentCountryID>
											</optgroup>
										</cfif>
									</cfloop>
								</select>
							</div>
							<div class="span3">
								<label class="tsAppBodyText questionText" for="mzip">* Zip Code:</label>
								<input type="text" name="mzip" id="mzip" placeholder="Zip Code" class="input-block-level">
							</div>
						</div>
						<br>
						<div class="row-fluid">
							<div class="span3">
								<label class="tsAppBodyText questionText" for="mphone">* Phone:</label>
								<input type="tel" name="mphone" id="mphone" placeholder="xxx-xxx-xxxx" class="input-block-level">
							</div>
							<div class="span3">
								<label class="tsAppBodyText questionText" for="mfax">Fax:</label>
								<input type="tel" name="mfax" id="mfax" placeholder="xxx-xxx-xxxx" class="input-block-level">
							</div>
							<div class="span6 tsAppBodyText">
								<br>Please enter phone numbers in XXX-XXX-XXXX format.
							</div>
						</div>
						<br>
						<div class="row-fluid">
							<div class="span6">
								<label class="tsAppBodyText questionText" for="email">* Email:</label>
								<input type="email" name="email" id="email" placeholder="<EMAIL>" class="input-block-level">
							</div>
							<div class="span6">
								<label class="tsAppBodyText questionText" for="website">Website:</label>
								<input type="url" name="website" id="website" placeholder="http://yourwebsite.com" class="input-block-level">
							</div>
						</div>
					</div>
				</fieldset>
				<br>
				<fieldset class="section">
					<legend class="tsAppLegendTitle sectionTitle">Home Address</legend>

					<div class="container-fluid">
						<label class="tsAppBodyText questionText" for="haddress">Home Address:</label>
						<input type="text" name="haddress" id="haddress" placeholder="123 Main St" class="input-block-level">
						<br><br>
						<div class="row-fluid">
							<div class="span5">
								<label class="tsAppBodyText questionText" for="hcity">City:</label>
								<input type="text" name="hcity" id="hcity" placeholder="City" class="input-block-level">
							</div>
							<div class="span4">
								<label class="tsAppBodyText questionText" for="hstateID">State:</label>
								<select name="hstateID" id="hstateID" class="tsAppBodyText input-block-level">
									<option value="">Choose...</option>
									<cfset local.currentCountryID = 0>
									<cfloop query="local.qryStates">
										<cfif local.qryStates.countryID neq local.currentCountryID>
											<cfset local.currentCountryID = local.qryStates.countryID>
											<optgroup label="#local.qryStates.country#">
										</cfif>
										<option value="#local.qryStates.stateID#">#local.qryStates.stateName# (#local.qryStates.stateCode#)</option>
										<cfif local.qryStates.currentrow eq local.qryStates.recordcount or local.qryStates.countryID[local.qryStates.currentrow+1] neq local.currentCountryID>
											</optgroup>
										</cfif>
									</cfloop>
								</select>
							</div>
							<div class="span3">
								<label class="tsAppBodyText questionText" for="hzip">Zip Code:</label>
								<input type="text" name="hzip" id="hzip" placeholder="Zip Code" class="input-block-level">
							</div>
						</div>
						<br>
						<div class="row-fluid">
							<div class="span3">
								<label class="tsAppBodyText questionText" for="mphone">Home Phone:</label>
								<input type="tel" name="hphone" id="hphone" placeholder="xxx-xxx-xxxx" class="input-block-level">
							</div>
							<div class="span3">
								<label class="tsAppBodyText questionText" for="mfax">Cell Phone:</label>
								<input type="tel" name="hcell" id="hcell" placeholder="xxx-xxx-xxxx" class="input-block-level">
							</div>
							<div class="span6 tsAppBodyText">
								<br>Please enter phone numbers in XXX-XXX-XXXX format.
							</div>
						</div>
					</div>
				</fieldset>
				<br><br>
				<fieldset class="section">
					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span6">
								<label class="tsAppBodyText questionText" for="sponsor">Name of Sponsor:</label>
								<input type="text" name="sponsor" id="sponsor" class="input-block-level">
							</div>
							<div class="span6">
								<label class="tsAppBodyText questionText" for="chapter">* Chapter: <a href="/?pg=ChaptersOverview" target="_blank">Click here for more details.</a></label>
								<cfselect name="chapter" id="chapter" class="input-block-level">
									<option value=""></option>
									<cfloop query="local.qryHomeChapters">
										<option value="#local.qryHomeChapters.columnValueString#">#local.qryHomeChapters.columnValueString#</option>
									</cfloop>
								</cfselect>
							</div>
						</div>
						<br>
						<label class="checkbox tsAppBodyText">
							<input type="checkbox" name="chkNoDir" id="chkNoDir" value="1">
							Please do not list me in the CAAA Directory
						</label>
					</div>
				</fieldset>
				<br>
				<fieldset class="section">
					<legend class="tsAppLegendTitle sectionTitle">Optional Demographic Information</legend>
					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span6">
								<label class="tsAppBodyText questionText" for="raceEthnicity">Race:</label>
								<cfselect name="raceEthnicity" id="raceEthnicity" class="input-block-level">
									<option value=""></option>
									<option value="Asian">Asian</option>
									<option value="Black/African American">Black/African American</option>
									<option value="Latino/Hispanic">Latino/Hispanic</option>
									<option value="Bi-Racial/Multi-Racial">Bi-Racial/Multi-Racial</option>
									<option value="Caucasian">Caucasian</option>
									<option value="Native American">Native American</option>
									<option value="Other">Other</option>
								</cfselect>
							</div>
							<div class="span6">
								<label class="tsAppBodyText questionText" for="gender">Gender:</label>
								<cfselect name="gender" id="gender" class="input-block-level">
									<option value=""></option>
									<option value="Male">Male</option>
									<option value="Female">Female</option>
								</cfselect>
							</div>
						</div>
					</div>
				</fieldset>
				<br><br>
				<fieldset class="section">
					<legend class="tsAppLegendTitle sectionTitle">Professional Information</legend>
					<div class="container-fluid">
						<div class="row-fluid">
							<div class="span4">
								<label class="radio tsAppBodyText">
									<input type="radio" name="proType" id="proType_A" value="A" onClick="showPro();">
									Applicants' Attorney
								</label>
							</div>
							<div class="span4">
								<label class="radio tsAppBodyText">
									<input type="radio" name="proType" id="proType_O" value="O" onClick="showPro();">
									Non-Applicants' Attorney/Other Professional
								</label>
							</div>
						</div>
					</div>
				</fieldset>
				<div id="divProA" style="display:none;">
					<br>
					<fieldset class="section">
						<div class="container-fluid">
							<div class="row-fluid">
								<div class="span4">
									<label class="tsAppBodyText questionText" for="barNumber">* California State Bar Number:</label>
									<input type="text" name="barNumber" id="barNumber" class="input-block-level">
								</div>
							</div>
							<br>
							<div class="row-fluid">
								<legend class="tsAppLegendTitle sectionTitle">* Date admitted to CA Bar:</legend>
								<div class="span3">
									<label class="tsAppBodyText questionText" for="barMonth">Month:</label>
									<select name="barMonth"  id="barMonth"  class="tsAppBodyText input-block-level" onchange="javascript:showPro('A');">
										<option value="">Choose...</option>
										<cfloop from="1" to="12" index="local.thism">
											<option value="#local.thism#">#monthAsString(local.thism)#</option>
										</cfloop>
									</select>
								</div>
								<div class="span3">
									<label class="tsAppBodyText questionText" for="barDay">Day:</label>
									<select name="barDay"  id="barDay"  class="tsAppBodyText input-block-level" onchange="javascript:showPro('A');">
										<option value="">Choose...</option>
										<cfloop from="1" to="31" index="local.thisd">
											<option value="#local.thisd#">#local.thisd#</option>
										</cfloop>
									</select>
								</div>
								<div class="span3">
									<label class="tsAppBodyText questionText" for="barYear">Year:</label>
									<select name="barYear"  id="barYear"  class="tsAppBodyText input-block-level" onchange="javascript:showPro('A');">
										<option value="">Choose...</option>
										<cfloop from="#year(now())#" to="#year(now()) - 70#" step="-1" index="local.thisy">
											<option value="#local.thisy#">#local.thisy#</option>
										</cfloop>
									</select>
								</div>
							</div>
							<br>
							<div class="row-fluid">
								<div class="span4">
									<label class="tsAppBodyText questionText" for="lawschool">Law School:</label>
									<select name="lawschool"  id="lawschool"  class="tsAppBodyText input-block-level">
										<option value="">Choose...</option>
										<cfloop query="local.qryLawschools">
											<cfoutput>
												<option value="#local.qryLawschools.columnValueString#">#local.qryLawschools.columnValueString#</option>
											</cfoutput>
										</cfloop>
									</select>
								</div>
							</div>
							<br>
							<div class="row-fluid">
								<div class="span4">
									<label class="tsAppBodyText questionText" for="wcabYear">* First Year before the WCAB:</label>
									<select name="wcabYear"  id="wcabYear"  class="tsAppBodyText input-block-level">
										<option value="">Choose...</option>
										<cfloop from="#year(now())#" to="#year(now()) - 70#" step="-1" index="local.thisy">
											<option value="#local.thisy#">#local.thisy#</option>
										</cfloop>
									</select>
								</div>
							</div>
							<br>
							<div class="row-fluid">
								<div class="span5">
									<label class="tsAppBodyText questionText" for="wcPct">* Percentage of Practice in Workers' Compensation:</label>
									<select name="wcPct"  id="wcPct"  class="tsAppBodyText input-block-level">
										<option value="">Choose...</option>
										<cfloop query="local.qryWCPercentages">
											<cfoutput>
												<option value="#local.qryWCPercentages.columnValueString#">#local.qryWCPercentages.columnValueString#</option>
											</cfoutput>
										</cfloop>
									</select>
								</div>
							</div>
							<br>
							<div class="row-fluid">
								<div class="span6">
									<label class="tsAppBodyText questionText" for="selWCDC">* Do you handle any Workers' Compensation Defense Cases:</label>
									<input type="text" name="selWCDC" id="txtSenator" class="input-block-level">
								</div>
							</div>
							<br>
							<div class="row-fluid">
								<legend class="tsAppLegendTitle sectionTitle">Membership Agreement:</legend>
								<label class="checkbox tsAppBodyText">
									<input type="checkbox" name="regMembAgreement" id="regMembAgreement" value="I Agree">
									<div class="span1" style="margin:0px;width:10px">*</div><div class="span11" style="margin:2px;"> <cfoutput>#local.strPageFields.MembershipAgreement#</cfoutput></div>
								</label>
							</div>
							<br>
							<div class="row-fluid">
								<div class="span8">
									<label class="tsAppBodyText questionText" for="practiceAreas">Other Areas of Practice:</label>
									<select name="practiceAreas"  id="practiceAreas"  class="tsAppBodyText input-block-level" multiple="true" size="10" >
										<cfloop query="local.qryAoP">
											<cfoutput>
												<option value="#local.qryAoP.columnValueString#">#local.qryAoP.columnValueString#</option>
											</cfoutput>
										</cfloop>
									</select>
								</div>
							</div>
							<br>
						</div>
					</fieldset>
				</div>
				<div id="divProO" style="display:none;">
					<br>
					<fieldset class="section">
						<div class="container-fluid">
							<div class="row-fluid">
								<div class="span8">
									<label class="tsAppBodyText questionText" for="bizNatures">Nature of Business:</label>
									<select name="bizNatures"  id="bizNatures"  class="tsAppBodyText input-block-level"  multiple="true" size="10">
										<cfloop query="local.qryBizNatures">
											<cfoutput>
												<option value="#local.qryBizNatures.columnValueString#">#local.qryBizNatures.columnValueString#</option>
											</cfoutput>
										</cfloop>
									</select>
								</div>
							</div>
						</div>
					</fieldset>
				</div>
				<br>
				<div id="divProL" style="display:none;">
					<br>
					<fieldset class="section">
						<div class="container-fluid">
							<div class="row-fluid">
								<div class="span5">
									<label class="tsAppBodyText questionText" for="lawschool_student">* Law School:</label>
									<select name="lawschool_student"  id="lawschool_student"  class="tsAppBodyText input-block-level">
										<option value="">Choose...</option>
										<cfloop query="local.qryLawschools">
											<cfoutput>
												<option value="#local.qryLawschools.columnValueString#">#local.qryLawschools.columnValueString#</option>
											</cfoutput>
										</cfloop>
									</select>
								</div>
							</div>
						</div>
					</fieldset>
				</div>
				<br>
				<div id="divRates" style="display:none;">
					<legend class="tsAppLegendTitle sectionTitle"><strong>Rates</strong></legend>
					<br>
					<div id="divRegRates" style="display:none;">
						<fieldset class="section">				
							<div class="container-fluid">
								<div class="row-fluid">
									<legend class="tsAppLegendTitle sectionTitle">Regular Member Fees</legend>
									<br>
								</div>
								<div class="row-fluid">
									<label class="radio tsAppBodyText">
										<cfinput type="radio" name="proRate" id="proRate" value="FYM" style="vertical-align:top;"> 
										<strong>$350</strong> -  First Time Member (Never been a member of CAAA)
									</label>
								</div>
								<div class="row-fluid">
									<label class="radio tsAppBodyText">
										<cfinput type="radio" name="proRate" id="proRate" value="WBC" style="vertical-align:top;"> 
										<strong>$500</strong> - Welcome Back to CAAA (Have been out of CAAA for 2 years or more)
									</label>
								</div>
								<div class="row-fluid">
									<label class="radio tsAppBodyText">
										<cfinput type="radio" name="proRate" id="proRate" value="LT3" style="vertical-align:baseline;">
										<strong>$661.25</strong> - Less than 3 years experience before the WCAB
									</label>
								</div>	
								<div class="row-fluid">
									<label class="radio tsAppBodyText">
										<cfinput type="radio" name="proRate" id="proRate" value="3WC" style="vertical-align:baseline;">
										<strong>$891.25</strong> - Three years experience before the WCAB and one-third or more of practice is in workers' compensation
									</label>						
								<div class="row-fluid">
									<label class="radio tsAppBodyText">
										<cfinput type="radio" name="proRate" id="proRate" value="5WC" style="vertical-align:baseline;">
										<strong>$1144.25</strong> - Five years experience before the WCAB and one-third or more of practice is in workers' compensation
									</label>
								</div>
								<div class="row-fluid">
									<label class="radio tsAppBodyText">
										<cfinput type="radio" name="proRate" id="proRate" value="OTW" style="vertical-align:baseline;">
										<strong>$862.50</strong> - Less than one-third of practice in workers' compensation
									</label>
								</div>
								</div>
								<div class="row-fluid">
									<label class="radio tsAppBodyText">
										<cfinput type="radio" name="proRate" id="proRate" value="SRM" style="vertical-align:baseline;">
										<strong>$1,437.50</strong> - Sustaining Regular Member
									</label>
								</div>
								<div class="row-fluid">
									<label class="radio tsAppBodyText">
										<cfinput type="radio" name="proRate" id="proRate" value="RPI" style="vertical-align:baseline;">
										<strong>$1,495</strong> - Patron Individual
									</label>
								</div>
								<div class="row-fluid">
									<label class="radio tsAppBodyText">
										<cfinput type="radio" name="proRate" id="proRate" value="RPF" style="vertical-align:baseline;">
										<strong>$2,875</strong> - Patron Firm
									</label>
								</div>
							</div>
						</fieldset>
					</div>
					<div id="divAssocRates" style="display:none;">
						<fieldset class="section">
							<div class="container-fluid">
								<div class="row-fluid">
									<legend class="tsAppLegendTitle sectionTitle">Associate Member Fees</legend>
									<br>
								</div>
								<div class="row-fluid">
									<label class="radio tsAppBodyText">
										<cfinput type="radio" name="proRate" id="proRate" value="FYA" style="vertical-align:top;" onClick="showPro();"> 
										<strong>$600</strong> - First Time Associate Member<br> Membership for a single person joining CAAA for the first time.
									</label>
								</div>
								<div class="row-fluid">
									<label class="radio tsAppBodyText">
										<cfinput type="radio" name="proRate" id="proRate" value="LSM" style="vertical-align:top;" onClick="showPro();"> 
										<strong>$115</strong> - Law Student<br> Rate applies to Law Students who are new or renewing  
									</label>
								</div>
								<div class="row-fluid">
									<label class="radio tsAppBodyText">
										<cfinput type="radio" name="proRate" id="proRate" value="WBC" style="vertical-align:top;" onClick="showPro();"> 
										<strong>$500</strong> - Welcome Back to CAAA <br> Membership for a single person who has been out of CAAA for two years or more.
									</label>
								</div>
								<div class="row-fluid">
									<label class="radio tsAppBodyText">
										<cfinput type="radio" name="proRate" id="proRate" value="SAM" style="vertical-align:top;" onClick="showPro();"> 
										<strong>$1,035</strong> - Silver Membership<br> Membership for a single person.
									</label>
								</div>
								<div class="row-fluid">
									<label class="radio tsAppBodyText">
										<cfinput type="radio" name="proRate" id="proRate" value="GAM" style="vertical-align:top;" onClick="showPro();"> 
										<strong>$1,782.50</strong> - Gold Membership<br> For up to three (3) persons in a company, recognition in Winter & Summer convention syllabi.  
									</label>
								</div>
								<div class="row-fluid">
									<label class="radio tsAppBodyText">
										<cfinput type="radio" name="proRate" id="proRate" value="PAM" style="vertical-align:top;" onClick="showPro();"> 
										<strong>$2,875</strong> - Platinum Membership<br> For up to ten (10) persons in a company, recognition in Winter & Summer convention syllabi.
									</label>
								</div>
								<div class="row-fluid">
									<label class="radio tsAppBodyText">
										<cfinput type="radio" name="proRate" id="proRate" value="ICR" style="vertical-align:top;" onClick="showPro();"> 
										<strong>$488.75</strong> - Interpreters & Court Reporters.
									</label>
								</div>
							</div>
						</fieldset>
					</div>
				</div>
				<br><br>
				<button type="Submit" name="btnSubmit" class="btn btn-primary">Next</button>
				<br>
				<span class="tsAppBodyText formIntro" style="text-align:center;"><em>*<small>Denotes required field</small></em></span>
				<cfinclude template="/model/cfformprotect/cffp.cfm" />
			</cfform>
			<br>
		</cfif>
	</cfoutput>
</cfif>